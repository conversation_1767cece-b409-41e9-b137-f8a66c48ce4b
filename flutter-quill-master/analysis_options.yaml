include: package:flutter_lints/flutter.yaml

linter:
  rules:
    always_declare_return_types: true
    always_put_required_named_parameters_first: true
    annotate_overrides: true
    avoid_empty_else: true
    avoid_escaping_inner_quotes: true
    avoid_print: true
    avoid_types_on_closure_parameters: true
    avoid_void_async: true
    cascade_invocations: true
    directives_ordering: true
    omit_local_variable_types: true
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: true
    prefer_const_declarations: true
    prefer_final_fields: true
    prefer_final_in_for_each: true
    prefer_final_locals: true
    prefer_initializing_formals: true
    prefer_int_literals: true
    prefer_interpolation_to_compose_strings: true
    prefer_relative_imports: true
    prefer_single_quotes: true
    sort_constructors_first: true
    sort_unnamed_constructors_first: true
    unnecessary_lambdas: true
    unnecessary_parenthesis: true
    unnecessary_string_interpolations: true
    avoid_web_libraries_in_flutter: true
