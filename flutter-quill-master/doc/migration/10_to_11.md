# 🔄 Migration from 10.x.x to 11.x.x

If you're using version `10.x.x`, we recommend fixing all the deprecations before migrating to `11.x.x` for a smoother migration.

> [!IMPORTANT]
> Once you're able to build and run the app successfully, ensure to read [breaking behavior](#-breaking-behavior).
> See if any changes affect your usage and update the existing code.

## 📋 1. Clipboard

The `super_clipboard` plugin has been removed from `flutter_quill` and `flutter_quill_extensions`.

Remove the following if used:

```diff
- FlutterQuillExtensions.useSuperClipboardPlugin();
```

You can either use our default implementation or continue using `super_clipboard`, if you're unsure, try with **option A** unless you have a reason to use **option B**.

### ⚙️ A. Using the new default implementation

> [!NOTE]
> You only need to remove the `super_clipboard` configuration if you're not using [super_clipboard](https://pub.dev/packages/super_clipboard) which was introduced in your app as a transitive dependency.

The [configuration of `super_clipboard`](https://pub.dev/packages/super_clipboard#getting-started) is no longer required.

The following snippet in your `android/app/src/main/AndroidManifest.xml` **should be removed** otherwise you will be unable to launch the **Android app**:

```xml
<provider
    android:name="com.superlist.super_native_extensions.DataProvider"
    android:authorities="<your-package-name>.SuperClipboardDataProvider"
    android:exported="true"
    android:grantUriPermissions="true" >
</provider>
```

It can be found inside the `<application>` tag if you have [added it](https://pub.dev/packages/super_clipboard#android-support).

See the [`quill_native_bridge` platform configuration](https://pub.dev/packages/quill_native_bridge#-platform-configuration) (**optional** for copying images on **Android**).

#### 🔧 Other Optional changes

The `super_clipboard` is no longer a dependency of `flutter_quill_extensions`.

As such it's no longer required to set the `minSdkVersion` to `23` on **Android**. If the main reason you updated
the version was `flutter_quill_extensions` then you can restore the Flutter default now (currently `21`).

Open the `android/app/build.gradle` file:

- Use the Flutter default `minSdkVersion`:

```kotlin
android {
  defaultConfig {
   minSdk = flutter.minSdkVersion
 }
}
```

- Use the Flutter default `ndkVersion`:

```kotlin
android {
  ndkVersion = flutter.ndkVersion
}
```

> [!NOTE]
> You should only apply this optional change if you're not using
> [`super_clipboard`](https://pub.dev/packages/super_clipboard) or you don't have a reason to change the Flutter default.

### ⚙️ B. Continue using the `super_clipboard` implementation

Use the new default implementation or if you want to continue using `super_clipboard`, use the package [quill_super_clipboard](https://pub.dev/packages/quill_super_clipboard) (**support might be discontinued in future releases**).

> [!WARNING]
> The support of [quill_super_clipboard](https://pub.dev/packages/quill_super_clipboard) might be discontinued. It's still possible to
> override the default implementation manually.

See [#2229](https://github.com/singerdmx/flutter-quill/issues/2229). 

## 📝 2. Quill Controller

The `QuillController` should now be passed to the `QuillEditor` and `QuillSimpleToolbar` constructors instead of the configuration class.

**Before**:

```dart
QuillEditor.basic(
    config: QuillEditorConfig(
      controller: _controller,
    ),
  )
```

**After**:

```dart
QuillEditor.basic(
    controller: _controller,
)
```

<details>
<summary>The change</summary>

```diff
QuillEditor.basic(
+   controller: _controller,
    config: QuillEditorConfig(
-      controller: _controller,
    ),
  )
```

</details>

> [!NOTE]
> The class `QuillEditorConfigurations` has been renamed to `QuillEditorConfig`. See [renames to configuration classes](#5-renames-to-configuration-classes) section.

See [#2037](https://github.com/singerdmx/flutter-quill/discussions/2037) for discussion. Thanks to [#2078](https://github.com/singerdmx/flutter-quill/pull/2078).

> [!TIP]
> The `QuillToolbar` widget has been removed and is no longer
required for custom toolbars, see [removal of the `QuillToolbar`](#️-8-removal-of-the-quilltoolbar) section.

## 🧹 3. Removal of the `QuillEditorProvider` and `QuillToolbarProvider` inherited widgets

It's no longer possible to access the `QuillController`, the `QuillEditorConfiugrations`, and `QuillSimpleToolbarConfigurations` using the `BuildContext`.
Instead, you will have to pass them through constructors (revert to the old behavior).

The extension methods on `BuildContext` like `requireQuillEditorConfigurations`, `quillEditorConfigurations`, and `quillEditorElementOptions` have been removed.

See [#2301](https://github.com/singerdmx/flutter-quill/issues/2301).

## 🌐 4. Required localization delegate

This project uses the [Flutter Localizations library](https://api.flutter.dev/flutter/flutter_localizations/flutter_localizations-library.html), requiring `FlutterQuillLocalizations.delegate` to be included in your app widget (e.g., `MaterialApp`, `WidgetsApp`, `CupertinoApp`).

Previously, we used a helper widget (`FlutterQuillLocalizationsWidget`) to manually provide localization delegates, but this approach was inefficient and error-prone, causing unexpected bugs. It has been removed.

To use the `QuillEditor` and `QuillSimpleToolbar` widgets, add the required delegates as shown:

```dart
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

MaterialApp(
  localizationsDelegates: const [
    // Your other delegates...
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    FlutterQuillLocalizations.delegate,
  ],
);
```

<p align="center">OR (less code with less control)</p>

```dart
import 'package:flutter_quill/flutter_quill.dart';

MaterialApp(
  localizationsDelegates: FlutterQuillLocalizations.localizationsDelegates,
);
```

The widget `FlutterQuillLocalizationsWidget` has been removed.

The library `package:flutter_quill/translations.dart` has been removed and the replacement is `package:flutter_quill/flutter_quill.dart`

## 🔧 5. Renames to configuration classes

- **Renames `QuillEditorConfigurations` to `QuillEditorConfig` and `QuillEditor.configurations` to `QuillEditor.config`.**
- **Renames `QuillRawEditorConfigurations` to `QuillRawEditorConfig` and `QuillRawEditor.configurations` to `QuillRawEditor.config`.**
- **Renames `QuillSimpleToolbarConfigurations` to `QuillSimpleToolbarConfig` and `QuillSimpleToolbar.configurations` to `QuillSimpleToolbar.config`.**
- **Renames `QuillSearchConfigurations` to `QuillSearchConfig` and `QuillEditorConfig.searchConfigurations` to `QuillEditorConfig.searchConfig`.**
- **Renames `QuillControllerConfigurations` to `QuillControllerConfig` and `QuillController.configurations` to `QuillController.config`.** The `configurations` parameter in the `QuillController.basic()` factory constructor was also renamed to `config`.
- **Renames `QuillToolbarImageConfigurations` to `QuillToolbarImageConfig` and `QuillToolbarImageButtonOptions.imageButtonConfigurations` to `QuillToolbarImageButtonOptions.imageButtonConfig`.**

All class names have been updated to replace `Configurations` with `Config`, and the related parameter name has been changed from `configurations` to `config`.

## 🧩 6. Refactoring of the Embed block interface

The `EmbedBuilder.build()` and `EmbedButtonBuilder` have both been changed.

### 📥 The `EmbedBuilder.build()` method

All the properties (except `context`) have been encapsulated into one class `EmbedContext`.

```diff
  Widget build(
    BuildContext context,
-    QuillController controller,
-    Embed node,
-    bool readOnly,
-    bool inline,
-    TextStyle textStyle,
+    EmbedContext embedContext,
  ) {
-   controller.replaceText();
+   embedContext.controller.replaceText();
 }
```

### 🔘 The `EmbedButtonBuilder` function

All the properties have been encapsulated into one class `EmbedButtonContext` and the `BuildContext` property has been added.

```diff
- (controller, toolbarIconSize, iconTheme, dialogTheme) =>
-  QuillToolbarImageButton(
-    controller: controller,
-    options: imageButtonOptions,
-  )
+ (context, embedContext) => QuillToolbarImageButton(
+  controller: embedContext.controller,
+  options: imageButtonOptions,
+ ),
```

The `flutter_quill_extensions` has been updated.

> [!TIP]
> For more details, see [custom embed blocks](https://github.com/singerdmx/flutter-quill/blob/master/doc/custom_embed_blocks.md).

## 🔄 7. The `flutter_quill_extensions`

- Removes `ImagePickerService` from `OnRequestPickVideo` and `OnRequestPickImage`.
- Removes `ImageSaverService` from `ImageOptionsMenu`.
- Removes `QuillSharedExtensionsConfigurations`.
- The return type (`ImageProvider`) of `ImageEmbedBuilderProviderBuilder` has been made `null` so you can return `null` and fallback to our default handling. See [#2317](https://github.com/singerdmx/flutter-quill/pull/2317).
- Removes `QuillSharedExtensionsConfigurations.assetsPrefix`. Use `imageProviderBuilder` to support image assets. See [Image assets support](https://pub.dev/packages/flutter_quill_extensions#-image-assets).
- Removes YouTube video support. To migrate see [CHANGELOG of 10.8.0](https://github.com/singerdmx/flutter-quill/releases/tag/v10.8.0). See [#2284](https://github.com/singerdmx/flutter-quill/issues/2284).
- Removes the deprecated class `FlutterQuillExtensions`.
- Removes the deprecated and experimental table embed support.
- Avoid exporting `flutter_quill_extensions/utils.dart`.

## ✒️ 8. Removal of the `QuillToolbar`

The `QuillToolbar` widget has been removed as it's no longer necessary for `QuillSimpleToolbar` or **custom toolbars**.

Previously, `QuillToolbar` was required to provide a toolbar provider and localization delegate. Additionally, the `QuillToolbarConfigurations` class has been removed.

To migrate, add the [required localization delegate](#-4-required-localization-delegate) in your app widget
and remove the `QuillToolbar`.

```diff
- QuillToolbar(
-  configurations: const QuillToolbarConfigurations(),
-  child: YourCustomToolbar(),
- );
+ YourCustomToolbar();
```

See the [custom toolbar](https://github.com/singerdmx/flutter-quill/blob/master/doc/custom_toolbar.md) page for an example.

Customizing the buttons (that are from `flutter_quill`) within `QuillToolbarConfigurations` in a custom toolbar is **no longer supported**.
Instead, you can use the constructor of each button widget, an example:

```dart
final QuillController _controller = QuillController.basic();
final QuillToolbarBaseButtonOptions _baseOptions = QuillToolbarBaseButtonOptions(
  afterButtonPressed: () {
    // Do something
  }
);

YourCustomToolbar(
  buttons: [
    // Example of using buttons of the `QuillSimpleToolbar` in your custom toolbar.
    // Those buttons are from the flutter_quill library.
    // Pass the _baseOptions to all buttons.
    QuillToolbarToggleStyleButton(
      controller: _controller,
      baseOptions: _baseOptions,
      attribute: Attribute.bold,
    ),
    QuillToolbarClearFormatButton(
      controller: _controller,
      baseOptions: _baseOptions,
    ),
    QuillToolbarFontSizeButton(
      controller: _controller,
      baseOptions: _baseOptions,
      // Override the base button options within options, also allow button-specific options
      options: const QuillToolbarFontSizeButtonOptions(
        items: {'Small': '15', 'Medium': '24.5', 'Large': '46'},
      )
    )
  ]
);
```

> [!NOTE]
> This might be confusing: `QuillToolbar` is **not a visual toolbar** on its own like `QuillSimpleToolbar`. It's a non-visual widget that only 
ensures to provide the localization delegate and the toolbar provider.

<details>

<summary>Expand to see explanation about QuillToolbar vs QuillSimpleToolbar</summary>

This section explains the main difference between `QuillSimpleToolbar` and `QuillToolbar`.

- The `QuillSimpleToolbar` widget is a basic, straightforward toolbar provided by the library, which uses `QuillToolbar` internally.
- The non-visual `QuillToolbar` widget is utilized within `QuillSimpleToolbar` and can also be used to build a custom toolbar.
Before version `11.0.0`, it provided the toolbar provider and localization delegate, 
which supported the buttons provided by the library used in `QuillSimpleToolbar`. For custom toolbars, `QuillToolbar` 
is only needed if you use the library’s toolbar buttons from `flutter_quill`. Those buttons are used in `QuillSimpleToolbar`.

The `QuillToolbar` is different depending on the release you're using:

* On `7.x.x` and older versions, the `QuillToolbar.basic()` was the equivalent of `QuillSimpleToolbar`. The widget `QuillSimpleToolbar` didn't exist.
* On `9.x.x` and newer versions, the `QuillToolbar` has been changed to be a non-visual widget and `QuillSimpleToolbar` was added (the visual widget).
* On `11.0.0` and newer versions, the `QuillToolbar` is no longer needed and has been removed, and the `QuillSimpleToolbar` works without. It is no longer
required for **custom toolbars**.

</details>

## 📎 Minor changes

- `QuillEditorConfig.readOnly` has been removed and is accessible in `QuillController.readOnly`.
- `QuillController.editorFocusNode` has been removed, and is accessible in the `QuillEditor` widget.
- `QuillController.editorConfig` has been removed, and is accessible in the `QuillEditor` widget.
- `QuillEditorBuilderWidget` and `QuillEditorConfig.builder` have been removed as there's no valid use-case and this can be confusing.
- `QuillToolbarLegacySearchDialog` and `QuillToolbarLegacySearchButton` have been removed and replaced with `QuillToolbarSearchDialog` and `QuillToolbarSearchButton` which has been introduced in [9.4.0](https://github.com/singerdmx/flutter-quill/releases/tag/v9.4.0). `QuillSimpleToolbarConfigu.searchButtonType` is removed too.
- The property `dialogBarrierColor` has been removed from all buttons, use the `DialogTheme` in your `ThemeData` instead to customize it. See [Override a theme](https://docs.flutter.dev/cookbook/design/themes#override-a-theme).
- The deprecated members `QuillRawEditorConfig.enableMarkdownStyleConversion` and `QuillEditorConfig.enableMarkdownStyleConversion` has been removed. See [#2214](https://github.com/singerdmx/flutter-quill/issues/2214).
- Removes `QuillSharedConfigurations.extraConfigurations`. The optional configuration of `flutter_quill_extensions` should be separated.
- Renames the classes:
  - `QuillEditorBulletPoint` to `QuillBulletPoint`
  - `QuillEditorCheckboxPoint` to `QuillCheckbox`
  - `QuillEditorNumberPoint` to `QuillNumberPoint`.
- Removes `QuillEditorElementOptions` and `QuillEditorConfig.elementOptions`. To customize the leading, see [#2146](https://github.com/singerdmx/flutter-quill/pull/2146) as an example. The classes related to `QuillEditorElementOptions` such as `QuillEditorCodeBlockElementOptions` has been removed.
- Removes `QuillController.toolbarConfigurations` to not store anything specific to the `QuillSimpleToolbar` in the `QuillController`.
- Removes `QuillToolbarBaseButtonOptions.globalIconSize` and `QuillToolbarBaseButtonOptions.globalIconButtonFactor`. Both are deprecated for at least 10 months.
- Removes `QuillToolbarFontSizeButton.defaultDisplayText` (deprecated for more than 10 months).
- Removes `fontSizesValues` and `fontFamilyValues` from `QuillSimpleToolbarConfig` since those were used only in `QuillToolbarFontSizeButton` and `QuillToolbarFontFamilyButton`. Pass them to `items` (which exist in each button configuration) directly.
- Removes the deprecated library `flutter_quill/extensions.dart` since the name was confusing, it's for `flutter_quill_extensions`.
- Removes the deprecated library `flutter_quill/markdown_quill.dart`. Suggested alternatives: [markdown_quill](https://pub.dev/packages/markdown_quill) or [quill_markdown](https://pub.dev/packages/quill_markdown).
- Removes `Document.fromHtml`. Use an alternative such as [flutter_quill_delta_from_html](https://pub.dev/packages/flutter_quill_delta_from_html).
- Removes `QuillControllerConfig.editorConfig` (not being used and invalid).
- Remove `QuillSharedConfigurations` (it's no longer used). It was previously used to set the `Local` for both `QuillEditor` and `QuillToolbar` simultaneously.
- Removes the experimental method `QuillController.setContents`.
- Renames `isOnTapOutsideEnabled` from `QuillRawEditorConfig` and `QuillEditorConfig` to `onTapOutsideEnabled`.
- Removes editor configuration from `Document`. Instead, only require the needed parameters as internal members. Updates `Line.getPlainText()`.
- The class `OptionalSize` are no longer exported as part of `package:flutter_quill/flutter_quill.dart`.
- Renames `QuillToolbarToggleCheckListButtonOptions.isShouldRequestKeyboard` to `QuillToolbarToggleCheckListButtonOptions.shouldRequestKeyboard`.
- Moved `onClipboardPaste` from `QuillControllerConfig` to `QuillClipboardConfig`. Added `clipboardConfig` property to `QuillControllerConfig`.
- Moved `onImagePaste` and `onGifPaste` from the editor's config (`QuillEditorConfig` or `QuillRawEditorConfig`) to the clipboard's config (`QuillClipboardConfig`), which is part of the controller's config (`QuillControllerConfig`).

## 💥 Breaking behavior

The existing code works and compiles but the functionality has changed in a non-backward-compatible way:

### 1. The `QuillClipboardConfig.onClipboardPaste` is not a fallback anymore when couldn't handle the paste operation by default

The `QuillClipboardConfig.onClipboardPaste` has been updated to allow to override of the default clipboard paste handling instead of only handling the clipboard paste if the default logic didn't paste. See the updated docs comment of [`QuillClipboardConfig.onClipboardPaste`](https://github.com/singerdmx/flutter-quill/blob/master/lib/src/controller/clipboard/quill_clipboard_config.dart#L18-L47) for an example.

Previously it was a fallback function that will be called when the default paste is not handled successfully.

To migrate, use the [`QuillClipboardConfig.onUnprocessedPaste`](https://github.com/singerdmx/flutter-quill/blob/master/lib/src/controller/clipboard/quill_clipboard_config.dart#L49-L53) callback instead.

```diff
- QuillControllerConfig(
-   onClipboardPaste: () {}
- )
+  QuillControllerConfig(
+   clipboardConfig: QuillClipboardConfig(
+     onUnprocessedPaste: () {}
+   )
+ )
```

### 2. No longer handle asset images by default in `flutter_quill_extensions`

The **flutter_quill_extensions** does not handle `AssetImage` anymore by default when loading images, instead use `imageProviderBuilder` to override the default handling. 

To support loading image assets (images bundled within your app):

```dart
FlutterQuillEmbeds.editorBuilders(
    imageEmbedConfig:
        QuillEditorImageEmbedConfig(
      imageProviderBuilder: (context, imageUrl) {
        if (imageUrl.startsWith('assets/')) {
          return AssetImage(imageUrl);
        }
        // Fallback to default handling
        return null;
      },
    ),  
)
```

Ensures to replace `assets` with your assets directory name or change the logic to fit your needs.

### 3. No longer request editor focus by default after pressing a `QuillSimpleToolbar`'s button

The `QuillSimpleToolbar` and related toolbar buttons no longer request focus from the editor after pressing a button (**revert to the old behavior**).

Here is a minimal example to use to the old behavior using `QuillSimpleToolbar`:

```dart
final QuillController _controller = QuillController.basic();
final _editorFocusNode = FocusNode();
final _editorScrollController = ScrollController();

QuillSimpleToolbar(
  controller: _controller,
  config: QuillSimpleToolbarConfig(
    buttonOptions: QuillSimpleToolbarButtonOptions(
      base: QuillToolbarBaseButtonOptions(
        afterButtonPressed: _editorFocusNode.requestFocus
      )
    )
  )
),
Expanded(
  child: QuillEditor(controller: _controller, focusNode: _editorFocusNode, scrollController: _editorScrollController)
)
```

With a custom toolbar:

```dart
final QuillController _controller = QuillController.basic();
final _editorFocusNode = FocusNode();
final _editorScrollController = ScrollController();

final QuillToolbarBaseButtonOptions _baseOptions = QuillToolbarBaseButtonOptions(
  afterButtonPressed: _editorFocusNode.requestFocus
);

YourCustomToolbar(
  buttons: [
    // Pass the _baseOptions to all buttons.
    QuillToolbarClearFormatButton(
      controller: _controller,
      baseOptions: _baseOptions,
    ),
    QuillToolbarFontSizeButton(
      controller: _controller,
      baseOptions: _baseOptions,
    ),
    // all the other buttons
  ]
),
Expanded(
  child: QuillEditor(controller: _controller, focusNode: _editorFocusNode, scrollController: _editorScrollController)
)
```

Don't forgot to dispose the `QuillController`, `FocusNode` and `ScrollController` in the `dispose()` method:

```dart
@override
void dispose() {
  _controller.dispose();
  _editorFocusNode.dispose();
  _editorScrollController.dispose();
  super.dispose();
}
```

## 🚧 Experimental

APIs that were indicated as stable but are now updated to indicate
that they are experimental, which means that they might be removed or changed
in non-major releases:

- The `QuillSearchConfig` and search within embed objects feature. Related [#2090](https://github.com/singerdmx/flutter-quill/pull/2090).
- The `QuillController.clipboardPaste()` and `QuillEditorConfig.onGifPaste`.
- The `QuillEditorConfig.characterShortcutEvents` and `QuillEditorConfig.spaceShortcutEvents`.
- The `QuillControllerConfig.onClipboardPaste`.
- The `QuillEditorConfig.customLeadingBlockBuilder`.
- The magnifier feature including `QuillEditorConfig.magnifierConfiguration`.
- The `shouldNotifyListeners` in `QuillController.replaceText()`, `QuillController.replaceText()`, `QuillController.formatSelection()`.
- The `QuillController.clipboardSelection()`.
- The `CopyCutServiceProvider`, `CopyCutService`, and `DefaultCopyCutService`.

The functionality itself has not changed and no experimental changes were introduced.
