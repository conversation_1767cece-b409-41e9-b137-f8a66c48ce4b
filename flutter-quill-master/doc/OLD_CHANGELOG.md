# Changelog

All notable changes to this project will be documented in this file.

> [!NOTE]
> This is the archived version of the `CHANGELOG.md` file. See [the newer `CHANGELOG.md`](https://github.com/singerdmx/flutter-quill/blob/master/CHANGELOG.md).

## 11.0.0-dev.1

This release is identical to [11.0.0-dev.0](https://github.com/singerdmx/flutter-quill/releases/tag/v11.0.0-dev.0), mainly published to update the min published version (from pub.dev) of `flutter_quill` in `flutter_quill_test` and `flutter_quill_extensions`.

See the [migration guide](https://github.com/singerdmx/flutter-quill/blob/master/doc/migration/10_to_11.md).

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v11.0.0-dev.0...v11.0.0-dev.1

## 10.8.5

* fix: allow all correct URLs to be formatted by @orevial in https://github.com/singerdmx/flutter-quill/pull/2328
* fix(macos): Implement actions for ExpandSelectionToDocumentBoundaryIntent and ExpandSelectionToLineBreakIntent to use keyboard shortcuts, unrelated cleanup to the bug fix. by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2279

## New Contributors
* @orevial made their first contribution at https://github.com/singerdmx/flutter-quill/pull/2328

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.8.4...v10.8.5

## 10.8.4

- [Fixes an unhandled exception](https://github.com/singerdmx/flutter-quill/commit/8dd559b825030d29b30b32b353a08dcc13dc42b7) in case `getClipboardFiles()` wasn't supported
- [Updates min version](https://github.com/singerdmx/flutter-quill/commit/49569e47b038c5f61b7521571c102cf5ad5a0e3f) of internal dependency `quill_native_bridge`

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.8.3...v10.8.4

## 10.8.3

This release is identical to [v10.8.3-dev.0](https://github.com/singerdmx/flutter-quill/releases/tag/v10.8.3-dev.0), mainly published to bump the minimum version of [flutter_quill](https://pub.dev/packages/flutter_quill) in [flutter_quill_extensions](https://pub.dev/packages/flutter_quill_extensions) and to publish [quill-super-clipboard](https://github.com/FlutterQuill/quill-super-clipboard/).

A new identical release `10.9.0` will be published soon with a release description.

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.8.2...v10.8.3

## 10.8.3-dev.0

A non-pre-release version with this change will be published soon.

* feat: Use quill_native_bridge as default impl in DefaultClipboardService, fix related bugs in the extensions package by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2230


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.8.2...v10.8.3-dev.0

## 10.8.2

* Fixed minor typo in Hungarian (hu) localization by @G-Greg in https://github.com/singerdmx/flutter-quill/pull/2307


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.8.1...v10.8.2

## 10.8.1

- This release fixes the compilation issue when building the project with [Flutter/Wasm](https://docs.flutter.dev/platform-integration/web/wasm) target on the web. Also, update the conditional import check to avoid using `dart.library.html`:

  ```dart
  import 'web/quill_controller_web_stub.dart'
      if (dart.library.html) 'web/quill_controller_web_real.dart';
  ```
  
  To fix critical bugs that prevent using the editor on Wasm.
  
  > Flutter/Wasm is stable as of [Flutter 3.22](https://medium.com/flutter/whats-new-in-flutter-3-22-fbde6c164fe3) though it's likely that you might experience some issues when using this new target, if you experienced any issues related to Wasm support related to Flutter Quill, feel free to [open an issue](https://github.com/singerdmx/flutter-quill/issues).
  
  Issue #1889 is fixed by temporarily replacing the plugin [flutter_keyboard_visibility](https://pub.dev/packages/flutter_keyboard_visibility) with [flutter_keyboard_visibility_temp_fork](https://pub.dev/packages/flutter_keyboard_visibility_temp_fork) since `flutter_keyboard_visibility` depend on `dart:html`. Also updated the `compileSdkVersion` to `34` instead of `31` as a workaround to [Flutter #63533](https://github.com/flutter/flutter/issues/63533).

- Support for Hungarian (hu) localization was added by @G-Greg in https://github.com/singerdmx/flutter-quill/pull/2291.
- [dart_quill_delta](https://pub.dev/packages/dart_quill_delta/) has been moved to [FlutterQuill/dart-quill-delta](https://github.com/FlutterQuill/dart-quill-delta) (outside of this repo) and they have separated version now.


## New Contributors
* @G-Greg made their first contribution at https://github.com/singerdmx/flutter-quill/pull/2291

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.8.0...v10.8.1

## 10.8.0

> [!CAUTION]
> This release can be breaking change for `flutter_quill_extensions` users as it remove the built-in support for loading YouTube videos

If you're using [flutter_quill_extensions](https://pub.dev/packages/flutter_quill_extensions) then this release, can be a breaking change for you if you load videos in the editor and expect YouTube videos to be supported, [youtube_player_flutter](https://pub.dev/packages/youtube_player_flutter) and [flutter_inappwebview](https://pub.dev/packages/flutter_inappwebview) are no longer dependencies of the extensions package, which are used to support loading YouTube Iframe videos on non-web platforms, more details about the discussion and reasons in [#2286](https://github.com/singerdmx/flutter-quill/pull/2286) and [#2284](https://github.com/singerdmx/flutter-quill/issues/2284).

We have added an experimental property that gives you more flexibility and control about the implementation you want to use for loading videos.

> [!WARNING]
> It's likely to experience some common issues while implementing this feature, especially on desktop platforms as the support for [flutter_inappwebview_windows](https://pub.dev/packages/flutter_inappwebview_windows) and [flutter_inappwebview_macos](https://pub.dev/packages/flutter_inappwebview_macos) before 2 days. Some of the issues are in the Flutter Quill editor.

If you want loading YouTube videos to be a feature again with the latest version of Flutter Quill, you can use an existing plugin or package, or implement your own solution. For example, you might use [`youtube_video_player`](https://pub.dev/packages/youtube_video_player) or [`youtube_player_flutter`](https://pub.dev/packages/youtube_player_flutter), which was previously used in [`flutter_quill_extensions`](https://pub.dev/packages/flutter_quill_extensions).

Here’s an example setup using `youtube_player_flutter`:

```shell
flutter pub add youtube_player_flutter
```

Example widget configuration:

```dart
import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class YoutubeVideoPlayer extends StatefulWidget {
  const YoutubeVideoPlayer({required this.videoUrl, super.key});

  final String videoUrl;

  @override
  State<YoutubeVideoPlayer> createState() => _YoutubeVideoPlayerState();
}

class _YoutubeVideoPlayerState extends State<YoutubeVideoPlayer> {
  late final YoutubePlayerController _youtubePlayerController;
  @override
  void initState() {
    super.initState();
    _youtubePlayerController = YoutubePlayerController(
      initialVideoId: YoutubePlayer.convertUrlToId(widget.videoUrl) ??
          (throw StateError('Expect a valid video URL')),
      flags: const YoutubePlayerFlags(
        autoPlay: true,
        mute: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayer(
      controller: _youtubePlayerController,
      showVideoProgressIndicator: true,
    );
  }

  @override
  void dispose() {
    _youtubePlayerController.dispose();
    super.dispose();
  }
}

```

Then, integrate it with `QuillEditorVideoEmbedConfigurations`

```dart
FlutterQuillEmbeds.editorBuilders(
      videoEmbedConfigurations: QuillEditorVideoEmbedConfigurations(
        customVideoBuilder: (videoUrl, readOnly) {
          // Example: Check for YouTube Video URL and return your
          // YouTube video widget here.
          bool isYouTubeUrl(String videoUrl) {
            try {
              final uri = Uri.parse(videoUrl);
              return uri.host == 'www.youtube.com' ||
                  uri.host == 'youtube.com' ||
                  uri.host == 'youtu.be' ||
                  uri.host == 'www.youtu.be';
            } catch (_) {
              return false;
            }
          }

          if (isYouTubeUrl(videoUrl)) {
            return YoutubeVideoPlayer(
              videoUrl: videoUrl,
            );
          }

          // Return null to fallback to the default logic
          return null;
        },
        ignoreYouTubeSupport: true,
      ),
);
```

> [!NOTE]
> This example illustrates a basic approach, additional adjustments might be necessary to meet your specific needs. YouTube video support is no longer included in this project. Keep in mind that `customVideoBuilder` is experimental and can change without being considered as breaking change. More details in [breaking changes](https://github.com/singerdmx/flutter-quill#-breaking-changes) section.

[`super_clipboard`](https://pub.dev/packages/super_clipboard) will also no longer a dependency of `flutter_quill_extensions` once [PR #2230](https://github.com/singerdmx/flutter-quill/pull/2230) is ready.

We're looking forward to your feedback.

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.7...v10.8.0

## 10.7.7

This version is nearly identical to `10.7.6` with a build failure bug fix in [#2283](https://github.com/singerdmx/flutter-quill/pull/2283) related to unmerged change in [#2230](https://github.com/singerdmx/flutter-quill/pull/2230)


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.6...v10.7.7

## 10.7.6

* Code Comments Typo fixes by @Luismi74 in https://github.com/singerdmx/flutter-quill/pull/2267
* docs: add important note for contributors before introducing new features by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2269
* docs(readme): add 'Breaking Changes' section by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2275
* Fix: Resolved issue with broken IME composing rect in Windows desktop(re-implementation) by @agata in https://github.com/singerdmx/flutter-quill/pull/2282

## New Contributors
* @Luismi74 made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2267

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.5...v10.7.6

## 10.7.5

* fix(ci): add flutter pub get step for quill_native_bridge by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2265
* revert: "Resolved issue with broken IME composing rect in Windows desktop" by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2266


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.4...v10.7.5

## 10.7.4

* chore: remove pubspec_overrides.yaml and pubspec_overrides.yaml.disabled by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2262
* ci: remove quill_native_bridge from automated publishing workflow by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2263


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.3...v10.7.4

## 10.7.3

- Deprecate `FlutterQuillExtensions` in `flutter_quill_extensions`
- Update the minimum version of `flutter_quill` and `super_clipboard` in `flutter_quill_extensions` to avoid using deprecated code.

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.2...v10.7.3

## 10.7.2

## What's Changed
* chore: deprecate flutter_quill/extensions.dart by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2258

This is a minor release introduced to upload a new version of `flutter_quill` and `flutter_quill_extensions` to update the minimum required to avoid using deprecated code in `flutter_quill_extensions`.


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.1...v10.7.2

## 10.7.1

* chore: deprecate markdown_quill export, ignore warnings by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2256
* chore: deprecate spell checker service by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2255


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.7.0...v10.7.1

## 10.7.0

* Chore: deprecate embed table feature by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2254


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.6.6...v10.7.0

## 10.6.6

* Bug fix: Removing check not allowing spell check on web by @joeserhtf in https://github.com/singerdmx/flutter-quill/pull/2252

## New Contributors
* @joeserhtf made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2252

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.6.5...v10.6.6

## 10.6.5

* Refine IME composing range styling by applying underline as text style by @agata in https://github.com/singerdmx/flutter-quill/pull/2244


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.6.4...v10.6.5

## 10.6.4

* fix: the composing text did not show an underline during IME conversion by @agata in https://github.com/singerdmx/flutter-quill/pull/2242


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.6.3...v10.6.4

## 10.6.3

* Fix: Resolved issue with broken IME composing rect in Windows desktop by @agata in https://github.com/singerdmx/flutter-quill/pull/2239


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.6.2...v10.6.3

## 10.6.2

* Fix: QuillToolbarToggleStyleButton Switching failure by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2234


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.6.1...v10.6.2

## 10.6.1

* Chore: update `flutter_quill_delta_from_html` to remove exception calls by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2232


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.6.0...v10.6.1

## 10.6.0

* docs: cleanup the docs, remove outdated resources, general changes by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2227
* Feat: customizable character and space shortcut events by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2228


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.19...v10.6.0

## 10.5.19

* fix: properties other than 'style' for custom inline code styles (such as 'backgroundColor') were not being applied correctly by @agata in https://github.com/singerdmx/flutter-quill/pull/2226


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.18...v10.5.19

## 10.5.18

* feat(web): rich text paste from Clipboard using HTML by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2009
* revert: disable rich text paste feature on web as a workaround by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2221
* refactor: moved shortcuts and onKeyEvents to its own file by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2223


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.17...v10.5.18

## 10.5.17

* feat(l10n): localize all untranslated.json by @erdnx in https://github.com/singerdmx/flutter-quill/pull/2217
* Fix: Block Attributes are not displayed if the editor is empty by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2210

## New Contributors
* @erdnx made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2217

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.16...v10.5.17

## 10.5.16

* chore: remove device_info_plus and add quill_native_bridge to access platform specific APIs by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2194
* Not show/update/hiden mangnifier when manifier config is disbale by @demoYang in https://github.com/singerdmx/flutter-quill/pull/2212


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.14...v10.5.16

## 10.5.15-dev.0

Introduce `quill_native_bridge` which is an internal plugin to use by `flutter_quill` to access platform APIs.

For now, the only functionality it supports is to check whatever the iOS app is running on iOS simulator without requiring [`device_info_plus`](pub.dev/packages/device_info_plus) as a dependency.

> [!NOTE]
> `quill_native_bridge` is a plugin for internal use and should not be used in production applications
> as breaking changes can happen and can removed at any time.

For more details and discussion see [#2194](https://github.com/singerdmx/flutter-quill/pull/2194).

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.14...v10.5.15-dev.0

## 10.5.14

* chore(localization): add Greek language support by @DKalathas in https://github.com/singerdmx/flutter-quill/pull/2206

## New Contributors
* @DKalathas made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2206

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.13...v10.5.14

## 10.5.13

* Revert "Fix: Allow backspace at start of document to remove block style and header style by @agata in https://github.com/singerdmx/flutter-quill/pull/2201


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.12...v10.5.13

## 10.5.12

* Fix: Backspace remove block attributes at start by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2200


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.11...v10.5.12

## 10.5.11

* Enhancement: Backspace handling at the start of blocks in delete rules by @agata in https://github.com/singerdmx/flutter-quill/pull/2199


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.10...v10.5.11

## 10.5.10

* Allow backspace at start of document to remove block style and header style by @agata in https://github.com/singerdmx/flutter-quill/pull/2198


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.9...v10.5.10

## 10.5.9

* chore: improve platform check by using constants and defaultTargetPlatform by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2188


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.8...v10.5.9

## 10.5.8

* Feat: Add configuration option to always indent on TAB key press by @agata in https://github.com/singerdmx/flutter-quill/pull/2187

## New Contributors
* @agata made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2187

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.7...v10.5.8

## 10.5.7

* chore(example): downgrade Kotlin from 1.9.24 to 1.7.10 by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2185
* style: refactor build leading function style, width, and padding parameters for custom node leading builder by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2182


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.6...v10.5.7

## 10.5.6

* chore(deps): update super_clipboard to 0.8.20 in flutter_quill_extensions by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2181
* Update quill_screen.dart, i chaged the logic for showing a lock when … by @rightpossible in https://github.com/singerdmx/flutter-quill/pull/2183

## New Contributors
* @rightpossible made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2183

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.5...v10.5.6

## 10.5.5

* Fix text selection handles when scroll mobile by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2176


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.4...v10.5.5

## 10.5.4

* Add Thai (th) localization by @silkyland in https://github.com/singerdmx/flutter-quill/pull/2175

## New Contributors
* @silkyland made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2175

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.3...v10.5.4

## 10.5.3

* Fix: Assertion Failure in line.dart When Editing Text with Block-Level Attributes by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2174


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.2...v10.5.3

## 10.5.2

* fix(toolbar): regard showDividers in simple toolbar by @realth000 in https://github.com/singerdmx/flutter-quill/pull/2172

## New Contributors
* @realth000 made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2172

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.1...v10.5.2

## 10.5.1

* fix drag selection extension (does not start at tap location if you are dragging quickly by @jezell in https://github.com/singerdmx/flutter-quill/pull/2170


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.5.0...v10.5.1

## 10.5.0

* Feat: custom leading builder by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2146


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.9...v10.5.0

## 10.4.9

* fix floating cursor not disappearing after scroll end by @vishna in https://github.com/singerdmx/flutter-quill/pull/2163


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.8...v10.4.9

## 10.4.8

* Fix: direction has no opposite effect if the language is rtl by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2154


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.7...v10.4.8

## 10.4.7

* Fix: Unable to scroll 2nd editor window by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2152


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.6...v10.4.7

## 10.4.6

* Handle null child query by @jezell in https://github.com/singerdmx/flutter-quill/pull/2151


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.5...v10.4.6

## 10.4.5

* chore!: move spell checker to example by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2145


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.4...v10.4.5

## 10.4.4

* fix custom recognizer builder not being passed to editabletextblock by @jezell in https://github.com/singerdmx/flutter-quill/pull/2143
* fix null reference exception when dragging selection on non scrollable selection by @jezell in https://github.com/singerdmx/flutter-quill/pull/2144


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.3...v10.4.4

## 10.4.3

* Chore: update simple_spell_checker package by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2139


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.2...v10.4.3

## 10.4.2

* Revert "fix: Double click to select text sometimes doesn't work. ([#2086](https://github.com/singerdmx/flutter-quill/pull/2086))

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.1...v10.4.2

## 10.4.1

* Chore: improve Spell checker API to the example by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2133


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.4.0...v10.4.1

## 10.4.0

* Copy TapAndPanGestureRecognizer from TextField by @demoYang in https://github.com/singerdmx/flutter-quill/pull/2128
* enhance stringToColor with a custom defined palette from `DefaultStyles` by @vishna in https://github.com/singerdmx/flutter-quill/pull/2095
* Feat: include spell checker for example app by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2127

## New Contributors
* @vishna made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2095

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.3.3...v10.4.0

## 10.3.2

* Fix: Loss of style when backspace by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2125


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.3.1...v10.3.2

## 10.3.1

* Chore: Move spellchecker service to extensions by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2120


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.3.0...v10.3.1

## 10.3.0

* Feat: Spellchecker for Flutter Quill by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2118


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.2.1...v10.3.0

## 10.2.1

* Fix: context menu is visible even when selection is collapsed by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2116
* Fix: unsafe operation while getting overlayEntry in text_selection by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2117


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.2.0...v10.2.1

## 10.2.0

* refactor!: restructure project into modular architecture for flutter_quill_extensions by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2106
* Fix: Link selection and editing by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2114


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.10...v10.2.0

## 10.1.10

* Fix(example): image_cropper outdated version by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2100
* Using dart.library.js_interop instead of dart.library.html by @h1376h in https://github.com/singerdmx/flutter-quill/pull/2103

## New Contributors
* @h1376h made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2103

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.9...v10.1.10

## 10.1.9

* restore ability to pass in key to QuillEditor by @mtallenca in https://github.com/singerdmx/flutter-quill/pull/2093


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.8...v10.1.9

## 10.1.8

* Enhancement: Search within Embed objects by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2090


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.7...v10.1.8

## 10.1.7

* Feature/allow shortcut override by @InstrinsicAutomations in https://github.com/singerdmx/flutter-quill/pull/2089

## New Contributors
* @InstrinsicAutomations made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2089

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.6...v10.1.7

## 10.1.6

* fixed #1295 Double click to select text sometimes doesn't work. by @li8607 in https://github.com/singerdmx/flutter-quill/pull/2086


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.5...v10.1.6

## 10.1.5

* ref: add `VerticalSpacing.zero` and `HorizontalSpacing.zero` named constants by @adil192 in https://github.com/singerdmx/flutter-quill/pull/2083


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.4...v10.1.5

## 10.1.4

* Fix: collectStyles for lists and alignments by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2082


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.3...v10.1.4

## 10.1.3

* Move Controller outside of configurations data class by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2078


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.2...v10.1.3

## 10.1.2

* Fix Multiline paste with attributes and embeds by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2074


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.1...v10.1.2

## 10.1.1

* Toolbar dividers fixes + Docs updates by @troyanskiy in https://github.com/singerdmx/flutter-quill/pull/2071

## New Contributors
* @troyanskiy made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2071

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.1.0...v10.1.1

## 10.1.0

* Feat: support for customize copy and cut Embeddables to Clipboard by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2067


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.10...v10.1.0

## 10.0.10

* fix: Hide selection toolbar if editor loses focus by @huandu in https://github.com/singerdmx/flutter-quill/pull/2066


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.9...v10.0.10

## 10.0.9

* Fix: manual checking of directionality by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2063


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.8...v10.0.9

## 10.0.8

* feat: add callback to handle performAction by @huandu in https://github.com/singerdmx/flutter-quill/pull/2061
* fix: Invalid selection when tapping placeholder text by @huandu in https://github.com/singerdmx/flutter-quill/pull/2062


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.7...v10.0.8

## 10.0.7

* Fix: RTL issues by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2060


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.6...v10.0.7

## 10.0.6

* fix: textInputAction is not set when creating QuillRawEditorConfiguration by @huandu in https://github.com/singerdmx/flutter-quill/pull/2057

## New Contributors
* @huandu made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2057

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.5...v10.0.6

## 10.0.5

* Add tests for PreserveInlineStylesRule and fix link editing. Other minor fixes. by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2058


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.4...v10.0.5

## 10.0.4

* Add ability to set up horizontal spacing for block style by @dimkanovikov in https://github.com/singerdmx/flutter-quill/pull/2051
* add catalan language by @spilioio in https://github.com/singerdmx/flutter-quill/pull/2054

## New Contributors
* @dimkanovikov made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2051
* @spilioio made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2054

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.3...v10.0.4

## 10.0.3

* doc(Delta): more documentation about Delta by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2042
* doc(attribute): added documentation about Attribute class and how create one by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2048
* if magnifier removes toolbar, restore it when it is hidden by @mtallenca in https://github.com/singerdmx/flutter-quill/pull/2049


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.2...v10.0.3

## 10.0.2

* chore(scripts): migrate the scripts from sh to dart by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2036
* Have the ability to create custom rules, closes #1162 by @Guillergood in https://github.com/singerdmx/flutter-quill/pull/2040


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.1...v10.0.2

## 10.0.1

This release is identical to [10.0.0](https://github.com/singerdmx/flutter-quill/releases/tag/v10.0.0) with a fix that addresses issue #2034 by requiring `10.0.0` as the minimum version for quill related dependencies.

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v10.0.0...v10.0.1

## 10.0.0

* refactor: restructure project into modular architecture for flutter_quill by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2032
* chore: update GitHub PR template by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2033


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.6.0...v10.0.0

## 9.6.0

* [feature] : quill add magnifier by @demoYang in https://github.com/singerdmx/flutter-quill/pull/2026

## New Contributors
* @demoYang made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2026

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.23...v9.6.0

## 9.5.23

* add untranslated Kurdish keys by @Xoshbin in https://github.com/singerdmx/flutter-quill/pull/2029


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.22...v9.5.23

## 9.5.22

* Fix outdated contributor guide link on PR template by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2027
* Fix(rule): PreserveInlineStyleRule assume the type of the operation data and throw stacktrace by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2028


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.21...v9.5.22

## 9.5.21

* Fix: Key actions not being handled by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/2025


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.20...v9.5.21

## 9.5.20

* Remove useless delta_x_test by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2017
* Update flutter_quill_delta_from_html package on pubspec.yaml by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2018


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.19...v9.5.20

## 9.5.19

* fixed #1835 Embed Reloads on Cmd Key Press by @li8607 in https://github.com/singerdmx/flutter-quill/pull/2013

## New Contributors
* @li8607 made their first contribution in https://github.com/singerdmx/flutter-quill/pull/2013

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.18...v9.5.19

## 9.5.18

* Refactor: Moved core link button functions to link.dart by @Alspb in https://github.com/singerdmx/flutter-quill/pull/2008
* doc: more documentation about Rules by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2014


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.17...v9.5.18

## 9.5.17

* Feat(config): added option to disable automatic list conversion by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2011


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.16...v9.5.17

## 9.5.16

* chore: drop support for HTML, PDF, and Markdown converting functions by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/1997
* docs(readme): update the extensions package to document the Rich Text Paste feature on web by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/2001
* Fix(test): delta_x tests fail by wrong expected Delta for video embed by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2010


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.15...v9.5.16

## 9.5.15

* Update delta_from_html to fix nested lists issues and more by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/2000


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.14...v9.5.15

## 9.5.14

* docs(readme): update 'Conversion to HTML' section to include more details by @EchoEllet in https://github.com/singerdmx/flutter-quill/pull/1996
* Update flutter_quill_delta_from_html on pubspec.yaml to fix current issues by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1999


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.13...v9.5.14

## 9.5.13

* Added new default ConverterOptions configurations by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1990


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.12...v9.5.13

## 9.5.12

* fix: Fixed passing textStyle to formula embed by @shubham030 in https://github.com/singerdmx/flutter-quill/pull/1989

## New Contributors
* @shubham030 made their first contribution in https://github.com/singerdmx/flutter-quill/pull/1989

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.11...v9.5.12

## 9.5.11

* Update flutter_quill_delta_from_html in pubspec.yaml by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1988


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.10...v9.5.11

## 9.5.10

* chore: remove dependency html converter by @ellet0 in https://github.com/singerdmx/flutter-quill/pull/1987
* Fix: LineHeight button to use MenuAnchor by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/1986


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.9...v9.5.10

## 9.5.9

* Update pubspec.yaml to remove html2md by @singerdmx in https://github.com/singerdmx/flutter-quill/pull/1985


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.8...v9.5.9

## 9.5.8

* fix(typo): fix typo ClipboardServiceProvider.instacne by @ellet0 in https://github.com/singerdmx/flutter-quill/pull/1983
* Feat: New way to get Delta from HTML inputs by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1984


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.7...v9.5.8

## 9.5.7

* refactor: context menu function, add test code by @n7484443 in https://github.com/singerdmx/flutter-quill/pull/1979
* Fix: PreserveInlineStylesRule by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/1980


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.6...v9.5.7

## 9.5.6

* fix: common link is detected as a video link by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1978


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.5...v9.5.6

## 9.5.5

* fix: context menu behavior in mouse, desktop env by @n7484443 in https://github.com/singerdmx/flutter-quill/pull/1976


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.4...v9.5.5

## 9.5.4

* Feat: Line height support by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1972


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.3...v9.5.4

## 9.5.3

* Perf: Performance optimization by @Alspb in https://github.com/singerdmx/flutter-quill/pull/1964


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.2...v9.5.3

## 9.5.2

* Fix style settings by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/1962


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.1...v9.5.2

## 9.5.1

* feat(extensions): Youtube Video Player Support Mode by @ellet0 in https://github.com/singerdmx/flutter-quill/pull/1916


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.5.0...v9.5.1

## 9.5.0

* Partial support for table embed by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1960


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.9...v9.5.0

## 9.4.9

* Upgrade photo_view to 0.15.0 for flutter_quill_extensions by @singerdmx in https://github.com/singerdmx/flutter-quill/pull/1958


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.8...v9.4.9

## 9.4.8

* Add support for html underline and videos  by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1955


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.7...v9.4.8

## 9.4.7

* fixed #1953 italic detection error by @CatHood0 in https://github.com/singerdmx/flutter-quill/pull/1954

## New Contributors
* @CatHood0 made their first contribution in https://github.com/singerdmx/flutter-quill/pull/1954

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.6...v9.4.7

## 9.4.6

* fix: search dialog throw an exception due to missing FlutterQuillLocalizations.delegate in the editor by @ellet0 in https://github.com/singerdmx/flutter-quill/pull/1938
* fix(editor): implement editor shortcut action for home and end keys to fix exception about unimplemented ScrollToDocumentBoundaryIntent by @ellet0 in https://github.com/singerdmx/flutter-quill/pull/1937


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.5...v9.4.6

## 9.4.5

* fix: color picker hex unfocus on web by @geronimol in https://github.com/singerdmx/flutter-quill/pull/1934

## New Contributors
* @geronimol made their first contribution in https://github.com/singerdmx/flutter-quill/pull/1934

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.4...v9.4.5

## 9.4.4

* fix: Enabled link regex to be overridden by @JoepHeijnen in https://github.com/singerdmx/flutter-quill/pull/1931


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.3...v9.4.4

## 9.4.3

* Fix: setState() called after dispose(): QuillToolbarClipboardButtonState #1895 by @windows7lake in https://github.com/singerdmx/flutter-quill/pull/1926

## New Contributors
* @windows7lake made their first contribution in https://github.com/singerdmx/flutter-quill/pull/1926

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.2...v9.4.3

## 9.4.2

* Respect autofocus, closes #1923 by @Guillergood in https://github.com/singerdmx/flutter-quill/pull/1924


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.1...v9.4.2

## 9.4.1

* replace base64 regex string by @salba360496 in https://github.com/singerdmx/flutter-quill/pull/1919

## New Contributors
* @salba360496 made their first contribution in https://github.com/singerdmx/flutter-quill/pull/1919

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.4.0...v9.4.1

## 9.4.0

This release can be used without changing anything, although it can break the behavior a little, we provided a way to use the old behavior in `9.3.x`

- Thanks to @Alspb, the search bar/dialog has been reworked for improved UI that fits **Material 3** look and feel, the search happens on the fly, and other minor changes, if you want the old search bar, you can restore it with one line if you're using `QuillSimpleToolbar`:
  ```dart
  QuillToolbar.simple(
    configurations: QuillSimpleToolbarConfigurations(
      searchButtonType: SearchButtonType.legacy,
    ),
  )
  ```
  While the changes are mostly to the `QuillToolbarSearchDialog` and it seems this should be `searchDialogType`, we provided the old button with the old dialog in case we update the button in the future.

   If you're using `QuillToolbarSearchButton` in a custom Toolbar, you don't need anything to get the new button. if you want the old button, use the `QuillToolbarLegacySearchButton` widget
    
    Consider using the improved button with the improved dialog as the legacy button might removed in future releases (for now, it's not deprecated)

  <details>
  <summary>Before</summary>
  
  ![image](https://github.com/singerdmx/flutter-quill/assets/73608287/9b40ad03-717f-4518-95f1-8d9cad773b2b)
  
  
  </details> 
  
   <details>
  <summary>Improved</summary>
  
  ![image](https://github.com/singerdmx/flutter-quill/assets/73608287/e581733d-63fa-4984-9c41-4a325a0a0c04)
  
  </details>
  
  For the detailed changes, see #1904

- Korean translations by @leegh519 in https://github.com/singerdmx/flutter-quill/pull/1911

- The usage of `super_clipboard` plugin in `flutter_quill` has been moved to the `flutter_quill_extensions` package, this will restore the old behavior in `8.x.x` though it will break the `onImagePaste`, `onGifPaste` and rich text pasting from HTML or Markdown, most of those features are available in `super_clipboard` plugin except `onImagePaste` which was available as we were using [pasteboard](https://pub.dev/packages/pasteboard), Unfortunately, it's no longer supported on recent versions of Flutter, and some functionalities such as an image from Clipboard and Html paste are not supported on some platforms such as Android, your project will continue to work, calls of `onImagePaste` and `onGifPaste` will be ignored unless you include [flutter_quill_extensions](https://pub.dev/packages/flutter_quill_extensions) package in your project and call:

  ```dart
  FlutterQuillExtensions.useSuperClipboardPlugin();
  ```
  Before using any `flutter_quill` widgets, this will restore the old behavior in `9.x.x`
  
  We initially wanted to publish `flutter_quill_super_clipboard` to allow:
  - Using `super_clipboard` without `flutter_quill_extensions` packages and plugins
  - Using `flutter_quill_extensions` with optional `super_clipboard`
  
  To simplify the usage, we moved it to `flutter_quill_extensions`, let us know if you want any of the use cases above.
  
  Overall `super_clipboard` is a Comprehensive clipboard plugin with a lot of features, the only thing that developers didn't want is Rust installation even though it's automated.

 The main goal of `ClipboardService` is to make `super_clipboard` optional, you can use your own implementation, and create a class that implements `ClipboardService`, which you can get by:
 ```dart
  // ignore: implementation_imports
  import 'package:flutter_quill/src/services/clipboard/clipboard_service.dart';
 ```

 Then you can call:
 ```dart
 // ignore: implementation_imports
import 'package:flutter_quill/src/services/clipboard/clipboard_service_provider.dart';
 ClipboardServiceProvider.setInstance(YourClipboardService());
```
 
 The interface could change at any time and will be updated internally for `flutter_quill` and `flutter_quill_extensions`, we didn't export those two classes by default to avoid breaking changes in case you use them as we might change them in the future.

 If you use the above imports, you might get **breaking changes** in **non-breaking change releases**.

- Subscript and Superscript should now work for all languages and characters

  The previous implementation required the Apple 'SF-Pro-Display-Regular.otf' font which is only licensed/permitted for use on Apple devices.
We have removed the Apple font from the example

- Allow pasting Markdown and HTML file content from the system to the editor

  Before `9.4.x` if you try to copy an HTML or Markdown file, and paste it into the editor, you will get the file name in the editor
  Copying an HTML file, or HTML content from apps and websites is different than copying plain text.

  This is why this change requires `super_clipboard` implementation as this is platform-dependent:
  ```dart
  FlutterQuillExtensions.useSuperClipboardPlugin();
  ```
   as mentioned above.
  
    The following example for copying a Markdown file:

    <details>
    <summary>Markdown File Content</summary>
    
    ```md
  
    **Note**: This package supports converting from HTML back to Quill delta but it's experimental and used internally when pasting HTML content from the clipboard to the Quill Editor
      
      You have two options:
      
      1. Using [quill_html_converter](./quill_html_converter/) to convert to HTML, the package can convert the Quill delta to HTML well
      (it uses [vsc_quill_delta_to_html](https://pub.dev/packages/vsc_quill_delta_to_html)), it is just a handy extension to do it more quickly
      1. Another option is to use
      [vsc_quill_delta_to_html](https://pub.dev/packages/vsc_quill_delta_to_html) to convert your document
      to HTML.
         This package has full support for all Quill operations—including images, videos, formulas,
      tables, and mentions.
         Conversion can be performed in vanilla Dart (i.e., server-side or CLI) or in Flutter.
      It is a complete Dart part of the popular and mature [quill-delta-to-html](https://www.npmjs.com/package/quill-delta-to-html)
      Typescript/Javascript package.
         this package doesn't convert the HTML back to Quill Delta as far as we know  
  
    ```

    </details>
    
    <details>
    <summary>Before</summary>
    
    ![image](https://github.com/singerdmx/flutter-quill/assets/73608287/03f5ae20-796c-4e8b-8668-09a994211c1e)
    
    </details>
    
    <details>
    <summary>After</summary>
    
    ![image](https://github.com/singerdmx/flutter-quill/assets/73608287/7e3a1987-36e7-4665-944a-add87d24e788)
    
    </details>
  
    Markdown, and HTML converting from and to Delta are **currently far from perfect**, the current implementation could improved a lot 
    however **it will likely not work like expected**, due to differences between HTML and Delta, see this [comment](https://github.com/slab/quill/issues/1551#issuecomment-311458570) for more info.
  
    ![Copying Markdown file into Flutter Quill Editor](https://github.com/singerdmx/flutter-quill/assets/73608287/63bd6ba6-cc49-4335-84dc-91a0fa5c95a9)
  
    For more details see #1915
  
   Using or converting to HTML or Markdown is highly experimental and shouldn't be used for production applications. 
  
   We use it internally as it is more suitable for our specific use case., copying content from external websites and pasting it into the editor 
   previously breaks the styles, while the current implementation is not ready, it provides a better user experience and doesn't have many downsides.

 Feel free to report any bugs or feature requests at [Issues](https://github.com/singerdmx/flutter-quill/issues) or drop any suggestions and questions at [Discussions](https://github.com/singerdmx/flutter-quill/discussions)

## New Contributors
* @leegh519 made their first contribution in https://github.com/singerdmx/flutter-quill/pull/1911

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.21...v9.4.0

## 9.3.21

* fix: assertion failure for swipe typing and undo on Android by @crasowas in https://github.com/singerdmx/flutter-quill/pull/1898


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.20...v9.3.21

## 9.3.20

* Fix: Issue 1887 by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/1892
* fix: toolbar style change will be invalid when inputting more than 2 characters at a time by @crasowas in https://github.com/singerdmx/flutter-quill/pull/1890

## New Contributors
* @crasowas made their first contribution in https://github.com/singerdmx/flutter-quill/pull/1890

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.19...v9.3.20

## 9.3.19

* Fix reported issues by @AtlasAutocode in https://github.com/singerdmx/flutter-quill/pull/1886


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.18...v9.3.19

## 9.3.18

* Fix: Undo/redo cursor position fixed by @Alspb in https://github.com/singerdmx/flutter-quill/pull/1885


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.17...v9.3.18

## 9.3.17

* Update super_clipboard plugin to 0.8.15 to address [#1882](https://github.com/singerdmx/flutter-quill/issues/1882)


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.16...v9.3.17

## 9.3.16

* Update `lint` dev package to 4.0.0
* Require at least version 0.8.13 of the plugin

**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.15...v9.3.16

## 9.3.15


* Ci/automate updating the  files by @ellet0 in https://github.com/singerdmx/flutter-quill/pull/1879
* Updating outdated README.md and adding a few guidelines for CONTRIBUTING.md


**Full Changelog**: https://github.com/singerdmx/flutter-quill/compare/v9.3.14...v9.3.15

## 9.3.14

* Chore/use original color picker package in [#1877](https://github.com/singerdmx/flutter-quill/pull/1877)

## 9.3.13

* fix: `readOnlyMouseCursor` losing in construction function
* Fix block multi-line selection style

## 9.3.12

* Add `readOnlyMouseCursor` to config mouse cursor type

## 9.3.11

* Fix typo in QuillHtmlConverter
* Fix re-create checkbox

## 9.3.10

* Support clipboard actions from the toolbar

## 9.3.9

* fix: MD Parsing for multi space
* fix: FontFamily and FontSize toolbars track the text selected in the editor
* feat: Add checkBoxReadOnly property which can override readOnly for checkbox

## 9.3.8

* fix: removed misleading parameters
* fix: added missed translations for ru, es, de
* added translations for Nepali Locale('ne', 'NP')

## 9.3.7

* Fix for keyboard jumping when switching focus from a TextField
* Toolbar button styling to reflect cursor position when running on desktops with keyboard to move care

## 9.3.6

* Add SK and update CS locales [#1796](https://github.com/singerdmx/flutter-quill/pull/1796)
* Fixes:
  * QuillIconTheme changes for FontFamily and FontSize buttons are not applied [#1797](https://github.com/singerdmx/flutter-quill/pull/1796)
  * Make the arrow_drop_down icons in the QuillToolbar the same size for all MenuAnchor buttons [#1799](https://github.com/singerdmx/flutter-quill/pull/1796)

## 9.3.5

* Update the minimum version for the packages to support `device_info_plus` version 10.0.0 [#1783](https://github.com/singerdmx/flutter-quill/issues/1783)
* Update the minimum version for `youtube_player_flutter` to new major version 9.0.0 in the `flutter_quill_extensions`

## 9.3.4

* fix: multiline styling stuck/not working properly [#1782](https://github.com/singerdmx/flutter-quill/pull/1782)

## 9.3.3

* Update `quill_html_converter` versions

## 9.3.2

* Fix dispose of text painter [#1774](https://github.com/singerdmx/flutter-quill/pull/1774)

## 9.3.1

* Require Flutter 3.19.0 as minimum version

## 9.3.0

* **Breaking change**: `Document.fromHtml(html)` is now returns `Document` instead of `Delta`, use `DeltaX.fromHtml` to return `Delta`
* Update old deprecated api from Flutter 3.19
* Scribble scroll fix by @mtallenca in https://github.com/singerdmx/flutter-quill/pull/1745

## 9.2.14

* feat: move cursor after inserting video/image
* Apple pencil

## 9.2.13

* Fix crash with inserting text from contextMenuButtonItems
* Fix incorrect behaviour of context menu 
* fix: selection handles behaviour and unnessesary style assert
* Update quill_fr.arb

## 9.2.12

* Fix safari clipboard bug
* Add the option to disable clipboard functionality

## 9.2.11

* Fix a bug where it has problems with pasting text into the editor when the clipboard has styled text

## 9.2.10

* Update example screenshots
* Refactor `Container` to `QuillContainer` with backward compatibility
* A workaround fix in history feature

## 9.2.9

* Refactor the type of `Delta().toJson()` to be more clear type

## 9.2.8

* feat: Export Container node as QuillContainer
* fix web cursor position / height (don't use iOS logic)
* Added Swedish translation

## 9.2.6

* [fix selection.affinity always downstream after updateEditingValue](https://github.com/singerdmx/flutter-quill/pull/1682)
* Bumb version of `super_clipboard`

## 9.2.5

* Bumb version of `super_clipboard`

## 9.2.4

* Use fixed version of intl

## 9.2.3

* remove unncessary column in Flutter quill video embed block

## 9.2.2

* Fix bug [#1627](https://github.com/singerdmx/flutter-quill/issues/1627)

## 9.2.1

* Fix [bug](https://github.com/singerdmx/flutter-quill/issues/1119#issuecomment-1872605246) with font size button
* Added ro RO translations
* 📖 Update zh, zh_CN translations

## 9.2.0

* Require minimum version `6.0.0` of `flutter_keyboard_visibility` to fix some build issues with Android Gradle Plugin 8.2.0
* Add on image clicked in `flutter_quill_extensions` callback
* Deprecate `globalIconSize` and `globalIconButtonFactor`, use `iconSize` and `iconButtonFactor` instead
* Fix the `QuillToolbarSelectAlignmentButtons`

## 9.1.1

* Require `super_clipboard` minimum version `0.8.1` to fix some bug with Linux build failure

## 9.1.1-dev

* Fix bug [#1636](https://github.com/singerdmx/flutter-quill/issues/1636)
* Fix a where you paste styled content (HTML) it always insert a new line at first even if the document is empty
* Fix the font size button and migrate to `MenuAnchor`
* The `defaultDisplayText` is no longer required in the font size and header dropdown buttons
* Add pdf converter in a new package (`quill_pdf_converter`)

## 9.1.0

* Fix the simple toolbar by add properties of `IconButton` and fix some buttons

## 9.1.0-dev.2

* Fix the history buttons

## 9.1.0-dev.1

* Bug fixes in the simple toolbar buttons

## 9.1.0-dev

* **Breaking Change**: in the `QuillSimpleToolbar` Fix the `QuillIconTheme` by replacing all the properties with two properties of type `ButtonStyle`, use `IconButton.styleFrom()`

## 9.0.6

* Fix bug in QuillToolbarSelectAlignmentButtons

## 9.0.5

* You can now use most of the buttons without internal provider

## 9.0.4

* Feature: [#1611](https://github.com/singerdmx/flutter-quill/issues/1611)
* Export missing widgets

## 9.0.3

* Flutter Quill Extensions:
  * Fix file image support for web image emebed builder

## 9.0.2

* Remove unused properties in the `QuillToolbarSelectHeaderStyleDropdownButton`
* Fix the `QuillSimpleToolbar` when `useMaterial3` is false, please upgrade to the latest version of flutter for better support

## 9.0.2-dev.3

* Export `QuillSingleChildScrollView`

## 9.0.2-dev.2

* Add the new translations for ru, uk arb files by [#1575](https://github.com/singerdmx/flutter-quill/pull/1575)
* Add a new dropdown button by [#1575](https://github.com/singerdmx/flutter-quill/pull/1575)
* Update the default style values by [#1575](https://github.com/singerdmx/flutter-quill/pull/1575)
* Fix bug [#1562](https://github.com/singerdmx/flutter-quill/issues/1562)
* Fix the second bug of [#1480](https://github.com/singerdmx/flutter-quill/issues/1480)

## 9.0.2-dev.1

* Add configurations for the new dropdown `QuillToolbarSelectHeaderStyleButton`, you can use the orignal one or this
* Fix the [issue](https://github.com/singerdmx/flutter-quill/issues/1119) when enter is pressed, all font settings is lost

## 9.0.2-dev

* **Breaking change** Remove the spacer widget, removed the controller option for each button
* Add `toolbarRunSpacing` property to the simple toolbar

## 9.0.1

* Fix default icon size

## 9.0.0

* This version is quite stable but it's not how we wanted to be, because the lack of time and there are not too many maintainers active, we decided to publish it, we might make a new breaking changes verion

## 9.0.1-dev.1

* Flutter Quill Extensions:
  * Update `QuillImageUtilities` and fixining some bugs

## 9.0.1-dev

* Test new GitHub workflows

## 9.0.0-dev-10

* Fix a bug of the improved pasting HTML contents contents into the editor

## 9.0.0-dev-9

* Improves the new logic of pasting HTML contents into the Editor
* Update `README.md` and the doc
* Dispose the `QuillToolbarSelectHeaderStyleButton` state listener in `dispose`
* Upgrade the font family button to material 3
* Rework the font family and font size functionalities to change the font once and type all over the editor

## 9.0.0-dev-8

* Better support for pasting HTML contents from external websites to the editor
* The experimental support of converting the HTML from `quill_html_converter` is now built-in in the `flutter_quill` and removed from there (Breaking change for `quill_html_converter`)

## 9.0.0-dev-7

* Fix a bug in chaning the background/font color of ol/ul list
* Flutter Quill Extensions:
  * Fix link bug in the video url
  * Fix patterns

## 9.0.0-dev-6

* Move the `child` from `QuillToolbarConfigurations` into `QuillToolbar` directly
* Bug fixes
* Add the ability to change the background and font color of the ol/ul elements dots and numbers
* Flutter Quill Extensions:
  * **Breaking Change**: The `imageProviderBuilder`is now providing the context and image url

## 9.0.0-dev-5

* The `QuillToolbar` is now accepting only `child` with no configurations so you can customize everything you wants, the `QuillToolbar.simple()` or `QuillSimpleToolbar` implements a simple toolbar that is based on `QuillToolbar`, you are free to use it but it just an example and not standard
* Flutter Quill Extensions:
  * Improve the camera button

## 9.0.0-dev-4

* The options parameter in all of the buttons is no longer required which can be useful to create custom toolbar with minimal efforts
* Toolbar buttons fixes in both `flutter_quill` and `flutter_quill_extensions`
* The `QuillProvider` has been dropped and no longer used, the providers will be used only internally from now on and we will not using them as much as possible

## 9.0.0-dev-3

* Breaking Changes:
  * Rename `QuillToolbar` to `QuillSimpleToolbar`
  * Rename `QuillBaseToolbar` to `QuillToolbar`
  * Replace `pasteboard` with `rich_cliboard`
* Fix a bug in the example when inserting an image from url
* Flutter Quill Extensions:
 * Add support for copying the image to the system cliboard

## 9.0.0-dev-2

* An attemp to fix CI automated publishing

## 9.0.0-dev-1

* An attemp to fix CI automated publishing

## 9.0.0-dev

* **Major Breaking change**: The `QuillProvider` is now optional, the `controller` parameter has been moved to the `QuillEditor` and `QuillToolbar` once again.
* Flutter Quill Extensions;
  * **Breaking Change**: Completly change the way how the source code structured to more basic and simple way, organize folders and file names, if you use the library
from `flutter_quill_extensions.dart` then there is nothing you need to do, but if you are using any other import then you need to re-imports
embed, this won't affect how quill js work
  * Improvemenets to the image embed
  * Add support for `margin` for web
  * Add untranslated strings to the `quill_en.arb`

## 8.6.4

* The default value of `keyboardAppearance` for the iOS will be the one from the App/System theme mode instead of always using the `Brightness.light`
* Fix typos in `README.md`

## 8.6.3

* Update the minimum flutter version to `3.16.0`

## 8.6.2

* Restore use of alternative QuillToolbarLinkStyleButton2 widget

## 8.6.1

* Temporary revert style bug fix

## 8.6.0

* **Breaking Change** Support [Flutter 3.16](https://medium.com/flutter/whats-new-in-flutter-3-16-dba6cb1015d1), please upgrade to the latest stable version of flutter to use this update
* **Breaking Change**: Remove Deprecated Fields
* **Breaking Change**: Extract the shared things between `QuillToolbarConfigurations` and `QuillBaseToolbarConfigurations`
* **Breaking Change**: You no longer need to use `QuillToolbarProvider` when using custom toolbar buttons, the example has been updated
* Bug fixes

## 8.5.5

* Now when opening dialogs by `QuillToolbar` you will not get an exception when you don't use `FlutterQuillLocalizations.delegate` in your `WidgetsApp`, `MaterialApp`, or `CupertinoApp`. The fix is for the `QuillToolbarSearchButton`, `QuillToolbarLinkStyleButton`, and `QuillToolbarColorButton` buttons

## 8.5.4

* The `mobileWidth`, `mobileHeight`, `mobileMargin`, and `mobileAlignment` is now deprecated in `flutter_quill`, they are now defined in `flutter_quill_extensions`
* Deprecate `replaceStyleStringWithSize` function which is in `string.dart`
* Deprecate `alignment`, and `margin` as they don't conform to official Quill JS

## 8.5.3

* Update doc
* Update `README.md` and `CHANGELOG.md`
* Fix typos
* Use `immutable` when possible
* Update `.pubignore`

## 8.5.2

* Updated `README.md`.
* Feature: Added the ability to include a custom callback when the `QuillToolbarColorButton` is pressed.
* The `QuillToolbar` now implements `PreferredSizeWidget`, enabling usage in the AppBar, similar to `QuillBaseToolbar`.

## 8.5.1

* Updated `README.md`.

## 8.5.0

* Migrated to `flutter_localizations` for translations.
* Fixed: Translated all previously untranslated localizations.
* Fixed: Added translations for missing items.
* Fixed: Introduced default Chinese fallback translation.
* Removed: Unused parameters `items` in `QuillToolbarFontFamilyButtonOptions` and `QuillToolbarFontSizeButtonOptions`.
* Updated: Documentation.

## 8.4.4

* Updated `.pubignore` to ignore unnecessary files and folders.

## 8.4.3

* Updated `CHANGELOG.md`.

## 8.4.2

* **Breaking change**: Configuration for `QuillRawEditor` has been moved to a separate class. Additionally, `readOnly` has been renamed to `isReadOnly`. If using `QuillEditor`, no action is required.
* Introduced the ability for developers to override `TextInputAction` in both `QuillRawEditor` and `QuillEditor`.
* Enabled using `QuillRawEditor` without `QuillEditorProvider`.
* Bug fixes.
* Added image cropping implementation in the example.

## 8.4.1

* Added `copyWith` in `OptionalSize` class.

## 8.4.0

* **Breaking change**: Updated `QuillCustomButton` to use `QuillCustomButtonOptions`. Moved all properties from `QuillCustomButton` to `QuillCustomButtonOptions`, replacing `iconData` with `icon` widget for increased customization.
* **Breaking change**: `customButtons` in `QuillToolbarConfigurations` is now of type `List<QuillToolbarCustomButtonOptions>`.
* Bug fixes following the `8.0.0` update.
* Updated `README.md`.
* Improved platform checking.

## 8.3.0

* Added `iconButtonFactor` property to `QuillToolbarBaseButtonOptions` for customizing button size relative to its icon size (defaults to `kIconButtonFactor`, consistent with previous releases).

## 8.2.6

* Organized `QuillRawEditor` code.

## 8.2.5

* Added `builder` property in `QuillEditorConfigurations`.

## 8.2.4

* Adhered to Flutter best practices.
* Fixed auto-focus bug.

## 8.2.3

* Updated `README.md`.

## 8.2.2

* Moved `flutter_quill_test` to a separate package: [flutter_quill_test](https://pub.dev/packages/flutter_quill_test).

## 8.2.1

* Updated `README.md`.

## 8.2.0

* Added the option to add configurations for `flutter_quill_extensions` using `extraConfigurations`.

## 8.1.11

* Followed Dart best practices by using `lints` and removed `pedantic` and `platform` since they are not used.
* Fixed text direction bug.
* Updated `README.md`.

## 8.1.10

* Secret for automated publishing to pub.dev.

## 8.1.9

* Fixed automated publishing to pub.dev.

## 8.1.8

* Fixed automated publishing to pub.dev.

## 8.1.7

* Automated publishing to pub.dev.

## 8.1.6

* Fixed compatibility with `integration_test` by downgrading the minimum version of the platform package to 3.1.0.

## 8.1.5

* Reversed background/font color toolbar button icons.

## 8.1.4

* Reversed background/font color toolbar button tooltips.

## 8.1.3

* Moved images to screenshots instead of `README.md`.

## 8.1.2

* Fixed a bug related to the regexp of the insert link dialog.
* Required Dart 3 as the minimum version.
* Code cleanup.
* Added a spacer widget between each button in the `QuillToolbar`.

## 8.1.1

* Fixed null error in line.dart #1487(https://github.com/singerdmx/flutter*quill/issues/1487).

## 8.1.0

* Fixed a word typo of `mirgration` to `migration` in the readme & migration document.
* Updated migration guide.
* Removed property `enableUnfocusOnTapOutside` in `QuillEditor` configurations and added `isOnTapOutsideEnabled` instead.
* Added a new callback called `onTapOutside` in the `QuillEditorConfigurations` to perform actions when tapping outside the editor.
* Fixed a bug that caused the web platform to not unfocus the editor when tapping outside of it. To override this, please pass a value to the `onTapOutside` callback.
* Removed the old property of `iconTheme`. Instead, pass `iconTheme` in the button options; you will find the `base` property inside it with `iconTheme`.

## 8.0.0

* If you have migrated recently, don't be alarmed by this update; it adds documentation, a migration guide, and marks the version as a more stable release. Although there are breaking changes (as reported by some developers), the major version was not changed due to time constraints during development. A single property was also renamed from `code` to `codeBlock` in the `elements` of the new `QuillEditorConfigurations` class.
* Updated the README for better readability.

## 7.10.2

* Removed line numbers from code blocks by default. You can still enable this feature thanks to the new configurations in the `QuillEditor`. Find the `elementOptions` property and enable `enableLineNumbers`.

## 7.10.1

* Fixed issues and utilized the new parameters.
* No longer need to use `MaterialApp` for most toolbar button child builders.
* Compatibility with [fresh_quill_extensions](https://pub.dev/packages/fresh_quill_extensions), a temporary alternative to [flutter_quill_extensions](https://pub.dev/packages/flutter_quill_extensions).
* Updated most of the documentation in `README.md`.

## 7.10.0

* **Breaking change**: `QuillToolbar.basic()` can be accessed directly from `QuillToolbar()`, and the old `QuillToolbar` can be accessed from `QuillBaseToolbar`.
* Refactored Quill editor and toolbar configurations into a single class each.
* After changing checkbox list values, the controller will not request keyboard focus by default.
* Moved toolbar and editor configurations directly into the widget but still use inherited widgets internally.
* Fixes to some code after the refactoring.

## 7.9.0

* Buttons Improvemenets
* Refactor all the button configurations that used in `QuillToolbar.basic()` but there are still few lefts
* **Breaking change**: Remove some configurations from the QuillToolbar and move them to the new `QuillProvider`, please notice this is a development version and this might be changed in the next few days, the stable release will be ready in less than 3 weeks
* Update `flutter_quill_extensions` and it will be published into pub.dev soon.
* Allow you to customize the search dialog by custom callback with child builder

## 7.8.0

* **Important note**: this is not test release yet, it works but need more test and changes and breaking changes, we don't have development version and it will help us if you try the latest version and report the issues in Github but if you want a stable version please use `7.4.16`. this refactoring process will not take long and should be done less than three weeks with the testing.
* We managed to refactor most of the buttons configurations and customizations in the `QuillProvider`, only three lefts then will start on refactoring the toolbar configurations
* Code improvemenets

## 7.7.0

* **Breaking change**: We have mirgrated more buttons in the toolbar configurations, you can do change them in the `QuillProvider`
* Important bug fixes

## 7.6.1

* Bug fixes

## 7.6.0

* **Breaking change**: To customize the buttons in the toolbar, you can do that in the `QuillProvider`

## 7.5.0

* **Breaking change**: The widgets `QuillEditor` and `QuillToolbar` are no longer have controller parameter, instead you need to make sure in the widget tree you have wrapped them with `QuillProvider` widget and provide the controller and the require configurations

## 7.4.16

* Update documentation and README.md

## 7.4.15

* Custom style attrbuites for platforms other than mobile (alignment, margin, width, height)
* Bug fixes and other improvemenets

## 7.4.14

* Improve performance by reducing the number of widgets rebuilt by listening to media query for only the needed things, for example instead of using `MediaQuery.of(context).size`, now we are using `MediaQuery.sizeOf(context)`
* Add MediaButton for picking the images only since the video one is not ready
* A  new feature which allows customizing the text selection in quill editor which is useful for custom theme design system for custom app widget

## 7.4.13

* Fixed tab editing when in readOnly mode.

## 7.4.12

* Update the minimum version of device_info_plus to 9.1.0.

## 7.4.11

* Add sw locale.

## 7.4.10

* Update translations.

## 7.4.9

* Style recognition fixes.

## 7.4.8

* Upgrade dependencies.

## 7.4.7

* Add Vietnamese and German translations.

## 7.4.6

* Fix more null errors in Leaf.retain [##1394](https://github.com/singerdmx/flutter-quill/issues/1394) and Line.delete [##1395](https://github.com/singerdmx/flutter-quill/issues/1395).

## 7.4.5

* Fix null error in Container.insert [##1392](https://github.com/singerdmx/flutter-quill/issues/1392).

## 7.4.4

* Fix extra padding on checklists [##1131](https://github.com/singerdmx/flutter-quill/issues/1131).

## 7.4.3

* Fixed a space input error on iPad.

## 7.4.2

* Fix bug with keepStyleOnNewLine for link.

## 7.4.1

* Fix toolbar dividers condition.

## 7.4.0

* Support Flutter version 3.13.0.

## 7.3.3

* Updated Dependencies conflicting.

## 7.3.2

* Added builder for custom button in _LinkDialog.

## 7.3.1

* Added case sensitive and whole word search parameters.
* Added wrap around.
* Moved search dialog to the bottom in order not to override the editor and the text found.
* Other minor search dialog enhancements.

## 7.3.0

* Add default attributes to basic factory.

## 7.2.19

* Feat/link regexp.

## 7.2.18

* Fix paste block text in words apply same style.

## 7.2.17

* Fix paste text mess up style.
* Add support copy/cut block text.

## 7.2.16

* Allow for custom context menu.

## 7.2.15

* Add flutter_quill.delta library which only exposes Delta datatype.

## 7.2.14

* Fix errors when the editor is used in the `screenshot` package.

## 7.2.13

* Fix around image can't delete line break.

## 7.2.12

* Add support for copy/cut select image and text together.

## 7.2.11

* Add affinity for localPosition.

## 7.2.10

* LINE._getPlainText queryChild inclusive=false.

## 7.2.9

* Add toPlainText method to `EmbedBuilder`.

## 7.2.8

* Add custom button widget in toolbar.

## 7.2.7

* Fix language code of Japan.

## 7.2.6

* Style custom toolbar buttons like builtins.

## 7.2.5

* Always use text cursor for editor on desktop.

## 7.2.4

* Fixed keepStyleOnNewLine.

## 7.2.3

* Get pixel ratio from view.

## 7.2.2

* Prevent operations on stale editor state.

## 7.2.1

* Add support for android keyboard content insertion.
* Enhance color picker, enter hex color and color palette option.

## 7.2.0

* Checkboxes, bullet points, and number points are now scaled based on the default paragraph font size.

## 7.1.20

* Pass linestyle to embedded block.

## 7.1.19

* Fix Rtl leading alignment problem.

## 7.1.18

* Support flutter latest version.

## 7.1.17+1

* Updates `device_info_plus` to version 9.0.0 to benefit from AGP 8 (see [changelog##900](https://pub.dev/packages/device_info_plus/changelog##900)).

## 7.1.16

* Fixed subscript key from 'sup' to 'sub'.

## 7.1.15

* Fixed a bug introduced in 7.1.7 where each section in `QuillToolbar` was displayed on its own line.

## 7.1.14

* Add indents change for multiline selection.

## 7.1.13

* Add custom recognizer.

## 7.1.12

* Add superscript and subscript styles.

## 7.1.11

* Add inserting indents for lines of list if text is selected.

## 7.1.10

* Image embedding tweaks
  * Add MediaButton which is intened to superseed the ImageButton and VideoButton. Only image selection is working.
  * Implement image insert for web (image as base64)

## 7.1.9

* Editor tweaks PR from bambinoua(https://github.com/bambinoua).
  * Shortcuts now working in Mac OS
  * QuillDialogTheme is extended with new properties buttonStyle, linkDialogConstraints, imageDialogConstraints, isWrappable, runSpacing,
  * Added LinkStyleButton2 with new LinkStyleDialog (similar to Quill implementation
  * Conditinally use Row or Wrap for dialog's children.
  * Update minimum Dart SDK version to 2.17.0 to use enum extensions.
  * Use merging shortcuts and actions correclty (if the key combination is the same)

## 7.1.8

* Dropdown tweaks
  * Add itemHeight, itemPadding, defaultItemColor for customization of dropdown items.
  * Remove alignment property as useless.
  * Fix bugs with max width when width property is null.

## 7.1.7

* Toolbar tweaks.
  * Implement tooltips for embed CameraButton, VideoButton, FormulaButton, ImageButton.
  * Extends customization for SelectAlignmentButton, QuillFontFamilyButton, QuillFontSizeButton adding padding, text style, alignment, width.
  * Add renderFontFamilies to QuillFontFamilyButton to show font faces in dropdown.
  * Add AxisDivider and its named constructors for for use in parent project.
  * Export ToolbarButtons enum to allow specify tooltips for SelectAlignmentButton.
  * Export QuillFontFamilyButton, SearchButton as they were not exported before.
  * Deprecate items property in QuillFontFamilyButton, QuillFontSizeButton as the it can be built usinr rawItemsMap.
  * Make onSelection QuillFontFamilyButton, QuillFontSizeButton omittable as no need to execute callback outside if controller is passed to widget.

Now the package is more friendly for web projects.

## 7.1.6

* Add enableUnfocusOnTapOutside field to RawEditor and Editor widgets.

## 7.1.5

* Add tooltips for toolbar buttons.

## 7.1.4

* Fix inserting tab character in lists.

## 7.1.3

* Fix ios cursor bug when word.length==1.

## 7.1.2

* Fix non scrollable editor exception, when tapped under content.

## 7.1.1

* customLinkPrefixes parameter * makes possible to open links with custom protoco.

## 7.1.0

* Fix ordered list numeration with several lists in document.

## 7.0.9

* Use const constructor for EmbedBuilder.

## 7.0.8

* Fix IME position bug with scroller.

## 7.0.7

* Add TextFieldTapRegion for contextMenu.

## 7.0.6

* Fix line style loss on new line from non string.

## 7.0.5

* Fix IME position bug for Mac and Windows.
* Unfocus when tap outside editor. fix the bug that cant refocus in afterButtonPressed after click ToggleStyleButton on Mac.

## 7.0.4

* Have text selection span full line height for uneven sized text.

## 7.0.3

* Fix ordered list numeration for lists with more than one level of list.

## 7.0.2

* Allow widgets to override widget span properties.

## 7.0.1

* Update i18n_extension dependency to version 8.0.0.

## 7.0.0

* Breaking change: Tuples are no longer used. They have been replaced with a number of data classes.

## 6.4.4

* Increased compatibility with Flutter widget tests.

## 6.4.3

* Update dependencies (collection: 1.17.0, flutter_keyboard_visibility: 5.4.0, quiver: 3.2.1, tuple: 2.0.1, url_launcher: 6.1.9, characters: 1.2.1, i18n_extension: 7.0.0, device_info_plus: 8.1.0)

## 6.4.2

* Replace `buildToolbar` with `contextMenuBuilder`.

## 6.4.1

* Control the detect word boundary behaviour.

## 6.4.0

* Use `axis` to make the toolbar vertical.
* Use `toolbarIconCrossAlignment` to align the toolbar icons on the cross axis.
* Breaking change: `QuillToolbar`'s parameter `toolbarHeight` was renamed to `toolbarSize`.

## 6.3.5

* Ability to add custom shortcuts.

## 6.3.4

* Update clipboard status prior to showing selected text overlay.

## 6.3.3

* Fixed handling of mac intents.

## 6.3.2

* Added `unknownEmbedBuilder` to QuillEditor.
* Fix error style when input chinese japanese or korean.

## 6.3.1

* Add color property to the basic factory function.

## 6.3.0

* Support Flutter 3.7.

## 6.2.2

* Fix: nextLine getter null where no assertion.

## 6.2.1

* Revert "Align numerical and bullet lists along with text content".

## 6.2.0

* Align numerical and bullet lists along with text content.

## 6.1.12

* Apply i18n for default font dropdown option labels corresponding to 'Clear'.

## 6.1.11

* Remove iOS hack for delaying focus calculation.

## 6.1.10

* Delay focus calculation for iOS.

## 6.1.9

* Bump keyboard show up wait to 1 sec.

## 6.1.8

* Recalculate focus when showing keyboard.

## 6.1.7

* Add czech localizations.

## 6.1.6

* Upgrade i18n_extension to 6.0.0.

## 6.1.5

* Fix formatting exception.

## 6.1.4

* Add double quotes validation.

## 6.1.3

* Revert "fix order list numbering (##988)".

## 6.1.2

* Add typing shortcuts.

## 6.1.1

* Fix order list numbering.

## 6.1.0

* Add keyboard shortcuts for editor actions.

## 6.0.10

* Upgrade device info plus to ^7.0.0.

## 6.0.9

* Don't throw showAutocorrectionPromptRect not implemented. The function is called with every keystroke as a user is typing.

## 6.0.8+1

* Fixes null pointer when setting documents.

## 6.0.8

* Make QuillController.document mutable.

## 6.0.7

* Allow disabling of selection toolbar.

## 6.0.6+1

* Revert 6.0.6.

## 6.0.6

* Fix wrong custom embed key.

## 6.0.5

* Fixes toolbar buttons stealing focus from editor.

## 6.0.4

* Bug fix for Type 'Uint8List' not found.

## 6.0.3

* Add ability to paste images.

## 6.0.2

* Address Dart Analysis issues.

## 6.0.1

* Changed translation country code (zh_HK -> zh_hk) to lower case, which is required for i18n_extension used in flutter_quill.
* Add localization in example's main to demonstrate translation.
* Issue Windows selection's copy / paste tool bar not shown ##861: add selection's copy / paste toolbar, escape to hide toolbar, mouse right click to show toolbar, ctrl-Y / ctrl-Z to undo / redo.
* Image and video displayed in Windows platform caused screen flickering while selecting text, a sample_data_nomedia.json asset is added for Desktop to demonstrate the added features.
* Known issue: keyboard action sometimes causes exception mentioned in Flutter's issue ##106475 (Windows Keyboard shortcuts stop working after modifier key repeat flutter/flutter##106475).
* Know issue: user needs to click the editor to get focus before toolbar is able to display.

## 6.0.0 BREAKING CHANGE

* Removed embed (image, video & formula) blocks from the package to reduce app size.

These blocks have been moved to the package `flutter_quill_extensions`, migrate by filling the `embedBuilders` and `embedButtons` parameters as follows:

```
import 'package:flutter_quill_extensions/flutter_quill_extensions.dart';

QuillEditor.basic(
  controller: controller,
  embedBuilders: FlutterQuillEmbeds.builders(),
);

QuillToolbar.basic(
  controller: controller,
  embedButtons: FlutterQuillEmbeds.buttons(),
);
```

## 5.4.2

* Upgrade i18n_extension.

## 5.4.1

* Update German Translation.

## 5.4.0

* Added Formula Button (for maths support).

## 5.3.2

* Add more font family.

## 5.3.1

* Enable search when text is not empty.

## 5.3.0

* Added search function.

## 5.2.11

* Remove default small color.

## 5.2.10

* Don't wrap the QuillEditor's child in the EditorTextSelectionGestureDetector if selection is disabled.

## 5.2.9

* Added option to modify SelectHeaderStyleButton options.
* Added option to click again on h1, h2, h3 button to go back to normal.

## 5.2.8

* Remove tooltip for LinkStyleButton.
* Make link match regex case insensitive.

## 5.2.7

* Add locale to QuillEditor.basic.

## 5.2.6

* Fix keyboard pops up when resizing the image.

## 5.2.5

* Upgrade youtube_player_flutter_quill to 8.2.2.

## 5.2.4

* Upgrade youtube_player_flutter_quill to 8.2.1.

## 5.2.3

* Flutter Quill Doesn't Work On iOS 16 or Xcode 14 Betas (Stored properties cannot be marked potentially unavailable with '@available').

## 5.2.2

* Fix Web Unsupported operation: Platform.\_operatingSystem error.

## 5.2.1

* Rename QuillCustomIcon to QuillCustomButton.

## 5.2.0

* Support font family selection.

## 5.1.1

* Update README.

## 5.1.0

* Added CustomBlockEmbed and customElementsEmbedBuilder.

## 5.0.5

* Upgrade device_info_plus to 4.0.0.

## 5.0.4

* Added onVideoInit callback for video documents.

## 5.0.3

* Update dependencies.

## 5.0.2

* Keep cursor position on checkbox tap.

## 5.0.1

* Fix static analysis errors.

## 5.0.0

* Flutter 3.0.0 support.

## 4.2.3

* Ignore color:inherit and convert double to int for level.

## 4.2.2

* Add clear option to font size dropdown.

## 4.2.1

* Refactor font size dropdown.

## 4.2.0

* Ensure selectionOverlay is available for showToolbar.

## 4.1.9

* Using properly iconTheme colors.

## 4.1.8

* Update font size dropdown.

## 4.1.7

* Convert FontSize to a Map to allow for named Font Size.

## 4.1.6

* Update quill_dropdown_button.dart.

## 4.1.5

* Add Font Size dropdown to the toolbar.

## 4.1.4

* New borderRadius for iconTheme.

## 4.1.3

* Fix selection handles show/hide after paste, backspace, copy.

## 4.1.2

* Add full support for hardware keyboards (Chromebook, Android tablets, etc) that don't alter screen UI.

## 4.1.1

* Added textSelectionControls field in QuillEditor.

## 4.1.0

* Added Node to linkActionPickerDelegate.

## 4.0.12

* Add Persian(fa) language.

## 4.0.11

* Fix cut selection error in multi-node line.

## 4.0.10

* Fix vertical caret position bug.

## 4.0.9

* Request keyboard focus when no child is found.

## 4.0.8

* Fix blank lines do not display when **web*renderer=html.

## 4.0.7

* Refactor getPlainText (better handling of blank lines and lines with multiple markups.

## 4.0.6

* Bug fix for copying text with new lines.

## 4.0.5

* Fixed casting null to Tuple2 when link dialog is dismissed without any input (e.g. barrier dismissed).

## 4.0.4

* Bug fix for text direction rtl.

## 4.0.3

* Support text direction rtl.

## 4.0.2

* Clear toggled style on selection change.

## 4.0.1

* Fix copy/cut/paste/selectAll not working.

## 4.0.0

* Upgrade for Flutter 2.10.

## 3.9.11

* Added Indonesian translation.

## 3.9.10

* Fix for undoing a modification ending with an indented line.

## 3.9.9

* iOS: Save image whose filename does not end with image file extension.

## 3.9.8

* Added Urdu translation.

## 3.9.7

* Fix for clicking on the Link button without any text on a new line crashes.

## 3.9.6

* Apply locale to QuillEditor(contents).

## 3.9.5

* Fix image pasting.

## 3.9.4

* Hiding dialog after selecting action for image.

## 3.9.3

* Update ImageResizer for Android.

## 3.9.2

* Copy image with its style.

## 3.9.1

* Support resizing image.

## 3.9.0

* Image menu options for copy/remove.

## 3.8.8

* Update set textEditingValue.

## 3.8.7

* Fix checkbox not toggled correctly in toolbar button.

## 3.8.6

* Fix cursor position changes when checking/unchecking the checkbox.

## 3.8.5

* Fix \_handleDragUpdate in \_TextSelectionHandleOverlayState.

## 3.8.4

* Fix link dialog layout.

## 3.8.3

* Fix for errors on a non scrollable editor.

## 3.8.2

* Fix certain keys not working on web when editor is a child of a scroll view.

## 3.8.1

* Refactor \_QuillEditorState to QuillEditorState.

## 3.8.0

* Support pasting with format.

## 3.7.3

* Fix selection overlay for collapsed selection.

## 3.7.2

* Reverted Embed toPlainText change.

## 3.7.1

* Change Embed toPlainText to be empty string.

## 3.7.0

* Replace Toolbar showHistory group with individual showRedo and showUndo.

## 3.6.5

* Update Link dialogue for image/video.

## 3.6.4

* Link dialogue TextInputType.multiline.

## 3.6.3

* Bug fix for link button text selection.

## 3.6.2

* Improve link button.

## 3.6.1

* Remove SnackBar 'What is entered is not a link'.

## 3.6.0

* Allow link button to enter text.

## 3.5.3

* Change link button behavior.

## 3.5.2

* Bug fix for embed.

## 3.5.1

* Bug fix for platform util.

## 3.5.0

* Removed redundant classes.

## 3.4.4

* Add more translations.

## 3.4.3

* Preset link from attributes.

## 3.4.2

* Fix launch link edit mode.

## 3.4.1

* Placeholder effective in scrollable.

## 3.4.0

* Option to save image in read-only mode.

## 3.3.1

* Pass any specified key in QuillEditor constructor to super.

## 3.3.0

* Fixed Style toggle issue.

## 3.2.1

* Added new translations.

## 3.2.0

* Support multiple links insertion on the go.

## 3.1.1

* Add selection completed callback.

## 3.1.0

* Fixed image ontap functionality.

## 3.0.4

* Add maxContentWidth constraint to editor.

## 3.0.3

* Do not show caret on screen when the editor is not focused.

## 3.0.2

* Fix launch link for read-only mode.

## 3.0.1

* Handle null value of Attribute.link.

## 3.0.0

* Launch link improvements.
* Removed QuillSimpleViewer.

## 2.5.2

* Skip image when pasting.

## 2.5.1

* Bug fix for Desktop `Shift` + `Click` support.

## 2.5.0

* Update checkbox list.

## 2.4.1

* Desktop selection improvements.

## 2.4.0

* Improve inline code style.

## 2.3.3

* Improves selection rects to have consistent height regardless of individual segment text styles.

## 2.3.2

* Allow disabling floating cursor.

## 2.3.1

* Preserve last newline character on delete.

## 2.3.0

* Massive changes to support flutter 2.8.

## 2.2.2

* iOS - floating cursor.

## 2.2.1

* Bug fix for imports supporting flutter 2.8.

## 2.2.0

* Support flutter 2.8.

## 2.1.1

* Add methods of clearing editor and moving cursor.

## 2.1.0

* Add delete handler.

## 2.0.23

* Support custom replaceText handler.

## 2.0.22

* Fix attribute compare and fix font size parsing.

## 2.0.21

* Handle click on embed object.

## 2.0.20

* Improved UX/UI of Image widget.

## 2.0.19

* When uploading a video, applying indicator.

## 2.0.18

* Make toolbar dividers optional.

## 2.0.17

* Allow alignment of the toolbar icons to match WrapAlignment.

## 2.0.16

* Add hide / show alignment buttons.

## 2.0.15

* Implement change cursor to SystemMouseCursors.click when hovering a link styled text.

## 2.0.14

* Enable customize the checkbox widget using DefaultListBlockStyle style.

## 2.0.13

* Improve the scrolling performance by reducing the repaint areas.

## 2.0.12

* Fix the selection effect can't be seen as the textLine with background color.

## 2.0.11

* Fix visibility of text selection handlers on scroll.

## 2.0.10

* cursorConnt.color notify the text_line to repaint if it was disposed.

## 2.0.9

* Improve UX when trying to add a link.

## 2.0.8

* Adding translations to the toolbar.

## 2.0.7

* Added theming options for toolbar icons and LinkDialog.

## 2.0.6

* Avoid runtime error when placed inside TabBarView.

## 2.0.5

* Support inline code formatting.

## 2.0.4

* Enable history shortcuts for desktop.

## 2.0.3

* Fix cursor when line contains image.

## 2.0.2

* Address KeyboardListener class name conflict.

## 2.0.1

* Upgrade flutter_colorpicker to 0.5.0.

## 2.0.0

* Text Alignment functions + Block Format standards.

## 1.9.6

* Support putting QuillEditor inside a Scrollable view.

## 1.9.5

* Skip image when pasting.

## 1.9.4

* Bug fix for cursor position when tapping at the end of line with image(s).

## 1.9.3

* Bug fix when line only contains one image.

## 1.9.2

* Support for building custom inline styles.

## 1.9.1

* Cursor jumps to the most appropriate offset to display selection.

## 1.9.0

* Support inline image.

## 1.8.3

* Updated quill_delta.

## 1.8.2

* Support mobile image alignment.

## 1.8.1

* Support mobile custom size image.

## 1.8.0

* Support entering link for image/video.

## 1.7.3

* Bumps photo_view version.

## 1.7.2

* Fix static analysis error.

## 1.7.1

* Support Youtube video.

## 1.7.0

* Support video.

## 1.6.4

* Bug fix for clear format button.

## 1.6.3

* Fixed dragging right handle scrolling issue.

## 1.6.2

* Fixed the position of the selection status drag handle.

## 1.6.1

* Upgrade image_picker and flutter_colorpicker.

## 1.6.0

* Support Multi Row Toolbar.

## 1.5.0

* Remove file_picker dependency.

## 1.4.1

* Remove filesystem_picker dependency.

## 1.4.0

* Remove path_provider dependency.

## 1.3.4

* Add option to paintCursorAboveText.

## 1.3.3

* Upgrade file_picker version.

## 1.3.2

* Fix copy/paste bug.

## 1.3.1

* New logo.

## 1.3.0

* Support flutter 2.2.0.

## 1.2.2

* Checkbox supports tapping.

## 1.2.1

* Indented position not holding while editing.

## 1.2.0

* Fix image button cancel causes crash.

## 1.1.8

* Fix height of empty line bug.

## 1.1.7

* Fix text selection in read-only mode.

## 1.1.6

* Remove universal_html dependency.

## 1.1.5

* Enable "Select", "Select All" and "Copy" in read-only mode.

## 1.1.4

* Fix text selection issue.

## 1.1.3

* Update example folder.

## 1.1.2

* Add pedantic.

## 1.1.1

* Base64 image support.

## 1.1.0

* Support null safety.

## 1.0.9

* Web support for raw editor and keyboard listener.

## 1.0.8

* Support token attribute.

## 1.0.7

* Fix crash on web (dart:io).

## 1.0.6

* Add desktop support WINDOWS, MACOS and LINUX.

## 1.0.5

* Bug fix: Can not insert newline when Bold is toggled ON.

## 1.0.4

* Upgrade photo_view to ^0.11.0.

## 1.0.3

* Fix issue that text is not displayed while typing WEB.

## 1.0.2

* Update toolbar in sample home page.

## 1.0.1

* Fix static analysis errors.

## 1.0.0

* Support flutter 2.0.

## 1.0.0-dev.2

* Improve link handling for tel, mailto and etc.

## 1.0.0-dev.1

* Upgrade prerelease SDK & Bump for master.

## 0.3.5

* Fix for cursor focus issues when keyboard is on.

## 0.3.4

* Improve link handling for tel, mailto and etc.

## 0.3.3

* More fix on cursor focus issue when keyboard is on.

## 0.3.2

* Fix cursor focus issue when keyboard is on.

## 0.3.1

* cursor focus when keyboard is on.

## 0.3.0

* Line Height calculated based on font size.

## 0.2.12

* Support placeholder.

## 0.2.11

* Fix static analysis error.

## 0.2.10

* Update TextInputConfiguration autocorrect to true in stable branch.

## 0.2.9

* Update TextInputConfiguration autocorrect to true.

## 0.2.8

* Support display local image besides network image in stable branch.

## 0.2.7

* Support display local image besides network image.

## 0.2.6

* Fix cursor after pasting.

## 0.2.5

* Toggle text/background color button in toolbar.

## 0.2.4

* Support the use of custom icon size in toolbar.

## 0.2.3

* Support custom styles and image on local device storage without uploading.

## 0.2.2

* Update git repo.

## 0.2.1

* Fix static analysis error.

## 0.2.0

* Add checked/unchecked list button in toolbar.

## 0.1.8

* Support font and size attributes.

## 0.1.7

* Support checked/unchecked list.

## 0.1.6

* Fix getExtentEndpointForSelection.

## 0.1.5

* Support text alignment.

## 0.1.4

* Handle url with trailing spaces.

## 0.1.3

* Handle cursor position change when undo/redo.

## 0.1.2

* Handle more text colors.

## 0.1.1

* Fix cursor issue when undo.

## 0.1.0

* Fix insert image.

## 0.0.9

* Handle rgba color.

## 0.0.8

* Fix launching url.

## 0.0.7

* Handle multiple image inserts.

## 0.0.6

* More toolbar functionality.

## 0.0.5

* Update example.

## 0.0.4

* Update example.

## 0.0.3

* Update home page meta data.

## 0.0.2

* Support image upload and launch url in read-only mode.

## 0.0.1

* Rich text editor based on Quill Delta.

