import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller/quill_controller.dart';
import '../document/attribute.dart';
import 'buttons/alignment/select_alignment_buttons.dart';
import 'buttons/arrow_indicated_list_button.dart';
import 'embed/embed_button_builder.dart';
import 'simple_toolbar.dart';
export 'buttons/alignment/select_alignment_button.dart';
export 'buttons/clear_format_button.dart';
export 'buttons/clipboard_button.dart';
export 'buttons/color/color_button.dart';
export 'buttons/custom_button_button.dart';
export 'buttons/font_family_button.dart';
export 'buttons/font_size_button.dart';
export 'buttons/hearder_style/select_header_style_buttons.dart';
export 'buttons/hearder_style/select_header_style_dropdown_button.dart';
export 'buttons/history_button.dart';
export 'buttons/indent_button.dart';
export 'buttons/link_style2_button.dart';
export 'buttons/link_style_button.dart';
export 'buttons/quill_icon_button.dart';
export 'buttons/search/search_button.dart';
export 'buttons/select_line_height_dropdown_button.dart';
export 'buttons/toggle_check_list_button.dart';
export 'buttons/toggle_style_button.dart';
export 'config/base_button_options.dart';
export 'config/simple_toolbar_config.dart';

RxBool isOpenBoldDialog = false.obs;
RxBool isOpenMenuDialog = false.obs;

class QuillSimpleToolbar extends StatefulWidget implements PreferredSizeWidget {
  const QuillSimpleToolbar({
    required this.controller,
    required this.focusNode,
    this.config = const QuillSimpleToolbarConfig(),
    super.key,
  });

  final QuillController controller;
  final FocusNode focusNode;
  final QuillSimpleToolbarConfig config;

  @override
  State<QuillSimpleToolbar> createState() => _QuillSimpleToolbarState();

  @override
  // TODO: implement preferredSize
  @override
  Size get preferredSize => config.axis == Axis.horizontal
      ? const Size.fromHeight(kDefaultToolbarSize)
      : const Size.fromWidth(kDefaultToolbarSize);
}

class _QuillSimpleToolbarState extends State<QuillSimpleToolbar> {
  Offset boldOffset = const Offset(0, 0);
  Offset menuOffset = const Offset(0, 0);

  double get _toolbarSize => widget.config.toolbarSize * 1.4;
  ScrollController scrollController = ScrollController();
  final GlobalKey _targetKey = GlobalKey();
  final GlobalKey _targetKey1 = GlobalKey();

  double leftSpacing = 0;
  double rightSpacing = 0;

  void _calculateSpacing() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final renderBox =
      _targetKey.currentContext?.findRenderObject() as RenderBox?;
      final renderBox1 =
      _targetKey1.currentContext?.findRenderObject() as RenderBox?;


      if (renderBox != null) {
        final double widgetPosition = renderBox.localToGlobal(Offset.zero).dx;

        setState(() {
          leftSpacing = widgetPosition - 40;
        });
      }
      if (renderBox1 != null) {
        final double widgetPosition = renderBox1.localToGlobal(Offset.zero).dx;
        final double screenWidth = MediaQuery.of(context).size.width;

        setState(() {
          rightSpacing = screenWidth - (widgetPosition + renderBox1.size.width + 50);
        });
      }
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // scrollController.addListener(_calculateSpacing);
    // scrollController.animateTo(1, duration: const Duration(milliseconds: 50), curve: Curves.bounceIn);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    // scrollController.removeListener(_calculateSpacing);
  }
  // ValueNotifier<double> height = ValueNotifier(0);

  @override
  Widget build(BuildContext context) {
    _calculateSpacing();
    final embedButtons = widget.config.embedButtons;

    List<Widget> childrenBuilder(BuildContext context) {
      final axis = widget.config.axis;

      final divider = SizedBox(
          height: _toolbarSize,
          child: QuillToolbarDivider(
            axis,
            color: widget.config.sectionDividerColor,
            space: widget.config.sectionDividerSpace,
          ));

      final groups = [
        [
          if (widget.config.showUndo)
            QuillToolbarHistoryButton(
              isUndo: true,
              options: widget.config.buttonOptions.undoHistory,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showRedo)
            QuillToolbarHistoryButton(
              isUndo: false,
              options: widget.config.buttonOptions.redoHistory,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showHeaderStyle) ...[
            if (widget.config.headerStyleType.isOriginal)
              QuillToolbarSelectHeaderStyleDropdownButton(
                controller: widget.controller,
                options:
                widget.config.buttonOptions.selectHeaderStyleDropdownButton,
                baseOptions: widget.config.buttonOptions.base,
              )
            else
              QuillToolbarSelectHeaderStyleButtons(
                controller: widget.controller,
                options: widget.config.buttonOptions.selectHeaderStyleButtons,
                baseOptions: widget.config.buttonOptions.base,
              ),
          ],
          if (widget.config.showFontFamily)
            QuillToolbarFontFamilyButton(
              options: widget.config.buttonOptions.fontFamily,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),

          /*InkWell(
            onTap: () {
              isOpenBoldDialog.value = !isOpenBoldDialog.value;
              isOpenMenuDialog.value = false;

              boldOffset = const Offset(0, 0);
            },
            child: Padding(
              key: _targetKey,
              padding: const EdgeInsets.only(
                left: 6,
                right: 10,
                top: 3,
              ),
              child: Image.asset(
                // AppAsset.biuImage,
                'assets/create/biu.png',
                height: 22,
              ),
            ),
          ),*/

          if (widget.config.showBoldButton)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.bold,
              options: widget.config.buttonOptions.bold,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (embedButtons != null)
            for (final builder in embedButtons)
              builder(
                context,
                EmbedButtonContext(
                  controller: widget.controller,
                  toolbarIconSize: kDefaultIconSize,
                  iconTheme: widget.config.iconTheme,
                  dialogTheme: widget.config.dialogTheme,
                  baseButtonOptions: widget.config.buttonOptions.base,
                ),
              ),
          if (widget.config.showListNumbers)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.ol,
              options: widget.config.buttonOptions.listNumbers,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showListBullets)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.ul,
              options: widget.config.buttonOptions.listBullets,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          // QuillToolbarSelectAlignmentButtons(
          //   controller: widget.controller,
          //   options: widget.config.buttonOptions.selectAlignmentButtons.copyWith(
          //     showLeftAlignment: widget.config.showLeftAlignment,
          //     showCenterAlignment: widget.config.showCenterAlignment,
          //     showRightAlignment: widget.config.showRightAlignment,
          //     showJustifyAlignment: widget.config.showJustifyAlignment,
          //   ),
          //   baseOptions: widget.config.buttonOptions.base,
          // ),
          QuillToolbarCyclingAlignmentButton(controller: widget.controller),
          /*Padding(
            key: _targetKey1,
            padding: const EdgeInsets.only(
              left: 10,
              right: 10,
            ),
            child: GestureDetector(
              onTapDown: (details) {
                isOpenMenuDialog.value = !isOpenMenuDialog.value;
                isOpenBoldDialog.value = false;
                menuOffset = const Offset(0, 0);
                // if (isOpenMenuDialog.value) {
                //   height.value = 120;
                // }else{
                //   height.value = 0.0;
                // }
                // // menuOffset = details.globalPosition ;
                // setState(() {});
              },
              child: const Icon(
                Icons.format_align_left_outlined,
                color: Color(0xFF747474),
              ),
            ),
          ),*/
          if (widget.config.showLink)
            widget.config.linkStyleType.isOriginal
                ? QuillToolbarLinkStyleButton(
              controller: widget.controller,
              options: widget.config.buttonOptions.linkStyle,
              baseOptions: widget.config.buttonOptions.base,
            )
                : QuillToolbarLinkStyleButton2(
              controller: widget.controller,
              options: widget.config.buttonOptions.linkStyle2,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showItalicButton)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.italic,
              options: widget.config.buttonOptions.italic,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showUnderLineButton)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.underline,
              options: widget.config.buttonOptions.underLine,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showStrikeThrough)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.strikeThrough,
              options: widget.config.buttonOptions.strikeThrough,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showInlineCode)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.inlineCode,
              options: widget.config.buttonOptions.inlineCode,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showSubscript)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.subscript,
              options: widget.config.buttonOptions.subscript,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showSuperscript)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.superscript,
              options: widget.config.buttonOptions.superscript,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showColorButton)
            QuillToolbarColorButton(
              focusNode: widget.focusNode,
              controller: widget.controller,
              isBackground: false,
              options: widget.config.buttonOptions.color,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showSmallButton)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.small,
              options: widget.config.buttonOptions.small,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),

          if (widget.config.showFontSize)
            QuillToolbarFontSizeButton(
              options: widget.config.buttonOptions.fontSize,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showBackgroundColorButton)
            QuillToolbarColorButton(
              focusNode: widget.focusNode,
              options: widget.config.buttonOptions.backgroundColor,
              controller: widget.controller,
              isBackground: true,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showClearFormat)
            QuillToolbarClearFormatButton(
              controller: widget.controller,
              options: widget.config.buttonOptions.clearFormat,
              baseOptions: widget.config.buttonOptions.base,
            ),
        ],
        [
          // if (config.showAlignmentButtons)
          //   QuillToolbarSelectAlignmentButtons(
          //     controller: controller,
          //     options: config.buttonOptions.selectAlignmentButtons.copyWith(
          //       showLeftAlignment: config.showLeftAlignment,
          //       showCenterAlignment: config.showCenterAlignment,
          //       showRightAlignment: config.showRightAlignment,
          //       showJustifyAlignment: config.showJustifyAlignment,
          //     ),
          //     baseOptions: config.buttonOptions.base,
          //   ),
          if (widget.config.showDirection)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.rtl,
              options: widget.config.buttonOptions.direction,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
        ],
        [
          if (widget.config.showLineHeightButton)
            QuillToolbarSelectLineHeightStyleDropdownButton(
              controller: widget.controller,
              options: widget
                  .config.buttonOptions.selectLineHeightStyleDropdownButton,
              baseOptions: widget.config.buttonOptions.base,
            ),
        ],
        [

          if (widget.config.showListCheck)
            QuillToolbarToggleCheckListButton(
              options: widget.config.buttonOptions.toggleCheckList,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showCodeBlock)
            QuillToolbarToggleStyleButton(
              attribute: Attribute.codeBlock,
              options: widget.config.buttonOptions.codeBlock,
              controller: widget.controller,
              baseOptions: widget.config.buttonOptions.base,
            ),
        ],
        [
          if (widget.config.showQuote)
            QuillToolbarToggleStyleButton(
              options: widget.config.buttonOptions.quote,
              controller: widget.controller,
              attribute: Attribute.blockQuote,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showIndent)
            QuillToolbarIndentButton(
              controller: widget.controller,
              isIncrease: true,
              options: widget.config.buttonOptions.indentIncrease,
              baseOptions: widget.config.buttonOptions.base,
            ),
          if (widget.config.showIndent)
            QuillToolbarIndentButton(
              controller: widget.controller,
              isIncrease: false,
              options: widget.config.buttonOptions.indentDecrease,
              baseOptions: widget.config.buttonOptions.base,
            ),
        ],
        [
          // if (config.showLink)
          //   config.linkStyleType.isOriginal
          //       ? QuillToolbarLinkStyleButton(
          //     controller: controller,
          //     options: config.buttonOptions.linkStyle,
          //     baseOptions: config.buttonOptions.base,
          //   )
          //       : QuillToolbarLinkStyleButton2(
          //     controller: controller,
          //     options: config.buttonOptions.linkStyle2,
          //     baseOptions: config.buttonOptions.base,
          //   ),
          if (widget.config.showSearchButton)
            QuillToolbarSearchButton(
              baseOptions: widget.config.buttonOptions.base,
              controller: widget.controller,
              options: widget.config.buttonOptions.search,
            ),
          if (widget.config.showClipboardCut)
            QuillToolbarClipboardButton(
              baseOptions: widget.config.buttonOptions.base,
              options: widget.config.buttonOptions.clipboardCut,
              controller: widget.controller,
              clipboardAction: ClipboardAction.cut,
            ),
          if (widget.config.showClipboardCopy)
            QuillToolbarClipboardButton(
              baseOptions: widget.config.buttonOptions.base,
              options: widget.config.buttonOptions.clipboardCopy,
              controller: widget.controller,
              clipboardAction: ClipboardAction.copy,
            ),
          if (widget.config.showClipboardPaste)
            QuillToolbarClipboardButton(
              baseOptions: widget.config.buttonOptions.base,
              options: widget.config.buttonOptions.clipboardPaste,
              controller: widget.controller,
              clipboardAction: ClipboardAction.paste,
            ),
        ],
        [
          for (final customButton in widget.config.customButtons)
            QuillToolbarCustomButton(
              baseOptions: widget.config.buttonOptions.base,
              options: customButton,
              controller: widget.controller,
            ),
        ],
      ];

      final buttonsAll = <Widget>[];

      for (var i = 0; i < groups.length; i++) {
        final buttons = groups[i];

        if (buttons.isNotEmpty) {
          if (buttonsAll.isNotEmpty && widget.config.showDividers) {
            buttonsAll.add(divider);
          }
          buttonsAll.addAll(buttons);
        }
      }

      return buttonsAll;
    }

    return Stack(
      clipBehavior: Clip.none,
      fit: StackFit.loose,
      children: [
        Container(
          // height: 50,
          alignment: Alignment.bottomCenter,
          child: Builder(
            builder: (context) {
              if (widget.config.multiRowsDisplay) {
                return Wrap(
                  direction: widget.config.axis,
                  alignment: widget.config.toolbarIconAlignment,
                  crossAxisAlignment:
                      widget.config.toolbarIconCrossAlignment,
                  runSpacing: widget.config.toolbarRunSpacing,
                  spacing: widget.config.toolbarSectionSpacing,
                  children: childrenBuilder(context),
                );
              }
              return Container(
                decoration: widget.config.decoration ??
                    BoxDecoration(
                      color: widget.config.color ??
                          Theme.of(context).canvasColor,
                    ),
                constraints: BoxConstraints.tightFor(
                  height: widget.config.axis == Axis.horizontal
                      ? _toolbarSize
                      : null,
                  width: widget.config.axis == Axis.vertical
                      ? _toolbarSize
                      : null,
                ),
                child: QuillToolbarArrowIndicatedButtonList(
                  axis: widget.config.axis,
                  buttons: childrenBuilder(context),
                ),
              );
            },
          ),
        ),
        Obx(
          () {
            return isOpenBoldDialog.value
                ? CustomSpeechBubble(
              leftSpace: leftSpacing,
                    position: boldOffset,
                    config: widget.config,
                    controller: widget.controller,
                  )
                : const SizedBox();
          },
        ),
        Obx(
          () {
            return isOpenMenuDialog.value
                ? CustomSpeechBubble1(
              rightSpace: rightSpacing,
                    position: menuOffset,
                    config: widget.config,
                    controller: widget.controller,
                  )
                : const SizedBox();
          },
        ),
        /* CustomSpeechBubble(
          position: boldOffset,
          config: widget.config,
          controller: widget.controller,
        ),
        CustomSpeechBubble1(
          position: menuOffset,
          config: widget.config,
          controller: widget.controller,
        )*/
      ],
    );
  }
}

/// The divider which is used for separation of buttons in the toolbar.
///
/// It can be used outside of this package, for example when user does not use
/// [QuillToolbar.basic] and compose toolbar's children on its own.
class QuillToolbarDivider extends StatelessWidget {
  const QuillToolbarDivider(
    this.axis, {
    super.key,
    this.color,
    this.space,
  });

  /// Provides a horizontal divider for vertical toolbar.
  const QuillToolbarDivider.horizontal({Key? key, Color? color, double? space})
      : this(Axis.horizontal, color: color, space: space, key: key);

  /// Provides a horizontal divider for horizontal toolbar.
  const QuillToolbarDivider.vertical({Key? key, Color? color, double? space})
      : this(Axis.vertical, color: color, space: space, key: key);

  /// The axis along which the toolbar is.
  final Axis axis;

  /// The color to use when painting this divider's line.
  final Color? color;

  /// The divider's space (width or height) depending of [axis].
  final double? space;

  @override
  Widget build(BuildContext context) {
    // Vertical toolbar requires horizontal divider, and vice versa
    return axis == Axis.vertical
        ? Divider(
            height: space,
            color: color,
            indent: 12,
            endIndent: 12,
          )
        : VerticalDivider(
            width: space,
            color: color,
            indent: 12,
            endIndent: 12,
          );
  }
}

class CustomSpeechBubble extends StatelessWidget {
  final Offset position;
  var config;
  var controller;
  var leftSpace;

  CustomSpeechBubble({
    super.key,
    required this.position,
    required this.config,
    required this.controller,
    required this.leftSpace,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    // Adjust bubble position to avoid overflowing the screen
    double bubbleX = position.dx - 70; // Offset for bubble width
    double bubbleY = position.dy + 50; // Offset for bubble height

    if (bubbleX < 0) bubbleX = 10; // Avoid left overflow
    if (bubbleX + 100 > screenSize.width)
      bubbleX = screenSize.width - 110; // Avoid right overflow
    if (bubbleY < 0) bubbleY = position.dy + 20; // Avoid top overflow

    return Positioned(
      left: leftSpace,
      bottom: bubbleY,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (config.showBoldButton)
                  QuillToolbarToggleStyleButton(
                    attribute: Attribute.bold,
                    options: config.buttonOptions.bold,
                    controller: controller,
                    baseOptions: config.buttonOptions.base,
                  ),
                if (config.showItalicButton)
                  QuillToolbarToggleStyleButton(
                    attribute: Attribute.italic,
                    options: config.buttonOptions.italic,
                    controller: controller,
                    baseOptions: config.buttonOptions.base,
                  ),
                if (config.showUnderLineButton)
                  QuillToolbarToggleStyleButton(
                    attribute: Attribute.underline,
                    options: config.buttonOptions.underLine,
                    controller: controller,
                    baseOptions: config.buttonOptions.base,
                  ),
              ],
            ),
          ),
          CustomPaint(
            size: const Size(20, 10),
            painter: TrianglePainter(),
          ),
        ],
      ),
    );
  }
}

class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = Colors.white;
    final path = Path()
      ..moveTo(0, 0) // Start at the top-left corner of the triangle
      ..lineTo(size.width / 2, size.height) // Draw to the bottom-center
      ..lineTo(size.width, 0) // Draw to the top-right corner
      ..close(); // Close the path to form a triangle

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class CustomSpeechBubble1 extends StatelessWidget {
  final Offset position;
  var config;
  var controller;
  var rightSpace;

  CustomSpeechBubble1({
    super.key,
    required this.position,
    required this.config,
    required this.controller,
    required this.rightSpace,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    // Adjust bubble position to avoid overflowing the screen
    double bubbleX = position.dx - 70; // Offset for bubble width
    double bubbleY = position.dy + 50; // Offset for bubble height

    if (bubbleX < 0) bubbleX = 10; // Avoid left overflow
    if (bubbleX + 100 > screenSize.width)
      bubbleX = screenSize.width - 110; // Avoid right overflow
    if (bubbleY < 0) bubbleY = position.dy + 20; // Avoid top overflow

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Positioned(
          right: rightSpace,
          bottom: bubbleY,
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (config.showAlignmentButtons)
                      QuillToolbarSelectAlignmentButtons(
                        controller: controller,
                        options: config.buttonOptions.selectAlignmentButtons
                            .copyWith(
                          showLeftAlignment: config.showLeftAlignment,
                          showCenterAlignment: config.showCenterAlignment,
                          showRightAlignment: config.showRightAlignment,
                          showJustifyAlignment: config.showJustifyAlignment,
                        ),
                        baseOptions: config.buttonOptions.base,
                      ),
                  ],
                ),
              ),
              CustomPaint(
                size: const Size(20, 10),
                painter: TrianglePainter1(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class TrianglePainter1 extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = Colors.white;
    final path = Path()
      ..moveTo(0, 0) // Start at the top-left corner of the triangle
      ..lineTo(size.width / 2, size.height) // Draw to the bottom-center
      ..lineTo(size.width, 0) // Draw to the top-right corner
      ..close(); // Close the path to form a triangle

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
