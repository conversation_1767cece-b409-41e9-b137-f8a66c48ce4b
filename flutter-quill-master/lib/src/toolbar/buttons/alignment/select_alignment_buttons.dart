import 'package:flutter/material.dart';

import '../../../controller/quill_controller.dart';
import '../../../document/attribute.dart';
import '../../config/simple_toolbar_button_options.dart';
import '../toggle_style_button.dart';

class QuillToolbarSelectAlignmentButtons extends StatelessWidget {
  const QuillToolbarSelectAlignmentButtons({
    required this.controller,
    this.options = const QuillToolbarSelectAlignmentButtonOptions(),

    /// Shares common options between all buttons, prefer the [options]
    /// over the [baseOptions].
    this.baseOptions,
    super.key,
  });

  final QuillToolbarBaseButtonOptions? baseOptions;

  // TODO: This button doesn't support the base button option

  final QuillController controller;
  final QuillToolbarSelectAlignmentButtonOptions options;

  List<Attribute> get _attrbuites {
    return options.attributes ??
        [
          if (options.showLeftAlignment) Attribute.leftAlignment,
          if (options.showCenterAlignment) Attribute.centerAlignment,
          if (options.showRightAlignment) Attribute.rightAlignment,
          if (options.showJustifyAlignment) Attribute.justifyAlignment,
        ];
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _attrbuites
          .map((e) => QuillToolbarToggleStyleButton(
                baseOptions: baseOptions,
                controller: controller,
                attribute: e,
                options: QuillToolbarToggleStyleButtonOptions(
                  iconData: options.iconData,
                  iconSize: options.iconSize,
                  iconButtonFactor: options.iconButtonFactor,
                  afterButtonPressed: options.afterButtonPressed,
                  iconTheme: options.iconTheme,
                  tooltip: options.tooltip,
                ),
              ))
          .toList(),
    );
  }
}

class QuillToolbarCyclingAlignmentButton extends StatefulWidget {
  final QuillController controller;
  final List<Attribute> cycleAlignments;

  const QuillToolbarCyclingAlignmentButton({
    super.key,
    required this.controller,
    this.cycleAlignments = const [
      Attribute.leftAlignment,
      Attribute.centerAlignment,
      Attribute.rightAlignment,
      Attribute.justifyAlignment,
    ],
  });

  @override
  State<QuillToolbarCyclingAlignmentButton> createState() =>
      _QuillToolbarCyclingAlignmentButtonState();
}

class _QuillToolbarCyclingAlignmentButtonState
    extends State<QuillToolbarCyclingAlignmentButton> {
  int _currentIndex = 0;

  void _cycleAlignment() {
    setState(() {
      _currentIndex = (_currentIndex + 1) % widget.cycleAlignments.length;
    });
    widget.controller
        .formatSelection(widget.cycleAlignments[_currentIndex]);
  }

  IconData _getIconForAttribute(Attribute attr) {
    switch (attr) {
      case Attribute.centerAlignment:
        return Icons.format_align_center;
      case Attribute.rightAlignment:
        return Icons.format_align_right;
      case Attribute.justifyAlignment:
        return Icons.format_align_justify;
      case Attribute.leftAlignment:
      default:
        return Icons.format_align_left;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentAttr = widget.cycleAlignments[_currentIndex];
    return IconButton(
      icon: Icon(_getIconForAttribute(currentAttr), color: const Color(0xFF747474),),
      onPressed: _cycleAlignment,
      tooltip: currentAttr.key,
    );
  }
}

