import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart' as color_picker
    show ColorPicker, MaterialPicker, colorToHex;

import '../../../document/style.dart';
import '../../../editor_toolbar_shared/color.dart';
import '../../../l10n/extensions/localizations_ext.dart';

enum _PickerType {
  material,
  color,
}

class ColorPickerDialog extends StatefulWidget {
  const ColorPickerDialog({
    required this.isBackground,
    required this.onRequestChangeColor,
    required this.isToggledColor,
    required this.selectionStyle,
    super.key,
  });
  final bool isBackground;

  final bool isToggledColor;
  final Function(BuildContext context, Color? color) onRequestChangeColor;
  final Style selectionStyle;

  @override
  State<ColorPickerDialog> createState() => ColorPickerDialogState();
}

class ColorPickerDialogState extends State<ColorPickerDialog> {
  var pickerType = _PickerType.material;
  var selectedColor = const Color(0xFF787E89);
  late FocusNode hexFocusNode;
  late final TextEditingController hexController;
  late void Function(void Function()) colorBoxSetState;

  @override
  void initState() {
    super.initState();
    hexFocusNode = FocusNode();
    hexController =
        TextEditingController(text: color_picker.colorToHex(selectedColor));
    if (widget.isToggledColor) {
      selectedColor = widget.isBackground
          ? hexToColor(widget.selectionStyle.attributes['background']?.value)
          : hexToColor(widget.selectionStyle.attributes['color']?.value);
    }
    hexFocusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: AlertDialog(
        title: Text(context.loc.selectColor),
        actions: [
          TextButton(
              onPressed: () {
                widget.onRequestChangeColor(context, selectedColor);
                Navigator.of(context).pop();
              },
              child: Text(context.loc.ok)),
        ],
        // backgroundColor: Colors.white,
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  TextButton(
                    onPressed: () {
                      setState(() {
                        pickerType = _PickerType.material;
                      });
                    },
                    child: Text(context.loc.material),
                  ),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        pickerType = _PickerType.color;
                      });
                    },
                    child: Text(context.loc.color),
                  ),
                  TextButton(
                    onPressed: () {
                      widget.onRequestChangeColor(context, null);
                      Navigator.of(context).pop();
                    },
                    child: Text(context.loc.clear),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Column(
                children: [
                  if (pickerType == _PickerType.material)
                    color_picker.MaterialPicker(
                      pickerColor: selectedColor,
                      onColorChanged: (color) {
                        widget.onRequestChangeColor(context, color);
                        Navigator.of(context).pop();
                      },
                    ),
                  if (pickerType == _PickerType.color)
                    color_picker.ColorPicker(
                      pickerColor: selectedColor,

                      onColorChanged: (color) {
                        FocusScope.of(context).unfocus();
                        hexFocusNode.unfocus(); // Dismiss keyboard
                        setState(() {
                          selectedColor = color;
                          hexController.text = color_picker.colorToHex(color);
                        });

                        colorBoxSetState(() {});
                        widget.onRequestChangeColor(context, color);
                      },

                    ),
                  const SizedBox(
                    height: 10,
                  ),
                  /*Row(
                    children: [
                      SizedBox(
                        width: 100,
                        height: 60,
                        child: TextFormField(
                          focusNode: hexFocusNode,
                          controller: hexController,
                          readOnly: true, // Prevents keyboard from opening automatically
                          onTap: () {
                            // Allow keyboard only when user taps the field
                            hexFocusNode.requestFocus();
                          },
                          decoration: InputDecoration(
                            labelText: context.loc.hex,
                            border: const OutlineInputBorder(),
                          ),
                        ),

                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      StatefulBuilder(
                        builder: (context, mcolorBoxSetState) {
                          colorBoxSetState = mcolorBoxSetState;
                          return Container(
                            width: 25,
                            height: 25,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.black45,
                              ),
                              color: selectedColor,
                              borderRadius: BorderRadius.circular(5),
                            ),
                          );
                        },
                      ),
                    ],
                  ),*/
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
