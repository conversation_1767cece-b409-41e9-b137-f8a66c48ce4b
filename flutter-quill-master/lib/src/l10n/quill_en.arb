{"@@locale": "en", "pasteLink": "Paste a link", "ok": "Ok", "selectColor": "Select Color", "gallery": "Gallery", "link": "Link", "open": "Open", "copy": "Copy", "remove": "Remove", "save": "Save", "zoom": "Zoom", "saved": "Saved", "text": "Text", "resize": "Resize", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "size": "Size", "small": "Small", "large": "Large", "huge": "<PERSON>ge", "clear": "Clear", "font": "Font", "search": "Search", "camera": "Camera", "video": "Video", "undo": "Undo", "redo": "Redo", "fontFamily": "<PERSON>ont family", "fontSize": "Font size", "bold": "Bold", "subscript": "Subscript", "superscript": "Superscript", "italic": "Italic", "underline": "Underline", "strikeThrough": "Strike through", "inlineCode": "Inline code", "fontColor": "Font color", "backgroundColor": "Background color", "clearFormat": "Clear format", "alignLeft": "<PERSON><PERSON> left", "alignCenter": "Align center", "alignRight": "Align right", "alignJustify": "Align justify", "@alignJustify": {"description": "Justify the text over the full window width"}, "justifyWinWidth": "Justify win width", "textDirection": "Text direction", "headerStyle": "Header style", "normal": "Normal", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "heading4": "Heading 4", "heading5": "Heading 5", "heading6": "Heading 6", "numberedList": "Numbered list", "bulletList": "Bullet list", "checkedList": "Checked list", "codeBlock": "Code block", "quote": "Quote", "increaseIndent": "Increase indent", "decreaseIndent": "Decrease indent", "insertURL": "Insert URL", "visitLink": "Visit link", "enterLink": "Enter link", "enterMedia": "Enter media", "edit": "Edit", "apply": "Apply", "hex": "Hex", "material": "Material", "color": "Color", "lineheight": "Line height", "findText": "Find text", "moveToPreviousOccurrence": "Move to previous occurrence", "moveToNextOccurrence": "Move to next occurrence", "savedUsingTheNetwork": "Saved using the network", "savedUsingLocalStorage": "Saved using the local storage", "theImageHasBeenSavedAt": "The image has been saved at: {imagePath}", "@theImageHasBeenSavedAt": {"description": "A message with a single parameter", "placeholders": {"imagePath": {"type": "String", "example": "path/to/location"}}}, "errorWhileSavingImage": "Error while saving image", "pleaseEnterTextForYourLink": "Please enter a text for your link (e.g., 'Learn more')", "pleaseEnterTheLinkURL": "Please enter the link URL (e.g., 'https://example.com')", "pleaseEnterAValidImageURL": "Please enter a valid image URL", "pleaseEnterAValidVideoURL": "Please enter a valid video url", "photo": "Photo", "image": "Image", "caseSensitivityAndWholeWordSearch": "Case sensitivity and whole word search", "caseSensitive": "Case sensitive", "wholeWord": "Whole word", "insertImage": "Insert image", "pickAPhotoFromYourGallery": "Pick a photo from your gallery", "takeAPhotoUsingYourCamera": "Take a photo using your camera", "pasteAPhotoUsingALink": "Paste a photo using a link", "pickAVideoFromYourGallery": "Pick a video from your gallery", "recordAVideoUsingYourCamera": "Record a video using your camera", "pasteAVideoUsingALink": "Paste a video using a link", "close": "Close", "searchSettings": "Search settings", "cut": "Cut", "paste": "Paste", "insertTable": "Insert table", "insertVideo": "Insert video"}