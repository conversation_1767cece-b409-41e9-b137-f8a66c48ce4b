{"@@locale": "el", "pasteLink": "Επικόλληση συνδέσμου", "ok": "Εντάξει", "selectColor": "Επιλογή χρώματος", "gallery": "Συλλογή", "link": "Σύνδεσμος", "open": "Άνοιγμα", "copy": "Αντιγραφή", "remove": "Αφαίρεση", "save": "Αποθήκευση", "zoom": "Μεγέθυνση", "saved": "Αποθηκεύτηκε", "text": "Κείμενο", "resize": "Αλλαγή μεγέθους", "width": "<PERSON>λ<PERSON><PERSON><PERSON>", "height": "Ύψος", "size": "Μέγεθος", "small": "Μικρό", "large": "Μεγάλο", "huge": "Τεράστιο", "clear": "Εκκαθάριση", "font": "Γραμματοσειρά", "search": "Αναζήτηση", "camera": "Κάμερα", "video": "Βίντεο", "undo": "Αναίρεση", "redo": "Επανάληψη", "fontFamily": "Οικογένεια γραμματοσειράς", "fontSize": "Μέγ<PERSON><PERSON>ος γραμματοσειράς", "bold": "Έντονο", "subscript": "Δείκτης κάτω", "superscript": "Εκθέτης", "italic": "Πλάγια", "underline": "Υπογράμμιση", "strikeThrough": "Διακριτή γραμμή", "inlineCode": "Ενσωματω<PERSON><PERSON><PERSON><PERSON> κώδικας", "fontColor": "Χρώμα γραμματοσειράς", "backgroundColor": "<PERSON>ρώ<PERSON><PERSON> φόντου", "clearFormat": "Εκκαθάριση μορφοποίησης", "alignLeft": "Στοίχιση αριστερά", "alignCenter": "Στοίχιση στο κέντρο", "alignRight": "Στοίχιση δεξιά", "alignJustify": "Πλήρης στοίχιση", "@alignJustify": {"description": "Στοίχιση του κειμένου σε όλο το πλάτος του παραθύρου"}, "justifyWinWidth": "Πλήρης στοίχιση πλάτους παραθύρου", "textDirection": "Κατεύθυνση κειμένου", "headerStyle": "Στυλ επικεφαλίδας", "normal": "Κανονικό", "heading1": "Επικεφαλίδα 1", "heading2": "Επικεφαλίδα 2", "heading3": "Επικεφαλίδα 3", "heading4": "Επικεφαλίδα 4", "heading5": "Επικεφαλίδα 5", "heading6": "Επικεφαλίδα 6", "numberedList": "Αριθμημένη λίστα", "bulletList": "Λίστα με κουκκίδες", "checkedList": "Λίστα ελέγχου", "codeBlock": "Μπλ<PERSON><PERSON> κώδικα", "quote": "Παράθεση", "increaseIndent": "Αύξηση εσοχής", "decreaseIndent": "Μείωση εσοχής", "insertURL": "Εισαγωγή URL", "visitLink": "Επίσκεψη συνδέσμου", "enterLink": "Εισαγωγή συνδέσμου", "enterMedia": "Εισαγωγή μέσων", "edit": "Επεξεργασία", "apply": "Εφαρμογή", "hex": "Εξαδικό", "material": "Υλικό", "color": "Χρώμα", "lineheight": "Ύψος γραμμής", "findText": "Εύρεση κειμένου", "moveToPreviousOccurrence": "Μετακ<PERSON>νηση στην προηγούμενη εμφάνιση", "moveToNextOccurrence": "Μετακίνηση στην επόμενη εμφάνιση", "savedUsingTheNetwork": "Αποθηκεύτηκε μέσω του δικτύου", "savedUsingLocalStorage": "Αποθηκεύτηκε μέσω τοπικής αποθήκευσης", "theImageHasBeenSavedAt": "Η εικόνα αποθηκεύτηκε στη διαδρομή: {imagePath}", "@theImageHasBeenSavedAt": {"description": "Μήνυμα με μια παράμετρο", "placeholders": {"imagePath": {"type": "String", "example": "διαδρομή/προς/τοποθεσία"}}}, "errorWhileSavingImage": "Σφάλμα κατά την αποθήκευση της εικόνας", "pleaseEnterTextForYourLink": "Εισαγάγετε κείμενο για τον σύνδεσμό σας (π.χ., 'Μάθετε περισσότερα')", "pleaseEnterTheLinkURL": "Εισαγάγετε το URL του συνδέσμου (π.χ., 'https://example.com')", "pleaseEnterAValidImageURL": "Εισαγάγετε έγκυρο URL εικόνας", "pleaseEnterAValidVideoURL": "Εισαγάγετε έγκυρο URL βίντεο", "photo": "Φωτογραφία", "image": "Εικόνα", "caseSensitivityAndWholeWordSearch": "Δια<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πεζών-κε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και αναζήτηση ολόκληρης λέξης", "caseSensitive": "Δια<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πεζών-κεφαλα<PERSON>ων", "wholeWord": "Ολόκληρη λέξη", "insertImage": "Εισαγωγή εικόνας", "pickAPhotoFromYourGallery": "Επιλέξτε φωτογραφία από τη συλλογή σας", "takeAPhotoUsingYourCamera": "Τραβήξτε φωτογραφία χρησιμοποιώντας την κάμερα σας", "pasteAPhotoUsingALink": "Επικολλήστε φωτογραφία χρησιμοποιώντας έναν σύνδεσμο", "pickAVideoFromYourGallery": "Επιλέξτε βίντεο από τη συλλογή σας", "recordAVideoUsingYourCamera": "Καταγρ<PERSON><PERSON><PERSON><PERSON> βίντεο χρησιμοποιώντας την κάμερα σας", "pasteAVideoUsingALink": "Επικολλήστε βίντεο χρησιμοποιώντας έναν σύνδεσμο", "close": "Κλείσιμο", "searchSettings": "Ρυθμίσεις αναζήτησης", "cut": "Αποκοπή", "paste": "Επικόλληση", "insertTable": "Εισαγωγή πίνακα"}