{"@@locale": "pt", "pasteLink": "Colar um link", "ok": "Ok", "selectColor": "Selecionar uma cor", "gallery": "Galeria", "link": "Link", "open": "Abra", "copy": "Copiar", "remove": "Remover", "save": "<PERSON><PERSON>", "zoom": "Zoom", "saved": "Salvo", "text": "Texto", "resize": "Redimencionar", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "size": "<PERSON><PERSON><PERSON>", "small": "Pequeno", "large": "Grande", "huge": "Gigante", "clear": "Limpar", "font": "Fonte", "search": "Search", "camera": "Camera", "video": "Video", "undo": "Undo", "redo": "Redo", "fontFamily": "<PERSON>ont family", "fontSize": "Font size", "bold": "Bold", "subscript": "Subscript", "superscript": "Superscript", "italic": "Italic", "underline": "Underline", "strikeThrough": "Strike through", "inlineCode": "Inline code", "fontColor": "Font color", "backgroundColor": "Background color", "clearFormat": "Clear format", "alignLeft": "<PERSON><PERSON> left", "alignCenter": "Align center", "alignRight": "Align right", "justifyWinWidth": "Justify win width", "textDirection": "Text direction", "headerStyle": "Header style", "numberedList": "Numbered list", "bulletList": "Bullet list", "checkedList": "Checked list", "codeBlock": "Code block", "quote": "Quote", "increaseIndent": "Increase indent", "decreaseIndent": "Decrease indent", "insertURL": "Insert URL", "visitLink": "Visit link", "enterLink": "Enter link", "enterMedia": "Enter media", "edit": "Edit", "apply": "Apply", "findText": "Find text", "moveToPreviousOccurrence": "Move to previous occurrence", "moveToNextOccurrence": "Move to next occurrence", "savedUsingTheNetwork": "Guardado através da network", "savedUsingLocalStorage": "Guardado através do armazenamento local", "errorWhileSavingImage": "Erro a gravar imagem", "pleaseEnterTextForYourLink": "e.g., 'Learn more'", "pleaseEnterTheLinkURL": "e.g., 'https://example.com'", "pleaseEnterAValidImageURL": "Please enter a valid image URL", "hex": "Hex", "material": "Material", "color": "Cor", "lineheight": "<PERSON>ura da linha", "pleaseEnterAValidVideoURL": "Por favor, insira uma URL de vídeo válida", "photo": "Foto", "image": "Imagem", "caseSensitivityAndWholeWordSearch": "Sensibilidade a maiúsculas e minúsculas e pesquisa de palavras inteiras", "insertImage": "Inserir imagem", "alignJustify": "Justificar texto", "normal": "Normal", "heading1": "Título 1", "heading2": "Título 2", "heading3": "Título 3", "heading4": "Título 4", "heading5": "Título 5", "heading6": "Título 6", "theImageHasBeenSavedAt": "A imagem foi salva em: {imagePath}", "caseSensitive": "Distinguir mai<PERSON> e minúsculas", "wholeWord": "Palavra inteira", "pickAPhotoFromYourGallery": "Escolha uma foto da sua galeria", "takeAPhotoUsingYourCamera": "Tire uma foto com a sua câmera", "pasteAPhotoUsingALink": "Cole uma foto usando um link", "pickAVideoFromYourGallery": "Escolha um vídeo da sua galeria", "recordAVideoUsingYourCamera": "Grave um vídeo com a sua câmera", "pasteAVideoUsingALink": "Cole um vídeo usando um link", "close": "<PERSON><PERSON><PERSON>", "searchSettings": "Configurações de pesquisa", "cut": "Cortar", "paste": "Colar", "insertTable": "<PERSON><PERSON><PERSON> tabela"}