{"@@locale": "de", "pasteLink": "<PERSON>", "ok": "OK", "selectColor": "Farbe auswählen", "gallery": "Galerie", "link": "Link", "open": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "remove": "Entfernen", "save": "Speichern", "zoom": "Zoomen", "saved": "Gespe<PERSON>rt", "text": "Text", "resize": "Größe ändern", "width": "Breite", "height": "<PERSON><PERSON><PERSON>", "size": "Größe", "small": "<PERSON>", "large": "<PERSON><PERSON><PERSON>", "huge": "<PERSON><PERSON><PERSON>", "clear": "Löschen", "font": "Sc<PERSON><PERSON>", "search": "<PERSON><PERSON>", "camera": "<PERSON><PERSON><PERSON>", "video": "Video", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redo": "Wiederherstellen", "fontFamily": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": "Schriftgröße", "bold": "<PERSON><PERSON>", "subscript": "Tiefgestellt", "superscript": "Hochgestellt", "italic": "<PERSON><PERSON><PERSON>", "underline": "Unterstreichen", "strikeThrough": "Durchstreichen", "inlineCode": "Inline-Code", "fontColor": "Schriftfarbe", "backgroundColor": "Hintergrundfarbe", "clearFormat": "Formatierung löschen", "alignLeft": "Linksbündig ausrichten", "alignCenter": "<PERSON><PERSON><PERSON>", "alignRight": "Rechtsbündig ausrichten", "justifyWinWidth": "Blocksatz", "textDirection": "Textrichtung", "headerStyle": "Überschrift-Stil", "numberedList": "Nummerierte Liste", "bulletList": "Aufzählungsliste", "checkedList": "Checkliste", "codeBlock": "Code-Block", "quote": "Zitat", "increaseIndent": "Einzug vergrößern", "decreaseIndent": "Einzug verkleinern", "insertURL": "URL einfügen", "visitLink": "<PERSON>", "enterLink": "<PERSON>", "enterMedia": "Medien einfügen", "edit": "<PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON><PERSON>", "findText": "Text suchen", "moveToPreviousOccurrence": "Zum vorherigen Auftreten springen", "moveToNextOccurrence": "Zum nächsten Auftreten springen", "savedUsingTheNetwork": "Mit dem Netzwerk gespeichert", "savedUsingLocalStorage": "Mit dem lokalen Speicher gespeichert", "errorWhileSavingImage": "Fehler beim Speichern des Bildes", "pleaseEnterTextForYourLink": "z.<PERSON>. '<PERSON>hr erfahren'", "pleaseEnterTheLinkURL": "z.B. 'https://example.com'", "pleaseEnterAValidImageURL": "Bitte geben Si<PERSON> eine gültige Bild-URL ein", "hex": "Hex", "material": "Material", "color": "Farbe", "lineheight": "Zeilenhöhe", "pleaseEnterAValidVideoURL": "<PERSON>te geben Si<PERSON> eine gültige Video-URL ein", "photo": "Foto", "image": "Bild", "caseSensitivityAndWholeWordSearch": "Groß- und Kleinschreibung sowie Ganzwortsuche", "insertImage": "Bild einfügen", "pickAPhotoFromYourGallery": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Foto aus Ihrer Galerie", "takeAPhotoUsingYourCamera": "<PERSON><PERSON> ein Foto mit Ihrer Kamera", "pasteAPhotoUsingALink": "<PERSON>ügen Si<PERSON> ein Foto über einen Link ein", "pickAVideoFromYourGallery": "<PERSON><PERSON><PERSON>en Sie ein Video aus Ihrer Galerie", "recordAVideoUsingYourCamera": "Nehmen Si<PERSON> ein Video mit Ihrer Kamera auf", "alignJustify": "Blocksatz", "normal": "Normal", "heading1": "Überschrift 1", "heading2": "Überschrift 2", "heading3": "Überschrift 3", "heading4": "Überschrift 4", "heading5": "Überschrift 5", "heading6": "Überschrift 6", "theImageHasBeenSavedAt": "Das Bild wurde gespeichert unter: {imagePath}", "caseSensitive": "Groß- und Kleinschreibung beachten", "wholeWord": "<PERSON><PERSON><PERSON>", "pasteAVideoUsingALink": "Video über einen Link einfügen", "close": "Schließen", "searchSettings": "Such-Einstellungen", "cut": "Ausschneiden", "paste": "Einfügen", "insertTable": "<PERSON>bell<PERSON> e<PERSON>fügen"}