{"@@locale": "ru", "pasteLink": "Вставить ссылку", "ok": "ОК", "selectColor": "Выбрать цвет", "gallery": "Галерея", "link": "Ссылка", "open": "Открыть", "copy": "Копировать", "remove": "Удалить", "save": "Сохранить", "zoom": "Увеличить", "saved": "Сохранено", "text": "Текст", "resize": "Изменить размер", "width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "size": "Размер", "small": "Маленький", "large": "Больш<PERSON>й", "huge": "Огромный", "clear": "Очистить", "font": "<PERSON>ри<PERSON><PERSON>", "search": "Поиск", "camera": "Камера", "video": "Видео", "undo": "Отменить", "redo": "Повторить", "fontFamily": "Семейство шрифтов", "fontSize": "Размер шрифта", "bold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscript": "Нижний индекс", "superscript": "Верхний индекс", "italic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underline": "Подчеркнутый", "strikeThrough": "Зачеркнутый", "inlineCode": "Встроенный код", "fontColor": "Цвет шрифта", "backgroundColor": "Цвет фона", "clearFormat": "Очистить форматирование", "alignLeft": "Выровнять по левому краю", "alignCenter": "Выровнять по центру", "alignRight": "Выровнять по правому краю", "justifyWinWidth": "Выровнять по ширине окна", "textDirection": "Направление текста", "headerStyle": "Стиль заголовка", "numberedList": "Нумерованный список", "bulletList": "Маркированный список", "checkedList": "Список с галочками", "codeBlock": "Блок кода", "quote": "Цитата", "increaseIndent": "Увеличить отступ", "decreaseIndent": "Уменьшить отступ", "insertURL": "Вставить URL", "visitLink": "Посетить ссылку", "enterLink": "Введите ссылку", "enterMedia": "Введите медиа", "edit": "Редактировать", "apply": "Применить", "findText": "Найти текст", "moveToPreviousOccurrence": "Перейти к предыдущему вхождению", "moveToNextOccurrence": "Перейти к следующему вхождению", "savedUsingTheNetwork": "Сохранено с использованием сети", "savedUsingLocalStorage": "Сохранено с использованием локального хранилища", "errorWhileSavingImage": "Ошибка при сохранении изображения", "pleaseEnterTextForYourLink": "например, 'Узнать больше'", "pleaseEnterTheLinkURL": "например, 'https://example.com'", "pleaseEnterAValidImageURL": "Пожалуйста, введите действительный URL изображения", "hex": "Hex", "material": "Материал", "color": "Цвет", "lineheight": "Высота линии", "pleaseEnterAValidVideoURL": "Пожалуйста, введите действительный URL-адрес видео", "photo": "Фото", "image": "Изображение", "caseSensitivityAndWholeWordSearch": "Учет регистра и поиск по всему слову", "insertImage": "Вставить изображение", "pickAPhotoFromYourGallery": "Выберите фотографию из вашей галереи", "takeAPhotoUsingYourCamera": "Сделайте фотографию, используя камеру", "pasteAPhotoUsingALink": "Вставьте фотографию, используя ссылку", "pickAVideoFromYourGallery": "Выберите видео из вашей галереи", "recordAVideoUsingYourCamera": "Запишите видео, используя камеру", "alignJustify": "Выравнивание по ширине", "normal": "Обычный", "heading1": "Заголовок 1", "heading2": "Заголовок 2", "heading3": "Заголовок 3", "heading4": "Заголовок 4", "heading5": "Заголовок 5", "heading6": "Заголовок 6", "theImageHasBeenSavedAt": "Изображение сохранено по адресу: {imagePath}", "caseSensitive": "Чувствителен к регистру", "wholeWord": "Целое слово", "pasteAVideoUsingALink": "Вставить видео по ссылке", "close": "Закрыть", "searchSettings": "Настройки поиска", "cut": "Вырезать", "paste": "Вставить", "insertTable": "Вставить таблицу"}