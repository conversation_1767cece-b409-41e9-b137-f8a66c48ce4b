{"@@locale": "fr", "pasteLink": "Coller un lien", "ok": "Ok", "selectColor": "<PERSON><PERSON> une couleur", "gallery": "Galerie", "link": "<PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "zoom": "<PERSON>mer", "saved": "Enregistrée", "text": "Texte", "resize": "Redimensionner", "width": "<PERSON><PERSON>", "height": "<PERSON><PERSON>", "size": "<PERSON><PERSON>", "small": "<PERSON>", "large": "Grand", "huge": "<PERSON><PERSON><PERSON>", "clear": "Supprimer la mise en forme", "font": "Police", "search": "<PERSON><PERSON><PERSON>", "camera": "Caméra", "video": "Vidéo", "undo": "Annuler", "redo": "<PERSON><PERSON><PERSON>", "fontFamily": "Famille de police", "fontSize": "Taille de police", "bold": "Gras", "subscript": "Indice", "superscript": "Exposant", "italic": "Italique", "underline": "<PERSON><PERSON><PERSON>", "strikeThrough": "<PERSON><PERSON>", "inlineCode": "Code en ligne", "fontColor": "Couleur de police", "backgroundColor": "<PERSON><PERSON><PERSON> de fond", "clearFormat": "Effacer la mise en forme", "alignLeft": "<PERSON><PERSON><PERSON> à gauche", "alignCenter": "Aligner au centre", "alignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "justifyWinWidth": "Justifier", "textDirection": "Direction du texte", "headerStyle": "Style d'en-tête", "normal": "Normal", "heading1": "Titre 1", "heading2": "Titre 2", "heading3": "Titre 3", "heading4": "Titre 4", "heading5": "Titre 5", "heading6": "Titre 6", "numberedList": "Liste numérotée", "bulletList": "Liste à puces", "checkedList": "Check-list", "codeBlock": "Bloc de code", "quote": "Citation", "increaseIndent": "Augmenter le retrait", "decreaseIndent": "Diminuer le retrait", "insertURL": "Insérer une URL", "visitLink": "Visiter", "enterLink": "Entrer un lien", "enterMedia": "Entrer un média", "edit": "Modifier", "apply": "Appliquer", "hex": "Hex", "material": "<PERSON><PERSON><PERSON>", "color": "<PERSON><PERSON><PERSON>", "lineheight": "<PERSON><PERSON> <PERSON> ligne", "findText": "Rechercher du texte", "moveToPreviousOccurrence": "Aller à l'occurrence précédente", "moveToNextOccurrence": "Aller à l'occurrence suivante", "savedUsingTheNetwork": "Enregistré via le réseau", "savedUsingLocalStorage": "Enregistré en utilisant le stockage local", "errorWhileSavingImage": "Erreur lors de l'enregistrement de l'image", "pleaseEnterTextForYourLink": "par exemple, 'En savoir plus'", "pleaseEnterTheLinkURL": "par exemple, 'https://example.com'", "pleaseEnterAValidImageURL": "Veuillez saisir une URL d'image valide", "pleaseEnterAValidVideoURL": "Veuillez entrer une URL vidéo valide", "photo": "Photo", "image": "Image", "caseSensitivityAndWholeWordSearch": "Sensibilité à la casse et recherche de mots entiers", "insertImage": "Insérer une image", "pickAPhotoFromYourGallery": "Choisissez une photo dans votre galerie", "takeAPhotoUsingYourCamera": "Prendre une photo avec votre appareil photo", "pasteAPhotoUsingALink": "Coller une photo à l'aide d'un lien", "pickAVideoFromYourGallery": "Choisissez une vidéo dans votre galerie", "recordAVideoUsingYourCamera": "Enregistrez une vidéo en utilisant votre caméra", "pasteAVideoUsingALink": "Coller une vidéo à l'aide d'un lien", "alignJustify": "Justifier le texte", "theImageHasBeenSavedAt": "L'image a été sauvegardée à : {imagePath}", "caseSensitive": "Re<PERSON>er la casse", "wholeWord": "<PERSON><PERSON> <PERSON>tier", "close": "<PERSON><PERSON><PERSON>", "searchSettings": "Paramètres de recherche", "cut": "Couper", "paste": "<PERSON><PERSON>", "insertTable": "Insérer un tableau"}