{"@@locale": "hu", "pasteLink": "<PERSON>", "ok": "Ok", "selectColor": "<PERSON><PERSON>ín kiválasztása", "gallery": "Galéria", "link": "Link", "open": "Megnyitás", "copy": "Másolás", "remove": "Eltávolítás", "save": "Men<PERSON>s", "zoom": "Nagyítás", "saved": "Mentve", "text": "Szöveg", "resize": "Átméretezés", "width": "Szélesség", "height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "small": "<PERSON><PERSON><PERSON>", "large": "<PERSON><PERSON>", "huge": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Törlés", "font": "Betűtípus", "search": "Keresés", "camera": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "redo": "Újra", "fontFamily": "Betűtípus", "fontSize": "Bet<PERSON><PERSON><PERSON>", "bold": "F<PERSON>lkövér", "subscript": "Alsó index", "superscript": "Felső index", "italic": "<PERSON><PERSON><PERSON>", "underline": "Aláhúzás", "strikeThrough": "Áthúzás", "inlineCode": "<PERSON><PERSON><PERSON>", "fontColor": "Betűszín", "backgroundColor": "Háttérsz<PERSON>", "clearFormat": "Formátum törl<PERSON>", "alignLeft": "Balra igazítás", "alignCenter": "Középre igazítás", "alignRight": "Jobbra igazítás", "alignJustify": "Sorkizárt", "@alignJustify": {"description": "A szöveg igazítása az ablak teljes szélességében"}, "justifyWinWidth": "Sorkizárt ablak szélesség", "textDirection": "Szöveg irány", "headerStyle": "<PERSON><PERSON><PERSON><PERSON> stílus", "normal": "<PERSON><PERSON><PERSON><PERSON>", "heading1": "Fejléc 1", "heading2": "Fejléc 2", "heading3": "Fejléc 3", "heading4": "Fejléc 4", "heading5": "Fejléc 5", "heading6": "Fejléc 6", "numberedList": "Számozott lista", "bulletList": "Felsorolás", "checkedList": "<PERSON><PERSON><PERSON><PERSON>a", "codeBlock": "<PERSON><PERSON><PERSON> b<PERSON>k", "quote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "increaseIndent": "Behúzás növelése", "decreaseIndent": "Behúzás csökkentése", "insertURL": "URL beszúrása", "visitLink": "<PERSON>", "enterLink": "<PERSON>", "enterMedia": "<PERSON><PERSON><PERSON>", "edit": "Szerkesztés", "apply": "Alkalmazás", "hex": "Hex", "material": "<PERSON><PERSON>", "color": "Szín", "lineheight": "<PERSON><PERSON>", "findText": "Szöveg keresése", "moveToPreviousOccurrence": "Ugrás az előző előfordulásra", "moveToNextOccurrence": "Ugrás a következő előfordulásra", "savedUsingTheNetwork": "Mentve h<PERSON><PERSON>", "savedUsingLocalStorage": "<PERSON>tve hely<PERSON> t<PERSON>", "theImageHasBeenSavedAt": "A kép elmentve: {imagePath}", "@theImageHasBeenSavedAt": {"description": "Üzenet egy paraméterrel", "placeholders": {"imagePath": {"type": "String", "example": "path/to/location"}}}, "errorWhileSavingImage": "Hiba a kép mentése közben", "pleaseEnterTextForYourLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, írja be a link szövegét (pl. „További információ”).", "pleaseEnterTheLinkURL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> be a link URL-t (pl. 'https://example.com')", "pleaseEnterAValidImageURL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes kép URL-t", "pleaseEnterAValidVideoURL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes videó URL-t", "photo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON>", "caseSensitivityAndWholeWordSearch": "Nagy- és kisbetűérzékenység és teljes szó keresés", "caseSensitive": "Nagy- és kisbetűérzékeny", "wholeWord": "<PERSON><PERSON><PERSON>", "insertImage": "<PERSON><PERSON><PERSON>", "pickAPhotoFromYourGallery": "Válasszon egy fényképet a galériájából", "takeAPhotoUsingYourCamera": "Készítsen egy fényképet a kamerájával", "pasteAPhotoUsingALink": "Illesszen be egy fényk<PERSON>pet egy linkkel", "pickAVideoFromYourGallery": "Válasszon egy videót a galériájából", "recordAVideoUsingYourCamera": "Vegyen fel egy videót a kamerájával", "pasteAVideoUsingALink": "Illesszen be egy videót egy linkkel", "close": "Bezárás", "searchSettings": "Keresési be<PERSON>", "cut": "Kivágás", "paste": "Beillesztés", "insertTable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}