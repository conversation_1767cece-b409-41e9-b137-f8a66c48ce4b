import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class FlutterQuillLocalizationsHi extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get pasteLink => 'लिंक पेस्ट करें';

  @override
  String get ok => 'ठीक है';

  @override
  String get selectColor => 'रंग चुनें';

  @override
  String get gallery => 'गैलरी';

  @override
  String get link => 'लिंक';

  @override
  String get open => 'खोलें';

  @override
  String get copy => 'कॉपी करें';

  @override
  String get remove => 'हटाएं';

  @override
  String get save => 'सुरक्षित करें';

  @override
  String get zoom => 'बड़ा करें';

  @override
  String get saved => 'सुरक्षित कर दिया गया है';

  @override
  String get text => 'शब्द';

  @override
  String get resize => 'आकार बदलें';

  @override
  String get width => 'चौड़ाई';

  @override
  String get height => 'ऊंचाई';

  @override
  String get size => 'Size';

  @override
  String get small => 'Small';

  @override
  String get large => 'Large';

  @override
  String get huge => 'Huge';

  @override
  String get clear => 'Clear';

  @override
  String get font => 'Font';

  @override
  String get search => 'Search';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Sूची का नाम';

  @override
  String get fontSize => 'फ़ॉन्ट का आकार';

  @override
  String get bold => 'ताक़तवर';

  @override
  String get subscript => 'अधोलेख';

  @override
  String get superscript => 'अद्भुतलेख';

  @override
  String get italic => 'तिरछा';

  @override
  String get underline => 'रेखांकन';

  @override
  String get strikeThrough => 'मार';

  @override
  String get inlineCode => 'लाइन कोड';

  @override
  String get fontColor => 'फॉन्ट का रंग';

  @override
  String get backgroundColor => 'पृष्ठभूमि का रंग';

  @override
  String get clearFormat => 'स्वच्छ स्वरूप';

  @override
  String get alignLeft => 'बाएं संरेखित करें';

  @override
  String get alignCenter => 'केंद्रित संरेखित करें';

  @override
  String get alignRight => 'दाएं संरेखित करें';

  @override
  String get alignJustify => 'सही संरेखित करें';

  @override
  String get justifyWinWidth => 'जस्टीफ़ी विन चौड़ाई';

  @override
  String get textDirection => 'टेक्स्ट की दिशा';

  @override
  String get headerStyle => 'हेडर शैली';

  @override
  String get normal => 'साधारण';

  @override
  String get heading1 => 'हेडिंग 1';

  @override
  String get heading2 => 'हेडिंग 2';

  @override
  String get heading3 => 'हेडिंग 3';

  @override
  String get heading4 => 'हेडिंग 4';

  @override
  String get heading5 => 'हेडिंग 5';

  @override
  String get heading6 => 'हेडिंग 6';

  @override
  String get numberedList => 'संख्याबद्ध सूची';

  @override
  String get bulletList => 'गोली दी गई सूची';

  @override
  String get checkedList => 'जाँची गई सूची';

  @override
  String get codeBlock => 'कोड ब्लॉक';

  @override
  String get quote => 'नोट';

  @override
  String get increaseIndent => 'इंडेंट बढ़ाएं';

  @override
  String get decreaseIndent => 'इंडेंट कम करें';

  @override
  String get insertURL => 'URL डालें';

  @override
  String get visitLink => 'लिंक देखें';

  @override
  String get enterLink => 'लिंक दर्ज करें';

  @override
  String get enterMedia => 'मीडिया दर्ज करें';

  @override
  String get edit => 'संपादित करें';

  @override
  String get apply => 'लागू करें';

  @override
  String get hex => 'हेक्स';

  @override
  String get material => 'सामग्री';

  @override
  String get color => 'रंग';

  @override
  String get lineheight => 'ऊंची लाईन';

  @override
  String get findText => 'मद को खोजें';

  @override
  String get moveToPreviousOccurrence => 'पिछले घटनांतर पर जाएं';

  @override
  String get moveToNextOccurrence => 'आगामी घटनांतर पर जाएं';

  @override
  String get savedUsingTheNetwork => 'नेटवर्क का उपयोग करके सहेजा गया';

  @override
  String get savedUsingLocalStorage =>
      'स्थानीय संग्रहण का उपयोग करके सहेजा गया';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'छवि को यहाँ संग्रहीत किया गया है: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'तस्वीर सहेजते समय त्रुटि';

  @override
  String get pleaseEnterTextForYourLink =>
      'कृपया अपने लिंक के लिए एक पाठ दर्ज करें (उदाहरण: \'और अधिक जानें\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'कृपया लिंक URL दर्ज करें (उदाहरण: \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'कृपया एक वैध चित्र URL दर्ज करें';

  @override
  String get pleaseEnterAValidVideoURL => 'कृपया एक वैध वीडियो URL दर्ज करें';

  @override
  String get photo => 'फोटो';

  @override
  String get image => 'छवि';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'केस सेंसिटिविटी और पूरे शब्द की खोज';

  @override
  String get caseSensitive => 'केस संवेदनशील';

  @override
  String get wholeWord => 'पूरा शब्द';

  @override
  String get insertImage => 'छवि डालें';

  @override
  String get pickAPhotoFromYourGallery => 'अपनी गैलरी से एक फोटो चुनें';

  @override
  String get takeAPhotoUsingYourCamera =>
      'अपनी कैमरा का उपयोग करके एक फोटो लें';

  @override
  String get pasteAPhotoUsingALink => 'लिंक का उपयोग करके एक फोटो पेस्ट करें';

  @override
  String get pickAVideoFromYourGallery => 'अपनी गैलरी से एक वीडियो चुनें';

  @override
  String get recordAVideoUsingYourCamera =>
      'अपनी कैमरा का उपयोग करके एक वीडियो रिकॉर्ड करें';

  @override
  String get pasteAVideoUsingALink => 'लिंक का उपयोग करके एक वीडियो पेस्ट करें';

  @override
  String get close => 'बंद करें';

  @override
  String get searchSettings => 'खोज सेटिंग्स';

  @override
  String get cut => 'काटें';

  @override
  String get paste => 'पेस्ट';

  @override
  String get insertTable => 'तालिका सम्मिलित करें';

  @override
  String get insertVideo => 'Insert video';
}
