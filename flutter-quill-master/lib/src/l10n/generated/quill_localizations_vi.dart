import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class FlutterQuillLocalizationsVi extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get pasteLink => 'Chèn liên kết';

  @override
  String get ok => 'Đồng ý';

  @override
  String get selectColor => 'Chọn Màu';

  @override
  String get gallery => 'Thư viện';

  @override
  String get link => 'Liên kết';

  @override
  String get open => 'Mở';

  @override
  String get copy => 'Sao chép';

  @override
  String get remove => 'Xoá';

  @override
  String get save => 'Lưu';

  @override
  String get zoom => 'Thu phóng';

  @override
  String get saved => 'Đã lưu';

  @override
  String get text => 'Chữ';

  @override
  String get resize => 'Resize';

  @override
  String get width => 'Rộng';

  @override
  String get height => 'Cao';

  @override
  String get size => '<PERSON>ích thước';

  @override
  String get small => 'Nhỏ';

  @override
  String get large => 'Lớn';

  @override
  String get huge => 'Rất lớn';

  @override
  String get clear => 'Xoá';

  @override
  String get font => 'Phông chữ';

  @override
  String get search => 'Tìm';

  @override
  String get camera => 'Máy ảnh';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Hoàn tác';

  @override
  String get redo => 'Làm lại';

  @override
  String get fontFamily => 'Phông chữ';

  @override
  String get fontSize => 'Cỡ chữ';

  @override
  String get bold => 'Đậm';

  @override
  String get subscript => 'Chèn dưới';

  @override
  String get superscript => 'Chèn trên';

  @override
  String get italic => 'Nghiêng';

  @override
  String get underline => 'Gạch chân';

  @override
  String get strikeThrough => 'Gạch ngang';

  @override
  String get inlineCode => 'Dòng mã';

  @override
  String get fontColor => 'Màu chữ';

  @override
  String get backgroundColor => 'Màu nền';

  @override
  String get clearFormat => 'Xoá định dạng';

  @override
  String get alignLeft => 'Căn trái';

  @override
  String get alignCenter => 'Căn giữa';

  @override
  String get alignRight => 'Căn phải';

  @override
  String get alignJustify => 'Căn chỉnh đều';

  @override
  String get justifyWinWidth => 'Căn đều chiều rộng';

  @override
  String get textDirection => 'Hướng văn bản';

  @override
  String get headerStyle => 'Kiểu tiêu đề';

  @override
  String get normal => 'Bình thường';

  @override
  String get heading1 => 'Tiêu đề 1';

  @override
  String get heading2 => 'Tiêu đề 2';

  @override
  String get heading3 => 'Tiêu đề 3';

  @override
  String get heading4 => 'Tiêu đề 4';

  @override
  String get heading5 => 'Tiêu đề 5';

  @override
  String get heading6 => 'Tiêu đề 6';

  @override
  String get numberedList => 'Danh sách có số';

  @override
  String get bulletList => 'Danh sách định dạng';

  @override
  String get checkedList => 'Danh sách kiểm tra';

  @override
  String get codeBlock => 'Khối mã';

  @override
  String get quote => 'Trích dẫn';

  @override
  String get increaseIndent => 'Tăng độ lề';

  @override
  String get decreaseIndent => 'Giảm độ lề';

  @override
  String get insertURL => 'Chèn URL';

  @override
  String get visitLink => 'Truy cập liên kết';

  @override
  String get enterLink => 'Nhập liên kết';

  @override
  String get enterMedia => 'Chèn phương tiện';

  @override
  String get edit => 'Chỉnh sửa';

  @override
  String get apply => 'Áp dụng';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Chất liệu';

  @override
  String get color => 'Màu';

  @override
  String get lineheight => 'Chiều cao giữa các dòng';

  @override
  String get findText => 'Tìm văn bản';

  @override
  String get moveToPreviousOccurrence => 'Di chuyển đến lần xuất hiện trước';

  @override
  String get moveToNextOccurrence => 'Di chuyển đến lần xuất hiện tiếp theo';

  @override
  String get savedUsingTheNetwork => 'Đã lưu bằng cách sử dụng mạng';

  @override
  String get savedUsingLocalStorage => 'Đã lưu sử dụng lưu trữ địa phương';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Hình ảnh đã được lưu tại: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Lỗi khi lưu hình ảnh';

  @override
  String get pleaseEnterTextForYourLink =>
      'Vui lòng nhập văn bản cho liên kết của bạn (ví dụ: \'Tìm hiểu thêm\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Vui lòng nhập URL của liên kết (ví dụ: \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Vui lòng nhập URL hình ảnh hợp lệ';

  @override
  String get pleaseEnterAValidVideoURL => 'Vui lòng nhập URL video hợp lệ';

  @override
  String get photo => 'Ảnh';

  @override
  String get image => 'Hình ảnh';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Độ nhạy cảm chữ hoa/chữ thường và tìm kiếm toàn bộ từ';

  @override
  String get caseSensitive => 'Nhạy cảm với chữ hoa/thường';

  @override
  String get wholeWord => 'Từ đầy đủ';

  @override
  String get insertImage => 'Chèn hình ảnh';

  @override
  String get pickAPhotoFromYourGallery => 'Chọn ảnh từ thư viện của bạn';

  @override
  String get takeAPhotoUsingYourCamera => 'Chụp ảnh bằng camera của bạn';

  @override
  String get pasteAPhotoUsingALink => 'Dán ảnh bằng liên kết';

  @override
  String get pickAVideoFromYourGallery => 'Chọn video từ thư viện của bạn';

  @override
  String get recordAVideoUsingYourCamera => 'Ghi video bằng camera của bạn';

  @override
  String get pasteAVideoUsingALink => 'Dán video bằng liên kết';

  @override
  String get close => 'Đóng';

  @override
  String get searchSettings => 'Cài đặt tìm kiếm';

  @override
  String get cut => 'Cắt';

  @override
  String get paste => 'Dán';

  @override
  String get insertTable => 'Chèn bảng';

  @override
  String get insertVideo => 'Insert video';
}
