import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Malay (`ms`).
class FlutterQuillLocalizationsMs extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsMs([String locale = 'ms']) : super(locale);

  @override
  String get pasteLink => 'Tampal Pautan';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Pilih Warna';

  @override
  String get gallery => 'Galeri';

  @override
  String get link => 'Pautan';

  @override
  String get open => 'Buka';

  @override
  String get copy => 'Salin';

  @override
  String get remove => 'Buang';

  @override
  String get save => 'Simpan';

  @override
  String get zoom => 'Zum';

  @override
  String get saved => 'Telah Disimpan';

  @override
  String get text => 'Perkataan';

  @override
  String get resize => 'Ubah saiz';

  @override
  String get width => 'Lebar';

  @override
  String get height => 'Tinggi';

  @override
  String get size => 'Saiz';

  @override
  String get small => 'Kecil';

  @override
  String get large => 'Besar';

  @override
  String get huge => 'Amat Besar';

  @override
  String get clear => 'Padam';

  @override
  String get font => 'Fon';

  @override
  String get search => 'Carian';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Font family';

  @override
  String get fontSize => 'Font size';

  @override
  String get bold => 'Bold';

  @override
  String get subscript => 'Subscript';

  @override
  String get superscript => 'Superscript';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get strikeThrough => 'Strike through';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Font color';

  @override
  String get backgroundColor => 'Background color';

  @override
  String get clearFormat => 'Clear format';

  @override
  String get alignLeft => 'Align left';

  @override
  String get alignCenter => 'Align center';

  @override
  String get alignRight => 'Align right';

  @override
  String get alignJustify => 'Rata kiri kanan';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Text direction';

  @override
  String get headerStyle => 'Header style';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Tajuk 1';

  @override
  String get heading2 => 'Tajuk 2';

  @override
  String get heading3 => 'Tajuk 3';

  @override
  String get heading4 => 'Tajuk 4';

  @override
  String get heading5 => 'Tajuk 5';

  @override
  String get heading6 => 'Tajuk 6';

  @override
  String get numberedList => 'Numbered list';

  @override
  String get bulletList => 'Bullet list';

  @override
  String get checkedList => 'Checked list';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Quote';

  @override
  String get increaseIndent => 'Increase indent';

  @override
  String get decreaseIndent => 'Decrease indent';

  @override
  String get insertURL => 'Insert URL';

  @override
  String get visitLink => 'Visit link';

  @override
  String get enterLink => 'Enter link';

  @override
  String get enterMedia => 'Enter media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Apply';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Bahan';

  @override
  String get color => 'Warna';

  @override
  String get lineheight => 'Ketinggian garisan';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Disimpan menggunakan rangkaian';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Imej telah disimpan di: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink =>
      'Sila masukkan teks untuk pautan anda (contoh, \'Ketahui lebih lanjut\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Sila masukkan URL pautan (contoh, \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Sila masukkan URL imej yang sah';

  @override
  String get pleaseEnterAValidVideoURL => 'Sila masukkan URL video yang sah';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Imej';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensitiviti huruf besar dan kecil dan carian penuh perkataan';

  @override
  String get caseSensitive => 'Sensitif huruf besar-kecil';

  @override
  String get wholeWord => 'Kata keseluruhan';

  @override
  String get insertImage => 'Masukkan imej';

  @override
  String get pickAPhotoFromYourGallery => 'Pilih foto dari galeri anda';

  @override
  String get takeAPhotoUsingYourCamera => 'Ambil foto menggunakan kamera anda';

  @override
  String get pasteAPhotoUsingALink => 'Tampal foto menggunakan pautan';

  @override
  String get pickAVideoFromYourGallery => 'Pilih video dari galeri anda';

  @override
  String get recordAVideoUsingYourCamera =>
      'Rakaman video menggunakan kamera anda';

  @override
  String get pasteAVideoUsingALink => 'Tampal video menggunakan pautan';

  @override
  String get close => 'Tutup';

  @override
  String get searchSettings => 'Tetapan carian';

  @override
  String get cut => 'Potong';

  @override
  String get paste => 'Tampal';

  @override
  String get insertTable => 'Masukkan jadual';

  @override
  String get insertVideo => 'Insert video';
}
