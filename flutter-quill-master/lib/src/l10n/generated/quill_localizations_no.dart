import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Norwegian (`no`).
class FlutterQuillLocalizationsNo extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsNo([String locale = 'no']) : super(locale);

  @override
  String get pasteLink => 'Lim inn lenke';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Velg farge';

  @override
  String get gallery => 'Galleri';

  @override
  String get link => 'Lenke';

  @override
  String get open => 'Åpne';

  @override
  String get copy => 'Kopier';

  @override
  String get remove => 'Fjern';

  @override
  String get save => 'Lagre';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Lagret';

  @override
  String get text => 'Tekst';

  @override
  String get resize => 'Endre størrelse';

  @override
  String get width => 'Bredde';

  @override
  String get height => 'Høyde';

  @override
  String get size => 'Størrelse';

  @override
  String get small => 'Liten';

  @override
  String get large => 'Stor';

  @override
  String get huge => 'Enorm';

  @override
  String get clear => 'Fjern';

  @override
  String get font => 'Skrifttype';

  @override
  String get search => 'Søk';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Angre';

  @override
  String get redo => 'Gjør om';

  @override
  String get fontFamily => 'Skriftfamilie';

  @override
  String get fontSize => 'Skriftstørrelse';

  @override
  String get bold => 'Fet';

  @override
  String get subscript => 'Senket skrift';

  @override
  String get superscript => 'Hevet skrift';

  @override
  String get italic => 'Kursiv';

  @override
  String get underline => 'Understreket';

  @override
  String get strikeThrough => 'Gjennomstreking';

  @override
  String get inlineCode => 'In-line kode';

  @override
  String get fontColor => 'Skriftfarge';

  @override
  String get backgroundColor => 'Bakgrunnsfarge';

  @override
  String get clearFormat => 'Fjern formatering';

  @override
  String get alignLeft => 'Venstrejuster';

  @override
  String get alignCenter => 'Sentrer';

  @override
  String get alignRight => 'Høyrejuster';

  @override
  String get alignJustify => 'Juster tekst';

  @override
  String get justifyWinWidth => 'Rettferdiggjør bredden';

  @override
  String get textDirection => 'Tekstretning';

  @override
  String get headerStyle => 'Overskriftsstil';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Overskrift 1';

  @override
  String get heading2 => 'Overskrift 2';

  @override
  String get heading3 => 'Overskrift 3';

  @override
  String get heading4 => 'Overskrift 4';

  @override
  String get heading5 => 'Overskrift 5';

  @override
  String get heading6 => 'Overskrift 6';

  @override
  String get numberedList => 'Nummerert liste';

  @override
  String get bulletList => 'Punktliste';

  @override
  String get checkedList => 'Avkrysset liste';

  @override
  String get codeBlock => 'Kodeblokk';

  @override
  String get quote => 'Sitert tekst';

  @override
  String get increaseIndent => 'Øk innrykk';

  @override
  String get decreaseIndent => 'Mink innrykk';

  @override
  String get insertURL => 'Sett inn URL';

  @override
  String get visitLink => 'Besøk lenken';

  @override
  String get enterLink => 'Skriv inn lenken';

  @override
  String get enterMedia => 'Sett inn media';

  @override
  String get edit => 'Rediger';

  @override
  String get apply => 'Bruk';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Materiale';

  @override
  String get color => 'Farge';

  @override
  String get lineheight => 'Linjehøyde';

  @override
  String get findText => 'Finn tekst';

  @override
  String get moveToPreviousOccurrence => 'Gå til forrige forekomst';

  @override
  String get moveToNextOccurrence => 'Gå til neste forekomst';

  @override
  String get savedUsingTheNetwork => 'Lagret ved hjelp av nettverket';

  @override
  String get savedUsingLocalStorage => 'Lagret ved hjelp av lokal lagring';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Bildet er lagret på: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Feil ved lagring av bilde';

  @override
  String get pleaseEnterTextForYourLink =>
      'Vennligst skriv inn tekst for lenken din (for eksempel \'Lær mer\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Vennligst skriv inn lenkens URL (for eksempel \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Vennligst skriv inn en gyldig bilde-URL';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Vennligst skriv inn en gyldig video-URL';

  @override
  String get photo => 'Bilde';

  @override
  String get image => 'Bilde';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Stor/liten bokstavfølsomhet og helordsøk';

  @override
  String get caseSensitive => 'Sakens følsomhet';

  @override
  String get wholeWord => 'Hele ord';

  @override
  String get insertImage => 'Sett inn bilde';

  @override
  String get pickAPhotoFromYourGallery => 'Velg et bilde fra galleriet ditt';

  @override
  String get takeAPhotoUsingYourCamera => 'Ta et bilde med kameraet ditt';

  @override
  String get pasteAPhotoUsingALink => 'Lim inn et bilde med en lenke';

  @override
  String get pickAVideoFromYourGallery => 'Velg en video fra galleriet ditt';

  @override
  String get recordAVideoUsingYourCamera => 'Ta opp en video med kameraet ditt';

  @override
  String get pasteAVideoUsingALink => 'Lim inn en video med en lenke';

  @override
  String get close => 'Lukk';

  @override
  String get searchSettings => 'Søkinnstillinger';

  @override
  String get cut => 'Klipp';

  @override
  String get paste => 'Lim inn';

  @override
  String get insertTable => 'Sett inn tabell';

  @override
  String get insertVideo => 'Insert video';
}
