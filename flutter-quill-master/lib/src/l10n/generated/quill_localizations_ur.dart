import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Urdu (`ur`).
class FlutterQuillLocalizationsUr extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsUr([String locale = 'ur']) : super(locale);

  @override
  String get pasteLink => 'لنک پیسٹ کریں';

  @override
  String get ok => 'ٹھیک ہے';

  @override
  String get selectColor => 'رنگ منتخب کریں';

  @override
  String get gallery => 'گیلری';

  @override
  String get link => 'لنک';

  @override
  String get open => 'کھولیں';

  @override
  String get copy => 'نقل';

  @override
  String get remove => 'ہٹا دیں';

  @override
  String get save => 'محفوظ کریں';

  @override
  String get zoom => 'زوم';

  @override
  String get saved => 'محفوظ کر لیا';

  @override
  String get text => 'متن';

  @override
  String get resize => 'سائز تبدیل کریں۔';

  @override
  String get width => 'چوڑائی';

  @override
  String get height => 'اونچائی';

  @override
  String get size => 'سائز';

  @override
  String get small => 'چھوٹا';

  @override
  String get large => 'بڑا';

  @override
  String get huge => 'بہت بڑا';

  @override
  String get clear => 'صاف';

  @override
  String get font => 'فونٹ';

  @override
  String get search => 'تلاش';

  @override
  String get camera => 'کیمرا';

  @override
  String get video => 'ویڈیو';

  @override
  String get undo => 'واپس';

  @override
  String get redo => 'دوبارہ';

  @override
  String get fontFamily => 'فونٹ خاندان';

  @override
  String get fontSize => 'فونٹ سائز';

  @override
  String get bold => 'ڈہوکی';

  @override
  String get subscript => 'نیچے لکھا';

  @override
  String get superscript => 'اوپر لکھا';

  @override
  String get italic => 'ٹیک کیا';

  @override
  String get underline => 'نیچے خط';

  @override
  String get strikeThrough => 'خط خوراک';

  @override
  String get inlineCode => 'ان لائن کوڈ';

  @override
  String get fontColor => 'فونٹ کا رنگ';

  @override
  String get backgroundColor => 'پس منظر کا رنگ';

  @override
  String get clearFormat => 'فارمیٹ صاف کریں';

  @override
  String get alignLeft => 'بائیں ہم آہنگ ہوں';

  @override
  String get alignCenter => 'مرکز میں ہم آہنگ ہوں';

  @override
  String get alignRight => 'دائیں ہم آہنگ ہوں';

  @override
  String get alignJustify => 'متن کو جڑائیں';

  @override
  String get justifyWinWidth => 'جسٹیفائی ون چوڑائی';

  @override
  String get textDirection => 'متن کی سمت';

  @override
  String get headerStyle => 'ہیڈر کا انداز';

  @override
  String get normal => 'معمول';

  @override
  String get heading1 => 'سرخی 1';

  @override
  String get heading2 => 'سرخی 2';

  @override
  String get heading3 => 'سرخی 3';

  @override
  String get heading4 => 'سرخی 4';

  @override
  String get heading5 => 'سرخی 5';

  @override
  String get heading6 => 'سرخی 6';

  @override
  String get numberedList => 'مرقم فہرست';

  @override
  String get bulletList => 'گولی فہرست';

  @override
  String get checkedList => 'چیک کی گئی فہرست';

  @override
  String get codeBlock => 'کوڈ بلاک';

  @override
  String get quote => 'حوالہ';

  @override
  String get increaseIndent => 'درجہ بڑھائیں';

  @override
  String get decreaseIndent => 'درجہ گھٹائیں';

  @override
  String get insertURL => 'یو آر ایل درج کریں';

  @override
  String get visitLink => 'لنک دیکھیں';

  @override
  String get enterLink => 'لنک درج کریں';

  @override
  String get enterMedia => 'میڈیا درج کریں';

  @override
  String get edit => 'ترتیب دیں';

  @override
  String get apply => 'لگائیں';

  @override
  String get hex => 'ہیکس';

  @override
  String get material => 'مواد';

  @override
  String get color => 'رنگ';

  @override
  String get lineheight => 'لکیر کی اونچائی';

  @override
  String get findText => 'متن تلاش کریں';

  @override
  String get moveToPreviousOccurrence => 'پچھلے واقعہ پر منتقل ہوں';

  @override
  String get moveToNextOccurrence => 'اگلے واقعہ پر منتقل ہوں';

  @override
  String get savedUsingTheNetwork => 'نیٹ ورک کا استعمال کر کے محفوظ ہوا';

  @override
  String get savedUsingLocalStorage =>
      'مقامی ذخیرہ کار استعمال کر کے محفوظ ہوا';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'تصویر یہاں محفوظ کی گئی ہے: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'تصویر کو محفوظ کرتے وقت خطا';

  @override
  String get pleaseEnterTextForYourLink =>
      'براہ کرم اپنے لنک کے لیے متن درج کریں (مثال کے طور پر، \'مزید جانیں\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'براہ کرم لنک کا URL درج کریں (مثال کے طور پر، \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'براہ کرم ایک درست تصویر URL درج کریں';

  @override
  String get pleaseEnterAValidVideoURL =>
      'براہ کرم ایک درست ویڈیو URL درج کریں';

  @override
  String get photo => 'تصویر';

  @override
  String get image => 'تصویر';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'معاملے کی حساسیت اور پورے الفاظ کی تلاش';

  @override
  String get caseSensitive => 'بڑی اور چھوٹی حروف کے لئے حساس';

  @override
  String get wholeWord => 'پورا لفظ';

  @override
  String get insertImage => 'تصویر داخل کریں';

  @override
  String get pickAPhotoFromYourGallery => 'اپنی گیلری سے تصویر منتخب کریں';

  @override
  String get takeAPhotoUsingYourCamera => 'اپنی کیمرہ استعمال کر کے تصویر لیں';

  @override
  String get pasteAPhotoUsingALink => 'لنک استعمال کر کے تصویر چسپاں کریں';

  @override
  String get pickAVideoFromYourGallery => 'اپنی گیلری سے ویڈیو منتخب کریں';

  @override
  String get recordAVideoUsingYourCamera =>
      'اپنی کیمرہ استعمال کر کے ویڈیو ریکارڈ کریں';

  @override
  String get pasteAVideoUsingALink => 'لنک استعمال کر کے ویڈیو چسپاں کریں';

  @override
  String get close => 'بند کریں';

  @override
  String get searchSettings => 'تلاش کی ترتیبات';

  @override
  String get cut => 'کٹائیں';

  @override
  String get paste => 'چسپاں کریں';

  @override
  String get insertTable => 'ٹیبل درج کریں';

  @override
  String get insertVideo => 'Insert video';
}
