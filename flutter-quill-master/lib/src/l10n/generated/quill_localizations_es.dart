import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class FlutterQuillLocalizationsEs extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get pasteLink => 'Pega un enlace';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Selecciona un color';

  @override
  String get gallery => 'Galería';

  @override
  String get link => 'Enlace';

  @override
  String get open => 'Abrir';

  @override
  String get copy => 'Copiar';

  @override
  String get remove => 'Eliminar';

  @override
  String get save => 'Guardar';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Guardado';

  @override
  String get text => 'Texto';

  @override
  String get resize => 'Redimensionar';

  @override
  String get width => 'Ancho';

  @override
  String get height => 'Alto';

  @override
  String get size => 'Tamaño';

  @override
  String get small => 'Pequeño';

  @override
  String get large => 'Grande';

  @override
  String get huge => 'Muy grande';

  @override
  String get clear => 'Borrar';

  @override
  String get font => 'Fuente';

  @override
  String get search => 'Buscar';

  @override
  String get camera => 'Cámara';

  @override
  String get video => 'Vídeo';

  @override
  String get undo => 'Deshacer';

  @override
  String get redo => 'Rehacer';

  @override
  String get fontFamily => 'Familia de fuentes';

  @override
  String get fontSize => 'Tamaño de fuente';

  @override
  String get bold => 'Negrita';

  @override
  String get subscript => 'Subíndice';

  @override
  String get superscript => 'Superíndice';

  @override
  String get italic => 'Cursiva';

  @override
  String get underline => 'Subrayado';

  @override
  String get strikeThrough => 'Tachado';

  @override
  String get inlineCode => 'Código en línea';

  @override
  String get fontColor => 'Color de fuente';

  @override
  String get backgroundColor => 'Color de fondo';

  @override
  String get clearFormat => 'Quitar formato';

  @override
  String get alignLeft => 'Alinear a la izquierda';

  @override
  String get alignCenter => 'Centrar';

  @override
  String get alignRight => 'Alinear a la derecha';

  @override
  String get alignJustify => 'Justificar texto';

  @override
  String get justifyWinWidth => 'Justificar';

  @override
  String get textDirection => 'Dirección del texto';

  @override
  String get headerStyle => 'Estilo de encabezado';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Encabezado 1';

  @override
  String get heading2 => 'Encabezado 2';

  @override
  String get heading3 => 'Encabezado 3';

  @override
  String get heading4 => 'Encabezado 4';

  @override
  String get heading5 => 'Encabezado 5';

  @override
  String get heading6 => 'Encabezado 6';

  @override
  String get numberedList => 'Lista numerada';

  @override
  String get bulletList => 'Lista con viñetas';

  @override
  String get checkedList => 'Lista de comprobación';

  @override
  String get codeBlock => 'Bloque de código';

  @override
  String get quote => 'Cita';

  @override
  String get increaseIndent => 'Aumentar sangría';

  @override
  String get decreaseIndent => 'Disminuir sangría';

  @override
  String get insertURL => 'Insertar URL';

  @override
  String get visitLink => 'Visitar enlace';

  @override
  String get enterLink => 'Introducir enlace';

  @override
  String get enterMedia => 'Insertar medio';

  @override
  String get edit => 'Editar';

  @override
  String get apply => 'Aplicar';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Color';

  @override
  String get lineheight => 'Interlineado';

  @override
  String get findText => 'Buscar texto';

  @override
  String get moveToPreviousOccurrence => 'Ir a la ocurrencia anterior';

  @override
  String get moveToNextOccurrence => 'Ir a la siguiente ocurrencia';

  @override
  String get savedUsingTheNetwork => 'Guardado usando la red';

  @override
  String get savedUsingLocalStorage => 'Guardado usando almacenamiento local';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'La imagen se ha guardado en: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error al guardar imagen';

  @override
  String get pleaseEnterTextForYourLink => 'p.ej., \'Aprende más\'';

  @override
  String get pleaseEnterTheLinkURL => 'p.ej., \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL =>
      'Por favor, introduzca una URL de imagen válida';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Por favor, ingrese una URL de video válida';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Imagen';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilidad a mayúsculas y búsqueda de palabras completas';

  @override
  String get caseSensitive => 'Distinción entre mayúsculas y minúsculas';

  @override
  String get wholeWord => 'Palabra completa';

  @override
  String get insertImage => 'Insertar imagen';

  @override
  String get pickAPhotoFromYourGallery => 'Elige una foto de tu galería';

  @override
  String get takeAPhotoUsingYourCamera => 'Toma una foto con tu cámara';

  @override
  String get pasteAPhotoUsingALink => 'Pega una foto usando un enlace';

  @override
  String get pickAVideoFromYourGallery => 'Elige un video de tu galería';

  @override
  String get recordAVideoUsingYourCamera => 'Graba un video con tu cámara';

  @override
  String get pasteAVideoUsingALink => 'Pegar un video usando un enlace';

  @override
  String get close => 'Cerrar';

  @override
  String get searchSettings => 'Configuración de búsqueda';

  @override
  String get cut => 'Cortar';

  @override
  String get paste => 'Pegar';

  @override
  String get insertTable => 'Insertar tabla';

  @override
  String get insertVideo => 'Insert video';
}
