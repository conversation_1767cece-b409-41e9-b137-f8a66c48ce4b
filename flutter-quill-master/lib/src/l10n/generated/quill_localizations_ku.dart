import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Kurdish (`ku`).
class FlutterQuillLocalizationsKu extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsKu([String locale = 'ku']) : super(locale);

  @override
  String get pasteLink => 'لینک دابنێ';

  @override
  String get ok => 'باشە';

  @override
  String get selectColor => 'ڕەنگێک هەڵبژێرە';

  @override
  String get gallery => 'گالەری';

  @override
  String get link => 'لینک';

  @override
  String get open => 'بیکەرەوە';

  @override
  String get copy => 'لەبەرگرتنەوە';

  @override
  String get remove => 'بیسڕەوە';

  @override
  String get save => 'پاشەکەوتی بکە';

  @override
  String get zoom => 'هێنانە پێشەوە';

  @override
  String get saved => 'پاشەکەوت کرا';

  @override
  String get text => 'نوسین';

  @override
  String get resize => 'گۆڕینی قەبارە';

  @override
  String get width => 'پانی';

  @override
  String get height => 'بەرزی';

  @override
  String get size => 'قەبارە';

  @override
  String get small => 'بچووک';

  @override
  String get large => 'گەورە';

  @override
  String get huge => 'زۆر گەورەز';

  @override
  String get clear => 'پاککردنەوە';

  @override
  String get font => 'فۆنت';

  @override
  String get search => 'گەڕان';

  @override
  String get camera => 'کامێرا';

  @override
  String get video => 'ڤیدیۆ';

  @override
  String get undo => 'پاشگەزبوونەوە';

  @override
  String get redo => 'دووبارەکردنەوە';

  @override
  String get fontFamily => 'خێزانی فۆنت';

  @override
  String get fontSize => 'قەبارەی نوسین';

  @override
  String get bold => 'ڕەش';

  @override
  String get subscript => 'ژێر نووسین';

  @override
  String get superscript => 'سەر نووسین';

  @override
  String get italic => 'لارکردنەوە';

  @override
  String get underline => 'ژێرهێڵ';

  @override
  String get strikeThrough => 'هێڵ بەسەرداهێنان';

  @override
  String get inlineCode => 'کۆدی ناو هێڵ';

  @override
  String get fontColor => 'ڕەنگی فۆنت';

  @override
  String get backgroundColor => 'رەنگی پشتەوە';

  @override
  String get clearFormat => 'پاککردنەوەی کاریگەرییەکان';

  @override
  String get alignLeft => 'بۆ چەپ';

  @override
  String get alignCenter => 'بۆ ناوەڕاست';

  @override
  String get alignRight => 'بۆ ڕاست';

  @override
  String get alignJustify => 'جوانکردن';

  @override
  String get justifyWinWidth => 'پانی ڕێکبخە';

  @override
  String get textDirection => 'ئاڕاستەی نووسین';

  @override
  String get headerStyle => 'شێوەی سەر';

  @override
  String get normal => 'ئاسایی';

  @override
  String get heading1 => 'سەری ١';

  @override
  String get heading2 => 'سەری ٢';

  @override
  String get heading3 => 'سەری ٣';

  @override
  String get heading4 => 'سەری ٤';

  @override
  String get heading5 => 'سەری ٥';

  @override
  String get heading6 => 'سەری ٦';

  @override
  String get numberedList => 'لیست بە ژمارە';

  @override
  String get bulletList => 'لیست بە خاڵ';

  @override
  String get checkedList => 'لیستی دیاریکردن';

  @override
  String get codeBlock => 'کۆد بلۆک';

  @override
  String get quote => 'دەق';

  @override
  String get increaseIndent => 'زیادکردنی نێوەند';

  @override
  String get decreaseIndent => 'کەمکردنی نێوەند';

  @override
  String get insertURL => 'دانانی لینک`';

  @override
  String get visitLink => 'سەردانی لینک بکە';

  @override
  String get enterLink => 'دانانی لینک';

  @override
  String get enterMedia => 'دانانی میدیا';

  @override
  String get edit => 'دەستکاری';

  @override
  String get apply => 'سەپاندن';

  @override
  String get hex => 'هێکس';

  @override
  String get material => 'بابەت';

  @override
  String get color => 'ڕەنگ';

  @override
  String get lineheight => 'بەرزی هێڵ';

  @override
  String get findText => 'دۆزینەوەی نوسین';

  @override
  String get moveToPreviousOccurrence => 'بچۆ بۆ ڕووداوی پێشوو';

  @override
  String get moveToNextOccurrence => 'بڕۆ بۆ ڕووداوی داهاتوو';

  @override
  String get savedUsingTheNetwork => 'لە ڕێی تۆڕەوە پاشەکەوتکرا';

  @override
  String get savedUsingLocalStorage => 'لە ڕێی دیسکی ناوخۆییەوە پاشەکەوتکرا';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'وێنەکە پاشەکەوت کرایە نێو';
  }

  @override
  String get errorWhileSavingImage => 'هەڵە ڕویدا لە کاتی پاشەکەوتکردنی وێنەدا';

  @override
  String get pleaseEnterTextForYourLink =>
      'تکایە دەقێک بۆ بەستەرەکەت دابنێ (بۆ نموونە، \'زیاتر بزانە\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'تکایە لینکێک بۆ بەستەرەکە دابنێ (بۆ نموونە، \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'تکایە لینکی وێنەی دروست دابنێ';

  @override
  String get pleaseEnterAValidVideoURL => 'تکایە لینکی ڤیدیۆیی دروست دابنێ';

  @override
  String get photo => 'وێنە';

  @override
  String get image => 'وێنە';

  @override
  String get caseSensitivityAndWholeWordSearch => 'گەڕان بۆ تەواوی وشە';

  @override
  String get caseSensitive => 'هسەتیاری پیتی گەورە و بچووک';

  @override
  String get wholeWord => 'گشت ووشەکان';

  @override
  String get insertImage => 'دانانی وێنە';

  @override
  String get pickAPhotoFromYourGallery => 'وێنەیەک لە گەلەرییەکەت هەڵبژێرە';

  @override
  String get takeAPhotoUsingYourCamera =>
      'بە بەکارهێنانی کامێراکەت وێنەیەک بگرە';

  @override
  String get pasteAPhotoUsingALink => 'لینکی وێنەیەک دابنێ';

  @override
  String get pickAVideoFromYourGallery => 'ڤیدیۆیەک لە گەلەرییەکەتەوە هەڵبژێرە';

  @override
  String get recordAVideoUsingYourCamera =>
      'بە بەکارهێنانی کامێراکەت ڤیدیۆیەک تۆمار بکە';

  @override
  String get pasteAVideoUsingALink => 'لینکی ڤیدیۆیەک دابنێ';

  @override
  String get close => 'دایخە';

  @override
  String get searchSettings => 'بە ناو ئامادەکارییەکاندا بگەڕێ';

  @override
  String get cut => 'بڕین';

  @override
  String get paste => 'دانان';

  @override
  String get insertTable => 'دانانی خشتە';

  @override
  String get insertVideo => 'Insert video';
}

/// The translations for Kurdish (`ku_CKB`).
class FlutterQuillLocalizationsKuCkb extends FlutterQuillLocalizationsKu {
  FlutterQuillLocalizationsKuCkb() : super('ku_CKB');

  @override
  String get pasteLink => 'لینک دابنێ';

  @override
  String get ok => 'باشە';

  @override
  String get selectColor => 'ڕەنگێک هەڵبژێرە';

  @override
  String get gallery => 'گالەری';

  @override
  String get link => 'لینک';

  @override
  String get open => 'بیکەرەوە';

  @override
  String get copy => 'لەبەرگرتنەوە';

  @override
  String get remove => 'بیسڕەوە';

  @override
  String get save => 'پاشەکەوتی بکە';

  @override
  String get zoom => 'هێنانە پێشەوە';

  @override
  String get saved => 'پاشەکەوت کرا';

  @override
  String get text => 'نوسین';

  @override
  String get resize => 'گۆڕینی قەبارە';

  @override
  String get width => 'پانی';

  @override
  String get height => 'بەرزی';

  @override
  String get size => 'قەبارە';

  @override
  String get small => 'بچووک';

  @override
  String get large => 'گەورە';

  @override
  String get huge => 'زۆر گەورەز';

  @override
  String get clear => 'پاککردنەوە';

  @override
  String get font => 'فۆنت';

  @override
  String get search => 'گەڕان';

  @override
  String get camera => 'کامێرا';

  @override
  String get video => 'ڤیدیۆ';

  @override
  String get undo => 'پاشگەزبوونەوە';

  @override
  String get redo => 'دووبارەکردنەوە';

  @override
  String get fontFamily => 'خێزانی فۆنت';

  @override
  String get fontSize => 'قەبارەی نوسین';

  @override
  String get bold => 'ڕەش';

  @override
  String get subscript => 'ژێر نووسین';

  @override
  String get superscript => 'سەر نووسین';

  @override
  String get italic => 'لارکردنەوە';

  @override
  String get underline => 'ژێرهێڵ';

  @override
  String get strikeThrough => 'هێڵ بەسەرداهێنان';

  @override
  String get inlineCode => 'کۆدی ناو هێڵ';

  @override
  String get fontColor => 'ڕەنگی فۆنت';

  @override
  String get backgroundColor => 'رەنگی پشتەوە';

  @override
  String get clearFormat => 'پاککردنەوەی کاریگەرییەکان';

  @override
  String get alignLeft => 'بۆ چەپ';

  @override
  String get alignCenter => 'بۆ ناوەڕاست';

  @override
  String get alignRight => 'بۆ ڕاست';

  @override
  String get alignJustify => 'جوانکردن';

  @override
  String get justifyWinWidth => 'پانی ڕێکبخە';

  @override
  String get textDirection => 'ئاڕاستەی نووسین';

  @override
  String get headerStyle => 'شێوەی سەر';

  @override
  String get normal => 'ئاسایی';

  @override
  String get heading1 => 'سەری ١';

  @override
  String get heading2 => 'سەری ٢';

  @override
  String get heading3 => 'سەری ٣';

  @override
  String get heading4 => 'سەری ٤';

  @override
  String get heading5 => 'سەری ٥';

  @override
  String get heading6 => 'سەری ٦';

  @override
  String get numberedList => 'لیست بە ژمارە';

  @override
  String get bulletList => 'لیست بە خاڵ';

  @override
  String get checkedList => 'لیستی دیاریکردن';

  @override
  String get codeBlock => 'کۆد بلۆک';

  @override
  String get quote => 'دەق';

  @override
  String get increaseIndent => 'زیادکردنی نێوەند';

  @override
  String get decreaseIndent => 'کەمکردنی نێوەند';

  @override
  String get insertURL => 'دانانی لینک`';

  @override
  String get visitLink => 'سەردانی لینک بکە';

  @override
  String get enterLink => 'دانانی لینک';

  @override
  String get enterMedia => 'دانانی میدیا';

  @override
  String get edit => 'دەستکاری';

  @override
  String get apply => 'سەپاندن';

  @override
  String get hex => 'هێکس';

  @override
  String get material => 'بابەت';

  @override
  String get color => 'ڕەنگ';

  @override
  String get lineheight => 'بەرزی هێڵ';

  @override
  String get findText => 'دۆزینەوەی نوسین';

  @override
  String get moveToPreviousOccurrence => 'بچۆ بۆ ڕووداوی پێشوو';

  @override
  String get moveToNextOccurrence => 'بڕۆ بۆ ڕووداوی داهاتوو';

  @override
  String get savedUsingTheNetwork => 'لە ڕێی تۆڕەوە پاشەکەوتکرا';

  @override
  String get savedUsingLocalStorage => 'لە ڕێی دیسکی ناوخۆییەوە پاشەکەوتکرا';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'وێنەکە پاشەکەوت کرایە نێو';
  }

  @override
  String get errorWhileSavingImage => 'هەڵە ڕویدا لە کاتی پاشەکەوتکردنی وێنەدا';

  @override
  String get pleaseEnterTextForYourLink =>
      'تکایە دەقێک بۆ بەستەرەکەت دابنێ (بۆ نموونە، \'زیاتر بزانە\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'تکایە لینکێک بۆ بەستەرەکە دابنێ (بۆ نموونە، \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'تکایە لینکی وێنەی دروست دابنێ';

  @override
  String get pleaseEnterAValidVideoURL => 'تکایە لینکی ڤیدیۆیی دروست دابنێ';

  @override
  String get photo => 'وێنە';

  @override
  String get image => 'وێنە';

  @override
  String get caseSensitivityAndWholeWordSearch => 'گەڕان بۆ تەواوی وشە';

  @override
  String get caseSensitive => 'هسەتیاری پیتی گەورە و بچووک';

  @override
  String get wholeWord => 'گشت ووشەکان';

  @override
  String get insertImage => 'دانانی وێنە';

  @override
  String get pickAPhotoFromYourGallery => 'وێنەیەک لە گەلەرییەکەت هەڵبژێرە';

  @override
  String get takeAPhotoUsingYourCamera =>
      'بە بەکارهێنانی کامێراکەت وێنەیەک بگرە';

  @override
  String get pasteAPhotoUsingALink => 'لینکی وێنەیەک دابنێ';

  @override
  String get pickAVideoFromYourGallery => 'ڤیدیۆیەک لە گەلەرییەکەتەوە هەڵبژێرە';

  @override
  String get recordAVideoUsingYourCamera =>
      'بە بەکارهێنانی کامێراکەت ڤیدیۆیەک تۆمار بکە';

  @override
  String get pasteAVideoUsingALink => 'لینکی ڤیدیۆیەک دابنێ';

  @override
  String get close => 'دایخە';

  @override
  String get searchSettings => 'بە ناو ئامادەکارییەکاندا بگەڕێ';

  @override
  String get cut => 'بڕین';

  @override
  String get paste => 'دانان';

  @override
  String get insertTable => 'دانانی خشتە';
}
