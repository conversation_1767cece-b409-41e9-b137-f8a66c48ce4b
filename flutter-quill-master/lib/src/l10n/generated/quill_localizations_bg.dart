import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bulgarian (`bg`).
class FlutterQuillLocalizationsBg extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsBg([String locale = 'bg']) : super(locale);

  @override
  String get pasteLink => 'Поставете връзка';

  @override
  String get ok => 'Да';

  @override
  String get selectColor => 'Изберете цвят';

  @override
  String get gallery => 'Галерия';

  @override
  String get link => 'Връзка';

  @override
  String get open => 'Отвори';

  @override
  String get copy => 'Копирай';

  @override
  String get remove => 'Премахни';

  @override
  String get save => 'Запази';

  @override
  String get zoom => 'Увеличи';

  @override
  String get saved => 'Запазено';

  @override
  String get text => 'Текст';

  @override
  String get resize => 'Промяна на размера';

  @override
  String get width => 'Ширина';

  @override
  String get height => 'Височина';

  @override
  String get size => 'Размер';

  @override
  String get small => 'Малък';

  @override
  String get large => 'Голям';

  @override
  String get huge => 'Огромен';

  @override
  String get clear => 'Изчисти';

  @override
  String get font => 'Шрифт';

  @override
  String get search => 'Търси';

  @override
  String get camera => 'Камера';

  @override
  String get video => 'Видео';

  @override
  String get undo => 'Отмени';

  @override
  String get redo => 'Възстанови';

  @override
  String get fontFamily => 'Шрифт';

  @override
  String get fontSize => 'Размер на шрифта';

  @override
  String get bold => 'Получер';

  @override
  String get subscript => 'Индекс';

  @override
  String get superscript => 'Надпис';

  @override
  String get italic => 'Курсив';

  @override
  String get underline => 'Подчертан';

  @override
  String get strikeThrough => 'Зачертан';

  @override
  String get inlineCode => 'Вграден код';

  @override
  String get fontColor => 'Цвят на шрифта';

  @override
  String get backgroundColor => 'Цвят на фона';

  @override
  String get clearFormat => 'Изчисти формат';

  @override
  String get alignLeft => 'Подравни вляво';

  @override
  String get alignCenter => 'Подравни в центъра';

  @override
  String get alignRight => 'Подравни вдясно';

  @override
  String get alignJustify => 'Подравни текста';

  @override
  String get justifyWinWidth => 'Подравни във всяка колонка';

  @override
  String get textDirection => 'Посока на текста';

  @override
  String get headerStyle => 'Стил на заглавието';

  @override
  String get normal => 'Нормален';

  @override
  String get heading1 => 'Заглавие 1';

  @override
  String get heading2 => 'Заглавие 2';

  @override
  String get heading3 => 'Заглавие 3';

  @override
  String get heading4 => 'Заглавие 4';

  @override
  String get heading5 => 'Заглавие 5';

  @override
  String get heading6 => 'Заглавие 6';

  @override
  String get numberedList => 'Номериран списък';

  @override
  String get bulletList => 'Маркиран списък';

  @override
  String get checkedList => 'Списък с отметки';

  @override
  String get codeBlock => 'Блок с код';

  @override
  String get quote => 'Цитат';

  @override
  String get increaseIndent => 'Увеличи отстъпа';

  @override
  String get decreaseIndent => 'Намали отстъпа';

  @override
  String get insertURL => 'Вмъкни URL';

  @override
  String get visitLink => 'Посети връзка';

  @override
  String get enterLink => 'Въведи връзка';

  @override
  String get enterMedia => 'Въведи медия';

  @override
  String get edit => 'Редактирай';

  @override
  String get apply => 'Приложи';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Цвят';

  @override
  String get lineheight => 'височина на линията';

  @override
  String get findText => 'Намери текст';

  @override
  String get moveToPreviousOccurrence => 'Премести към предишното съвпадение';

  @override
  String get moveToNextOccurrence => 'Премести към следващото съвпадение';

  @override
  String get savedUsingTheNetwork => 'Запазено с помощта на мрежата';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Снимката е запазена в: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink => 'Например, \'Научете повече\'';

  @override
  String get pleaseEnterTheLinkURL => 'Например, \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL =>
      'Моля, въведете валиден URL на изображението';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Моля, въведете валиден URL адрес за видео';

  @override
  String get photo => 'Снимка';

  @override
  String get image => 'Изображение';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Чувствителност на кутията и търсене на цялата дума';

  @override
  String get caseSensitive => 'Чувствителност на регистъра';

  @override
  String get wholeWord => 'Цяла дума';

  @override
  String get insertImage => 'Вмъкване на изображение';

  @override
  String get pickAPhotoFromYourGallery => 'Изберете снимка от галерията';

  @override
  String get takeAPhotoUsingYourCamera => 'Снимайте с камерата';

  @override
  String get pasteAPhotoUsingALink => 'Поставете снимка чрез линк';

  @override
  String get pickAVideoFromYourGallery => 'Изберете видео от галерията';

  @override
  String get recordAVideoUsingYourCamera => 'Запишете видео с камерата';

  @override
  String get pasteAVideoUsingALink => 'Поставете видео чрез линк';

  @override
  String get close => 'Затвори';

  @override
  String get searchSettings => 'Настройки за търсене';

  @override
  String get cut => 'Изрежи';

  @override
  String get paste => 'Постави';

  @override
  String get insertTable => 'Вмъкни таблица';

  @override
  String get insertVideo => 'Insert video';
}
