import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class FlutterQuillLocalizationsTr extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get pasteLink => 'Bağlantıyı Yapıştır';

  @override
  String get ok => 'Tamam';

  @override
  String get selectColor => 'Renk Seçin';

  @override
  String get gallery => 'Galeri';

  @override
  String get link => 'Bağlantı';

  @override
  String get open => 'Açık';

  @override
  String get copy => 'Kopyala';

  @override
  String get remove => 'Kaldır';

  @override
  String get save => 'Kayıt Et';

  @override
  String get zoom => 'Yakınlaştır';

  @override
  String get saved => 'Kaydedildi';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Yeniden Boyutlandır';

  @override
  String get width => 'Genişlik';

  @override
  String get height => 'Yükseklik';

  @override
  String get size => 'Boyut';

  @override
  String get small => 'Küçük';

  @override
  String get large => 'Büyük';

  @override
  String get huge => 'Daha Büyük';

  @override
  String get clear => 'Temizle';

  @override
  String get font => 'Yazı tipi';

  @override
  String get search => 'Ara';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Geri';

  @override
  String get redo => 'İleri';

  @override
  String get fontFamily => 'Yazı Türü';

  @override
  String get fontSize => 'Yazı Boyutu';

  @override
  String get bold => 'Kalın';

  @override
  String get subscript => 'Alt Simge';

  @override
  String get superscript => 'Üst Simge';

  @override
  String get italic => 'İtalik';

  @override
  String get underline => 'Altı Çizili';

  @override
  String get strikeThrough => 'Üsti Çizili';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Yazı Rengi';

  @override
  String get backgroundColor => 'Vurgu Rengi';

  @override
  String get clearFormat => 'Formatı Temizle';

  @override
  String get alignLeft => 'Sola Hizala';

  @override
  String get alignCenter => 'Ortaya Hizala';

  @override
  String get alignRight => 'Sağa Hizala';

  @override
  String get alignJustify => 'Yasla';

  @override
  String get justifyWinWidth => 'Kenarlara Hizala';

  @override
  String get textDirection => 'Metin Yönü';

  @override
  String get headerStyle => 'Başlık Stili';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Başlık 1';

  @override
  String get heading2 => 'Başlık 2';

  @override
  String get heading3 => 'Başlık 3';

  @override
  String get heading4 => 'Başlık 4';

  @override
  String get heading5 => 'Başlık 5';

  @override
  String get heading6 => 'Başlık 6';

  @override
  String get numberedList => 'Numaralı Liste';

  @override
  String get bulletList => 'Madde Listesi';

  @override
  String get checkedList => 'Kontrol Listesi';

  @override
  String get codeBlock => 'Kod Blogu';

  @override
  String get quote => 'Alıntı';

  @override
  String get increaseIndent => 'Girintiyi Artır';

  @override
  String get decreaseIndent => 'Girintiyi Azalt';

  @override
  String get insertURL => 'URL Giriniz';

  @override
  String get visitLink => 'Bağlantıyı Ziyaret Et';

  @override
  String get enterLink => 'Bağlantı Giriniz';

  @override
  String get enterMedia => 'Medya Giriniz';

  @override
  String get edit => 'Düzenle';

  @override
  String get apply => 'Uygula';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Malzeme';

  @override
  String get color => 'Renk';

  @override
  String get lineheight => 'Satır yüksekliği';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Saved using the network';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Resim şu adreste kaydedildi: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink => 'e.g., \'Learn more\'';

  @override
  String get pleaseEnterTheLinkURL => 'e.g., \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Lütfen geçerli bir video URL\'si girin';

  @override
  String get photo => 'Fotoğraf';

  @override
  String get image => 'Görüntü';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Büyük/küçük harf hassasiyeti ve tam kelime arama';

  @override
  String get caseSensitive => 'Büyük/küçük harf duyarlı';

  @override
  String get wholeWord => 'Tam kelime';

  @override
  String get insertImage => 'Görüntü ekle';

  @override
  String get pickAPhotoFromYourGallery => 'Galeriğinizden fotoğraf seçin';

  @override
  String get takeAPhotoUsingYourCamera => 'Kameranızla fotoğraf çekin';

  @override
  String get pasteAPhotoUsingALink =>
      'Bir bağlantı kullanarak fotoğraf yapıştırın';

  @override
  String get pickAVideoFromYourGallery => 'Galeriğinizden video seçin';

  @override
  String get recordAVideoUsingYourCamera => 'Kameranızla video kaydedin';

  @override
  String get pasteAVideoUsingALink =>
      'Bir bağlantı kullanarak video yapıştırın';

  @override
  String get close => 'Kapat';

  @override
  String get searchSettings => 'Arama ayarları';

  @override
  String get cut => 'Kes';

  @override
  String get paste => 'Yapıştır';

  @override
  String get insertTable => 'Tablo ekle';

  @override
  String get insertVideo => 'Insert video';
}
