import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class FlutterQuillLocalizationsKo extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get pasteLink => '링크를 붙여 넣어 주세요';

  @override
  String get ok => '확인';

  @override
  String get selectColor => '색상 선택';

  @override
  String get gallery => '갤러리';

  @override
  String get link => '링크';

  @override
  String get open => '열기';

  @override
  String get copy => '복사하기';

  @override
  String get remove => '제거하기';

  @override
  String get save => '저장하기';

  @override
  String get zoom => '확대하기';

  @override
  String get saved => '저장되었습니다';

  @override
  String get text => '제목';

  @override
  String get resize => '크기조정';

  @override
  String get width => '넓이';

  @override
  String get height => '높이';

  @override
  String get size => '크기';

  @override
  String get small => '작게';

  @override
  String get large => '크게';

  @override
  String get huge => '매우크게';

  @override
  String get clear => '초기화';

  @override
  String get font => '글꼴';

  @override
  String get search => '검색';

  @override
  String get camera => '카메라';

  @override
  String get video => '비디오';

  @override
  String get undo => '되돌리기';

  @override
  String get redo => '다시실행';

  @override
  String get fontFamily => '글꼴';

  @override
  String get fontSize => '글자 크기';

  @override
  String get bold => '굵게';

  @override
  String get subscript => '아래 첨자';

  @override
  String get superscript => '위 첨자';

  @override
  String get italic => '기울이기';

  @override
  String get underline => '밑줄';

  @override
  String get strikeThrough => '취소선';

  @override
  String get inlineCode => '인라인 코드';

  @override
  String get fontColor => '글자 색상';

  @override
  String get backgroundColor => '배경 색상';

  @override
  String get clearFormat => '서식 지우기';

  @override
  String get alignLeft => '왼쪽 정렬';

  @override
  String get alignCenter => '가운데 정렬';

  @override
  String get alignRight => '오른쪽 정렬';

  @override
  String get alignJustify => '양쪽 정렬';

  @override
  String get justifyWinWidth => '좌우로 정렬';

  @override
  String get textDirection => '텍스트 방향';

  @override
  String get headerStyle => '헤더 스타일';

  @override
  String get normal => '일반 텍스트';

  @override
  String get heading1 => '제목 1';

  @override
  String get heading2 => '제목 2';

  @override
  String get heading3 => '제목 3';

  @override
  String get heading4 => '제목 4';

  @override
  String get heading5 => '제목 5';

  @override
  String get heading6 => '제목 6';

  @override
  String get numberedList => '번호 매기기 목록';

  @override
  String get bulletList => '글머리 기호 목록';

  @override
  String get checkedList => '체크리스트';

  @override
  String get codeBlock => '코드 블록';

  @override
  String get quote => '인용';

  @override
  String get increaseIndent => '들여쓰기 증가';

  @override
  String get decreaseIndent => '들여쓰기 감소';

  @override
  String get insertURL => 'URL 삽입';

  @override
  String get visitLink => '링크 방문';

  @override
  String get enterLink => '링크 입력';

  @override
  String get enterMedia => '미디어 입력';

  @override
  String get edit => '편집';

  @override
  String get apply => '적용';

  @override
  String get hex => 'Hex 값';

  @override
  String get material => 'Material 색상';

  @override
  String get color => '색상';

  @override
  String get lineheight => '선 높이';

  @override
  String get findText => '찾기';

  @override
  String get moveToPreviousOccurrence => '이전 위치로 이동';

  @override
  String get moveToNextOccurrence => '다음 위치로 이동';

  @override
  String get savedUsingTheNetwork => '네트워크를 통해 저장';

  @override
  String get savedUsingLocalStorage => '로컬 스토리지를 통해 저장';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return '이미지가 저장되었습니다 $imagePath';
  }

  @override
  String get errorWhileSavingImage => '이미지를 저장하는데 실패했습니다';

  @override
  String get pleaseEnterTextForYourLink => '링크 제목 입력';

  @override
  String get pleaseEnterTheLinkURL => '예시) \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => '유효한 이미지 URL을 입력하세요';

  @override
  String get pleaseEnterAValidVideoURL => '유효한 비디오 URL을 입력하세요';

  @override
  String get photo => '사진';

  @override
  String get image => '이미지';

  @override
  String get caseSensitivityAndWholeWordSearch => '대소문자 구분 및 전체 단어 검색';

  @override
  String get caseSensitive => '대소문자 구분';

  @override
  String get wholeWord => '전체 단어';

  @override
  String get insertImage => '이미지 삽입';

  @override
  String get pickAPhotoFromYourGallery => '갤러리에서 이미지 선택';

  @override
  String get takeAPhotoUsingYourCamera => '카메라로 사진 촬영';

  @override
  String get pasteAPhotoUsingALink => '이미지 링크 입력';

  @override
  String get pickAVideoFromYourGallery => '갤러리에서 동영상 선택';

  @override
  String get recordAVideoUsingYourCamera => '카메라로 동영상 촬영';

  @override
  String get pasteAVideoUsingALink => '동영상 링크 입력';

  @override
  String get close => '닫기';

  @override
  String get searchSettings => '검색 설정';

  @override
  String get cut => '잘라내기';

  @override
  String get paste => '붙여넣기';

  @override
  String get insertTable => '테이블 삽입';

  @override
  String get insertVideo => 'Insert video';
}
