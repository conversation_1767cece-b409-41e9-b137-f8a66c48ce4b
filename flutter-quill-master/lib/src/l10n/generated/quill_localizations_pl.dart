import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Polish (`pl`).
class FlutterQuillLocalizationsPl extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsPl([String locale = 'pl']) : super(locale);

  @override
  String get pasteLink => 'Wklej link';

  @override
  String get ok => 'OK';

  @override
  String get selectColor => 'Wybierz kolor';

  @override
  String get gallery => 'Galeria';

  @override
  String get link => 'Link';

  @override
  String get open => 'Otwórz';

  @override
  String get copy => 'Kopiuj';

  @override
  String get remove => 'Usuń';

  @override
  String get save => 'Zapisz';

  @override
  String get zoom => 'Powiększenie';

  @override
  String get saved => 'Zapisano';

  @override
  String get text => 'Tekst';

  @override
  String get resize => 'Resize';

  @override
  String get width => 'Width';

  @override
  String get height => 'Height';

  @override
  String get size => 'Size';

  @override
  String get small => 'Small';

  @override
  String get large => 'Large';

  @override
  String get huge => 'Huge';

  @override
  String get clear => 'Clear';

  @override
  String get font => 'Font';

  @override
  String get search => 'Search';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Font family';

  @override
  String get fontSize => 'Font size';

  @override
  String get bold => 'Bold';

  @override
  String get subscript => 'Subscript';

  @override
  String get superscript => 'Superscript';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get strikeThrough => 'Strike through';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Font color';

  @override
  String get backgroundColor => 'Background color';

  @override
  String get clearFormat => 'Clear format';

  @override
  String get alignLeft => 'Align left';

  @override
  String get alignCenter => 'Align center';

  @override
  String get alignRight => 'Align right';

  @override
  String get alignJustify => 'Wyjustuj tekst';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Text direction';

  @override
  String get headerStyle => 'Header style';

  @override
  String get normal => 'Normalny';

  @override
  String get heading1 => 'Nagłówek 1';

  @override
  String get heading2 => 'Nagłówek 2';

  @override
  String get heading3 => 'Nagłówek 3';

  @override
  String get heading4 => 'Nagłówek 4';

  @override
  String get heading5 => 'Nagłówek 5';

  @override
  String get heading6 => 'Nagłówek 6';

  @override
  String get numberedList => 'Numbered list';

  @override
  String get bulletList => 'Bullet list';

  @override
  String get checkedList => 'Checked list';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Quote';

  @override
  String get increaseIndent => 'Increase indent';

  @override
  String get decreaseIndent => 'Decrease indent';

  @override
  String get insertURL => 'Insert URL';

  @override
  String get visitLink => 'Visit link';

  @override
  String get enterLink => 'Enter link';

  @override
  String get enterMedia => 'Enter media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Apply';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Materiał';

  @override
  String get color => 'Kolor';

  @override
  String get lineheight => 'Wysokość linii';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Saved using the network';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Obrazek został zapisany w: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink => 'e.g., \'Learn more\'';

  @override
  String get pleaseEnterTheLinkURL => 'e.g., \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Proszę wprowadzić poprawny adres URL wideo';

  @override
  String get photo => 'Zdjęcie';

  @override
  String get image => 'Obraz';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Czułość na wielkość liter i wyszukiwanie całego słowa';

  @override
  String get caseSensitive => 'Uwzględniaj wielkość liter';

  @override
  String get wholeWord => 'Całe słowo';

  @override
  String get insertImage => 'Wstaw obraz';

  @override
  String get pickAPhotoFromYourGallery => 'Wybierz zdjęcie z galerii';

  @override
  String get takeAPhotoUsingYourCamera => 'Zrób zdjęcie używając aparatu';

  @override
  String get pasteAPhotoUsingALink => 'Wklej zdjęcie używając linku';

  @override
  String get pickAVideoFromYourGallery => 'Wybierz wideo z galerii';

  @override
  String get recordAVideoUsingYourCamera => 'Nagraj wideo używając aparatu';

  @override
  String get pasteAVideoUsingALink => 'Wklej wideo używając linku';

  @override
  String get close => 'Zamknij';

  @override
  String get searchSettings => 'Ustawienia wyszukiwania';

  @override
  String get cut => 'Wytnij';

  @override
  String get paste => 'Wklej';

  @override
  String get insertTable => 'Wstaw tabelę';

  @override
  String get insertVideo => 'Insert video';
}
