import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swedish (`sv`).
class FlutterQuillLocalizationsSv extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsSv([String locale = 'sv']) : super(locale);

  @override
  String get pasteLink => 'Klistra in länk';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Välj färg';

  @override
  String get gallery => 'Galleri';

  @override
  String get link => 'Länk';

  @override
  String get open => 'Öppna';

  @override
  String get copy => 'Kopiera';

  @override
  String get remove => 'Ta bort';

  @override
  String get save => 'Spara';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Sparad';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Ändra storlek';

  @override
  String get width => 'Bredd';

  @override
  String get height => 'Höjd';

  @override
  String get size => 'Storlek';

  @override
  String get small => 'Liten';

  @override
  String get large => 'Stor';

  @override
  String get huge => 'Enorm';

  @override
  String get clear => 'Rensa';

  @override
  String get font => 'Typsnitt';

  @override
  String get search => 'Sök';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Ångra';

  @override
  String get redo => 'Gör om';

  @override
  String get fontFamily => 'Typsnittsfamilj';

  @override
  String get fontSize => 'Storlek';

  @override
  String get bold => 'Fet';

  @override
  String get subscript => 'Nedsänkt';

  @override
  String get superscript => 'Upphöjd';

  @override
  String get italic => 'Kursiv';

  @override
  String get underline => 'Understruken';

  @override
  String get strikeThrough => 'Genomstruken';

  @override
  String get inlineCode => 'Inlinekod';

  @override
  String get fontColor => 'Fontfärg';

  @override
  String get backgroundColor => 'Bakgrundsfärg';

  @override
  String get clearFormat => 'Rensa format';

  @override
  String get alignLeft => 'Vänsterjustera';

  @override
  String get alignCenter => 'Centrera';

  @override
  String get alignRight => 'Högerjustera';

  @override
  String get alignJustify => 'Justera text';

  @override
  String get justifyWinWidth => 'Justera till fönsterbredd';

  @override
  String get textDirection => 'Textriktning';

  @override
  String get headerStyle => 'Rubrikstil';

  @override
  String get normal => 'Brödtext';

  @override
  String get heading1 => 'Rubrik 1';

  @override
  String get heading2 => 'Rubrik 2';

  @override
  String get heading3 => 'Rubrik 3';

  @override
  String get heading4 => 'Rubrik 4';

  @override
  String get heading5 => 'Rubrik 5';

  @override
  String get heading6 => 'Rubrik 6';

  @override
  String get numberedList => 'Numrerad lista';

  @override
  String get bulletList => 'Punktlista';

  @override
  String get checkedList => 'Markerad lista';

  @override
  String get codeBlock => 'Kodblock';

  @override
  String get quote => 'Citat';

  @override
  String get increaseIndent => 'Öka indrag';

  @override
  String get decreaseIndent => 'Minska indrag';

  @override
  String get insertURL => 'Infoga URL';

  @override
  String get visitLink => 'Besök länk';

  @override
  String get enterLink => 'Ange länk';

  @override
  String get enterMedia => 'Ange media';

  @override
  String get edit => 'Redigera';

  @override
  String get apply => 'Tillämpa';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Färg';

  @override
  String get lineheight => 'Radavstånd';

  @override
  String get findText => 'Hitta text';

  @override
  String get moveToPreviousOccurrence => 'Gå till föregående förekomst';

  @override
  String get moveToNextOccurrence => 'Gå till nästa förekomst';

  @override
  String get savedUsingTheNetwork => 'Sparad med hjälp av nätverket';

  @override
  String get savedUsingLocalStorage => 'Sparad med hjälp av lokal lagring';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Bilden har sparats på: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Fel vid när bilden skulle sparas';

  @override
  String get pleaseEnterTextForYourLink =>
      'Ange text för din länk (t.ex. \'Lär dig mer\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Ange URL för länken (t.ex. \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Ange en giltig bild-URL';

  @override
  String get pleaseEnterAValidVideoURL => 'Ange en giltig video-URL';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Bild';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Skiftlägeskänsligt och helordsökning';

  @override
  String get caseSensitive => 'Skilja mellan versaler och gemener';

  @override
  String get wholeWord => 'Hela ord';

  @override
  String get insertImage => 'Infoga bild';

  @override
  String get pickAPhotoFromYourGallery => 'Välj ett foto från ditt galleri';

  @override
  String get takeAPhotoUsingYourCamera => 'Ta ett foto med din kamera';

  @override
  String get pasteAPhotoUsingALink => 'Klistra in ett foto med en länk';

  @override
  String get pickAVideoFromYourGallery => 'Välj en video från ditt galleri';

  @override
  String get recordAVideoUsingYourCamera => 'Spela in en video med din kamera';

  @override
  String get pasteAVideoUsingALink => 'Klistra in en video med en länk';

  @override
  String get close => 'Stäng';

  @override
  String get searchSettings => 'Sökinställningar';

  @override
  String get cut => 'Klipp ut';

  @override
  String get paste => 'Klistra in';

  @override
  String get insertTable => 'Infoga tabell';

  @override
  String get insertVideo => 'Insert video';
}
