import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class FlutterQuillLocalizationsDe extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get pasteLink => 'Link hinzufügen';

  @override
  String get ok => 'OK';

  @override
  String get selectColor => 'Farbe auswählen';

  @override
  String get gallery => 'Galerie';

  @override
  String get link => 'Link';

  @override
  String get open => 'Öffnen';

  @override
  String get copy => 'Kopieren';

  @override
  String get remove => 'Entfernen';

  @override
  String get save => 'Speichern';

  @override
  String get zoom => 'Zoomen';

  @override
  String get saved => 'Gespeichert';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Größe ändern';

  @override
  String get width => 'Breite';

  @override
  String get height => 'Höhe';

  @override
  String get size => 'Größe';

  @override
  String get small => 'Klein';

  @override
  String get large => 'Groß';

  @override
  String get huge => 'Riesig';

  @override
  String get clear => 'Löschen';

  @override
  String get font => 'Schrift';

  @override
  String get search => 'Suchen';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Rückgängig';

  @override
  String get redo => 'Wiederherstellen';

  @override
  String get fontFamily => 'Schriftart';

  @override
  String get fontSize => 'Schriftgröße';

  @override
  String get bold => 'Fett';

  @override
  String get subscript => 'Tiefgestellt';

  @override
  String get superscript => 'Hochgestellt';

  @override
  String get italic => 'Kursiv';

  @override
  String get underline => 'Unterstreichen';

  @override
  String get strikeThrough => 'Durchstreichen';

  @override
  String get inlineCode => 'Inline-Code';

  @override
  String get fontColor => 'Schriftfarbe';

  @override
  String get backgroundColor => 'Hintergrundfarbe';

  @override
  String get clearFormat => 'Formatierung löschen';

  @override
  String get alignLeft => 'Linksbündig ausrichten';

  @override
  String get alignCenter => 'Zentriert ausrichten';

  @override
  String get alignRight => 'Rechtsbündig ausrichten';

  @override
  String get alignJustify => 'Blocksatz';

  @override
  String get justifyWinWidth => 'Blocksatz';

  @override
  String get textDirection => 'Textrichtung';

  @override
  String get headerStyle => 'Überschrift-Stil';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Überschrift 1';

  @override
  String get heading2 => 'Überschrift 2';

  @override
  String get heading3 => 'Überschrift 3';

  @override
  String get heading4 => 'Überschrift 4';

  @override
  String get heading5 => 'Überschrift 5';

  @override
  String get heading6 => 'Überschrift 6';

  @override
  String get numberedList => 'Nummerierte Liste';

  @override
  String get bulletList => 'Aufzählungsliste';

  @override
  String get checkedList => 'Checkliste';

  @override
  String get codeBlock => 'Code-Block';

  @override
  String get quote => 'Zitat';

  @override
  String get increaseIndent => 'Einzug vergrößern';

  @override
  String get decreaseIndent => 'Einzug verkleinern';

  @override
  String get insertURL => 'URL einfügen';

  @override
  String get visitLink => 'Link öffnen';

  @override
  String get enterLink => 'Link eingeben';

  @override
  String get enterMedia => 'Medien einfügen';

  @override
  String get edit => 'Bearbeiten';

  @override
  String get apply => 'Anwenden';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Farbe';

  @override
  String get lineheight => 'Zeilenhöhe';

  @override
  String get findText => 'Text suchen';

  @override
  String get moveToPreviousOccurrence => 'Zum vorherigen Auftreten springen';

  @override
  String get moveToNextOccurrence => 'Zum nächsten Auftreten springen';

  @override
  String get savedUsingTheNetwork => 'Mit dem Netzwerk gespeichert';

  @override
  String get savedUsingLocalStorage => 'Mit dem lokalen Speicher gespeichert';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Das Bild wurde gespeichert unter: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Fehler beim Speichern des Bildes';

  @override
  String get pleaseEnterTextForYourLink => 'z.B. \'Mehr erfahren\'';

  @override
  String get pleaseEnterTheLinkURL => 'z.B. \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL =>
      'Bitte geben Sie eine gültige Bild-URL ein';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Bitte geben Sie eine gültige Video-URL ein';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Bild';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Groß- und Kleinschreibung sowie Ganzwortsuche';

  @override
  String get caseSensitive => 'Groß- und Kleinschreibung beachten';

  @override
  String get wholeWord => 'Ganzes Wort';

  @override
  String get insertImage => 'Bild einfügen';

  @override
  String get pickAPhotoFromYourGallery =>
      'Wählen Sie ein Foto aus Ihrer Galerie';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Machen Sie ein Foto mit Ihrer Kamera';

  @override
  String get pasteAPhotoUsingALink => 'Fügen Sie ein Foto über einen Link ein';

  @override
  String get pickAVideoFromYourGallery =>
      'Wählen Sie ein Video aus Ihrer Galerie';

  @override
  String get recordAVideoUsingYourCamera =>
      'Nehmen Sie ein Video mit Ihrer Kamera auf';

  @override
  String get pasteAVideoUsingALink => 'Video über einen Link einfügen';

  @override
  String get close => 'Schließen';

  @override
  String get searchSettings => 'Such-Einstellungen';

  @override
  String get cut => 'Ausschneiden';

  @override
  String get paste => 'Einfügen';

  @override
  String get insertTable => 'Tabelle einfügen';

  @override
  String get insertVideo => 'Insert video';
}
