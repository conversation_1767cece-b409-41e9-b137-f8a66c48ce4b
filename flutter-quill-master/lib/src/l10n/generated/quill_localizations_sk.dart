import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Slovak (`sk`).
class FlutterQuillLocalizationsSk extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsSk([String locale = 'sk']) : super(locale);

  @override
  String get pasteLink => 'Vložiť odkaz';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Vybrať farbu';

  @override
  String get gallery => 'Galéria';

  @override
  String get link => 'Odkaz';

  @override
  String get open => 'Otvoriť';

  @override
  String get copy => 'Kopírovať';

  @override
  String get remove => 'Odstrániť';

  @override
  String get save => 'Uložiť';

  @override
  String get zoom => 'Priblížiť';

  @override
  String get saved => 'Uložené';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Zmeniť veľkosť';

  @override
  String get width => 'Š<PERSON>rka';

  @override
  String get height => 'Výška';

  @override
  String get size => 'Veľkosť';

  @override
  String get small => 'Malý';

  @override
  String get large => 'Velký';

  @override
  String get huge => 'Obrovsky';

  @override
  String get clear => 'Vymazať';

  @override
  String get font => 'Písmo';

  @override
  String get search => 'Hľadanie';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Vrátiť';

  @override
  String get redo => 'Znova';

  @override
  String get fontFamily => 'Rodina písiem';

  @override
  String get fontSize => 'Veľkosť písma';

  @override
  String get bold => 'Tučné';

  @override
  String get subscript => 'Dolný index';

  @override
  String get superscript => 'Horný index';

  @override
  String get italic => 'Kurzíva';

  @override
  String get underline => 'Podčiarknutie';

  @override
  String get strikeThrough => 'Prečiarknuté';

  @override
  String get inlineCode => 'Inline kód';

  @override
  String get fontColor => 'Farba písma';

  @override
  String get backgroundColor => 'Farba pozadia';

  @override
  String get clearFormat => 'Odstrániť formátovanie';

  @override
  String get alignLeft => 'Zarovnať vľavo';

  @override
  String get alignCenter => 'Zarovnať na stred';

  @override
  String get alignRight => 'Zarovnať vpravo';

  @override
  String get alignJustify => 'Zarovnať do bloku';

  @override
  String get justifyWinWidth => 'Zarovnať na šírku okna';

  @override
  String get textDirection => 'Smer textu';

  @override
  String get headerStyle => 'Štýl záhlavia';

  @override
  String get normal => 'Normálny';

  @override
  String get heading1 => 'Hlavička 1';

  @override
  String get heading2 => 'Hlavička 2';

  @override
  String get heading3 => 'Hlavička 3';

  @override
  String get heading4 => 'Hlavička 4';

  @override
  String get heading5 => 'Hlavička 5';

  @override
  String get heading6 => 'Hlavička 6';

  @override
  String get numberedList => 'Číslovaný zoznam';

  @override
  String get bulletList => 'Zoznam s odrážkami';

  @override
  String get checkedList => 'Zoznam s označením';

  @override
  String get codeBlock => 'Blok kódu';

  @override
  String get quote => 'Citácia';

  @override
  String get increaseIndent => 'Zväčšiť odsadenie';

  @override
  String get decreaseIndent => 'Zmenšiť odsadenie';

  @override
  String get insertURL => 'Vložiť URL';

  @override
  String get visitLink => 'Navštíviť odkaz';

  @override
  String get enterLink => 'Zadať odkaz';

  @override
  String get enterMedia => 'Zadať médium';

  @override
  String get edit => 'Upraviť';

  @override
  String get apply => 'Použiť';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Materiál';

  @override
  String get color => 'Farba';

  @override
  String get lineheight => 'Výška riadku';

  @override
  String get findText => 'Nájsť text';

  @override
  String get moveToPreviousOccurrence => 'Prejsť na predchádzajúce výskyty';

  @override
  String get moveToNextOccurrence => 'Prejsť na ďalší výskyt';

  @override
  String get savedUsingTheNetwork => 'Uložené pomocou siete';

  @override
  String get savedUsingLocalStorage => 'Uložené pomocou lokálneho úložiska';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Obrázok bol uložený v: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Chyba pri ukladaní obrázka';

  @override
  String get pleaseEnterTextForYourLink =>
      'Prosím zadajte text pre váš odkaz (napr. \'Ďalšie informácie\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Prosím zadajte URL odkazu (napr. \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Prosím zadajte platnú URL adresu obrázka';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Prosím zadajte platnú URL adresu videa';

  @override
  String get photo => 'Fotografia';

  @override
  String get image => 'Obrázok';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Citlivosť na veľkosť písmen a vyhľadávanie celých slov';

  @override
  String get caseSensitive => 'Rozlišovať veľké a malé písmená';

  @override
  String get wholeWord => 'Celé slovo';

  @override
  String get insertImage => 'Vložiť obrázok';

  @override
  String get pickAPhotoFromYourGallery => 'Vyberte fotografiu z vašej galérie';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Odfotografovať fotografiu pomocou vašej kamery';

  @override
  String get pasteAPhotoUsingALink => 'Vložiť fotografiu pomocou odkazu';

  @override
  String get pickAVideoFromYourGallery => 'Vyberte video z vašej galérie';

  @override
  String get recordAVideoUsingYourCamera =>
      'Natočiť video pomocou vašej kamery';

  @override
  String get pasteAVideoUsingALink => 'Vložiť video pomocou odkazu';

  @override
  String get close => 'Zatvoriť';

  @override
  String get searchSettings => 'Nastavenia vyhľadávania';

  @override
  String get cut => 'Vystrihnúť';

  @override
  String get paste => 'Vložiť';

  @override
  String get insertTable => 'Vložiť tabuľku';

  @override
  String get insertVideo => 'Insert video';
}
