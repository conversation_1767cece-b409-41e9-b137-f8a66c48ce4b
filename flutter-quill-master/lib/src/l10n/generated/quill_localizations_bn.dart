import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bengali Bangla (`bn`).
class FlutterQuillLocalizationsBn extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsBn([String locale = 'bn']) : super(locale);

  @override
  String get pasteLink => 'লিঙ্ক পেস্ট করুন';

  @override
  String get ok => 'ওকে';

  @override
  String get selectColor => 'কালার সিলেক্ট করুন';

  @override
  String get gallery => 'গ্যালারি';

  @override
  String get link => 'লিঙ্ক';

  @override
  String get open => 'ওপেন';

  @override
  String get copy => 'কপি';

  @override
  String get remove => 'রিমুভ';

  @override
  String get save => 'সেভ';

  @override
  String get zoom => 'জুম';

  @override
  String get saved => 'সেভড';

  @override
  String get text => 'টেক্সট';

  @override
  String get resize => 'রিসাইজ';

  @override
  String get width => 'প্রস্থ';

  @override
  String get height => 'দৈর্ঘ্য';

  @override
  String get size => 'সাইজ';

  @override
  String get small => 'ছোট';

  @override
  String get large => 'বড়';

  @override
  String get huge => 'বিশাল';

  @override
  String get clear => 'ক্লিয়ার';

  @override
  String get font => 'ফন্ট';

  @override
  String get search => 'সার্চ';

  @override
  String get camera => 'ক্যামেরা';

  @override
  String get video => 'ভিডিও';

  @override
  String get undo => 'আন্ডু';

  @override
  String get redo => 'রিডু';

  @override
  String get fontFamily => 'ফন্ট ফ্যামিলি';

  @override
  String get fontSize => 'ফন্ট সাইজ';

  @override
  String get bold => 'বোল্ড';

  @override
  String get subscript => 'সাবস্ক্রিপ্ট';

  @override
  String get superscript => 'সুপারস্ক্রিপ্ট';

  @override
  String get italic => 'ইটালিক';

  @override
  String get underline => 'আন্ডারলাইন';

  @override
  String get strikeThrough => 'স্ট্রাইক থ্রু';

  @override
  String get inlineCode => 'ইনলাইন কোড';

  @override
  String get fontColor => 'ফন্ট কালার';

  @override
  String get backgroundColor => 'ব্যাকগ্রাউন্ড কালার';

  @override
  String get clearFormat => 'ক্লিয়ার ফরম্যাট';

  @override
  String get alignLeft => 'বাম সারিবদ্ধ';

  @override
  String get alignCenter => 'কেন্দ্র সারিবদ্ধ';

  @override
  String get alignRight => 'ডান সারিবদ্ধ';

  @override
  String get alignJustify => 'সর্বোচ্চ জাস্টিফাই';

  @override
  String get justifyWinWidth => 'প্রস্থের সাথে সংযত';

  @override
  String get textDirection => 'টেক্সট ডিরেকশন';

  @override
  String get headerStyle => 'হেডার স্টাইল';

  @override
  String get normal => 'সাধারণ';

  @override
  String get heading1 => 'শিরোনাম 1';

  @override
  String get heading2 => 'শিরোনাম 2';

  @override
  String get heading3 => 'শিরোনাম 3';

  @override
  String get heading4 => 'শিরোনাম 4';

  @override
  String get heading5 => 'শিরোনাম 5';

  @override
  String get heading6 => 'শিরোনাম 6';

  @override
  String get numberedList => 'সংখ্যাযুক্ত তালিকা';

  @override
  String get bulletList => 'বুলেট তালিকা';

  @override
  String get checkedList => 'চেক করা তালিকা';

  @override
  String get codeBlock => 'কোড ব্লক';

  @override
  String get quote => 'উক্তি';

  @override
  String get increaseIndent => 'ইন্ডেন্ট বাড়ান';

  @override
  String get decreaseIndent => 'ইন্ডেন্ট কমান';

  @override
  String get insertURL => 'UR দিন';

  @override
  String get visitLink => 'ভিজিট লিঙ্ক';

  @override
  String get enterLink => 'লিঙ্ক দিন';

  @override
  String get enterMedia => 'মিডিয়া দিন';

  @override
  String get edit => 'ইডিট';

  @override
  String get apply => 'এপ্লাই';

  @override
  String get hex => 'হেক্স';

  @override
  String get material => 'ম্যাটারিয়াল';

  @override
  String get color => 'কালার';

  @override
  String get lineheight => 'লাইনের উচ্চতা';

  @override
  String get findText => 'পাঠ্য খুঁজুন';

  @override
  String get moveToPreviousOccurrence => 'পূর্ববর্তী ঘটনায় চলুন';

  @override
  String get moveToNextOccurrence => 'পরবর্তী ঘটনায় চলুন';

  @override
  String get savedUsingTheNetwork => 'নেটওয়ার্ক ব্যবহার করে সংরক্ষিত';

  @override
  String get savedUsingLocalStorage => 'স্থানীয় সংরক্ষণ ব্যবহার করে সংরক্ষিত';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'ছবিটি সংরক্ষিত হয়েছে: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'চিত্র সংরক্ষণে সময়ে ত্রুটি';

  @override
  String get pleaseEnterTextForYourLink =>
      'আপনার লিঙ্কের জন্য একটি টেক্সট লিখুন (উদাঃ \'আরও জানুন\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'দয়া করে লিঙ্ক URL লিখুন (উদাঃ \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'দয়া করে একটি বৈধ চিত্র URL লিখুন';

  @override
  String get pleaseEnterAValidVideoURL => 'দয়া করে একটি বৈধ ভিডিও URL লিখুন';

  @override
  String get photo => 'ফটো';

  @override
  String get image => 'চিত্র';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'কেস সেন্সিটিভিটি এবং পূর্ণ শব্দ অনুসন্ধান';

  @override
  String get caseSensitive => 'বড়-ছোট অক্ষর বিভাজন';

  @override
  String get wholeWord => 'সম্পূর্ণ শব্দ';

  @override
  String get insertImage => 'চিত্র সন্নিবেশ';

  @override
  String get pickAPhotoFromYourGallery =>
      'আপনার গ্যালারি থেকে একটি ছবি নির্বাচন করুন';

  @override
  String get takeAPhotoUsingYourCamera =>
      'আপনার ক্যামেরা ব্যবহার করে একটি ছবি তুলুন';

  @override
  String get pasteAPhotoUsingALink =>
      'একটি লিঙ্ক ব্যবহার করে একটি ছবি পেস্ট করুন';

  @override
  String get pickAVideoFromYourGallery =>
      'আপনার গ্যালারি থেকে একটি ভিডিও নির্বাচন করুন';

  @override
  String get recordAVideoUsingYourCamera =>
      'আপনার ক্যামেরা ব্যবহার করে একটি ভিডিও রেকর্ড করুন';

  @override
  String get pasteAVideoUsingALink =>
      'একটি লিঙ্ক ব্যবহার করে একটি ভিডিও পেস্ট করুন';

  @override
  String get close => 'বন্ধ করুন';

  @override
  String get searchSettings => 'অনুসন্ধান সেটিংস';

  @override
  String get cut => 'কাটা';

  @override
  String get paste => 'পেস্ট';

  @override
  String get insertTable => 'টেবিল যোগ করুন';

  @override
  String get insertVideo => 'Insert video';
}
