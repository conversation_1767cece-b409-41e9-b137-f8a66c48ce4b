import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Italian (`it`).
class FlutterQuillLocalizationsIt extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsIt([String locale = 'it']) : super(locale);

  @override
  String get pasteLink => 'Incolla un collegamento';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Seleziona Colore';

  @override
  String get gallery => 'Galleria';

  @override
  String get link => 'Collegamento';

  @override
  String get open => 'Apri';

  @override
  String get copy => 'Copia';

  @override
  String get remove => 'Rimuovi';

  @override
  String get save => 'Salva';

  @override
  String get zoom => 'Ingrandisci';

  @override
  String get saved => 'Salvato';

  @override
  String get text => 'Testo';

  @override
  String get resize => 'Ridimensiona';

  @override
  String get width => 'Larghezza';

  @override
  String get height => 'Altezza';

  @override
  String get size => 'Dimensione';

  @override
  String get small => 'Piccolo';

  @override
  String get large => 'Largo';

  @override
  String get huge => 'Enorme';

  @override
  String get clear => 'Cancella';

  @override
  String get font => 'Font';

  @override
  String get search => 'Ricerca';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Annulla';

  @override
  String get redo => 'Ripeti';

  @override
  String get fontFamily => 'Famiglia del carattere';

  @override
  String get fontSize => 'Dimensione del carattere';

  @override
  String get bold => 'Grassetto';

  @override
  String get subscript => 'Pedice';

  @override
  String get superscript => 'Apice';

  @override
  String get italic => 'Corsivo';

  @override
  String get underline => 'Sottolineato';

  @override
  String get strikeThrough => 'Barrato';

  @override
  String get inlineCode => 'Codice inline';

  @override
  String get fontColor => 'Colore del carattere';

  @override
  String get backgroundColor => 'Colore di sfondo';

  @override
  String get clearFormat => 'Cancella formato';

  @override
  String get alignLeft => 'Allinea a sinistra';

  @override
  String get alignCenter => 'Allinea al centro';

  @override
  String get alignRight => 'Allinea a destra';

  @override
  String get alignJustify => 'Giustifica il testo';

  @override
  String get justifyWinWidth => 'Giustifica per larghezza finestra';

  @override
  String get textDirection => 'Direzione testo';

  @override
  String get headerStyle => 'Stile intestazione';

  @override
  String get normal => 'Normale';

  @override
  String get heading1 => 'Intestazione 1';

  @override
  String get heading2 => 'Intestazione 2';

  @override
  String get heading3 => 'Intestazione 3';

  @override
  String get heading4 => 'Intestazione 4';

  @override
  String get heading5 => 'Intestazione 5';

  @override
  String get heading6 => 'Intestazione 6';

  @override
  String get numberedList => 'Elenco numerato';

  @override
  String get bulletList => 'Elenco puntato';

  @override
  String get checkedList => 'Elenco con segni di spunta';

  @override
  String get codeBlock => 'Blocco di codice';

  @override
  String get quote => 'Citazione';

  @override
  String get increaseIndent => 'Aumenta rientro';

  @override
  String get decreaseIndent => 'Diminuisci rientro';

  @override
  String get insertURL => 'Inserisci URL';

  @override
  String get visitLink => 'Visita il collegamento';

  @override
  String get enterLink => 'Inserisci il collegamento';

  @override
  String get enterMedia => 'Inserisci multimedia';

  @override
  String get edit => 'Modifica';

  @override
  String get apply => 'Applica';

  @override
  String get hex => 'Esadecimale';

  @override
  String get material => 'Materiale';

  @override
  String get color => 'Colore';

  @override
  String get lineheight => 'Altezza della linea';

  @override
  String get findText => 'Trova testo';

  @override
  String get moveToPreviousOccurrence => 'Vai all\'occorrenza precedente';

  @override
  String get moveToNextOccurrence => 'Vai all\'occorrenza successiva';

  @override
  String get savedUsingTheNetwork => 'Salvato utilizzando la rete';

  @override
  String get savedUsingLocalStorage =>
      'Salvato utilizzando la memorizzazione locale';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'L\'immagine è stata salvata in: $imagePath';
  }

  @override
  String get errorWhileSavingImage =>
      'Errore durante il salvataggio dell\'immagine';

  @override
  String get pleaseEnterTextForYourLink =>
      'Inserisci un testo per il tuo link (ad esempio, \'Per saperne di più\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Inserisci l\'URL del link (ad esempio, \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Inserisci un URL di immagine valido';

  @override
  String get pleaseEnterAValidVideoURL => 'Inserisci un URL video valido';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Immagine';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilità maiuscole/minuscole e ricerca di parole intere';

  @override
  String get caseSensitive => 'Rispetta maiuscole e minuscole';

  @override
  String get wholeWord => 'Parola intera';

  @override
  String get insertImage => 'Inserisci immagine';

  @override
  String get pickAPhotoFromYourGallery => 'Scegli una foto dalla tua galleria';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Scatta una foto con la tua fotocamera';

  @override
  String get pasteAPhotoUsingALink => 'Incolla una foto utilizzando un link';

  @override
  String get pickAVideoFromYourGallery => 'Scegli un video dalla tua galleria';

  @override
  String get recordAVideoUsingYourCamera =>
      'Registra un video con la tua fotocamera';

  @override
  String get pasteAVideoUsingALink => 'Incolla un video utilizzando un link';

  @override
  String get close => 'Chiudi';

  @override
  String get searchSettings => 'Impostazioni di ricerca';

  @override
  String get cut => 'Taglia';

  @override
  String get paste => 'Incolla';

  @override
  String get insertTable => 'Inserisci tabella';

  @override
  String get insertVideo => 'Insert video';
}
