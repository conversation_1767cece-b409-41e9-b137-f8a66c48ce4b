import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class FlutterQuillLocalizationsZh extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get pasteLink => '粘贴链接';

  @override
  String get ok => '确认';

  @override
  String get selectColor => '选择颜色';

  @override
  String get gallery => '相册';

  @override
  String get link => '链接';

  @override
  String get open => '打开';

  @override
  String get copy => '复制';

  @override
  String get remove => '移除';

  @override
  String get save => '保存';

  @override
  String get zoom => '缩放';

  @override
  String get saved => '已保存';

  @override
  String get text => '文字';

  @override
  String get resize => '调整大小';

  @override
  String get width => '宽度';

  @override
  String get height => '高度';

  @override
  String get size => '文字大小';

  @override
  String get small => '小字号';

  @override
  String get large => '大字号';

  @override
  String get huge => '超大字号';

  @override
  String get clear => '清除';

  @override
  String get font => '字体';

  @override
  String get search => '搜索';

  @override
  String get camera => '拍照';

  @override
  String get video => '录像';

  @override
  String get undo => '撤销';

  @override
  String get redo => '重做';

  @override
  String get fontFamily => '字体';

  @override
  String get fontSize => '字号';

  @override
  String get bold => '粗体';

  @override
  String get subscript => '下标';

  @override
  String get superscript => '上标';

  @override
  String get italic => '斜体';

  @override
  String get underline => '下划线';

  @override
  String get strikeThrough => '删除线';

  @override
  String get inlineCode => '内联代码';

  @override
  String get fontColor => '字体颜色';

  @override
  String get backgroundColor => '背景颜色';

  @override
  String get clearFormat => '清除格式';

  @override
  String get alignLeft => '左对齐';

  @override
  String get alignCenter => '居中对齐';

  @override
  String get alignRight => '右对齐';

  @override
  String get alignJustify => '两端对齐';

  @override
  String get justifyWinWidth => '两端对齐';

  @override
  String get textDirection => '文本方向';

  @override
  String get headerStyle => '标题样式';

  @override
  String get normal => '正文';

  @override
  String get heading1 => '一级标题';

  @override
  String get heading2 => '二级标题';

  @override
  String get heading3 => '三级标题';

  @override
  String get heading4 => '四级标题';

  @override
  String get heading5 => '五级标题';

  @override
  String get heading6 => '六级标题';

  @override
  String get numberedList => '有序列表';

  @override
  String get bulletList => '无序列表';

  @override
  String get checkedList => '任务列表';

  @override
  String get codeBlock => '代码块';

  @override
  String get quote => '引言';

  @override
  String get increaseIndent => '增加缩进';

  @override
  String get decreaseIndent => '减少缩进';

  @override
  String get insertURL => '插入链接';

  @override
  String get visitLink => '访问链接';

  @override
  String get enterLink => '输入链接';

  @override
  String get enterMedia => '输入媒体';

  @override
  String get edit => '编辑';

  @override
  String get apply => '应用';

  @override
  String get hex => '十六进制';

  @override
  String get material => 'Material 设计';

  @override
  String get color => '颜色';

  @override
  String get lineheight => '行高';

  @override
  String get findText => '搜索文本';

  @override
  String get moveToPreviousOccurrence => '上一个匹配项';

  @override
  String get moveToNextOccurrence => '下一个匹配项';

  @override
  String get savedUsingTheNetwork => '通过网络保存';

  @override
  String get savedUsingLocalStorage => '使用本地存储保存';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return '图片已保存到: $imagePath';
  }

  @override
  String get errorWhileSavingImage => '保存图像时发生错误';

  @override
  String get pleaseEnterTextForYourLink => '如\'了解更多\'';

  @override
  String get pleaseEnterTheLinkURL => '如\'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => '请输入有效的图像URL';

  @override
  String get pleaseEnterAValidVideoURL => '请输入有效的视频URL';

  @override
  String get photo => '照片';

  @override
  String get image => '图像';

  @override
  String get caseSensitivityAndWholeWordSearch => '区分大小写和整词搜索';

  @override
  String get caseSensitive => '区分大小写';

  @override
  String get wholeWord => '完整单词';

  @override
  String get insertImage => '插入图像';

  @override
  String get pickAPhotoFromYourGallery => '从相册选取图片';

  @override
  String get takeAPhotoUsingYourCamera => '使用相机拍照';

  @override
  String get pasteAPhotoUsingALink => '从链接获取图片';

  @override
  String get pickAVideoFromYourGallery => '从相册选取视频';

  @override
  String get recordAVideoUsingYourCamera => '使用相机录制';

  @override
  String get pasteAVideoUsingALink => '从链接获取视频';

  @override
  String get close => '关闭';

  @override
  String get searchSettings => '搜索设置';

  @override
  String get cut => '剪切';

  @override
  String get paste => '粘贴';

  @override
  String get insertTable => '插入表格';

  @override
  String get insertVideo => 'Insert video';
}

/// The translations for Chinese, as used in China (`zh_CN`).
class FlutterQuillLocalizationsZhCn extends FlutterQuillLocalizationsZh {
  FlutterQuillLocalizationsZhCn() : super('zh_CN');

  @override
  String get pasteLink => '粘贴链接';

  @override
  String get ok => '确认';

  @override
  String get selectColor => '选择颜色';

  @override
  String get gallery => '相册';

  @override
  String get link => '链接';

  @override
  String get open => '打开';

  @override
  String get copy => '复制';

  @override
  String get remove => '移除';

  @override
  String get save => '保存';

  @override
  String get zoom => '缩放';

  @override
  String get saved => '已保存';

  @override
  String get text => '文字';

  @override
  String get resize => '调整大小';

  @override
  String get width => '宽度';

  @override
  String get height => '高度';

  @override
  String get size => '文字大小';

  @override
  String get small => '小字号';

  @override
  String get large => '大字号';

  @override
  String get huge => '超大字号';

  @override
  String get clear => '清除';

  @override
  String get font => '字体';

  @override
  String get search => '搜索';

  @override
  String get camera => '拍照';

  @override
  String get video => '录像';

  @override
  String get undo => '撤销';

  @override
  String get redo => '重做';

  @override
  String get fontFamily => '字体';

  @override
  String get fontSize => '字号';

  @override
  String get bold => '粗体';

  @override
  String get subscript => '下标';

  @override
  String get superscript => '上标';

  @override
  String get italic => '斜体';

  @override
  String get underline => '下划线';

  @override
  String get strikeThrough => '删除线';

  @override
  String get inlineCode => '内联代码';

  @override
  String get fontColor => '字体颜色';

  @override
  String get backgroundColor => '背景颜色';

  @override
  String get clearFormat => '清除格式';

  @override
  String get alignLeft => '左对齐';

  @override
  String get alignCenter => '居中对齐';

  @override
  String get alignRight => '右对齐';

  @override
  String get alignJustify => '两端对齐';

  @override
  String get justifyWinWidth => '两端对齐';

  @override
  String get textDirection => '文本方向';

  @override
  String get headerStyle => '标题样式';

  @override
  String get normal => '正文';

  @override
  String get heading1 => '一级标题';

  @override
  String get heading2 => '二级标题';

  @override
  String get heading3 => '三级标题';

  @override
  String get heading4 => '四级标题';

  @override
  String get heading5 => '五级标题';

  @override
  String get heading6 => '六级标题';

  @override
  String get numberedList => '有序列表';

  @override
  String get bulletList => '无序列表';

  @override
  String get checkedList => '任务列表';

  @override
  String get codeBlock => '代码块';

  @override
  String get quote => '引言';

  @override
  String get increaseIndent => '增加缩进';

  @override
  String get decreaseIndent => '减少缩进';

  @override
  String get insertURL => '插入链接';

  @override
  String get visitLink => '访问链接';

  @override
  String get enterLink => '输入链接';

  @override
  String get enterMedia => '输入媒体';

  @override
  String get edit => '编辑';

  @override
  String get apply => '应用';

  @override
  String get hex => '十六进制';

  @override
  String get material => 'Material 设计';

  @override
  String get color => '颜色';

  @override
  String get lineheight => '行高';

  @override
  String get findText => '搜索文本';

  @override
  String get moveToPreviousOccurrence => '上一个匹配项';

  @override
  String get moveToNextOccurrence => '下一个匹配项';

  @override
  String get savedUsingTheNetwork => '通过网络保存';

  @override
  String get savedUsingLocalStorage => '使用本地存储保存';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return '图片已保存到: $imagePath';
  }

  @override
  String get errorWhileSavingImage => '保存图像时发生错误';

  @override
  String get pleaseEnterTextForYourLink => '如\'了解更多\'';

  @override
  String get pleaseEnterTheLinkURL => '如\'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => '请输入有效的图像URL';

  @override
  String get pleaseEnterAValidVideoURL => '请输入有效的视频URL';

  @override
  String get photo => '照片';

  @override
  String get image => '图像';

  @override
  String get caseSensitivityAndWholeWordSearch => '区分大小写和整词搜索';

  @override
  String get caseSensitive => '区分大小写';

  @override
  String get wholeWord => '完整单词';

  @override
  String get insertImage => '插入图像';

  @override
  String get pickAPhotoFromYourGallery => '从相册选取图片';

  @override
  String get takeAPhotoUsingYourCamera => '使用相机拍照';

  @override
  String get pasteAPhotoUsingALink => '从链接获取图片';

  @override
  String get pickAVideoFromYourGallery => '从相册选取视频';

  @override
  String get recordAVideoUsingYourCamera => '使用相机录制';

  @override
  String get pasteAVideoUsingALink => '从链接获取视频';

  @override
  String get close => '关闭';

  @override
  String get searchSettings => '搜索设置';

  @override
  String get cut => '剪切';

  @override
  String get paste => '粘贴';

  @override
  String get insertTable => '插入表格';
}

/// The translations for Chinese, as used in Hong Kong (`zh_HK`).
class FlutterQuillLocalizationsZhHk extends FlutterQuillLocalizationsZh {
  FlutterQuillLocalizationsZhHk() : super('zh_HK');

  @override
  String get pasteLink => '貼上連結';

  @override
  String get ok => '確定';

  @override
  String get selectColor => '選擇顏色';

  @override
  String get gallery => '圖片庫';

  @override
  String get link => '連結';

  @override
  String get open => '開啓';

  @override
  String get copy => '複製';

  @override
  String get remove => '移除';

  @override
  String get save => '儲存';

  @override
  String get zoom => '放大';

  @override
  String get saved => '已儲存';

  @override
  String get text => '文字';

  @override
  String get resize => '變更大小';

  @override
  String get width => '寛';

  @override
  String get height => '高';

  @override
  String get size => '大小';

  @override
  String get small => '小';

  @override
  String get large => '大';

  @override
  String get huge => '超大';

  @override
  String get clear => '清除';

  @override
  String get font => '字型';

  @override
  String get search => '搜尋';

  @override
  String get camera => '相機';

  @override
  String get video => '錄影';

  @override
  String get undo => '撤銷';

  @override
  String get redo => '重做';

  @override
  String get fontFamily => '字體';

  @override
  String get fontSize => '字號';

  @override
  String get bold => '粗體';

  @override
  String get subscript => '下標';

  @override
  String get superscript => '上標';

  @override
  String get italic => '斜體';

  @override
  String get underline => '下劃線';

  @override
  String get strikeThrough => '刪除線';

  @override
  String get inlineCode => '內聯代碼';

  @override
  String get fontColor => '字體顏色';

  @override
  String get backgroundColor => '背景顏色';

  @override
  String get clearFormat => '清除格式';

  @override
  String get alignLeft => '左對齊';

  @override
  String get alignCenter => '居中對齊';

  @override
  String get alignRight => '右對齊';

  @override
  String get alignJustify => '兩端對齊';

  @override
  String get justifyWinWidth => '兩端對齊';

  @override
  String get textDirection => '文本方向';

  @override
  String get headerStyle => '標題樣式';

  @override
  String get normal => '正常';

  @override
  String get heading1 => '標題 1';

  @override
  String get heading2 => '標題 2';

  @override
  String get heading3 => '標題 3';

  @override
  String get heading4 => '標題 4';

  @override
  String get heading5 => '標題 5';

  @override
  String get heading6 => '標題 6';

  @override
  String get numberedList => '有序列表';

  @override
  String get bulletList => '無序列表';

  @override
  String get checkedList => '任務列表';

  @override
  String get codeBlock => '代碼塊';

  @override
  String get quote => '引言';

  @override
  String get increaseIndent => '增加縮進';

  @override
  String get decreaseIndent => '減少縮進';

  @override
  String get insertURL => '插入鏈接';

  @override
  String get visitLink => '訪問鏈接';

  @override
  String get enterLink => '輸入鏈接';

  @override
  String get enterMedia => '輸入媒體';

  @override
  String get edit => '編輯';

  @override
  String get apply => '應用';

  @override
  String get hex => '十六進制';

  @override
  String get material => '物料';

  @override
  String get color => '顏色';

  @override
  String get lineheight => '行高';

  @override
  String get findText => '搜尋文本';

  @override
  String get moveToPreviousOccurrence => '上一個匹配項';

  @override
  String get moveToNextOccurrence => '下一個匹配項';

  @override
  String get savedUsingTheNetwork => '通過網絡保存';

  @override
  String get savedUsingLocalStorage => '使用本地存儲保存';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return '圖片已儲存於: $imagePath';
  }

  @override
  String get errorWhileSavingImage => '保存圖像時發生錯誤';

  @override
  String get pleaseEnterTextForYourLink => '例如，\'了解更多\'';

  @override
  String get pleaseEnterTheLinkURL => '例如，\'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => '請輸入有效的圖像URL';

  @override
  String get pleaseEnterAValidVideoURL => '請輸入有效的視頻URL';

  @override
  String get photo => '照片';

  @override
  String get image => '圖像';

  @override
  String get caseSensitivityAndWholeWordSearch => '區分大小寫和整詞搜索';

  @override
  String get caseSensitive => '區分大小寫';

  @override
  String get wholeWord => '完整字詞';

  @override
  String get insertImage => '插入圖像';

  @override
  String get pickAPhotoFromYourGallery => '從相簿中選擇照片';

  @override
  String get takeAPhotoUsingYourCamera => '使用相機拍攝照片';

  @override
  String get pasteAPhotoUsingALink => '使用連結貼上照片';

  @override
  String get pickAVideoFromYourGallery => '從相簿中選擇影片';

  @override
  String get recordAVideoUsingYourCamera => '使用相機錄製影片';

  @override
  String get pasteAVideoUsingALink => '使用連結貼上影片';

  @override
  String get close => '關閉';

  @override
  String get searchSettings => '搜尋設定';

  @override
  String get cut => '剪切';

  @override
  String get paste => '貼上';

  @override
  String get insertTable => '插入表格';
}
