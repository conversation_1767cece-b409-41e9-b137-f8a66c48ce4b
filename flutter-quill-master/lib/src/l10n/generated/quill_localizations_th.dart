import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Thai (`th`).
class FlutterQuillLocalizationsTh extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsTh([String locale = 'th']) : super(locale);

  @override
  String get pasteLink => 'วางลิงก์';

  @override
  String get ok => 'ตกลง';

  @override
  String get selectColor => 'เลือกสี';

  @override
  String get gallery => 'แกลเลอรี';

  @override
  String get link => 'ลิงก์';

  @override
  String get open => 'เปิด';

  @override
  String get copy => 'คัดลอก';

  @override
  String get remove => 'ลบ';

  @override
  String get save => 'บันทึก';

  @override
  String get zoom => 'ขยาย';

  @override
  String get saved => 'บันทึกแล้ว';

  @override
  String get text => 'ข้อความ';

  @override
  String get resize => 'ปรับขนาด';

  @override
  String get width => 'ความกว้าง';

  @override
  String get height => 'ความสูง';

  @override
  String get size => 'ขนาด';

  @override
  String get small => 'เล็ก';

  @override
  String get large => 'ใหญ่';

  @override
  String get huge => 'ใหญ่มาก';

  @override
  String get clear => 'ล้าง';

  @override
  String get font => 'แบบอักษร';

  @override
  String get search => 'ค้นหา';

  @override
  String get camera => 'กล้อง';

  @override
  String get video => 'วิดีโอ';

  @override
  String get undo => 'เลิกทำ';

  @override
  String get redo => 'ทำซ้ำ';

  @override
  String get fontFamily => 'ตระกูลแบบอักษร';

  @override
  String get fontSize => 'ขนาดแบบอักษร';

  @override
  String get bold => 'ตัวหนา';

  @override
  String get subscript => 'ตัวห้อย';

  @override
  String get superscript => 'ตัวยก';

  @override
  String get italic => 'ตัวเอียง';

  @override
  String get underline => 'ขีดเส้นใต้';

  @override
  String get strikeThrough => 'ขีดฆ่า';

  @override
  String get inlineCode => 'โค้ดแบบอินไลน์';

  @override
  String get fontColor => 'สีแบบอักษร';

  @override
  String get backgroundColor => 'สีพื้นหลัง';

  @override
  String get clearFormat => 'ล้างการจัดรูปแบบ';

  @override
  String get alignLeft => 'จัดชิดซ้าย';

  @override
  String get alignCenter => 'จัดกึ่งกลาง';

  @override
  String get alignRight => 'จัดชิดขวา';

  @override
  String get alignJustify => 'จัดเต็มแนว';

  @override
  String get justifyWinWidth => 'จัดเต็มความกว้างหน้าต่าง';

  @override
  String get textDirection => 'ทิศทางข้อความ';

  @override
  String get headerStyle => 'สไตล์หัวข้อ';

  @override
  String get normal => 'ปกติ';

  @override
  String get heading1 => 'หัวข้อ 1';

  @override
  String get heading2 => 'หัวข้อ 2';

  @override
  String get heading3 => 'หัวข้อ 3';

  @override
  String get heading4 => 'หัวข้อ 4';

  @override
  String get heading5 => 'หัวข้อ 5';

  @override
  String get heading6 => 'หัวข้อ 6';

  @override
  String get numberedList => 'รายการแบบตัวเลข';

  @override
  String get bulletList => 'รายการแบบสัญลักษณ์';

  @override
  String get checkedList => 'รายการแบบเช็ค';

  @override
  String get codeBlock => 'บล็อกโค้ด';

  @override
  String get quote => 'อ้างอิง';

  @override
  String get increaseIndent => 'เพิ่มการเยื้อง';

  @override
  String get decreaseIndent => 'ลดการเยื้อง';

  @override
  String get insertURL => 'แทรก URL';

  @override
  String get visitLink => 'เยี่ยมชมลิงก์';

  @override
  String get enterLink => 'ใส่ลิงก์';

  @override
  String get enterMedia => 'ใส่สื่อ';

  @override
  String get edit => 'แก้ไข';

  @override
  String get apply => 'นำไปใช้';

  @override
  String get hex => 'เลขฐานสิบหก';

  @override
  String get material => 'วัสดุ';

  @override
  String get color => 'สี';

  @override
  String get lineheight => 'ความสูงบรรทัด';

  @override
  String get findText => 'ค้นหาข้อความ';

  @override
  String get moveToPreviousOccurrence => 'ย้ายไปยังการปรากฏก่อนหน้า';

  @override
  String get moveToNextOccurrence => 'ย้ายไปยังการปรากฏถัดไป';

  @override
  String get savedUsingTheNetwork => 'บันทึกโดยใช้เครือข่าย';

  @override
  String get savedUsingLocalStorage => 'บันทึกโดยใช้พื้นที่จัดเก็บในเครื่อง';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'รูปภาพถูกบันทึกที่: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'เกิดข้อผิดพลาดขณะบันทึกรูปภาพ';

  @override
  String get pleaseEnterTextForYourLink =>
      'กรุณาใส่ข้อความสำหรับลิงก์ของคุณ (เช่น \'เรียนรู้เพิ่มเติม\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'กรุณาใส่ URL ของลิงก์ (เช่น \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'กรุณาใส่ URL รูปภาพที่ถูกต้อง';

  @override
  String get pleaseEnterAValidVideoURL => 'กรุณาใส่ URL วิดีโอที่ถูกต้อง';

  @override
  String get photo => 'รูปถ่าย';

  @override
  String get image => 'รูปภาพ';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'การค้นหาแบบคำนึงถึงตัวพิมพ์ใหญ่-เล็กและคำเต็ม';

  @override
  String get caseSensitive => 'คำนึงถึงตัวพิมพ์ใหญ่-เล็ก';

  @override
  String get wholeWord => 'คำเต็ม';

  @override
  String get insertImage => 'แทรกรูปภาพ';

  @override
  String get pickAPhotoFromYourGallery => 'เลือกรูปถ่ายจากแกลเลอรีของคุณ';

  @override
  String get takeAPhotoUsingYourCamera => 'ถ่ายรูปโดยใช้กล้องของคุณ';

  @override
  String get pasteAPhotoUsingALink => 'วางรูปถ่ายโดยใช้ลิงก์';

  @override
  String get pickAVideoFromYourGallery => 'เลือกวิดีโอจากแกลเลอรีของคุณ';

  @override
  String get recordAVideoUsingYourCamera => 'บันทึกวิดีโอโดยใช้กล้องของคุณ';

  @override
  String get pasteAVideoUsingALink => 'วางวิดีโอโดยใช้ลิงก์';

  @override
  String get close => 'ปิด';

  @override
  String get searchSettings => 'การตั้งค่าการค้นหา';

  @override
  String get cut => 'ตัด';

  @override
  String get paste => 'วาง';

  @override
  String get insertTable => 'แทรกตาราง';

  @override
  String get insertVideo => 'Insert video';
}
