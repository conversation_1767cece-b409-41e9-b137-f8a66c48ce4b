import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Catalan Valencian (`ca`).
class FlutterQuillLocalizationsCa extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsCa([String locale = 'ca']) : super(locale);

  @override
  String get pasteLink => 'Enganxa un enllaç';

  @override
  String get ok => 'D\'acord';

  @override
  String get selectColor => 'Selecciona el color';

  @override
  String get gallery => 'Galeria';

  @override
  String get link => 'Enllaç';

  @override
  String get open => 'Obre';

  @override
  String get copy => 'Copia';

  @override
  String get remove => 'Elimina';

  @override
  String get save => 'Desa';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Desat';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Redimensiona';

  @override
  String get width => 'Amplada';

  @override
  String get height => 'Alçada';

  @override
  String get size => 'Mida';

  @override
  String get small => 'Petit';

  @override
  String get large => 'Gran';

  @override
  String get huge => 'Enorme';

  @override
  String get clear => 'Neteja';

  @override
  String get font => 'Font';

  @override
  String get search => 'Cerca';

  @override
  String get camera => 'Càmera';

  @override
  String get video => 'Vídeo';

  @override
  String get undo => 'Desfés';

  @override
  String get redo => 'Refés';

  @override
  String get fontFamily => 'Tipus de lletra';

  @override
  String get fontSize => 'Mida de lletra';

  @override
  String get bold => 'Negreta';

  @override
  String get subscript => 'Subíndex';

  @override
  String get superscript => 'Superíndex';

  @override
  String get italic => 'Cursiva';

  @override
  String get underline => 'Subratllat';

  @override
  String get strikeThrough => 'Ratllat';

  @override
  String get inlineCode => 'Codi en línia';

  @override
  String get fontColor => 'Color de lletra';

  @override
  String get backgroundColor => 'Color de fons';

  @override
  String get clearFormat => 'Neteja format';

  @override
  String get alignLeft => 'Alinea a l\'esquerra';

  @override
  String get alignCenter => 'Alinea al centre';

  @override
  String get alignRight => 'Alinea a la dreta';

  @override
  String get alignJustify => 'Justifica';

  @override
  String get justifyWinWidth => 'Justifica l\'amplada de la finestra';

  @override
  String get textDirection => 'Direcció del text';

  @override
  String get headerStyle => 'Estil de capçalera';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Capçalera 1';

  @override
  String get heading2 => 'Capçalera 2';

  @override
  String get heading3 => 'Capçalera 3';

  @override
  String get heading4 => 'Capçalera 4';

  @override
  String get heading5 => 'Capçalera 5';

  @override
  String get heading6 => 'Capçalera 6';

  @override
  String get numberedList => 'Llista numerada';

  @override
  String get bulletList => 'Llista de punts';

  @override
  String get checkedList => 'Llista de verificació';

  @override
  String get codeBlock => 'Bloc de codi';

  @override
  String get quote => 'Cita';

  @override
  String get increaseIndent => 'Augmenta la sagnia';

  @override
  String get decreaseIndent => 'Redueix la sagnia';

  @override
  String get insertURL => 'Insereix URL';

  @override
  String get visitLink => 'Visita l\'enllaç';

  @override
  String get enterLink => 'Introdueix l\'enllaç';

  @override
  String get enterMedia => 'Introdueix mitjans';

  @override
  String get edit => 'Edita';

  @override
  String get apply => 'Aplica';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Color';

  @override
  String get lineheight => 'Alçada de línia';

  @override
  String get findText => 'Troba text';

  @override
  String get moveToPreviousOccurrence => 'Mou-te a l\'ocurrència anterior';

  @override
  String get moveToNextOccurrence => 'Mou-te a la següent ocurrència';

  @override
  String get savedUsingTheNetwork => 'Desat utilitzant la xarxa';

  @override
  String get savedUsingLocalStorage => 'Desat en l\'emmagatzematge local';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'La imatge s\'ha desat a: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error en desar la imatge';

  @override
  String get pleaseEnterTextForYourLink =>
      'Si us plau, introdueix un text per al teu enllaç (p. ex., \'Saber més\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Si us plau, introdueix l\'URL de l\'enllaç (p. ex., \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Si us plau, introdueix un URL d\'imatge vàlid';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Si us plau, introdueix un URL de vídeo vàlid';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Imatge';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilitat a majúscules i minúscules i cerca de paraula completa';

  @override
  String get caseSensitive => 'Distingir majúscules i minúscules';

  @override
  String get wholeWord => 'Paraula completa';

  @override
  String get insertImage => 'Insereix imatge';

  @override
  String get pickAPhotoFromYourGallery => 'Tria una foto de la teva galeria';

  @override
  String get takeAPhotoUsingYourCamera => 'Fes una foto amb la teva càmera';

  @override
  String get pasteAPhotoUsingALink => 'Enganxa una foto mitjançant un enllaç';

  @override
  String get pickAVideoFromYourGallery => 'Tria un vídeo de la teva galeria';

  @override
  String get recordAVideoUsingYourCamera => 'Grava un vídeo amb la teva càmera';

  @override
  String get pasteAVideoUsingALink => 'Enganxa un vídeo mitjançant un enllaç';

  @override
  String get close => 'Tanca';

  @override
  String get searchSettings => 'Configuració de cerca';

  @override
  String get cut => 'Talla';

  @override
  String get paste => 'Enganxa';

  @override
  String get insertTable => 'Insereix taula';

  @override
  String get insertVideo => 'Insert video';
}
