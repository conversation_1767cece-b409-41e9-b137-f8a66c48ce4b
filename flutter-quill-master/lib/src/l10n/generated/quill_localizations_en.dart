import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class FlutterQuillLocalizationsEn extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get pasteLink => 'Paste a link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Select Color';

  @override
  String get gallery => 'Gallery';

  @override
  String get link => 'Link';

  @override
  String get open => 'Open';

  @override
  String get copy => 'Copy';

  @override
  String get remove => 'Remove';

  @override
  String get save => 'Save';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Saved';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Resize';

  @override
  String get width => 'Width';

  @override
  String get height => 'Height';

  @override
  String get size => 'Size';

  @override
  String get small => 'Small';

  @override
  String get large => 'Large';

  @override
  String get huge => 'Huge';

  @override
  String get clear => 'Clear';

  @override
  String get font => 'Font';

  @override
  String get search => 'Search';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Font family';

  @override
  String get fontSize => 'Font size';

  @override
  String get bold => 'Bold';

  @override
  String get subscript => 'Subscript';

  @override
  String get superscript => 'Superscript';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get strikeThrough => 'Strike through';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Font color';

  @override
  String get backgroundColor => 'Background color';

  @override
  String get clearFormat => 'Clear format';

  @override
  String get alignLeft => 'Align left';

  @override
  String get alignCenter => 'Align center';

  @override
  String get alignRight => 'Align right';

  @override
  String get alignJustify => 'Align justify';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Text direction';

  @override
  String get headerStyle => 'Header style';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Heading 1';

  @override
  String get heading2 => 'Heading 2';

  @override
  String get heading3 => 'Heading 3';

  @override
  String get heading4 => 'Heading 4';

  @override
  String get heading5 => 'Heading 5';

  @override
  String get heading6 => 'Heading 6';

  @override
  String get numberedList => 'Numbered list';

  @override
  String get bulletList => 'Bullet list';

  @override
  String get checkedList => 'Checked list';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Quote';

  @override
  String get increaseIndent => 'Increase indent';

  @override
  String get decreaseIndent => 'Decrease indent';

  @override
  String get insertURL => 'Insert URL';

  @override
  String get visitLink => 'Visit link';

  @override
  String get enterLink => 'Enter link';

  @override
  String get enterMedia => 'Enter media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Apply';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Color';

  @override
  String get lineheight => 'Line height';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Saved using the network';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'The image has been saved at: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink =>
      'Please enter a text for your link (e.g., \'Learn more\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Please enter the link URL (e.g., \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL => 'Please enter a valid video url';

  @override
  String get photo => 'Photo';

  @override
  String get image => 'Image';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Case sensitivity and whole word search';

  @override
  String get caseSensitive => 'Case sensitive';

  @override
  String get wholeWord => 'Whole word';

  @override
  String get insertImage => 'Insert image';

  @override
  String get pickAPhotoFromYourGallery => 'Pick a photo from your gallery';

  @override
  String get takeAPhotoUsingYourCamera => 'Take a photo using your camera';

  @override
  String get pasteAPhotoUsingALink => 'Paste a photo using a link';

  @override
  String get pickAVideoFromYourGallery => 'Pick a video from your gallery';

  @override
  String get recordAVideoUsingYourCamera => 'Record a video using your camera';

  @override
  String get pasteAVideoUsingALink => 'Paste a video using a link';

  @override
  String get close => 'Close';

  @override
  String get searchSettings => 'Search settings';

  @override
  String get cut => 'Cut';

  @override
  String get paste => 'Paste';

  @override
  String get insertTable => 'Insert table';

  @override
  String get insertVideo => 'Insert video';
}

/// The translations for English, as used in the United States (`en_US`).
class FlutterQuillLocalizationsEnUs extends FlutterQuillLocalizationsEn {
  FlutterQuillLocalizationsEnUs() : super('en_US');

  @override
  String get pasteLink => 'Paste a link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Select Color';

  @override
  String get gallery => 'Gallery';

  @override
  String get link => 'Link';

  @override
  String get open => 'Open';

  @override
  String get copy => 'Copy';

  @override
  String get remove => 'Remove';

  @override
  String get save => 'Save';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Saved';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Resize';

  @override
  String get width => 'Width';

  @override
  String get height => 'Height';

  @override
  String get size => 'Size';

  @override
  String get small => 'Small';

  @override
  String get large => 'Large';

  @override
  String get huge => 'Huge';

  @override
  String get clear => 'Clear';

  @override
  String get font => 'Font';

  @override
  String get search => 'Search';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Font family';

  @override
  String get fontSize => 'Font size';

  @override
  String get bold => 'Bold';

  @override
  String get subscript => 'Subscript';

  @override
  String get superscript => 'Superscript';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get strikeThrough => 'Strike through';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Font color';

  @override
  String get backgroundColor => 'Background color';

  @override
  String get clearFormat => 'Clear format';

  @override
  String get alignLeft => 'Align left';

  @override
  String get alignCenter => 'Align center';

  @override
  String get alignRight => 'Align right';

  @override
  String get alignJustify => 'Justify text';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Text direction';

  @override
  String get headerStyle => 'Header style';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Heading 1';

  @override
  String get heading2 => 'Heading 2';

  @override
  String get heading3 => 'Heading 3';

  @override
  String get heading4 => 'Heading 4';

  @override
  String get heading5 => 'Heading 5';

  @override
  String get heading6 => 'Heading 6';

  @override
  String get numberedList => 'Numbered list';

  @override
  String get bulletList => 'Bullet list';

  @override
  String get checkedList => 'Checked list';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Quote';

  @override
  String get increaseIndent => 'Increase indent';

  @override
  String get decreaseIndent => 'Decrease indent';

  @override
  String get insertURL => 'Insert URL';

  @override
  String get visitLink => 'Visit link';

  @override
  String get enterLink => 'Enter link';

  @override
  String get enterMedia => 'Enter media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Apply';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Color';

  @override
  String get lineheight => 'Line height';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Saved using the network';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'The image has been saved at: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink =>
      'Please enter a text for your link (e.g., \'Learn more\')';

  @override
  String get pleaseEnterTheLinkURL =>
      '(e.g., \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL => 'Please enter a valid video URL';

  @override
  String get photo => 'Photo';

  @override
  String get image => 'Image';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Case sensitivity and whole word search';

  @override
  String get caseSensitive => 'Case sensitive';

  @override
  String get wholeWord => 'Whole word';

  @override
  String get insertImage => 'Insert Image';

  @override
  String get pickAPhotoFromYourGallery => 'Pick a photo from your gallery';

  @override
  String get takeAPhotoUsingYourCamera => 'Take a photo using your camera';

  @override
  String get pasteAPhotoUsingALink => 'Paste a photo using a link';

  @override
  String get pickAVideoFromYourGallery => 'Pick a video from your gallery';

  @override
  String get recordAVideoUsingYourCamera => 'Record a video using your camera';

  @override
  String get pasteAVideoUsingALink => 'Paste a video using a link';

  @override
  String get close => 'Close';

  @override
  String get searchSettings => 'Search settings';

  @override
  String get cut => 'Cut';

  @override
  String get paste => 'Paste';

  @override
  String get insertTable => 'Insert table';
}
