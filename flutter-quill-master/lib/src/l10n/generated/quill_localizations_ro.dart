import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Romanian Moldavian Moldovan (`ro`).
class FlutterQuillLocalizationsRo extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsRo([String locale = 'ro']) : super(locale);

  @override
  String get pasteLink => 'Lipește un link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Selectează culoarea';

  @override
  String get gallery => 'Galerie';

  @override
  String get link => 'Link';

  @override
  String get open => 'Deschide';

  @override
  String get copy => 'Copiază';

  @override
  String get remove => 'Elimină';

  @override
  String get save => 'Salvează';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Salvat';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Redimensionează';

  @override
  String get width => 'Lățime';

  @override
  String get height => 'Înălțime';

  @override
  String get size => 'Mărime';

  @override
  String get small => 'Mic';

  @override
  String get large => 'Mare';

  @override
  String get huge => 'Enorm';

  @override
  String get clear => 'Șterge';

  @override
  String get font => 'Font';

  @override
  String get search => 'Caută';

  @override
  String get camera => 'Cameră';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Anulează';

  @override
  String get redo => 'Refă';

  @override
  String get fontFamily => 'Familie de fonturi';

  @override
  String get fontSize => 'Dimensiune font';

  @override
  String get bold => 'Îngroșat';

  @override
  String get subscript => 'Indice inferior';

  @override
  String get superscript => 'Indice superior';

  @override
  String get italic => 'Cursiv';

  @override
  String get underline => 'Subliniat';

  @override
  String get strikeThrough => 'Tăiat';

  @override
  String get inlineCode => 'Cod în linie';

  @override
  String get fontColor => 'Culoare font';

  @override
  String get backgroundColor => 'Culoare fundal';

  @override
  String get clearFormat => 'Șterge formatul';

  @override
  String get alignLeft => 'Aliniază la stânga';

  @override
  String get alignCenter => 'Aliniază în centru';

  @override
  String get alignRight => 'Aliniază la dreapta';

  @override
  String get alignJustify => 'Justificare';

  @override
  String get justifyWinWidth => 'Justifică lățimea ferestrei';

  @override
  String get textDirection => 'Direcție text';

  @override
  String get headerStyle => 'Stil antet';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Titlu 1';

  @override
  String get heading2 => 'Titlu 2';

  @override
  String get heading3 => 'Titlu 3';

  @override
  String get heading4 => 'Titlu 4';

  @override
  String get heading5 => 'Titlu 5';

  @override
  String get heading6 => 'Titlu 6';

  @override
  String get numberedList => 'Listă numerotată';

  @override
  String get bulletList => 'Listă cu buline';

  @override
  String get checkedList => 'Listă cu bifă';

  @override
  String get codeBlock => 'Bloc de cod';

  @override
  String get quote => 'Citat';

  @override
  String get increaseIndent => 'Crește indentarea';

  @override
  String get decreaseIndent => 'Scade indentarea';

  @override
  String get insertURL => 'Introdu URL-ul';

  @override
  String get visitLink => 'Vizitează link-ul';

  @override
  String get enterLink => 'Introdu link-ul';

  @override
  String get enterMedia => 'Introdu media';

  @override
  String get edit => 'Editează';

  @override
  String get apply => 'Aplică';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Culoare';

  @override
  String get lineheight => 'Inaltimea liniei';

  @override
  String get findText => 'Găsește text';

  @override
  String get moveToPreviousOccurrence => 'Mutați la apariția anterioară';

  @override
  String get moveToNextOccurrence => 'Mutați la apariția următoare';

  @override
  String get savedUsingTheNetwork => 'Salvat cu ajutorul rețelei';

  @override
  String get savedUsingLocalStorage => 'Salvat în stocarea locală';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Imaginea a fost salvată la: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Eroare la salvarea imaginii';

  @override
  String get pleaseEnterTextForYourLink =>
      'Vă rugăm să introduceți un text pentru link-ul dvs. (de exemplu, \'Aflați mai multe\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Vă rugăm să introduceți URL-ul link-ului (de exemplu, \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Vă rugăm să introduceți un URL de imagine valid';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Vă rugăm să introduceți un URL de video valid';

  @override
  String get photo => 'Fotografie';

  @override
  String get image => 'Imagine';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilitate la majuscule și căutare cuvânt întreg';

  @override
  String get caseSensitive => 'Sensibilitate la majuscule și minuscule';

  @override
  String get wholeWord => 'Cuvânt întreg';

  @override
  String get insertImage => 'Introdu imaginea';

  @override
  String get pickAPhotoFromYourGallery => 'Alege o fotografie din galeria ta';

  @override
  String get takeAPhotoUsingYourCamera => 'Fă o fotografie folosind camera ta';

  @override
  String get pasteAPhotoUsingALink => 'Lipește o fotografie folosind un link';

  @override
  String get pickAVideoFromYourGallery => 'Alege un video din galeria ta';

  @override
  String get recordAVideoUsingYourCamera =>
      'Înregistrează un video folosind camera ta';

  @override
  String get pasteAVideoUsingALink => 'Lipește un video folosind un link';

  @override
  String get close => 'Închide';

  @override
  String get searchSettings => 'Setări de căutare';

  @override
  String get cut => 'Tăia';

  @override
  String get paste => 'Lipire';

  @override
  String get insertTable => 'Inserare tabel';

  @override
  String get insertVideo => 'Insert video';
}

/// The translations for Romanian Moldavian Moldovan, as used in Romania (`ro_RO`).
class FlutterQuillLocalizationsRoRo extends FlutterQuillLocalizationsRo {
  FlutterQuillLocalizationsRoRo() : super('ro_RO');

  @override
  String get pasteLink => 'Lipește un link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Selectează culoarea';

  @override
  String get gallery => 'Galerie';

  @override
  String get link => 'Link';

  @override
  String get open => 'Deschide';

  @override
  String get copy => 'Copiază';

  @override
  String get remove => 'Elimină';

  @override
  String get save => 'Salvează';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Salvat';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Redimensionează';

  @override
  String get width => 'Lățime';

  @override
  String get height => 'Înălțime';

  @override
  String get size => 'Mărime';

  @override
  String get small => 'Mic';

  @override
  String get large => 'Mare';

  @override
  String get huge => 'Enorm';

  @override
  String get clear => 'Șterge';

  @override
  String get font => 'Font';

  @override
  String get search => 'Caută';

  @override
  String get camera => 'Cameră';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Anulează';

  @override
  String get redo => 'Refă';

  @override
  String get fontFamily => 'Familie de fonturi';

  @override
  String get fontSize => 'Dimensiune font';

  @override
  String get bold => 'Îngroșat';

  @override
  String get subscript => 'Indice inferior';

  @override
  String get superscript => 'Indice superior';

  @override
  String get italic => 'Cursiv';

  @override
  String get underline => 'Subliniat';

  @override
  String get strikeThrough => 'Tăiat';

  @override
  String get inlineCode => 'Cod în linie';

  @override
  String get fontColor => 'Culoare font';

  @override
  String get backgroundColor => 'Culoare fundal';

  @override
  String get clearFormat => 'Șterge formatul';

  @override
  String get alignLeft => 'Aliniază la stânga';

  @override
  String get alignCenter => 'Aliniază în centru';

  @override
  String get alignRight => 'Aliniază la dreapta';

  @override
  String get alignJustify => 'Justificare';

  @override
  String get justifyWinWidth => 'Justifică lățimea ferestrei';

  @override
  String get textDirection => 'Direcție text';

  @override
  String get headerStyle => 'Stil antet';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Titlu 1';

  @override
  String get heading2 => 'Titlu 2';

  @override
  String get heading3 => 'Titlu 3';

  @override
  String get heading4 => 'Titlu 4';

  @override
  String get heading5 => 'Titlu 5';

  @override
  String get heading6 => 'Titlu 6';

  @override
  String get numberedList => 'Listă numerotată';

  @override
  String get bulletList => 'Listă cu buline';

  @override
  String get checkedList => 'Listă cu bifă';

  @override
  String get codeBlock => 'Bloc de cod';

  @override
  String get quote => 'Citat';

  @override
  String get increaseIndent => 'Crește indentarea';

  @override
  String get decreaseIndent => 'Scade indentarea';

  @override
  String get insertURL => 'Introdu URL-ul';

  @override
  String get visitLink => 'Vizitează link-ul';

  @override
  String get enterLink => 'Introdu link-ul';

  @override
  String get enterMedia => 'Introdu media';

  @override
  String get edit => 'Editează';

  @override
  String get apply => 'Aplică';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Culoare';

  @override
  String get lineheight => 'Inaltimea liniei';

  @override
  String get findText => 'Găsește text';

  @override
  String get moveToPreviousOccurrence => 'Mutați la apariția anterioară';

  @override
  String get moveToNextOccurrence => 'Mutați la apariția următoare';

  @override
  String get savedUsingTheNetwork => 'Salvat cu ajutorul rețelei';

  @override
  String get savedUsingLocalStorage => 'Salvat în stocarea locală';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Imaginea a fost salvată la: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Eroare la salvarea imaginii';

  @override
  String get pleaseEnterTextForYourLink =>
      'Vă rugăm să introduceți un text pentru link-ul dvs. (de exemplu, \'Aflați mai multe\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Vă rugăm să introduceți URL-ul link-ului (de exemplu, \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Vă rugăm să introduceți un URL de imagine valid';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Vă rugăm să introduceți un URL de video valid';

  @override
  String get photo => 'Fotografie';

  @override
  String get image => 'Imagine';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilitate la majuscule și căutare cuvânt întreg';

  @override
  String get caseSensitive => 'Sensibilitate la majuscule și minuscule';

  @override
  String get wholeWord => 'Cuvânt întreg';

  @override
  String get insertImage => 'Introdu imaginea';

  @override
  String get pickAPhotoFromYourGallery => 'Alege o fotografie din galeria ta';

  @override
  String get takeAPhotoUsingYourCamera => 'Fă o fotografie folosind camera ta';

  @override
  String get pasteAPhotoUsingALink => 'Lipește o fotografie folosind un link';

  @override
  String get pickAVideoFromYourGallery => 'Alege un video din galeria ta';

  @override
  String get recordAVideoUsingYourCamera =>
      'Înregistrează un video folosind camera ta';

  @override
  String get pasteAVideoUsingALink => 'Lipește un video folosind un link';

  @override
  String get close => 'Închide';

  @override
  String get searchSettings => 'Setări de căutare';

  @override
  String get cut => 'Tăia';

  @override
  String get paste => 'Lipire';

  @override
  String get insertTable => 'Inserare tabel';
}
