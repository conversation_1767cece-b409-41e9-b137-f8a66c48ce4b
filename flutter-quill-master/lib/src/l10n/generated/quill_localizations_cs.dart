import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Czech (`cs`).
class FlutterQuillLocalizationsCs extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsCs([String locale = 'cs']) : super(locale);

  @override
  String get pasteLink => 'Vložit odkaz';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Vybrat barvu';

  @override
  String get gallery => 'Galerie';

  @override
  String get link => 'Odkaz';

  @override
  String get open => 'Otevřít';

  @override
  String get copy => 'Kopírovat';

  @override
  String get remove => 'Odstranit';

  @override
  String get save => 'Uložit';

  @override
  String get zoom => 'Přiblížit';

  @override
  String get saved => 'Uloženo';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Změnit velikost';

  @override
  String get width => 'Šířka';

  @override
  String get height => 'Výška';

  @override
  String get size => 'Velikost';

  @override
  String get small => 'Malý';

  @override
  String get large => 'Velký';

  @override
  String get huge => 'Obrovský';

  @override
  String get clear => 'Smazat';

  @override
  String get font => 'Písmo';

  @override
  String get search => 'Hledat';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Zpět';

  @override
  String get redo => 'Znovu';

  @override
  String get fontFamily => 'Rodina písma';

  @override
  String get fontSize => 'Velikost písma';

  @override
  String get bold => 'Tučné';

  @override
  String get subscript => 'Dolní index';

  @override
  String get superscript => 'Horní index';

  @override
  String get italic => 'Kurzíva';

  @override
  String get underline => 'Podtržení';

  @override
  String get strikeThrough => 'Přeškrtnuté';

  @override
  String get inlineCode => 'Inline kód';

  @override
  String get fontColor => 'Barva písma';

  @override
  String get backgroundColor => 'Barva pozadí';

  @override
  String get clearFormat => 'Vymazat formátování';

  @override
  String get alignLeft => 'Zarovnat vlevo';

  @override
  String get alignCenter => 'Zarovnat na střed';

  @override
  String get alignRight => 'Zarovnat vpravo';

  @override
  String get alignJustify => 'Zarovnat do bloku';

  @override
  String get justifyWinWidth => 'Zarovnat do bloku';

  @override
  String get textDirection => 'Směr textu';

  @override
  String get headerStyle => 'Styl záhlaví';

  @override
  String get normal => 'Normální text';

  @override
  String get heading1 => 'Nadpis 1';

  @override
  String get heading2 => 'Nadpis 2';

  @override
  String get heading3 => 'Nadpis 3';

  @override
  String get heading4 => 'Nadpis 4';

  @override
  String get heading5 => 'Nadpis 5';

  @override
  String get heading6 => 'Nadpis 6';

  @override
  String get numberedList => 'Číslovaný seznam';

  @override
  String get bulletList => 'Seznam s odrážkami';

  @override
  String get checkedList => 'Seznam s zaškrtávacími políčky';

  @override
  String get codeBlock => 'Blokový kód';

  @override
  String get quote => 'Citace';

  @override
  String get increaseIndent => 'Zvětšit odsazení';

  @override
  String get decreaseIndent => 'Zmenšit odsazení';

  @override
  String get insertURL => 'Vložit URL';

  @override
  String get visitLink => 'Otevřít odkaz';

  @override
  String get enterLink => 'Vložit odkaz';

  @override
  String get enterMedia => 'Vložit média';

  @override
  String get edit => 'Upravit';

  @override
  String get apply => 'Použít';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Barva';

  @override
  String get lineheight => 'Výška čáry';

  @override
  String get findText => 'Najít text';

  @override
  String get moveToPreviousOccurrence => 'Přesunout na předchozí výskyt';

  @override
  String get moveToNextOccurrence => 'Přesunout na následující výskyt';

  @override
  String get savedUsingTheNetwork => 'Uloženo pomocí sítě';

  @override
  String get savedUsingLocalStorage => 'Uloženo lokálně';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Obrázek byl uložen v: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Chyba při ukládání obrázku';

  @override
  String get pleaseEnterTextForYourLink =>
      'Zadejte text pro váš odkaz (např., \'Dozvědět se více\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Zadejte URL odkazu (např., \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Zadejte platnou URL adresu obrázku';

  @override
  String get pleaseEnterAValidVideoURL => 'Zadejte platnou URL adresu videa';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Obrázek';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Citlivost na velká a malá písmena a vyhledávání celého slova';

  @override
  String get caseSensitive => 'Rozlišovat velikost písmen';

  @override
  String get wholeWord => 'Celé slovo';

  @override
  String get insertImage => 'Vložit obrázek';

  @override
  String get pickAPhotoFromYourGallery => 'Vybrat fotku z galerie';

  @override
  String get takeAPhotoUsingYourCamera => 'Použít fotoaparát';

  @override
  String get pasteAPhotoUsingALink => 'Vložit fotografii pomocí odkazu';

  @override
  String get pickAVideoFromYourGallery => 'Vyberte video z galerie';

  @override
  String get recordAVideoUsingYourCamera => 'Natočit video pomocí kamery';

  @override
  String get pasteAVideoUsingALink => 'Vložit video pomocí odkazu';

  @override
  String get close => 'Zavřít';

  @override
  String get searchSettings => 'Nastavení hledání';

  @override
  String get cut => 'Vyjmout';

  @override
  String get paste => 'Vložit';

  @override
  String get insertTable => 'Vložit tabulku';

  @override
  String get insertVideo => 'Insert video';
}
