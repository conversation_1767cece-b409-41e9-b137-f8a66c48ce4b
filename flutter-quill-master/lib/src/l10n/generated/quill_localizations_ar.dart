import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class FlutterQuillLocalizationsAr extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get pasteLink => 'نسخ الرابط';

  @override
  String get ok => 'نعم';

  @override
  String get selectColor => 'اختار اللون';

  @override
  String get gallery => 'المعرض';

  @override
  String get link => 'الرابط';

  @override
  String get open => 'فتح';

  @override
  String get copy => 'نسخ';

  @override
  String get remove => 'إزالة';

  @override
  String get save => 'حفظ';

  @override
  String get zoom => 'تكبير';

  @override
  String get saved => 'تم الحفظ';

  @override
  String get text => 'نص';

  @override
  String get resize => 'تحجيم';

  @override
  String get width => 'عرض';

  @override
  String get height => 'ارتفاع';

  @override
  String get size => 'حجم';

  @override
  String get small => 'صغير';

  @override
  String get large => 'كبير';

  @override
  String get huge => 'ضخم';

  @override
  String get clear => 'تنظيف';

  @override
  String get font => 'خط';

  @override
  String get search => 'بحث';

  @override
  String get camera => 'كاميرا';

  @override
  String get video => 'فيديو';

  @override
  String get undo => 'تراجع';

  @override
  String get redo => 'تقدم';

  @override
  String get fontFamily => 'عائلة الخط';

  @override
  String get fontSize => 'حجم الخط';

  @override
  String get bold => 'عريض';

  @override
  String get subscript => 'نص سفلي';

  @override
  String get superscript => 'نص علوي';

  @override
  String get italic => 'مائل';

  @override
  String get underline => 'تحته خط';

  @override
  String get strikeThrough => 'داخله خط';

  @override
  String get inlineCode => 'كود بوسط السطر';

  @override
  String get fontColor => 'لون الخط';

  @override
  String get backgroundColor => 'لون الخلفية';

  @override
  String get clearFormat => 'تنظيف التنسيق';

  @override
  String get alignLeft => 'محاذاة اليسار';

  @override
  String get alignCenter => 'محاذاة الوسط';

  @override
  String get alignRight => 'محاذاة اليمين';

  @override
  String get alignJustify => 'محاذاة النص';

  @override
  String get justifyWinWidth => 'تبرير مع العرض';

  @override
  String get textDirection => 'اتجاه النص';

  @override
  String get headerStyle => 'ستايل العنوان';

  @override
  String get normal => 'عادي';

  @override
  String get heading1 => 'ترويسة ١';

  @override
  String get heading2 => 'ترويسة ٢';

  @override
  String get heading3 => 'ترويسة ٣';

  @override
  String get heading4 => 'ترويسة ٤';

  @override
  String get heading5 => 'ترويسة ٥';

  @override
  String get heading6 => 'ترويسة ٦';

  @override
  String get numberedList => 'قائمة مرقمة';

  @override
  String get bulletList => 'قائمة منقطة';

  @override
  String get checkedList => 'قائمة للمهام';

  @override
  String get codeBlock => 'كود كامل';

  @override
  String get quote => 'اقتباس';

  @override
  String get increaseIndent => 'زيادة الهامش';

  @override
  String get decreaseIndent => 'تنقيص الهامش';

  @override
  String get insertURL => 'ادخل عنوان رابط';

  @override
  String get visitLink => 'زيارة الرابط';

  @override
  String get enterLink => 'ادخل رابط';

  @override
  String get enterMedia => 'ادخل وسائط';

  @override
  String get edit => 'تعديل';

  @override
  String get apply => 'تطبيق';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'اللون';

  @override
  String get lineheight => 'ارتفاع خط';

  @override
  String get findText => 'بحث عن نص';

  @override
  String get moveToPreviousOccurrence => 'الانتقال إلى الحدث السابق';

  @override
  String get moveToNextOccurrence => 'الانتقال إلى الحدث التالي';

  @override
  String get savedUsingTheNetwork => 'تم الحفظ باستخدام الشبكة';

  @override
  String get savedUsingLocalStorage => 'تم الحفظ باستخدام وحدة التخزين المحلية';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'تم حفظ الصورة في: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'حدث خطأ أثناء حفظ الصورة';

  @override
  String get pleaseEnterTextForYourLink => 'مثال: \'تعلم المزيد\'';

  @override
  String get pleaseEnterTheLinkURL => 'مثال: \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => 'الرجاء إدخال عنوان URL صحيح للصورة';

  @override
  String get pleaseEnterAValidVideoURL => 'الرجاء إدخال عنوان URL صالح للفيديو';

  @override
  String get photo => 'صورة';

  @override
  String get image => 'صورة';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'حالة الحساسية والبحث عن كلمة كاملة';

  @override
  String get caseSensitive => 'حساسية الحالة';

  @override
  String get wholeWord => 'كلمة كاملة';

  @override
  String get insertImage => 'إدراج صورة';

  @override
  String get pickAPhotoFromYourGallery => 'اختيار صورة من معرض الصور';

  @override
  String get takeAPhotoUsingYourCamera => 'التقاط صورة باستخدام الكاميرا';

  @override
  String get pasteAPhotoUsingALink => 'لصق صورة باستخدام رابط';

  @override
  String get pickAVideoFromYourGallery => 'اختيار فيديو من معرض الفيديو';

  @override
  String get recordAVideoUsingYourCamera => 'تسجيل فيديو باستخدام الكاميرا';

  @override
  String get pasteAVideoUsingALink => 'لصق فيديو باستخدام رابط';

  @override
  String get close => 'إغلاق';

  @override
  String get searchSettings => 'إعدادات البحث';

  @override
  String get cut => 'قص';

  @override
  String get paste => 'لصق';

  @override
  String get insertTable => 'إدراج جدول';

  @override
  String get insertVideo => 'Insert video';
}
