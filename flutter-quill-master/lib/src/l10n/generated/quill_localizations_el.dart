import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Modern Greek (`el`).
class FlutterQuillLocalizationsEl extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsEl([String locale = 'el']) : super(locale);

  @override
  String get pasteLink => 'Επικόλληση συνδέσμου';

  @override
  String get ok => 'Εντάξει';

  @override
  String get selectColor => 'Επιλογή χρώματος';

  @override
  String get gallery => 'Συλλογή';

  @override
  String get link => 'Σύνδεσμος';

  @override
  String get open => 'Άνοιγμα';

  @override
  String get copy => 'Αντιγραφή';

  @override
  String get remove => 'Αφαίρεση';

  @override
  String get save => 'Αποθήκευση';

  @override
  String get zoom => 'Μεγέθυνση';

  @override
  String get saved => 'Αποθηκεύτηκε';

  @override
  String get text => 'Κείμενο';

  @override
  String get resize => 'Αλλαγή μεγέθους';

  @override
  String get width => 'Πλάτος';

  @override
  String get height => 'Ύψος';

  @override
  String get size => 'Μέγεθος';

  @override
  String get small => 'Μικρό';

  @override
  String get large => 'Μεγάλο';

  @override
  String get huge => 'Τεράστιο';

  @override
  String get clear => 'Εκκαθάριση';

  @override
  String get font => 'Γραμματοσειρά';

  @override
  String get search => 'Αναζήτηση';

  @override
  String get camera => 'Κάμερα';

  @override
  String get video => 'Βίντεο';

  @override
  String get undo => 'Αναίρεση';

  @override
  String get redo => 'Επανάληψη';

  @override
  String get fontFamily => 'Οικογένεια γραμματοσειράς';

  @override
  String get fontSize => 'Μέγεθος γραμματοσειράς';

  @override
  String get bold => 'Έντονο';

  @override
  String get subscript => 'Δείκτης κάτω';

  @override
  String get superscript => 'Εκθέτης';

  @override
  String get italic => 'Πλάγια';

  @override
  String get underline => 'Υπογράμμιση';

  @override
  String get strikeThrough => 'Διακριτή γραμμή';

  @override
  String get inlineCode => 'Ενσωματωμένος κώδικας';

  @override
  String get fontColor => 'Χρώμα γραμματοσειράς';

  @override
  String get backgroundColor => 'Χρώμα φόντου';

  @override
  String get clearFormat => 'Εκκαθάριση μορφοποίησης';

  @override
  String get alignLeft => 'Στοίχιση αριστερά';

  @override
  String get alignCenter => 'Στοίχιση στο κέντρο';

  @override
  String get alignRight => 'Στοίχιση δεξιά';

  @override
  String get alignJustify => 'Πλήρης στοίχιση';

  @override
  String get justifyWinWidth => 'Πλήρης στοίχιση πλάτους παραθύρου';

  @override
  String get textDirection => 'Κατεύθυνση κειμένου';

  @override
  String get headerStyle => 'Στυλ επικεφαλίδας';

  @override
  String get normal => 'Κανονικό';

  @override
  String get heading1 => 'Επικεφαλίδα 1';

  @override
  String get heading2 => 'Επικεφαλίδα 2';

  @override
  String get heading3 => 'Επικεφαλίδα 3';

  @override
  String get heading4 => 'Επικεφαλίδα 4';

  @override
  String get heading5 => 'Επικεφαλίδα 5';

  @override
  String get heading6 => 'Επικεφαλίδα 6';

  @override
  String get numberedList => 'Αριθμημένη λίστα';

  @override
  String get bulletList => 'Λίστα με κουκκίδες';

  @override
  String get checkedList => 'Λίστα ελέγχου';

  @override
  String get codeBlock => 'Μπλοκ κώδικα';

  @override
  String get quote => 'Παράθεση';

  @override
  String get increaseIndent => 'Αύξηση εσοχής';

  @override
  String get decreaseIndent => 'Μείωση εσοχής';

  @override
  String get insertURL => 'Εισαγωγή URL';

  @override
  String get visitLink => 'Επίσκεψη συνδέσμου';

  @override
  String get enterLink => 'Εισαγωγή συνδέσμου';

  @override
  String get enterMedia => 'Εισαγωγή μέσων';

  @override
  String get edit => 'Επεξεργασία';

  @override
  String get apply => 'Εφαρμογή';

  @override
  String get hex => 'Εξαδικό';

  @override
  String get material => 'Υλικό';

  @override
  String get color => 'Χρώμα';

  @override
  String get lineheight => 'Ύψος γραμμής';

  @override
  String get findText => 'Εύρεση κειμένου';

  @override
  String get moveToPreviousOccurrence => 'Μετακίνηση στην προηγούμενη εμφάνιση';

  @override
  String get moveToNextOccurrence => 'Μετακίνηση στην επόμενη εμφάνιση';

  @override
  String get savedUsingTheNetwork => 'Αποθηκεύτηκε μέσω του δικτύου';

  @override
  String get savedUsingLocalStorage => 'Αποθηκεύτηκε μέσω τοπικής αποθήκευσης';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Η εικόνα αποθηκεύτηκε στη διαδρομή: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Σφάλμα κατά την αποθήκευση της εικόνας';

  @override
  String get pleaseEnterTextForYourLink =>
      'Εισαγάγετε κείμενο για τον σύνδεσμό σας (π.χ., \'Μάθετε περισσότερα\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Εισαγάγετε το URL του συνδέσμου (π.χ., \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Εισαγάγετε έγκυρο URL εικόνας';

  @override
  String get pleaseEnterAValidVideoURL => 'Εισαγάγετε έγκυρο URL βίντεο';

  @override
  String get photo => 'Φωτογραφία';

  @override
  String get image => 'Εικόνα';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Διαχωρισμός πεζών-κεφαλαίων και αναζήτηση ολόκληρης λέξης';

  @override
  String get caseSensitive => 'Διαχωρισμός πεζών-κεφαλαίων';

  @override
  String get wholeWord => 'Ολόκληρη λέξη';

  @override
  String get insertImage => 'Εισαγωγή εικόνας';

  @override
  String get pickAPhotoFromYourGallery =>
      'Επιλέξτε φωτογραφία από τη συλλογή σας';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Τραβήξτε φωτογραφία χρησιμοποιώντας την κάμερα σας';

  @override
  String get pasteAPhotoUsingALink =>
      'Επικολλήστε φωτογραφία χρησιμοποιώντας έναν σύνδεσμο';

  @override
  String get pickAVideoFromYourGallery => 'Επιλέξτε βίντεο από τη συλλογή σας';

  @override
  String get recordAVideoUsingYourCamera =>
      'Καταγράψτε βίντεο χρησιμοποιώντας την κάμερα σας';

  @override
  String get pasteAVideoUsingALink =>
      'Επικολλήστε βίντεο χρησιμοποιώντας έναν σύνδεσμο';

  @override
  String get close => 'Κλείσιμο';

  @override
  String get searchSettings => 'Ρυθμίσεις αναζήτησης';

  @override
  String get cut => 'Αποκοπή';

  @override
  String get paste => 'Επικόλληση';

  @override
  String get insertTable => 'Εισαγωγή πίνακα';

  @override
  String get insertVideo => 'Insert video';
}
