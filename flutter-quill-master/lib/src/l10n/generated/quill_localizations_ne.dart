import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Nepali (`ne`).
class FlutterQuillLocalizationsNe extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsNe([String locale = 'ne']) : super(locale);

  @override
  String get pasteLink => 'लिङ्क पेस्ट गर्नुहोस्';

  @override
  String get ok => 'ठिक छ';

  @override
  String get selectColor => 'रंग छान्नुहोस्';

  @override
  String get gallery => 'ग्यालेरी';

  @override
  String get link => 'लिङ्क';

  @override
  String get open => 'खोल्नुहोस्';

  @override
  String get copy => 'कापी';

  @override
  String get remove => 'हटाउनुहोस्';

  @override
  String get save => 'सेभ';

  @override
  String get zoom => 'जुम गर्नुहोस्';

  @override
  String get saved => 'सेभ गरियो';

  @override
  String get text => 'टेक्स्ट';

  @override
  String get resize => 'आकार परिवर्तन गर्नुहोस्';

  @override
  String get width => 'चौडाइ';

  @override
  String get height => 'उचाइ';

  @override
  String get size => 'आकार';

  @override
  String get small => 'सानो';

  @override
  String get large => 'ठूलो';

  @override
  String get huge => 'विशाल';

  @override
  String get clear => 'खाली गर्नुहोस्';

  @override
  String get font => 'फन्ट';

  @override
  String get search => 'खोज';

  @override
  String get camera => 'क्यामेरा';

  @override
  String get video => 'भिडियो';

  @override
  String get undo => 'पूर्ववत गर्नुहोस्';

  @override
  String get redo => 'पुनः गर्नुहोस्';

  @override
  String get fontFamily => 'फन्ट परिवार';

  @override
  String get fontSize => 'फन्ट साइज';

  @override
  String get bold => 'बोल्ड';

  @override
  String get subscript => 'सदस्यता';

  @override
  String get superscript => 'सुपरलिपि';

  @override
  String get italic => 'इटालिक';

  @override
  String get underline => 'रेखाङ्कन गर्नुहोस्';

  @override
  String get strikeThrough => 'मार्फत स्ट्राइक';

  @override
  String get inlineCode => 'इनलाईन कोड';

  @override
  String get fontColor => 'फन्टको रंग';

  @override
  String get backgroundColor => 'पृष्ठभूमि रङ';

  @override
  String get clearFormat => 'ढाँचा खाली गर्नुहोस्';

  @override
  String get alignLeft => 'बायाँ पङ्क्तिबद्ध';

  @override
  String get alignCenter => 'केन्द्र पङ्क्तिबद्ध';

  @override
  String get alignRight => 'दायाँ पङ्क्तिबद्ध';

  @override
  String get alignJustify => 'सही संरेखण';

  @override
  String get justifyWinWidth => 'जस्टीफ़ी विन चौड़ाई';

  @override
  String get textDirection => 'टेक्स्ट दिशा';

  @override
  String get headerStyle => 'हेडर शैली';

  @override
  String get normal => 'सामान्य';

  @override
  String get heading1 => 'हेडिङ १';

  @override
  String get heading2 => 'हेडिङ २';

  @override
  String get heading3 => 'हेडिङ ३';

  @override
  String get heading4 => 'हेडिङ ४';

  @override
  String get heading5 => 'हेडिङ ५';

  @override
  String get heading6 => 'हेडिङ ६';

  @override
  String get numberedList => 'अंकित सूची';

  @override
  String get bulletList => 'बुलेट सूची';

  @override
  String get checkedList => 'चेक सूची';

  @override
  String get codeBlock => 'कोड ब्लक';

  @override
  String get quote => 'उद्धरण';

  @override
  String get increaseIndent => 'इन्डेन्ट बढाउनुहोस्';

  @override
  String get decreaseIndent => 'इन्डेन्ट घटाउनुहोस्';

  @override
  String get insertURL => 'URL सम्मिलित गर्नुहोस्';

  @override
  String get visitLink => 'लिङ्कमा जानुहोस्';

  @override
  String get enterLink => 'लिङ्क प्रविष्ट गर्नुहोस्';

  @override
  String get enterMedia => 'मिडिया प्रविष्ट गर्नुहोस्';

  @override
  String get edit => 'सम्पादन गर्नुहोस्';

  @override
  String get apply => 'लागू';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'रङ';

  @override
  String get lineheight => 'रेखा-उचाइ';

  @override
  String get findText => 'टेक्स्ट फेला पार्नुहोस्';

  @override
  String get moveToPreviousOccurrence => 'अघिल्लो घटनामा जानुहोस्';

  @override
  String get moveToNextOccurrence => 'अर्को घटनामा सार्नुहोस्';

  @override
  String get savedUsingTheNetwork => 'नेटवर्क प्रयोग गरेर बचत गरियो';

  @override
  String get savedUsingLocalStorage => 'स्थानीय भण्डारण प्रयोग गरेर बचत गरियो';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'तस्बिर यहाँ सुरक्षित गरिएको छ: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'तस्बिर सुरक्षित गर्दा त्रुटि भयो';

  @override
  String get pleaseEnterTextForYourLink =>
      'कृपया आफ्नो लिङ्कको लागि पाठ प्रविष्ट गर्नुहोस् (जस्तै, \'थप जान्नुहोस्\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'कृपया लिङ्क URL प्रविष्ट गर्नुहोस् (जस्तै, \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'कृपया वैध छवि URL प्रविष्ट गर्नुहोस्';

  @override
  String get pleaseEnterAValidVideoURL =>
      'कृपया एउटा मान्य भिडियो url प्रविष्ट गर्नुहोस्';

  @override
  String get photo => 'फोटो';

  @override
  String get image => 'तस्बिर';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'केस संवेदनशीलता र सम्पूर्ण शब्द खोज';

  @override
  String get caseSensitive => 'वर्ण केस संवेदनशील';

  @override
  String get wholeWord => 'पूरा शब्द';

  @override
  String get insertImage => 'छवि सम्मिलित गर्नुहोस्';

  @override
  String get pickAPhotoFromYourGallery => 'आफ्नो ग्यालरीबाट फोटो छान्नुहोस्';

  @override
  String get takeAPhotoUsingYourCamera =>
      'आफ्नो क्यामेरा प्रयोग गरेर फोटो खिच्नुहोस्';

  @override
  String get pasteAPhotoUsingALink => 'लिङ्क प्रयोग गरेर फोटो टाँस्नुहोस्';

  @override
  String get pickAVideoFromYourGallery => 'आफ्नो ग्यालरीबाट भिडियो छान्नुहोस्';

  @override
  String get recordAVideoUsingYourCamera =>
      'आफ्नो क्यामेरा प्रयोग गरेर भिडियो रेकर्ड गर्नुहोस्';

  @override
  String get pasteAVideoUsingALink => 'लिङ्क प्रयोग गरेर भिडियो टाँस्नुहोस्';

  @override
  String get close => 'बन्द गर्नुहोस्';

  @override
  String get searchSettings => 'खोज सेटिङ';

  @override
  String get cut => 'काट्नुहोस्';

  @override
  String get paste => 'पेस्ट गर्नुहोस्';

  @override
  String get insertTable => 'तालिका समावेश गर्नुहोस्';

  @override
  String get insertVideo => 'Insert video';
}
