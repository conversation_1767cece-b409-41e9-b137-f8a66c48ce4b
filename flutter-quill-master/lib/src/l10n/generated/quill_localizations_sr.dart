import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Serbian (`sr`).
class FlutterQuillLocalizationsSr extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsSr([String locale = 'sr']) : super(locale);

  @override
  String get pasteLink => 'Nalepi vezu';

  @override
  String get ok => 'OK';

  @override
  String get selectColor => 'Odaberi boju';

  @override
  String get gallery => 'Galerija';

  @override
  String get link => 'Veza';

  @override
  String get open => 'Otvori';

  @override
  String get copy => 'Ko<PERSON>raj';

  @override
  String get remove => 'Ukloni';

  @override
  String get save => 'Sačuvaj';

  @override
  String get zoom => 'Uvećaj';

  @override
  String get saved => 'Sačuvano';

  @override
  String get text => 'Tekst';

  @override
  String get resize => 'Promeni veličinu';

  @override
  String get width => 'Širina';

  @override
  String get height => 'Visina';

  @override
  String get size => 'Veličina';

  @override
  String get small => 'Malo';

  @override
  String get large => 'Veliko';

  @override
  String get huge => 'Ogromno';

  @override
  String get clear => 'Obriši';

  @override
  String get font => 'Font';

  @override
  String get search => 'Pretraga';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Poništi';

  @override
  String get redo => 'Ponovo';

  @override
  String get fontFamily => 'Porodica fonta';

  @override
  String get fontSize => 'Veličina fonta';

  @override
  String get bold => 'Podebljano';

  @override
  String get subscript => 'Indeks';

  @override
  String get superscript => 'Stepen';

  @override
  String get italic => 'Iskošeno';

  @override
  String get underline => 'Podvučeno';

  @override
  String get strikeThrough => 'Precrtano';

  @override
  String get inlineCode => 'Ugrađeni kôd';

  @override
  String get fontColor => 'Boja fonta';

  @override
  String get backgroundColor => 'Boja pozadine';

  @override
  String get clearFormat => 'Obriši format';

  @override
  String get alignLeft => 'Poravnanje levo';

  @override
  String get alignCenter => 'Poravnanje centar';

  @override
  String get alignRight => 'Poravnanje desno';

  @override
  String get alignJustify => 'Poravnaj';

  @override
  String get justifyWinWidth => 'Centriraj širinu prozora';

  @override
  String get textDirection => 'Smer teksta';

  @override
  String get headerStyle => 'Stil zaglavlja';

  @override
  String get normal => 'Normalno';

  @override
  String get heading1 => 'Naslov 1';

  @override
  String get heading2 => 'Naslov 2';

  @override
  String get heading3 => 'Naslov 3';

  @override
  String get heading4 => 'Naslov 4';

  @override
  String get heading5 => 'Naslov 5';

  @override
  String get heading6 => 'Naslov 6';

  @override
  String get numberedList => 'Numerisana lista';

  @override
  String get bulletList => 'Lista sa znakovima';

  @override
  String get checkedList => 'Proverena lista';

  @override
  String get codeBlock => 'Blok koda';

  @override
  String get quote => 'Citat';

  @override
  String get increaseIndent => 'Povećaj uvlačenje';

  @override
  String get decreaseIndent => 'Smanji uvlačenje';

  @override
  String get insertURL => 'Ubaci URL';

  @override
  String get visitLink => 'Poseti link';

  @override
  String get enterLink => 'Unesi link';

  @override
  String get enterMedia => 'Unesi medij';

  @override
  String get edit => 'Uredi';

  @override
  String get apply => 'Primeni';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Materijal';

  @override
  String get color => 'Boja';

  @override
  String get lineheight => 'Visina linije';

  @override
  String get findText => 'Nađi tekst';

  @override
  String get moveToPreviousOccurrence => 'Idi na prethodno pojavljivanje';

  @override
  String get moveToNextOccurrence => 'Idi na sledeće pojavljivanje';

  @override
  String get savedUsingTheNetwork => 'Sačuvano korišćenjem mreže';

  @override
  String get savedUsingLocalStorage =>
      'Sačuvano korišćenjem lokalnog skladišta';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Slika je sačuvana na: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Greška pri čuvanju slike';

  @override
  String get pleaseEnterTextForYourLink =>
      'Unesite tekst za svoj link (na primer, \'Saznajte više\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Unesite URL linka (na primer, \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'Unesite važeći URL slike';

  @override
  String get pleaseEnterAValidVideoURL => 'Unesite važeći URL videa';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Slika';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Osetljivost na velika i mala slova i potraga za celom rečju';

  @override
  String get caseSensitive => 'Razlikovati velika i mala slova';

  @override
  String get wholeWord => 'Cela reč';

  @override
  String get insertImage => 'Umetni sliku';

  @override
  String get pickAPhotoFromYourGallery => 'Izaberite sliku iz vaše galerije';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Napravite fotografiju koristeći vašu kameru';

  @override
  String get pasteAPhotoUsingALink => 'Zalepite sliku koristeći link';

  @override
  String get pickAVideoFromYourGallery => 'Izaberite video iz vaše galerije';

  @override
  String get recordAVideoUsingYourCamera => 'Snimi video koristeći vašu kameru';

  @override
  String get pasteAVideoUsingALink => 'Zalepite video koristeći link';

  @override
  String get close => 'Zatvori';

  @override
  String get searchSettings => 'Podešavanja pretrage';

  @override
  String get cut => 'Iseci';

  @override
  String get paste => 'Nalepi';

  @override
  String get insertTable => 'Ubaci tabelu';

  @override
  String get insertVideo => 'Insert video';
}
