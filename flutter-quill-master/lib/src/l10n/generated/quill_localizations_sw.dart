import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swahili (`sw`).
class FlutterQuillLocalizationsSw extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsSw([String locale = 'sw']) : super(locale);

  @override
  String get pasteLink => 'Bandika Kiungo';

  @override
  String get ok => 'Sawa';

  @override
  String get selectColor => 'Chagua Rangi';

  @override
  String get gallery => 'Matunzio';

  @override
  String get link => 'Kiungo';

  @override
  String get open => 'Fungua';

  @override
  String get copy => 'Nakili';

  @override
  String get remove => 'Ondoa';

  @override
  String get save => 'Hifadhi';

  @override
  String get zoom => 'Kuza';

  @override
  String get saved => 'Imehifadhiwa';

  @override
  String get text => 'Maandishi';

  @override
  String get resize => 'Badilisha Ukubwa';

  @override
  String get width => 'Upana';

  @override
  String get height => 'Urefu';

  @override
  String get size => 'Ukubwa';

  @override
  String get small => 'Ndogo';

  @override
  String get large => 'Kubwa';

  @override
  String get huge => 'Kubwa Sana';

  @override
  String get clear => 'Wazi';

  @override
  String get font => 'Fonti';

  @override
  String get search => 'Tafuta';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Fanyua';

  @override
  String get redo => 'Fanya Upya';

  @override
  String get fontFamily => 'Familia ya Fonti';

  @override
  String get fontSize => 'Ukubwa wa Fonti';

  @override
  String get bold => 'Nono';

  @override
  String get subscript => 'Maandishi ys Chini';

  @override
  String get superscript => 'Maandishi ya Juu';

  @override
  String get italic => 'Italiki';

  @override
  String get underline => 'Pigia Mstari';

  @override
  String get strikeThrough => 'Ghairi Maandishi';

  @override
  String get inlineCode => 'Codi ya Laini Moja';

  @override
  String get fontColor => 'Rangi ya Fonti';

  @override
  String get backgroundColor => 'Rangi ya Nyuma';

  @override
  String get clearFormat => 'Muundo Wazi';

  @override
  String get alignLeft => 'Pangilia Kushoto';

  @override
  String get alignCenter => 'Pangilia Kati';

  @override
  String get alignRight => 'Pangilia Kulia';

  @override
  String get alignJustify => 'Panga sawa';

  @override
  String get justifyWinWidth => 'Kuhalalisha Upana wa Ushindi';

  @override
  String get textDirection => 'Mwelekeo wa Maandishi';

  @override
  String get headerStyle => 'Mtindo wa Mada';

  @override
  String get normal => 'Kawaida';

  @override
  String get heading1 => 'Kichwa 1';

  @override
  String get heading2 => 'Kichwa 2';

  @override
  String get heading3 => 'Kichwa 3';

  @override
  String get heading4 => 'Kichwa 4';

  @override
  String get heading5 => 'Kichwa 5';

  @override
  String get heading6 => 'Kichwa 6';

  @override
  String get numberedList => 'Orodha ya Nambari';

  @override
  String get bulletList => 'Orodha ya Risasi';

  @override
  String get checkedList => 'Orodha iliyoangaliwa';

  @override
  String get codeBlock => 'aya ya codi';

  @override
  String get quote => 'Nukuu';

  @override
  String get increaseIndent => 'Ongeza Ujongezaji';

  @override
  String get decreaseIndent => 'Punguza Ujongezaji';

  @override
  String get insertURL => 'Ingiza Kiungo';

  @override
  String get visitLink => 'Tembelea Kiungo';

  @override
  String get enterLink => 'Ingiza Kiungo';

  @override
  String get enterMedia => 'Ingiza Picha';

  @override
  String get edit => 'Harir';

  @override
  String get apply => 'Weka';

  @override
  String get hex => 'Hexi';

  @override
  String get material => 'Nyenzo';

  @override
  String get color => 'Rangi';

  @override
  String get lineheight => 'Urefu wa mstari';

  @override
  String get findText => 'Pata Maandishi';

  @override
  String get moveToPreviousOccurrence => 'Nenda Kwenye Tukio la Awali';

  @override
  String get moveToNextOccurrence => 'Nenda kwa Tukio linalofuata';

  @override
  String get savedUsingTheNetwork => 'Imehifadhiwa kwa kutumia mtandao';

  @override
  String get savedUsingLocalStorage => 'Imehifadhiwa kwa Hifadhi ya Ndani';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Picha imehifadhiwa kwenye: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Hitilafu Wakati wa Kuhifadhi Picha';

  @override
  String get pleaseEnterTextForYourLink => 'Kwa mfano, \'Jifunze zaidi\'';

  @override
  String get pleaseEnterTheLinkURL => 'Kwa mfano, \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL =>
      'Tafadhali ingiza URL halali ya picha';

  @override
  String get pleaseEnterAValidVideoURL => 'Tafadhali ingiza URL ya video ili';

  @override
  String get photo => 'Picha';

  @override
  String get image => 'Picha';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Uwiano wa herufi kubwa na ndogo na utafutaji wa neno zima';

  @override
  String get caseSensitive => 'Jitambulishe kwa herufi kubwa na ndogo';

  @override
  String get wholeWord => 'Neno zima';

  @override
  String get insertImage => 'Weka Picha';

  @override
  String get pickAPhotoFromYourGallery =>
      'Chagua picha kutoka kwenye galleri yako';

  @override
  String get takeAPhotoUsingYourCamera => 'Piga picha kwa kutumia kamera yako';

  @override
  String get pasteAPhotoUsingALink => 'Pasta picha kwa kutumia kiungo';

  @override
  String get pickAVideoFromYourGallery =>
      'Chagua video kutoka kwenye galleri yako';

  @override
  String get recordAVideoUsingYourCamera =>
      'Record video kwa kutumia kamera yako';

  @override
  String get pasteAVideoUsingALink => 'Pasta video kwa kutumia kiungo';

  @override
  String get close => 'Funga';

  @override
  String get searchSettings => 'Mipangilio ya utafutaji';

  @override
  String get cut => 'Katakata';

  @override
  String get paste => 'Bandika';

  @override
  String get insertTable => 'Ingiza jedwali';

  @override
  String get insertVideo => 'Insert video';
}
