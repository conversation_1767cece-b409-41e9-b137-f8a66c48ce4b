import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Danish (`da`).
class FlutterQuillLocalizationsDa extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsDa([String locale = 'da']) : super(locale);

  @override
  String get pasteLink => 'Indsæt link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Vælg farve';

  @override
  String get gallery => 'Galleri';

  @override
  String get link => 'Link';

  @override
  String get open => 'Åben';

  @override
  String get copy => 'Kopi';

  @override
  String get remove => 'Fjerne';

  @override
  String get save => 'Gemme';

  @override
  String get zoom => 'Zoom ind';

  @override
  String get saved => 'Gemt';

  @override
  String get text => 'Text';

  @override
  String get resize => 'Resize';

  @override
  String get width => 'Width';

  @override
  String get height => 'Height';

  @override
  String get size => 'Size';

  @override
  String get small => 'Small';

  @override
  String get large => 'Large';

  @override
  String get huge => 'Huge';

  @override
  String get clear => 'Clear';

  @override
  String get font => 'Font';

  @override
  String get search => 'Search';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Font family';

  @override
  String get fontSize => 'Font size';

  @override
  String get bold => 'Bold';

  @override
  String get subscript => 'Subscript';

  @override
  String get superscript => 'Superscript';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get strikeThrough => 'Strike through';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Font color';

  @override
  String get backgroundColor => 'Background color';

  @override
  String get clearFormat => 'Clear format';

  @override
  String get alignLeft => 'Align left';

  @override
  String get alignCenter => 'Align center';

  @override
  String get alignRight => 'Align right';

  @override
  String get alignJustify => 'Juster tekst';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Text direction';

  @override
  String get headerStyle => 'Header style';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Overskrift 1';

  @override
  String get heading2 => 'Overskrift 2';

  @override
  String get heading3 => 'Overskrift 3';

  @override
  String get heading4 => 'Overskrift 4';

  @override
  String get heading5 => 'Overskrift 5';

  @override
  String get heading6 => 'Overskrift 6';

  @override
  String get numberedList => 'Numbered list';

  @override
  String get bulletList => 'Bullet list';

  @override
  String get checkedList => 'Checked list';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Quote';

  @override
  String get increaseIndent => 'Increase indent';

  @override
  String get decreaseIndent => 'Decrease indent';

  @override
  String get insertURL => 'Insert URL';

  @override
  String get visitLink => 'Visit link';

  @override
  String get enterLink => 'Enter link';

  @override
  String get enterMedia => 'Enter media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Apply';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Materiale';

  @override
  String get color => 'Farve';

  @override
  String get lineheight => 'altezza della linea';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Saved using the network';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Billedet er gemt på: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink => 'e.g., \'Learn more\'';

  @override
  String get pleaseEnterTheLinkURL => 'e.g., \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL => 'Angiv en gyldig video-URL';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Billede';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Stor- og småbogstavsfølsomhed samt helordsøgning';

  @override
  String get caseSensitive => 'Store og små bogstaver';

  @override
  String get wholeWord => 'Hele ord';

  @override
  String get insertImage => 'Indsæt billede';

  @override
  String get pickAPhotoFromYourGallery => 'Vælg et billede fra dit galleri';

  @override
  String get takeAPhotoUsingYourCamera => 'Tag et billede med dit kamera';

  @override
  String get pasteAPhotoUsingALink => 'Indsæt et billede ved hjælp af et link';

  @override
  String get pickAVideoFromYourGallery => 'Vælg en video fra dit galleri';

  @override
  String get recordAVideoUsingYourCamera => 'Optag en video med dit kamera';

  @override
  String get pasteAVideoUsingALink => 'Indsæt en video ved hjælp af et link';

  @override
  String get close => 'Luk';

  @override
  String get searchSettings => 'Søgeindstillinger';

  @override
  String get cut => 'Klip';

  @override
  String get paste => 'Sæt ind';

  @override
  String get insertTable => 'Indsæt tabel';

  @override
  String get insertVideo => 'Insert video';
}
