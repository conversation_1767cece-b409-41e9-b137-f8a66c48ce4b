import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Persian (`fa`).
class FlutterQuillLocalizationsFa extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsFa([String locale = 'fa']) : super(locale);

  @override
  String get pasteLink => 'جایگذاری لینک';

  @override
  String get ok => 'تایید';

  @override
  String get selectColor => 'انتخاب رنگ';

  @override
  String get gallery => 'گالری';

  @override
  String get link => 'لینک';

  @override
  String get open => 'باز کردن';

  @override
  String get copy => 'کپی';

  @override
  String get remove => 'حذف';

  @override
  String get save => 'ذخیره';

  @override
  String get zoom => 'بزرگنمایی';

  @override
  String get saved => 'ذخیره شد';

  @override
  String get text => 'متن';

  @override
  String get resize => 'تغییر اندازه';

  @override
  String get width => 'عرض';

  @override
  String get height => 'طول';

  @override
  String get size => 'اندازه';

  @override
  String get small => 'کوچک';

  @override
  String get large => 'بزرگ';

  @override
  String get huge => 'خیلی بزرگ';

  @override
  String get clear => 'پاک کردن';

  @override
  String get font => 'فونت';

  @override
  String get search => 'جستجو';

  @override
  String get camera => 'دوربین';

  @override
  String get video => 'ویدیو';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Sخانواده فونت';

  @override
  String get fontSize => 'اندازه فونت';

  @override
  String get bold => 'توپر';

  @override
  String get subscript => 'زیرنویس';

  @override
  String get superscript => 'بالانویس';

  @override
  String get italic => 'مورب';

  @override
  String get underline => 'زیرخط';

  @override
  String get strikeThrough => 'خط خورده';

  @override
  String get inlineCode => 'کد درون خطی';

  @override
  String get fontColor => 'رنگ فونت';

  @override
  String get backgroundColor => 'رنگ زمینه';

  @override
  String get clearFormat => 'پاکسازی فرمت';

  @override
  String get alignLeft => 'چیدمان چپ';

  @override
  String get alignCenter => 'چیدمان وسط';

  @override
  String get alignRight => 'چیدمان راست';

  @override
  String get alignJustify => 'تراز کردن متن';

  @override
  String get justifyWinWidth => 'تضمین عرض پنجره';

  @override
  String get textDirection => 'جهت متن';

  @override
  String get headerStyle => 'سبک هدر';

  @override
  String get normal => 'معمولی';

  @override
  String get heading1 => 'عنوان 1';

  @override
  String get heading2 => 'عنوان 2';

  @override
  String get heading3 => 'عنوان 3';

  @override
  String get heading4 => 'عنوان 4';

  @override
  String get heading5 => 'عنوان 5';

  @override
  String get heading6 => 'عنوان 6';

  @override
  String get numberedList => 'لیست شماره‌دار';

  @override
  String get bulletList => 'لیست نقطه‌ای';

  @override
  String get checkedList => 'لیست با علامت';

  @override
  String get codeBlock => 'بلوک کد';

  @override
  String get quote => 'نقل قول';

  @override
  String get increaseIndent => 'افزایش تورفتگی';

  @override
  String get decreaseIndent => 'کاهش تورفتگی';

  @override
  String get insertURL => 'درج URL';

  @override
  String get visitLink => 'بازدید از لینک';

  @override
  String get enterLink => 'ورود لینک';

  @override
  String get enterMedia => 'ورود رسانه';

  @override
  String get edit => 'ویرایش';

  @override
  String get apply => 'اعمال';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'مواد';

  @override
  String get color => 'رنگ';

  @override
  String get lineheight => 'ارتفاع خط';

  @override
  String get findText => 'جستجوی متن';

  @override
  String get moveToPreviousOccurrence => 'انتقال به رخداد قبلی';

  @override
  String get moveToNextOccurrence => 'انتقال به رخداد بعدی';

  @override
  String get savedUsingTheNetwork => 'با استفاده از شبکه ذخیره شده است';

  @override
  String get savedUsingLocalStorage =>
      'ذخیره شده با استفاده از فضای ذخیره محلی';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'تصویر در: $imagePath ذخیره شده است';
  }

  @override
  String get errorWhileSavingImage => 'خطا در هنگام ذخیره تصویر';

  @override
  String get pleaseEnterTextForYourLink =>
      'لطفاً متن لینک خود را وارد کنید (مثال: \'بیشتر بدانید\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'لطفاً URL لینک را وارد کنید (مثال: \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'لطفاً یک URL تصویر معتبر وارد کنید';

  @override
  String get pleaseEnterAValidVideoURL => 'لطفاً یک URL ویدیوی معتبر وارد کنید';

  @override
  String get photo => 'عکس';

  @override
  String get image => 'تصویر';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'حساسیت به کوچکی و بزرگی حروف و جستجوی کلمه کامل';

  @override
  String get caseSensitive => 'حساس به بزرگی و کوچکی حروف';

  @override
  String get wholeWord => 'کلمه کامل';

  @override
  String get insertImage => 'وارد کردن تصویر';

  @override
  String get pickAPhotoFromYourGallery => 'یک عکس از گالری خود انتخاب کنید';

  @override
  String get takeAPhotoUsingYourCamera => 'با دوربین خود عکسی بگیرید';

  @override
  String get pasteAPhotoUsingALink => 'عکس را با استفاده از لینک پیست کنید';

  @override
  String get pickAVideoFromYourGallery => 'یک ویدیو از گالری خود انتخاب کنید';

  @override
  String get recordAVideoUsingYourCamera => 'با دوربین خود یک ویدیو ضبط کنید';

  @override
  String get pasteAVideoUsingALink => 'ویدیو را با استفاده از لینک پیست کنید';

  @override
  String get close => 'بستن';

  @override
  String get searchSettings => 'تنظیمات جستجو';

  @override
  String get cut => 'بریدن';

  @override
  String get paste => 'چسباندن';

  @override
  String get insertTable => 'درج جدول';

  @override
  String get insertVideo => 'Insert video';
}
