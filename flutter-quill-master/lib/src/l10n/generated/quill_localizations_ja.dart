import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class FlutterQuillLocalizationsJa extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get pasteLink => 'リンクをペースト';

  @override
  String get ok => '完了';

  @override
  String get selectColor => '色を選択';

  @override
  String get gallery => '写真集';

  @override
  String get link => 'リンク';

  @override
  String get open => '開く';

  @override
  String get copy => 'コピー';

  @override
  String get remove => '削除';

  @override
  String get save => '保存';

  @override
  String get zoom => '拡大';

  @override
  String get saved => '保存済み';

  @override
  String get text => '文字';

  @override
  String get resize => 'サイズを調整';

  @override
  String get width => '幅';

  @override
  String get height => '高さ';

  @override
  String get size => 'サイズ';

  @override
  String get small => '小さい';

  @override
  String get large => '大きい';

  @override
  String get huge => 'でっかい';

  @override
  String get clear => 'クリア';

  @override
  String get font => 'フォント';

  @override
  String get search => '検索';

  @override
  String get camera => 'カメラ';

  @override
  String get video => 'ビデオ';

  @override
  String get undo => '取り消し';

  @override
  String get redo => 'やり直し';

  @override
  String get fontFamily => 'フォントファミリー';

  @override
  String get fontSize => 'フォントサイズ';

  @override
  String get bold => '太字';

  @override
  String get subscript => '下付き';

  @override
  String get superscript => '上付き';

  @override
  String get italic => '斜体';

  @override
  String get underline => '下線';

  @override
  String get strikeThrough => '取り消し線';

  @override
  String get inlineCode => 'インラインコード';

  @override
  String get fontColor => 'フォントカラー';

  @override
  String get backgroundColor => 'ベースカラー';

  @override
  String get clearFormat => 'クリアフォーマット';

  @override
  String get alignLeft => '左揃え';

  @override
  String get alignCenter => 'センターアライメント';

  @override
  String get alignRight => '右揃え';

  @override
  String get alignJustify => '両端揃え';

  @override
  String get justifyWinWidth => '両端揃え';

  @override
  String get textDirection => '文字方向';

  @override
  String get headerStyle => 'タイトルスタイル';

  @override
  String get normal => '通常';

  @override
  String get heading1 => '見出し1';

  @override
  String get heading2 => '見出し2';

  @override
  String get heading3 => '見出し3';

  @override
  String get heading4 => '見出し4';

  @override
  String get heading5 => '見出し5';

  @override
  String get heading6 => '見出し6';

  @override
  String get numberedList => '順序付きリスト';

  @override
  String get bulletList => '順序無しリスト';

  @override
  String get checkedList => 'チェックボックス';

  @override
  String get codeBlock => 'コード';

  @override
  String get quote => '引用';

  @override
  String get increaseIndent => 'インデントを増やす';

  @override
  String get decreaseIndent => 'インデントを減らす';

  @override
  String get insertURL => 'ハイパーリンクを挿入';

  @override
  String get visitLink => 'ハイパーリンクを訪問';

  @override
  String get enterLink => 'ハイパーリンクを輸入';

  @override
  String get enterMedia => 'ミディアムを輸入';

  @override
  String get edit => '編集';

  @override
  String get apply => '応用';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Color';

  @override
  String get lineheight => '行の高さ';

  @override
  String get findText => '検索テキスト';

  @override
  String get moveToPreviousOccurrence => '前のマッチ';

  @override
  String get moveToNextOccurrence => '次のマッチ';

  @override
  String get savedUsingTheNetwork => 'ネットワークを使用して保存';

  @override
  String get savedUsingLocalStorage => 'ローカルストレージを使用して保存';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return '画像は次の場所に保存されました: $imagePath';
  }

  @override
  String get errorWhileSavingImage => '画像の保存中にエラーが発生しました';

  @override
  String get pleaseEnterTextForYourLink => '例: \'Learn more\'';

  @override
  String get pleaseEnterTheLinkURL => '例: \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => '有効な画像URLを入力してください';

  @override
  String get pleaseEnterAValidVideoURL => '有効なビデオURLを入力してください';

  @override
  String get photo => '写真';

  @override
  String get image => '画像';

  @override
  String get caseSensitivityAndWholeWordSearch => '大文字と小文字の区別と完全一致検索';

  @override
  String get caseSensitive => '大文字と小文字を区別する';

  @override
  String get wholeWord => '完全一致';

  @override
  String get insertImage => '画像を挿入';

  @override
  String get pickAPhotoFromYourGallery => 'ギャラリーから写真を選択';

  @override
  String get takeAPhotoUsingYourCamera => 'カメラで写真を撮る';

  @override
  String get pasteAPhotoUsingALink => 'リンクを使用して写真を貼り付ける';

  @override
  String get pickAVideoFromYourGallery => 'ギャラリーからビデオを選択';

  @override
  String get recordAVideoUsingYourCamera => 'カメラでビデオを録画する';

  @override
  String get pasteAVideoUsingALink => 'リンクを使用してビデオを貼り付ける';

  @override
  String get close => '閉じる';

  @override
  String get searchSettings => '検索設定';

  @override
  String get cut => '切り取り';

  @override
  String get paste => '貼り付け';

  @override
  String get insertTable => 'テーブルを挿入';

  @override
  String get insertVideo => 'Insert video';
}
