import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Khmer Central Khmer (`km`).
class FlutterQuillLocalizationsKm extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsKm([String locale = 'km']) : super(locale);

  @override
  String get pasteLink => 'បិទភ្ជាប់តំណ';

  @override
  String get ok => 'យល់ព្រម';

  @override
  String get selectColor => 'ជ្រើសរើសពណ៌';

  @override
  String get gallery => 'វិចិត្រសាល';

  @override
  String get link => 'តំណភ្ជាប់';

  @override
  String get open => 'បើក';

  @override
  String get copy => 'ចម្លង';

  @override
  String get remove => 'លុប';

  @override
  String get save => 'រក្សាទុក';

  @override
  String get zoom => 'ពង្រីក';

  @override
  String get saved => 'បានរក្សាទុក';

  @override
  String get text => 'អត្ថបទ';

  @override
  String get resize => 'ប្តូរទំហំ';

  @override
  String get width => 'ទទឹង';

  @override
  String get height => 'កម្ពស់';

  @override
  String get size => 'ទំហំ';

  @override
  String get small => 'តូច';

  @override
  String get large => 'ធំ';

  @override
  String get huge => 'ដ៏ធំ';

  @override
  String get clear => 'សម្អាត';

  @override
  String get font => 'ពុម្ពអក្សរ';

  @override
  String get search => 'ស្វែងរក';

  @override
  String get camera => 'កាមេរ៉ា';

  @override
  String get video => 'វីដេអូ';

  @override
  String get undo => 'មិនធ្វើវិញ';

  @override
  String get redo => 'ធ្វើឡើងវិញ';

  @override
  String get fontFamily => 'ពុម្ពអក្សរ';

  @override
  String get fontSize => 'ទំហំពុម្ពអក្សរ';

  @override
  String get bold => 'ដិត';

  @override
  String get subscript => 'អក្សរកាត់';

  @override
  String get superscript => 'អក្សរធំ';

  @override
  String get italic => 'ទ្រេត';

  @override
  String get underline => 'បន្ទាត់ពីក្រោម';

  @override
  String get strikeThrough => 'ឆូត';

  @override
  String get inlineCode => 'កូដក្នុងជួរ';

  @override
  String get fontColor => 'ពណ៌ពុម្ពអក្សរ';

  @override
  String get backgroundColor => 'ពណ៌ផ្ទៃខាងក្រោយ';

  @override
  String get clearFormat => 'សម្អាតទម្រង់';

  @override
  String get alignLeft => 'តម្រឹមឆ្វេង';

  @override
  String get alignCenter => 'តម្រឹមកណ្តាល';

  @override
  String get alignRight => 'តម្រឹមស្តាំ';

  @override
  String get alignJustify => 'តម្រឹមសងខាង';

  @override
  String get justifyWinWidth => 'កំណត់ទទឹងឈ្នះ';

  @override
  String get textDirection => 'ទិសដៅអត្ថបទ';

  @override
  String get headerStyle => 'រចនាប័ទ្មចំណងជើង';

  @override
  String get normal => 'ធម្មតា';

  @override
  String get heading1 => 'ចំណងជើង ១';

  @override
  String get heading2 => 'ចំណងជើង ២';

  @override
  String get heading3 => 'ចំណងជើង ៣';

  @override
  String get heading4 => 'ចំណងជើង ៤';

  @override
  String get heading5 => 'ចំណងជើង ៥';

  @override
  String get heading6 => 'ចំណងជើង ៦';

  @override
  String get numberedList => 'បញ្ជីលេខ';

  @override
  String get bulletList => 'បញ្ជីគ្រាប់';

  @override
  String get checkedList => 'បញ្ជីធីក';

  @override
  String get codeBlock => 'ប្លុកកូដ';

  @override
  String get quote => 'សម្រង់';

  @override
  String get increaseIndent => 'បង្កើនការចូលបន្ទាត់';

  @override
  String get decreaseIndent => 'បន្ថយការចូលបន្ទាត់';

  @override
  String get insertURL => 'បញ្ចូល URL';

  @override
  String get visitLink => 'ចូលទៅកាន់តំណ';

  @override
  String get enterLink => 'បញ្ចូលតំណ';

  @override
  String get enterMedia => 'បញ្ចូលមេឌា';

  @override
  String get edit => 'កែសម្រួល';

  @override
  String get apply => 'អនុវត្ត';

  @override
  String get hex => 'គោលដប់ប្រាំមួយ';

  @override
  String get material => 'សម្ភារៈ';

  @override
  String get color => 'ពណ៌';

  @override
  String get lineheight => 'កម្ពស់បន្ទាត់';

  @override
  String get findText => 'ស្វែងរកអត្ថបទ';

  @override
  String get moveToPreviousOccurrence => 'ផ្លាស់ទីទៅការព្រឹត្តិការណ៍មុន';

  @override
  String get moveToNextOccurrence => 'ផ្លាស់ទីទៅព្រឹត្តិការណ៍បន្ទាប់';

  @override
  String get savedUsingTheNetwork => 'បានរក្សាទុកដោយប្រើបណ្តាញ';

  @override
  String get savedUsingLocalStorage =>
      'បានរក្សាទុកដោយប្រើឧបករណ៍ផ្ទុកក្នុងតំបន់';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'រូបភាពត្រូវបានរក្សាទុកនៅ៖ $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'កំហុសខណៈពេលរក្សាទុករូបភាព';

  @override
  String get pleaseEnterTextForYourLink =>
      'សូមបញ្ចូលអត្ថបទសម្រាប់តំណរបស់អ្នក (ឧ. \'ស្វែងយល់បន្ថែម\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'សូមបញ្ចូល URL តំណ (ឧ. \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'សូមបញ្ចូល URL រូបភាពដែលត្រឹមត្រូវ';

  @override
  String get pleaseEnterAValidVideoURL => 'សូមបញ្ចូល url វីដេអូដែលត្រឹមត្រូវ';

  @override
  String get photo => 'រូបថត';

  @override
  String get image => 'រូបភាព';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'ករណីប្រកាន់អក្សរតូចធំ និងការស្វែងរកពាក្យទាំងមូល';

  @override
  String get caseSensitive => 'ករណីប្រកាន់អក្សរតូចធំ';

  @override
  String get wholeWord => 'ពាក្យទាំងមូល';

  @override
  String get insertImage => 'បញ្ចូលរូបភាព';

  @override
  String get pickAPhotoFromYourGallery => 'ជ្រើសរើសរូបថតពីវិចិត្រសាលរបស់អ្នក';

  @override
  String get takeAPhotoUsingYourCamera => 'ថតរូបដោយប្រើកាមេរ៉ារបស់អ្នក';

  @override
  String get pasteAPhotoUsingALink => 'បិទភ្ជាប់រូបថតដោយប្រើតំណ';

  @override
  String get pickAVideoFromYourGallery => 'ជ្រើសរើសវីដេអូពីវិចិត្រសាលរបស់អ្នក';

  @override
  String get recordAVideoUsingYourCamera => 'ថតវីដេអូដោយប្រើកាមេរ៉ារបស់អ្នក';

  @override
  String get pasteAVideoUsingALink => 'បិទភ្ជាប់វីដេអូដោយប្រើតំណ';

  @override
  String get close => 'បិទ';

  @override
  String get searchSettings => 'ការកំណត់ស្វែងរក';

  @override
  String get cut => 'កាត់';

  @override
  String get paste => 'បិទភ្ជាប់';

  @override
  String get insertTable => 'បញ្ចូលតារាង';

  @override
  String get insertVideo => 'បញ្ចូលវីដេអូ';
}
