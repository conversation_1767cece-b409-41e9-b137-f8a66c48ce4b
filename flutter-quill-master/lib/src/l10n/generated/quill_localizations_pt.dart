import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class FlutterQuillLocalizationsPt extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get pasteLink => 'Colar um link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Selecionar uma cor';

  @override
  String get gallery => 'Galeria';

  @override
  String get link => 'Link';

  @override
  String get open => 'Abra';

  @override
  String get copy => 'Copiar';

  @override
  String get remove => 'Remover';

  @override
  String get save => 'Salvar';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Salvo';

  @override
  String get text => 'Texto';

  @override
  String get resize => 'Redimencionar';

  @override
  String get width => 'Largura';

  @override
  String get height => 'Altura';

  @override
  String get size => 'Tamanho';

  @override
  String get small => 'Pequeno';

  @override
  String get large => 'Grande';

  @override
  String get huge => 'Gigante';

  @override
  String get clear => 'Limpar';

  @override
  String get font => 'Fonte';

  @override
  String get search => 'Search';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Font family';

  @override
  String get fontSize => 'Font size';

  @override
  String get bold => 'Bold';

  @override
  String get subscript => 'Subscript';

  @override
  String get superscript => 'Superscript';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get strikeThrough => 'Strike through';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Font color';

  @override
  String get backgroundColor => 'Background color';

  @override
  String get clearFormat => 'Clear format';

  @override
  String get alignLeft => 'Align left';

  @override
  String get alignCenter => 'Align center';

  @override
  String get alignRight => 'Align right';

  @override
  String get alignJustify => 'Justificar texto';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Text direction';

  @override
  String get headerStyle => 'Header style';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Título 1';

  @override
  String get heading2 => 'Título 2';

  @override
  String get heading3 => 'Título 3';

  @override
  String get heading4 => 'Título 4';

  @override
  String get heading5 => 'Título 5';

  @override
  String get heading6 => 'Título 6';

  @override
  String get numberedList => 'Numbered list';

  @override
  String get bulletList => 'Bullet list';

  @override
  String get checkedList => 'Checked list';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Quote';

  @override
  String get increaseIndent => 'Increase indent';

  @override
  String get decreaseIndent => 'Decrease indent';

  @override
  String get insertURL => 'Insert URL';

  @override
  String get visitLink => 'Visit link';

  @override
  String get enterLink => 'Enter link';

  @override
  String get enterMedia => 'Enter media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Apply';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Cor';

  @override
  String get lineheight => 'Altura da linha';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Guardado através da network';

  @override
  String get savedUsingLocalStorage =>
      'Guardado através do armazenamento local';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'A imagem foi salva em: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Erro a gravar imagem';

  @override
  String get pleaseEnterTextForYourLink => 'e.g., \'Learn more\'';

  @override
  String get pleaseEnterTheLinkURL => 'e.g., \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Por favor, insira uma URL de vídeo válida';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Imagem';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilidade a maiúsculas e minúsculas e pesquisa de palavras inteiras';

  @override
  String get caseSensitive => 'Distinguir maiúsculas e minúsculas';

  @override
  String get wholeWord => 'Palavra inteira';

  @override
  String get insertImage => 'Inserir imagem';

  @override
  String get pickAPhotoFromYourGallery => 'Escolha uma foto da sua galeria';

  @override
  String get takeAPhotoUsingYourCamera => 'Tire uma foto com a sua câmera';

  @override
  String get pasteAPhotoUsingALink => 'Cole uma foto usando um link';

  @override
  String get pickAVideoFromYourGallery => 'Escolha um vídeo da sua galeria';

  @override
  String get recordAVideoUsingYourCamera => 'Grave um vídeo com a sua câmera';

  @override
  String get pasteAVideoUsingALink => 'Cole um vídeo usando um link';

  @override
  String get close => 'Fechar';

  @override
  String get searchSettings => 'Configurações de pesquisa';

  @override
  String get cut => 'Cortar';

  @override
  String get paste => 'Colar';

  @override
  String get insertTable => 'Inserir tabela';

  @override
  String get insertVideo => 'Insert video';
}

/// The translations for Portuguese, as used in Brazil (`pt_BR`).
class FlutterQuillLocalizationsPtBr extends FlutterQuillLocalizationsPt {
  FlutterQuillLocalizationsPtBr() : super('pt_BR');

  @override
  String get pasteLink => 'Colar um link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Selecionar uma cor';

  @override
  String get gallery => 'Galeria';

  @override
  String get link => 'Link';

  @override
  String get open => 'Abrir';

  @override
  String get copy => 'Copiar';

  @override
  String get remove => 'Remover';

  @override
  String get save => 'Salvar';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Salvo';

  @override
  String get text => 'Texto';

  @override
  String get resize => 'Redimensionar';

  @override
  String get width => 'Largura';

  @override
  String get height => 'Altura';

  @override
  String get size => 'Tamanho';

  @override
  String get small => 'Pequeno';

  @override
  String get large => 'Grande';

  @override
  String get huge => 'Gigante';

  @override
  String get clear => 'Limpar';

  @override
  String get font => 'Fonte';

  @override
  String get search => 'Buscar';

  @override
  String get camera => 'Câmera';

  @override
  String get video => 'Vídeo';

  @override
  String get undo => 'Desfazer';

  @override
  String get redo => 'Refazer';

  @override
  String get fontFamily => 'Fonte';

  @override
  String get fontSize => 'Tamanho da fonte';

  @override
  String get bold => 'Negrito';

  @override
  String get subscript => 'Subscrito';

  @override
  String get superscript => 'Sobrescrito';

  @override
  String get italic => 'Itálico';

  @override
  String get underline => 'Sublinhado';

  @override
  String get strikeThrough => 'Tachado';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Cor da fonte';

  @override
  String get backgroundColor => 'Cor do fundo';

  @override
  String get clearFormat => 'Limpar formatação';

  @override
  String get alignLeft => 'Texto à esquerda';

  @override
  String get alignCenter => 'Centralizar';

  @override
  String get alignRight => 'Texto à direita';

  @override
  String get alignJustify => 'Justificar';

  @override
  String get justifyWinWidth => 'Justificado';

  @override
  String get textDirection => 'Direção do texto';

  @override
  String get headerStyle => 'Estilo de cabeçalho';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Cabeçalho 1';

  @override
  String get heading2 => 'Cabeçalho 2';

  @override
  String get heading3 => 'Cabeçalho 3';

  @override
  String get heading4 => 'Cabeçalho 4';

  @override
  String get heading5 => 'Cabeçalho 5';

  @override
  String get heading6 => 'Cabeçalho 6';

  @override
  String get numberedList => 'Numeração';

  @override
  String get bulletList => 'Marcadores';

  @override
  String get checkedList => 'Lista de verificação';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Citação';

  @override
  String get increaseIndent => 'Aumentar recuo';

  @override
  String get decreaseIndent => 'Diminuir recuo';

  @override
  String get insertURL => 'Inserir URL';

  @override
  String get visitLink => 'Visitar link';

  @override
  String get enterLink => 'Inserir link';

  @override
  String get enterMedia => 'Inserir mídia';

  @override
  String get edit => 'Editar';

  @override
  String get apply => 'Aplicar';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Cor';

  @override
  String get lineheight => 'Altura da linha';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Saved using the network';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'A imagem foi salva em: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink => 'e.g., \'Learn more\'';

  @override
  String get pleaseEnterTheLinkURL => 'e.g., \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Por favor, insira uma URL de vídeo válida';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Imagem';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilidade a maiúsculas e minúsculas e pesquisa de palavras inteiras';

  @override
  String get caseSensitive => 'Distinção entre maiúsculas e minúsculas';

  @override
  String get wholeWord => 'Palavra inteira';

  @override
  String get insertImage => 'Inserir imagem';

  @override
  String get pickAPhotoFromYourGallery => 'Escolha uma foto da sua galeria';

  @override
  String get takeAPhotoUsingYourCamera => 'Tire uma foto usando sua câmera';

  @override
  String get pasteAPhotoUsingALink => 'Cole uma foto usando um link';

  @override
  String get pickAVideoFromYourGallery => 'Escolha um vídeo da sua galeria';

  @override
  String get recordAVideoUsingYourCamera => 'Grave um vídeo usando sua câmera';

  @override
  String get pasteAVideoUsingALink => 'Cole um vídeo usando um link';

  @override
  String get close => 'Fechar';

  @override
  String get searchSettings => 'Configurações de pesquisa';

  @override
  String get cut => 'Cortar';

  @override
  String get paste => 'Colar';

  @override
  String get insertTable => 'Inserir tabela';
}
