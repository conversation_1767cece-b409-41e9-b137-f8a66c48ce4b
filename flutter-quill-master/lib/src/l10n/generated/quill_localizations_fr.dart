import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class FlutterQuillLocalizationsFr extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get pasteLink => 'Coller un lien';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Choisir une couleur';

  @override
  String get gallery => 'Galerie';

  @override
  String get link => 'Lien';

  @override
  String get open => 'Ouvrir';

  @override
  String get copy => 'Copier';

  @override
  String get remove => 'Supprimer';

  @override
  String get save => 'Sauvegarder';

  @override
  String get zoom => 'Zoomer';

  @override
  String get saved => 'Enregistrée';

  @override
  String get text => 'Texte';

  @override
  String get resize => 'Redimensionner';

  @override
  String get width => 'Largeur';

  @override
  String get height => 'Hauteur';

  @override
  String get size => 'Taille';

  @override
  String get small => 'Petit';

  @override
  String get large => 'Grand';

  @override
  String get huge => 'Énorme';

  @override
  String get clear => 'Supprimer la mise en forme';

  @override
  String get font => 'Police';

  @override
  String get search => 'Rechercher';

  @override
  String get camera => 'Caméra';

  @override
  String get video => 'Vidéo';

  @override
  String get undo => 'Annuler';

  @override
  String get redo => 'Refaire';

  @override
  String get fontFamily => 'Famille de police';

  @override
  String get fontSize => 'Taille de police';

  @override
  String get bold => 'Gras';

  @override
  String get subscript => 'Indice';

  @override
  String get superscript => 'Exposant';

  @override
  String get italic => 'Italique';

  @override
  String get underline => 'Souligné';

  @override
  String get strikeThrough => 'Barré';

  @override
  String get inlineCode => 'Code en ligne';

  @override
  String get fontColor => 'Couleur de police';

  @override
  String get backgroundColor => 'Couleur de fond';

  @override
  String get clearFormat => 'Effacer la mise en forme';

  @override
  String get alignLeft => 'Aligner à gauche';

  @override
  String get alignCenter => 'Aligner au centre';

  @override
  String get alignRight => 'Aligner à droite';

  @override
  String get alignJustify => 'Justifier le texte';

  @override
  String get justifyWinWidth => 'Justifier';

  @override
  String get textDirection => 'Direction du texte';

  @override
  String get headerStyle => 'Style d\'en-tête';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Titre 1';

  @override
  String get heading2 => 'Titre 2';

  @override
  String get heading3 => 'Titre 3';

  @override
  String get heading4 => 'Titre 4';

  @override
  String get heading5 => 'Titre 5';

  @override
  String get heading6 => 'Titre 6';

  @override
  String get numberedList => 'Liste numérotée';

  @override
  String get bulletList => 'Liste à puces';

  @override
  String get checkedList => 'Check-list';

  @override
  String get codeBlock => 'Bloc de code';

  @override
  String get quote => 'Citation';

  @override
  String get increaseIndent => 'Augmenter le retrait';

  @override
  String get decreaseIndent => 'Diminuer le retrait';

  @override
  String get insertURL => 'Insérer une URL';

  @override
  String get visitLink => 'Visiter';

  @override
  String get enterLink => 'Entrer un lien';

  @override
  String get enterMedia => 'Entrer un média';

  @override
  String get edit => 'Modifier';

  @override
  String get apply => 'Appliquer';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Matériel';

  @override
  String get color => 'Couleur';

  @override
  String get lineheight => 'Hauteur de la ligne';

  @override
  String get findText => 'Rechercher du texte';

  @override
  String get moveToPreviousOccurrence => 'Aller à l\'occurrence précédente';

  @override
  String get moveToNextOccurrence => 'Aller à l\'occurrence suivante';

  @override
  String get savedUsingTheNetwork => 'Enregistré via le réseau';

  @override
  String get savedUsingLocalStorage =>
      'Enregistré en utilisant le stockage local';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'L\'image a été sauvegardée à : $imagePath';
  }

  @override
  String get errorWhileSavingImage =>
      'Erreur lors de l\'enregistrement de l\'image';

  @override
  String get pleaseEnterTextForYourLink => 'par exemple, \'En savoir plus\'';

  @override
  String get pleaseEnterTheLinkURL => 'par exemple, \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL =>
      'Veuillez saisir une URL d\'image valide';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Veuillez entrer une URL vidéo valide';

  @override
  String get photo => 'Photo';

  @override
  String get image => 'Image';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensibilité à la casse et recherche de mots entiers';

  @override
  String get caseSensitive => 'Respecter la casse';

  @override
  String get wholeWord => 'Mot entier';

  @override
  String get insertImage => 'Insérer une image';

  @override
  String get pickAPhotoFromYourGallery =>
      'Choisissez une photo dans votre galerie';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Prendre une photo avec votre appareil photo';

  @override
  String get pasteAPhotoUsingALink => 'Coller une photo à l\'aide d\'un lien';

  @override
  String get pickAVideoFromYourGallery =>
      'Choisissez une vidéo dans votre galerie';

  @override
  String get recordAVideoUsingYourCamera =>
      'Enregistrez une vidéo en utilisant votre caméra';

  @override
  String get pasteAVideoUsingALink => 'Coller une vidéo à l\'aide d\'un lien';

  @override
  String get close => 'Fermer';

  @override
  String get searchSettings => 'Paramètres de recherche';

  @override
  String get cut => 'Couper';

  @override
  String get paste => 'Coller';

  @override
  String get insertTable => 'Insérer un tableau';

  @override
  String get insertVideo => 'Insert video';
}
