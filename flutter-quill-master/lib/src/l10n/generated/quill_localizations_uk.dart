import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Ukrainian (`uk`).
class FlutterQuillLocalizationsUk extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsUk([String locale = 'uk']) : super(locale);

  @override
  String get pasteLink => 'Вставити посилання';

  @override
  String get ok => 'ОК';

  @override
  String get selectColor => 'Вибрати колір';

  @override
  String get gallery => 'Галерея';

  @override
  String get link => 'Посилання';

  @override
  String get open => 'Відкрити';

  @override
  String get copy => 'Копіювати';

  @override
  String get remove => 'Видалити';

  @override
  String get save => 'Зберегти';

  @override
  String get zoom => 'Збільшити';

  @override
  String get saved => 'Збережено';

  @override
  String get text => 'Текст';

  @override
  String get resize => 'Змінити розмір';

  @override
  String get width => 'Ширина';

  @override
  String get height => 'Висота';

  @override
  String get size => 'Розмір';

  @override
  String get small => 'Малий';

  @override
  String get large => 'Великий';

  @override
  String get huge => 'Величезний';

  @override
  String get clear => 'Очистити';

  @override
  String get font => 'Шрифт';

  @override
  String get search => 'Пошук';

  @override
  String get camera => 'Камера';

  @override
  String get video => 'Відео';

  @override
  String get undo => 'Скасувати';

  @override
  String get redo => 'Повторити';

  @override
  String get fontFamily => 'Сімейство шрифтів';

  @override
  String get fontSize => 'Розмір шрифту';

  @override
  String get bold => 'Жирний';

  @override
  String get subscript => 'Нижній індекс';

  @override
  String get superscript => 'Верхній індекс';

  @override
  String get italic => 'Курсив';

  @override
  String get underline => 'Підкреслити';

  @override
  String get strikeThrough => 'Закреслений';

  @override
  String get inlineCode => 'Вбудований код';

  @override
  String get fontColor => 'Колір шрифту';

  @override
  String get backgroundColor => 'Колір фону';

  @override
  String get clearFormat => 'Очистити формат';

  @override
  String get alignLeft => 'Вирівняти ліворуч';

  @override
  String get alignCenter => 'Вирівняти по центру';

  @override
  String get alignRight => 'Вирівняти праворуч';

  @override
  String get alignJustify => 'Вирівняти по ширині';

  @override
  String get justifyWinWidth => 'Вирівняти за шириною вікна';

  @override
  String get textDirection => 'Напрямок тексту';

  @override
  String get headerStyle => 'Стиль заголовка';

  @override
  String get normal => 'Звичайний';

  @override
  String get heading1 => 'Заголовок 1';

  @override
  String get heading2 => 'Заголовок 2';

  @override
  String get heading3 => 'Заголовок 3';

  @override
  String get heading4 => 'Заголовок 4';

  @override
  String get heading5 => 'Заголовок 5';

  @override
  String get heading6 => 'Заголовок 6';

  @override
  String get numberedList => 'Нумерований список';

  @override
  String get bulletList => 'Маркований список';

  @override
  String get checkedList => 'Список з позначками';

  @override
  String get codeBlock => 'Блок коду';

  @override
  String get quote => 'Цитата';

  @override
  String get increaseIndent => 'Збільшити відступ';

  @override
  String get decreaseIndent => 'Зменшити відступ';

  @override
  String get insertURL => 'Вставити URL';

  @override
  String get visitLink => 'Відвідати посилання';

  @override
  String get enterLink => 'Ввести посилання';

  @override
  String get enterMedia => 'Ввести медіа';

  @override
  String get edit => 'Редагувати';

  @override
  String get apply => 'Застосувати';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Матеріал';

  @override
  String get color => 'Колір';

  @override
  String get lineheight => 'Висота лінії';

  @override
  String get findText => 'Знайти текст';

  @override
  String get moveToPreviousOccurrence => 'Перейти до попереднього випадку';

  @override
  String get moveToNextOccurrence => 'Перейти до наступного випадку';

  @override
  String get savedUsingTheNetwork => 'Збережено за допомогою мережі';

  @override
  String get savedUsingLocalStorage =>
      'Збережено за допомогою локального сховища';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Зображення збережено за адресою: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Помилка при збереженні зображення';

  @override
  String get pleaseEnterTextForYourLink => 'Наприклад, \'Дізнатися більше\'';

  @override
  String get pleaseEnterTheLinkURL => 'Наприклад, \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL =>
      'Будь ласка, введіть правильний URL-адресу зображення';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Будь ласка, введіть дійсну URL-адресу відео';

  @override
  String get photo => 'Фото';

  @override
  String get image => 'Зображення';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Чутливість до регістру та пошук цілих слів';

  @override
  String get caseSensitive => 'Чутливість до регістру';

  @override
  String get wholeWord => 'Слово повністю';

  @override
  String get insertImage => 'Вставити зображення';

  @override
  String get pickAPhotoFromYourGallery => 'Оберіть фотографію з вашої галереї';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Створіть фотографію, використовуючи фотокамеру';

  @override
  String get pasteAPhotoUsingALink =>
      'Вставте фотографію, використовуючи посилання';

  @override
  String get pickAVideoFromYourGallery => 'Оберіть відео з вашої галереї';

  @override
  String get recordAVideoUsingYourCamera =>
      'Запишіть відео, використовуючи відеокамеру';

  @override
  String get pasteAVideoUsingALink => 'Вставте відео, використовуючи посилання';

  @override
  String get close => 'Закрити';

  @override
  String get searchSettings => 'Налаштування пошуку';

  @override
  String get cut => 'Вирізати';

  @override
  String get paste => 'Вставити';

  @override
  String get insertTable => 'Вставити таблицю';

  @override
  String get insertVideo => 'Insert video';
}
