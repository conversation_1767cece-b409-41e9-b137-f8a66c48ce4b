import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hungarian (`hu`).
class FlutterQuillLocalizationsHu extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsHu([String locale = 'hu']) : super(locale);

  @override
  String get pasteLink => 'Link beillesztése';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Szín kiv<PERSON>tása';

  @override
  String get gallery => 'Galéria';

  @override
  String get link => 'Link';

  @override
  String get open => 'Megnyitás';

  @override
  String get copy => 'Másolás';

  @override
  String get remove => 'Eltávolítás';

  @override
  String get save => 'Mentés';

  @override
  String get zoom => 'Nagyítás';

  @override
  String get saved => 'Mentve';

  @override
  String get text => 'Szöveg';

  @override
  String get resize => 'Átméretezés';

  @override
  String get width => 'Sz<PERSON>lesség';

  @override
  String get height => 'Magasság';

  @override
  String get size => 'Méret';

  @override
  String get small => 'Kicsi';

  @override
  String get large => 'Nagy';

  @override
  String get huge => 'Óriási';

  @override
  String get clear => 'Törlés';

  @override
  String get font => 'Betűtípus';

  @override
  String get search => 'Keresés';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Videó';

  @override
  String get undo => 'Visszavonás';

  @override
  String get redo => 'Újra';

  @override
  String get fontFamily => 'Betűtípus';

  @override
  String get fontSize => 'Betűméret';

  @override
  String get bold => 'Félkövér';

  @override
  String get subscript => 'Alsó index';

  @override
  String get superscript => 'Felső index';

  @override
  String get italic => 'Dőlt';

  @override
  String get underline => 'Aláhúzás';

  @override
  String get strikeThrough => 'Áthúzás';

  @override
  String get inlineCode => 'Kód';

  @override
  String get fontColor => 'Betűszín';

  @override
  String get backgroundColor => 'Háttérszín';

  @override
  String get clearFormat => 'Formátum törlése';

  @override
  String get alignLeft => 'Balra igazítás';

  @override
  String get alignCenter => 'Középre igazítás';

  @override
  String get alignRight => 'Jobbra igazítás';

  @override
  String get alignJustify => 'Sorkizárt';

  @override
  String get justifyWinWidth => 'Sorkizárt ablak szélesség';

  @override
  String get textDirection => 'Szöveg irány';

  @override
  String get headerStyle => 'Fejléc stílus';

  @override
  String get normal => 'Normál';

  @override
  String get heading1 => 'Fejléc 1';

  @override
  String get heading2 => 'Fejléc 2';

  @override
  String get heading3 => 'Fejléc 3';

  @override
  String get heading4 => 'Fejléc 4';

  @override
  String get heading5 => 'Fejléc 5';

  @override
  String get heading6 => 'Fejléc 6';

  @override
  String get numberedList => 'Számozott lista';

  @override
  String get bulletList => 'Felsorolás';

  @override
  String get checkedList => 'Ellenőrző lista';

  @override
  String get codeBlock => 'Kód blokk';

  @override
  String get quote => 'Idézet';

  @override
  String get increaseIndent => 'Behúzás növelése';

  @override
  String get decreaseIndent => 'Behúzás csökkentése';

  @override
  String get insertURL => 'URL beszúrása';

  @override
  String get visitLink => 'Link megnyitása';

  @override
  String get enterLink => 'Link beírása';

  @override
  String get enterMedia => 'Média beírása';

  @override
  String get edit => 'Szerkesztés';

  @override
  String get apply => 'Alkalmazás';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Anyag';

  @override
  String get color => 'Szín';

  @override
  String get lineheight => 'Sor magasság';

  @override
  String get findText => 'Szöveg keresése';

  @override
  String get moveToPreviousOccurrence => 'Ugrás az előző előfordulásra';

  @override
  String get moveToNextOccurrence => 'Ugrás a következő előfordulásra';

  @override
  String get savedUsingTheNetwork => 'Mentve hálózaton keresztül';

  @override
  String get savedUsingLocalStorage => 'Mentve helyi tárhelyre';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'A kép elmentve: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Hiba a kép mentése közben';

  @override
  String get pleaseEnterTextForYourLink =>
      'Kérjük, írja be a link szövegét (pl. „További információ”).';

  @override
  String get pleaseEnterTheLinkURL =>
      'Kérjük, írja be a link URL-t (pl. \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Kérjük, adjon meg egy érvényes kép URL-t';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Kérjük, adjon meg egy érvényes videó URL-t';

  @override
  String get photo => 'Fénykép';

  @override
  String get image => 'Kép';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Nagy- és kisbetűérzékenység és teljes szó keresés';

  @override
  String get caseSensitive => 'Nagy- és kisbetűérzékeny';

  @override
  String get wholeWord => 'Teljes szó';

  @override
  String get insertImage => 'Kép beszúrása';

  @override
  String get pickAPhotoFromYourGallery =>
      'Válasszon egy fényképet a galériájából';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Készítsen egy fényképet a kamerájával';

  @override
  String get pasteAPhotoUsingALink => 'Illesszen be egy fényképet egy linkkel';

  @override
  String get pickAVideoFromYourGallery => 'Válasszon egy videót a galériájából';

  @override
  String get recordAVideoUsingYourCamera =>
      'Vegyen fel egy videót a kamerájával';

  @override
  String get pasteAVideoUsingALink => 'Illesszen be egy videót egy linkkel';

  @override
  String get close => 'Bezárás';

  @override
  String get searchSettings => 'Keresési beállítások';

  @override
  String get cut => 'Kivágás';

  @override
  String get paste => 'Beillesztés';

  @override
  String get insertTable => 'Táblázat beszúrása';

  @override
  String get insertVideo => 'Insert video';
}
