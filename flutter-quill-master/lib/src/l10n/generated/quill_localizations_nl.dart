import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Dutch Flemish (`nl`).
class FlutterQuillLocalizationsNl extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsNl([String locale = 'nl']) : super(locale);

  @override
  String get pasteLink => 'Plak een link';

  @override
  String get ok => 'Ok';

  @override
  String get selectColor => 'Selecteer kleur';

  @override
  String get gallery => 'Gallerij';

  @override
  String get link => 'Link';

  @override
  String get open => 'Open';

  @override
  String get copy => 'Kopieer';

  @override
  String get remove => 'Verwijderd';

  @override
  String get save => 'Opslaan';

  @override
  String get zoom => 'Zoom';

  @override
  String get saved => 'Opgeslagen';

  @override
  String get text => 'Tekst';

  @override
  String get resize => 'Formaat wijzigen';

  @override
  String get width => 'Breedte';

  @override
  String get height => 'Hoogte';

  @override
  String get size => 'Grootte';

  @override
  String get small => 'Small';

  @override
  String get large => 'Large';

  @override
  String get huge => 'Huge';

  @override
  String get clear => 'Clear';

  @override
  String get font => 'Font';

  @override
  String get search => 'Search';

  @override
  String get camera => 'Camera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Font family';

  @override
  String get fontSize => 'Font size';

  @override
  String get bold => 'Bold';

  @override
  String get subscript => 'Subscript';

  @override
  String get superscript => 'Superscript';

  @override
  String get italic => 'Italic';

  @override
  String get underline => 'Underline';

  @override
  String get strikeThrough => 'Strike through';

  @override
  String get inlineCode => 'Inline code';

  @override
  String get fontColor => 'Font color';

  @override
  String get backgroundColor => 'Background color';

  @override
  String get clearFormat => 'Clear format';

  @override
  String get alignLeft => 'Align left';

  @override
  String get alignCenter => 'Align center';

  @override
  String get alignRight => 'Align right';

  @override
  String get alignJustify => 'Uitvullen';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Text direction';

  @override
  String get headerStyle => 'Header style';

  @override
  String get normal => 'Normaal';

  @override
  String get heading1 => 'Kop 1';

  @override
  String get heading2 => 'Kop 2';

  @override
  String get heading3 => 'Kop 3';

  @override
  String get heading4 => 'Kop 4';

  @override
  String get heading5 => 'Kop 5';

  @override
  String get heading6 => 'Kop 6';

  @override
  String get numberedList => 'Numbered list';

  @override
  String get bulletList => 'Bullet list';

  @override
  String get checkedList => 'Checked list';

  @override
  String get codeBlock => 'Code block';

  @override
  String get quote => 'Quote';

  @override
  String get increaseIndent => 'Increase indent';

  @override
  String get decreaseIndent => 'Decrease indent';

  @override
  String get insertURL => 'Insert URL';

  @override
  String get visitLink => 'Visit link';

  @override
  String get enterLink => 'Enter link';

  @override
  String get enterMedia => 'Enter media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Apply';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Materiaal';

  @override
  String get color => 'Kleur';

  @override
  String get lineheight => 'Lijnhoogte';

  @override
  String get findText => 'Find text';

  @override
  String get moveToPreviousOccurrence => 'Move to previous occurrence';

  @override
  String get moveToNextOccurrence => 'Move to next occurrence';

  @override
  String get savedUsingTheNetwork => 'Opgeslagen via het netwerk';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'De afbeelding is opgeslagen op: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink =>
      'Voer tekst in voor uw link (bijvoorbeeld \'Meer weten\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Voer de URL van de link in (bijvoorbeeld \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Voer een geldige URL voor de afbeelding in';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Voer een geldige URL voor de video in';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Afbeelding';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Hoofdlettergevoeligheid en volledig woord zoeken';

  @override
  String get caseSensitive => 'Hoofdlettergevoelig';

  @override
  String get wholeWord => 'Heel woord';

  @override
  String get insertImage => 'Afbeelding invoegen';

  @override
  String get pickAPhotoFromYourGallery => 'Kies een foto uit je galerij';

  @override
  String get takeAPhotoUsingYourCamera => 'Maak een foto met je camera';

  @override
  String get pasteAPhotoUsingALink => 'Plak een foto met een link';

  @override
  String get pickAVideoFromYourGallery => 'Kies een video uit je galerij';

  @override
  String get recordAVideoUsingYourCamera => 'Maak een video met je camera';

  @override
  String get pasteAVideoUsingALink => 'Plak een video met een link';

  @override
  String get close => 'Sluiten';

  @override
  String get searchSettings => 'Zoekinstellingen';

  @override
  String get cut => 'Knippen';

  @override
  String get paste => 'Plakken';

  @override
  String get insertTable => 'Tabel invoegen';

  @override
  String get insertVideo => 'Insert video';
}
