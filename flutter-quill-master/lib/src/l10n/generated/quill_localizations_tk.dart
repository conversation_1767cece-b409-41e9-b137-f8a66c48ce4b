import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkmen (`tk`).
class FlutterQuillLocalizationsTk extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsTk([String locale = 'tk']) : super(locale);

  @override
  String get pasteLink => 'Baglanyşygy goýuň';

  @override
  String get ok => 'Bolýar';

  @override
  String get selectColor => 'Reňk saýla<PERSON>';

  @override
  String get gallery => 'Galereýa';

  @override
  String get link => 'Baglanyşyk';

  @override
  String get open => 'Aç';

  @override
  String get copy => 'Kopýala';

  @override
  String get remove => 'Poz';

  @override
  String get save => 'Sakla';

  @override
  String get zoom => 'Ulalt';

  @override
  String get saved => 'Saklandy';

  @override
  String get text => 'Tekst';

  @override
  String get resize => 'Ölçegini üýtget';

  @override
  String get width => 'In';

  @override
  String get height => 'Boý';

  @override
  String get size => 'Ölçegi';

  @override
  String get small => 'Kiçi';

  @override
  String get large => 'Uly';

  @override
  String get huge => 'Has uly';

  @override
  String get clear => 'Arassala';

  @override
  String get font => 'Şrift';

  @override
  String get search => 'Gözleg';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Wideo';

  @override
  String get undo => 'Yza al';

  @override
  String get redo => 'Öňe al';

  @override
  String get fontFamily => 'Şrift maşgalasy';

  @override
  String get fontSize => 'Şrift ululygy';

  @override
  String get bold => 'Galyň';

  @override
  String get subscript => 'Aşaky ýazgy';

  @override
  String get superscript => 'Ýokarky ýazgy';

  @override
  String get italic => 'Italik';

  @override
  String get underline => 'Aşagyny çyz';

  @override
  String get strikeThrough => 'Üstüni çyz';

  @override
  String get inlineCode => 'Bir setirde kod';

  @override
  String get fontColor => 'Şrift reňki';

  @override
  String get backgroundColor => 'Arka reňki';

  @override
  String get clearFormat => 'Formaty arassala';

  @override
  String get alignLeft => 'Çepe deňleşdir';

  @override
  String get alignCenter => 'Orta deňleşdir';

  @override
  String get alignRight => 'Saga deňleşdir';

  @override
  String get alignJustify => 'Düzgünlyä';

  @override
  String get justifyWinWidth => 'Justify win width';

  @override
  String get textDirection => 'Tekst ugry';

  @override
  String get headerStyle => 'Sözbaşy stili';

  @override
  String get normal => 'Halklaýyn';

  @override
  String get heading1 => 'Başlyk 1';

  @override
  String get heading2 => 'Başlyk 2';

  @override
  String get heading3 => 'Başlyk 3';

  @override
  String get heading4 => 'Başlyk 4';

  @override
  String get heading5 => 'Başlyk 5';

  @override
  String get heading6 => 'Başlyk 6';

  @override
  String get numberedList => 'Sanly sanaw';

  @override
  String get bulletList => 'Okly sanawy';

  @override
  String get checkedList => 'Tikli sanaw';

  @override
  String get codeBlock => 'Kod blogy';

  @override
  String get quote => 'Sitata';

  @override
  String get increaseIndent => 'Indent köpelt';

  @override
  String get decreaseIndent => 'Indent azalt';

  @override
  String get insertURL => 'URL goý';

  @override
  String get visitLink => 'Baglanyşyga giriň';

  @override
  String get enterLink => 'Baglanyşyk giriň';

  @override
  String get enterMedia => 'Mediýa giriziň';

  @override
  String get edit => 'Üýtget';

  @override
  String get apply => 'Ulan';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Reňk';

  @override
  String get lineheight => 'Çyzyk beýikligi';

  @override
  String get findText => 'Tekst tapyň';

  @override
  String get moveToPreviousOccurrence => 'Öňki hadysa geçiň';

  @override
  String get moveToNextOccurrence => 'Indiki hadysa geçiň';

  @override
  String get savedUsingTheNetwork => 'Ulgama ulanyp saklanan';

  @override
  String get savedUsingLocalStorage => 'Saved using the local storage';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Surat şu ýerde saklandy: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error while saving image';

  @override
  String get pleaseEnterTextForYourLink => 'Güýz öwrenmek)';

  @override
  String get pleaseEnterTheLinkURL => 'https://example.com';

  @override
  String get pleaseEnterAValidImageURL => 'Please enter a valid image URL';

  @override
  String get pleaseEnterAValidVideoURL => 'Lütfen güýjük wideo URL giriziň';

  @override
  String get photo => 'Surat';

  @override
  String get image => 'Surat';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Iňkisar we iň oňg söz gözleýinç';

  @override
  String get caseSensitive => 'Yazgy käbirlik';

  @override
  String get wholeWord => 'Doly söz';

  @override
  String get insertImage => 'Surat goş';

  @override
  String get pickAPhotoFromYourGallery => 'Galereýadan surat saýla';

  @override
  String get takeAPhotoUsingYourCamera => 'Kameranyňyzy ulanyp surat çek';

  @override
  String get pasteAPhotoUsingALink => 'Baglanyşyk ulanyp surat goý';

  @override
  String get pickAVideoFromYourGallery => 'Galereýadan wideo saýla';

  @override
  String get recordAVideoUsingYourCamera => 'Kameranyňyzy ulanyp wideo ýaz';

  @override
  String get pasteAVideoUsingALink => 'Baglanyşyk ulanyp wideo goý';

  @override
  String get close => 'Ýap';

  @override
  String get searchSettings => 'Gözleg sazlamalary';

  @override
  String get cut => 'Kes';

  @override
  String get paste => 'Goý';

  @override
  String get insertTable => 'Jadwal goş';

  @override
  String get insertVideo => 'Insert video';
}
