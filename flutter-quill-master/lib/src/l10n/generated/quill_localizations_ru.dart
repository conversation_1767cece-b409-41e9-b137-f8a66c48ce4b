import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class FlutterQuillLocalizationsRu extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get pasteLink => 'Вставить ссылку';

  @override
  String get ok => 'ОК';

  @override
  String get selectColor => 'Выбрать цвет';

  @override
  String get gallery => 'Галерея';

  @override
  String get link => 'Ссылка';

  @override
  String get open => 'Открыть';

  @override
  String get copy => 'Копировать';

  @override
  String get remove => 'Удалить';

  @override
  String get save => 'Сохранить';

  @override
  String get zoom => 'Увеличить';

  @override
  String get saved => 'Сохранено';

  @override
  String get text => 'Текст';

  @override
  String get resize => 'Изменить размер';

  @override
  String get width => 'Ширина';

  @override
  String get height => 'Высота';

  @override
  String get size => 'Размер';

  @override
  String get small => 'Маленький';

  @override
  String get large => 'Большой';

  @override
  String get huge => 'Огромный';

  @override
  String get clear => 'Очистить';

  @override
  String get font => 'Шрифт';

  @override
  String get search => 'Поиск';

  @override
  String get camera => 'Камера';

  @override
  String get video => 'Видео';

  @override
  String get undo => 'Отменить';

  @override
  String get redo => 'Повторить';

  @override
  String get fontFamily => 'Семейство шрифтов';

  @override
  String get fontSize => 'Размер шрифта';

  @override
  String get bold => 'Жирный';

  @override
  String get subscript => 'Нижний индекс';

  @override
  String get superscript => 'Верхний индекс';

  @override
  String get italic => 'Курсив';

  @override
  String get underline => 'Подчеркнутый';

  @override
  String get strikeThrough => 'Зачеркнутый';

  @override
  String get inlineCode => 'Встроенный код';

  @override
  String get fontColor => 'Цвет шрифта';

  @override
  String get backgroundColor => 'Цвет фона';

  @override
  String get clearFormat => 'Очистить форматирование';

  @override
  String get alignLeft => 'Выровнять по левому краю';

  @override
  String get alignCenter => 'Выровнять по центру';

  @override
  String get alignRight => 'Выровнять по правому краю';

  @override
  String get alignJustify => 'Выравнивание по ширине';

  @override
  String get justifyWinWidth => 'Выровнять по ширине окна';

  @override
  String get textDirection => 'Направление текста';

  @override
  String get headerStyle => 'Стиль заголовка';

  @override
  String get normal => 'Обычный';

  @override
  String get heading1 => 'Заголовок 1';

  @override
  String get heading2 => 'Заголовок 2';

  @override
  String get heading3 => 'Заголовок 3';

  @override
  String get heading4 => 'Заголовок 4';

  @override
  String get heading5 => 'Заголовок 5';

  @override
  String get heading6 => 'Заголовок 6';

  @override
  String get numberedList => 'Нумерованный список';

  @override
  String get bulletList => 'Маркированный список';

  @override
  String get checkedList => 'Список с галочками';

  @override
  String get codeBlock => 'Блок кода';

  @override
  String get quote => 'Цитата';

  @override
  String get increaseIndent => 'Увеличить отступ';

  @override
  String get decreaseIndent => 'Уменьшить отступ';

  @override
  String get insertURL => 'Вставить URL';

  @override
  String get visitLink => 'Посетить ссылку';

  @override
  String get enterLink => 'Введите ссылку';

  @override
  String get enterMedia => 'Введите медиа';

  @override
  String get edit => 'Редактировать';

  @override
  String get apply => 'Применить';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Материал';

  @override
  String get color => 'Цвет';

  @override
  String get lineheight => 'Высота линии';

  @override
  String get findText => 'Найти текст';

  @override
  String get moveToPreviousOccurrence => 'Перейти к предыдущему вхождению';

  @override
  String get moveToNextOccurrence => 'Перейти к следующему вхождению';

  @override
  String get savedUsingTheNetwork => 'Сохранено с использованием сети';

  @override
  String get savedUsingLocalStorage =>
      'Сохранено с использованием локального хранилища';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Изображение сохранено по адресу: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Ошибка при сохранении изображения';

  @override
  String get pleaseEnterTextForYourLink => 'например, \'Узнать больше\'';

  @override
  String get pleaseEnterTheLinkURL => 'например, \'https://example.com\'';

  @override
  String get pleaseEnterAValidImageURL =>
      'Пожалуйста, введите действительный URL изображения';

  @override
  String get pleaseEnterAValidVideoURL =>
      'Пожалуйста, введите действительный URL-адрес видео';

  @override
  String get photo => 'Фото';

  @override
  String get image => 'Изображение';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Учет регистра и поиск по всему слову';

  @override
  String get caseSensitive => 'Чувствителен к регистру';

  @override
  String get wholeWord => 'Целое слово';

  @override
  String get insertImage => 'Вставить изображение';

  @override
  String get pickAPhotoFromYourGallery =>
      'Выберите фотографию из вашей галереи';

  @override
  String get takeAPhotoUsingYourCamera =>
      'Сделайте фотографию, используя камеру';

  @override
  String get pasteAPhotoUsingALink => 'Вставьте фотографию, используя ссылку';

  @override
  String get pickAVideoFromYourGallery => 'Выберите видео из вашей галереи';

  @override
  String get recordAVideoUsingYourCamera => 'Запишите видео, используя камеру';

  @override
  String get pasteAVideoUsingALink => 'Вставить видео по ссылке';

  @override
  String get close => 'Закрыть';

  @override
  String get searchSettings => 'Настройки поиска';

  @override
  String get cut => 'Вырезать';

  @override
  String get paste => 'Вставить';

  @override
  String get insertTable => 'Вставить таблицу';

  @override
  String get insertVideo => 'Insert video';
}
