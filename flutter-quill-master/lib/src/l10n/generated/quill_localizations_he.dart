import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hebrew (`he`).
class FlutterQuillLocalizationsHe extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsHe([String locale = 'he']) : super(locale);

  @override
  String get pasteLink => 'הדבק את הלינק';

  @override
  String get ok => 'אוקי';

  @override
  String get selectColor => 'בחר צבע';

  @override
  String get gallery => 'גלריה';

  @override
  String get link => 'לינק';

  @override
  String get open => 'פתח';

  @override
  String get copy => 'העתק';

  @override
  String get remove => 'מחק';

  @override
  String get save => 'שמור';

  @override
  String get zoom => 'זום';

  @override
  String get saved => 'נשמר';

  @override
  String get text => 'טקסט';

  @override
  String get resize => 'שנה גודל';

  @override
  String get width => 'רוחב';

  @override
  String get height => 'גובה';

  @override
  String get size => 'גודל';

  @override
  String get small => 'קטן';

  @override
  String get large => 'גדול';

  @override
  String get huge => 'ענק';

  @override
  String get clear => 'מחוק';

  @override
  String get font => 'פונט';

  @override
  String get search => 'חפש';

  @override
  String get camera => 'מצלמה';

  @override
  String get video => 'וידאו';

  @override
  String get undo => 'בטל';

  @override
  String get redo => 'בצע שוב';

  @override
  String get fontFamily => 'משפחת הפונטים';

  @override
  String get fontSize => 'גודל הפונט';

  @override
  String get bold => 'מודגש';

  @override
  String get subscript => 'כתוב בתחתית השורה';

  @override
  String get superscript => 'כתוב בחלק העליון של השורה';

  @override
  String get italic => 'נטוי';

  @override
  String get underline => 'קו תחתון';

  @override
  String get strikeThrough => 'קו חוצה';

  @override
  String get inlineCode => 'קוד טקסט בתוך הטקסט';

  @override
  String get fontColor => 'צבע טקסט';

  @override
  String get backgroundColor => 'צבע רקע';

  @override
  String get clearFormat => 'נקה פורמט';

  @override
  String get alignLeft => 'יישור לשמאל';

  @override
  String get alignCenter => 'יישור למרכז';

  @override
  String get alignRight => 'יישור לימין';

  @override
  String get alignJustify => 'justify';

  @override
  String get justifyWinWidth => 'יישור לרוחב החלון';

  @override
  String get textDirection => 'כיוון הטקסט';

  @override
  String get headerStyle => 'סגנון הכותרת';

  @override
  String get normal => 'רגיל';

  @override
  String get heading1 => 'כותרת 1';

  @override
  String get heading2 => 'כותרת 2';

  @override
  String get heading3 => 'כותרת 3';

  @override
  String get heading4 => 'כותרת 4';

  @override
  String get heading5 => 'כותרת 5';

  @override
  String get heading6 => 'כותרת 6';

  @override
  String get numberedList => 'רשימה ממוספרת';

  @override
  String get bulletList => 'רשימה עם תבליטים';

  @override
  String get checkedList => 'רשימת תיקולים';

  @override
  String get codeBlock => 'בלוק קוד';

  @override
  String get quote => 'ציטוט';

  @override
  String get increaseIndent => 'הגדל את הזחות';

  @override
  String get decreaseIndent => 'הקטן את הזחות';

  @override
  String get insertURL => 'הוסף URL';

  @override
  String get visitLink => 'בקר בלינק';

  @override
  String get enterLink => 'הכנס לינק';

  @override
  String get enterMedia => 'הכנס מדיה';

  @override
  String get edit => 'ערוך';

  @override
  String get apply => 'החל';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'חומר';

  @override
  String get color => 'צבע';

  @override
  String get lineheight => 'גובה קו';

  @override
  String get findText => 'מצא טקסט';

  @override
  String get moveToPreviousOccurrence => 'התקדם להופעה הקודמת';

  @override
  String get moveToNextOccurrence => 'התקדם להופעה הבאה';

  @override
  String get savedUsingTheNetwork => 'נשמר באמצעות הרשת';

  @override
  String get savedUsingLocalStorage => 'נשמר באמצעות אחסון מקומי';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'התמונה נשמרה ב: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'שגיאה בעת שמירת התמונה';

  @override
  String get pleaseEnterTextForYourLink =>
      'אנא הזן טקסט לקישור שלך (לדוגמה, \'מידע נוסף\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'אנא הזן את כתובת ה-URL של הקישור (לדוגמה, \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL => 'אנא הזן כתובת URL תקינה של תמונה';

  @override
  String get pleaseEnterAValidVideoURL => 'אנא הזן כתובת URL תקינה של וידיאו';

  @override
  String get photo => 'תמונה';

  @override
  String get image => 'תמונה';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'רגישות לאותות רישיות וחיפוש לפי מילה שלמה';

  @override
  String get caseSensitive => 'מבחין בין רישיות לקטנות';

  @override
  String get wholeWord => 'מילה שלמה';

  @override
  String get insertImage => 'הכנס תמונה';

  @override
  String get pickAPhotoFromYourGallery => 'בחר תמונה מהגלריה שלך';

  @override
  String get takeAPhotoUsingYourCamera => 'צלם תמונה באמצעות המצלמה שלך';

  @override
  String get pasteAPhotoUsingALink => 'הדבק תמונה באמצעות קישור';

  @override
  String get pickAVideoFromYourGallery => 'בחר סרטון מהגלריה שלך';

  @override
  String get recordAVideoUsingYourCamera => 'קלט סרטון באמצעות המצלמה שלך';

  @override
  String get pasteAVideoUsingALink => 'הדבק סרטון באמצעות קישור';

  @override
  String get close => 'סגור';

  @override
  String get searchSettings => 'הגדרות חיפוש';

  @override
  String get cut => 'גזור';

  @override
  String get paste => 'הדבק';

  @override
  String get insertTable => 'הוסף טבלה';

  @override
  String get insertVideo => 'Insert video';
}
