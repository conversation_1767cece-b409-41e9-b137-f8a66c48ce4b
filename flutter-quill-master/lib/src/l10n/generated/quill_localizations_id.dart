import 'quill_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Indonesian (`id`).
class FlutterQuillLocalizationsId extends FlutterQuillLocalizations {
  FlutterQuillLocalizationsId([String locale = 'id']) : super(locale);

  @override
  String get pasteLink => 'Tempel tautan';

  @override
  String get ok => 'Oke';

  @override
  String get selectColor => 'Pilih Warna';

  @override
  String get gallery => 'Galeri';

  @override
  String get link => 'Tautan';

  @override
  String get open => 'Buka';

  @override
  String get copy => 'Salin';

  @override
  String get remove => 'Hapus';

  @override
  String get save => 'Simpan';

  @override
  String get zoom => 'Perbesar';

  @override
  String get saved => 'Tersimpan';

  @override
  String get text => 'Teks';

  @override
  String get resize => 'Ubah Ukuran';

  @override
  String get width => 'Lebar';

  @override
  String get height => 'Tinggi';

  @override
  String get size => 'Ukuran';

  @override
  String get small => 'Kecil';

  @override
  String get large => 'Besar';

  @override
  String get huge => 'Sangat Besar';

  @override
  String get clear => 'Hapus';

  @override
  String get font => 'Font';

  @override
  String get search => 'Cari';

  @override
  String get camera => 'Kamera';

  @override
  String get video => 'Video';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get fontFamily => 'Keluarga Font';

  @override
  String get fontSize => 'Ukuran Font';

  @override
  String get bold => 'Tebal';

  @override
  String get subscript => 'Subskrip';

  @override
  String get superscript => 'Superskrip';

  @override
  String get italic => 'Miring';

  @override
  String get underline => 'Garis Bawah';

  @override
  String get strikeThrough => 'Coret Saja';

  @override
  String get inlineCode => 'Kode Inline';

  @override
  String get fontColor => 'Warna Font';

  @override
  String get backgroundColor => 'Warna Latar';

  @override
  String get clearFormat => 'Hapus Format';

  @override
  String get alignLeft => 'Rata Kiri';

  @override
  String get alignCenter => 'Rata Tengah';

  @override
  String get alignRight => 'Rata Kanan';

  @override
  String get alignJustify => 'Rata kanan kiri';

  @override
  String get justifyWinWidth => 'Rata Kanan dan Kiri';

  @override
  String get textDirection => 'Arah Teks';

  @override
  String get headerStyle => 'Gaya Header';

  @override
  String get normal => 'Normal';

  @override
  String get heading1 => 'Judul 1';

  @override
  String get heading2 => 'Judul 2';

  @override
  String get heading3 => 'Judul 3';

  @override
  String get heading4 => 'Judul 4';

  @override
  String get heading5 => 'Judul 5';

  @override
  String get heading6 => 'Judul 6';

  @override
  String get numberedList => 'Daftar Bernomor';

  @override
  String get bulletList => 'Daftar Poin';

  @override
  String get checkedList => 'Daftar Dicentang';

  @override
  String get codeBlock => 'Blok Kode';

  @override
  String get quote => 'Kutipan';

  @override
  String get increaseIndent => 'Tambah Indentasi';

  @override
  String get decreaseIndent => 'Kurangi Indentasi';

  @override
  String get insertURL => 'Masukkan URL';

  @override
  String get visitLink => 'Kunjungi Tautan';

  @override
  String get enterLink => 'Masukkan Tautan';

  @override
  String get enterMedia => 'Masukkan Media';

  @override
  String get edit => 'Edit';

  @override
  String get apply => 'Terapkan';

  @override
  String get hex => 'Hex';

  @override
  String get material => 'Material';

  @override
  String get color => 'Warna';

  @override
  String get lineheight => 'Tinggi garis';

  @override
  String get findText => 'Temukan Teks';

  @override
  String get moveToPreviousOccurrence => 'Pindah ke Kejadian Sebelumnya';

  @override
  String get moveToNextOccurrence => 'Pindah ke Kejadian Berikutnya';

  @override
  String get savedUsingTheNetwork => 'Tersimpan menggunakan jaringan';

  @override
  String get savedUsingLocalStorage =>
      'Tersimpan menggunakan penyimpanan lokal';

  @override
  String theImageHasBeenSavedAt(String imagePath) {
    return 'Gambar telah disimpan di: $imagePath';
  }

  @override
  String get errorWhileSavingImage => 'Error saat menyimpan gambar';

  @override
  String get pleaseEnterTextForYourLink =>
      'Harap masukkan teks untuk tautan Anda (contoh: \'Pelajari lebih lanjut\')';

  @override
  String get pleaseEnterTheLinkURL =>
      'Harap masukkan URL tautan (contoh: \'https://example.com\')';

  @override
  String get pleaseEnterAValidImageURL =>
      'Harap masukkan URL gambar yang valid';

  @override
  String get pleaseEnterAValidVideoURL => 'Harap masukkan URL video yang valid';

  @override
  String get photo => 'Foto';

  @override
  String get image => 'Gambar';

  @override
  String get caseSensitivityAndWholeWordSearch =>
      'Sensitivitas huruf besar dan kecil dan pencarian kata utuh';

  @override
  String get caseSensitive => 'Sensitif huruf besar-kecil';

  @override
  String get wholeWord => 'Kata penuh';

  @override
  String get insertImage => 'Sisipkan Gambar';

  @override
  String get pickAPhotoFromYourGallery => 'Pilih foto dari galeri Anda';

  @override
  String get takeAPhotoUsingYourCamera => 'Ambil foto menggunakan kamera Anda';

  @override
  String get pasteAPhotoUsingALink => 'Tempel foto menggunakan tautan';

  @override
  String get pickAVideoFromYourGallery => 'Pilih video dari galeri Anda';

  @override
  String get recordAVideoUsingYourCamera =>
      'Rekam video menggunakan kamera Anda';

  @override
  String get pasteAVideoUsingALink => 'Tempel video menggunakan tautan';

  @override
  String get close => 'Tutup';

  @override
  String get searchSettings => 'Pengaturan pencarian';

  @override
  String get cut => 'Potong';

  @override
  String get paste => 'Tempel';

  @override
  String get insertTable => 'Sisipkan tabel';

  @override
  String get insertVideo => 'Insert video';
}
