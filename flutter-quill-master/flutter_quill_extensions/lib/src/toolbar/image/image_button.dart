import 'dart:convert';
import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/internal.dart';
import 'package:get_storage/get_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';
import '../../common/default_image_insert.dart';
import '../../common/image_video_utils.dart';
import '../../editor/image/image_embed_types.dart';
import '../quill_simple_toolbar_api.dart';
import 'api_use_dialog.dart';
import 'config/image_config.dart';
import 'select_image_source.dart';

// ignore: invalid_use_of_internal_member
class QuillToolbarImageButton extends QuillToolbarBaseButtonStateless {
  const QuillToolbarImageButton({
    required super.controller,
    QuillToolbarImageButtonOptions? options,

    /// Shares common options between all buttons, prefer the [options]
    /// over the [baseOptions].
    super.baseOptions,
    super.key,
  })  : _options = options,
        super(options: options);

  final QuillToolbarImageButtonOptions? _options;

  @override
  QuillToolbarImageButtonOptions? get options => _options;

  void _sharedOnPressed(BuildContext context) {
    _onPressedHandler(context);
    afterButtonPressed(context);
  }

  Future<void> _handleImageInsert(String imageUrl) async {
    await handleImageInsert(
      imageUrl,
      controller: controller,
      onImageInsertCallback: options?.imageButtonConfig?.onImageInsertCallback,
      onImageInsertedCallback:
          options?.imageButtonConfig?.onImageInsertedCallback,
    );
  }

  Future<void> _onPressedHandler(BuildContext context) async {
    final onRequestPickImage = options?.imageButtonConfig?.onRequestPickImage;
    if (onRequestPickImage != null) {
      final imageUrl = await onRequestPickImage(context);
      if (imageUrl != null) {
        await _handleImageInsert(imageUrl);
      }
      return;
    }

    final source = await showSelectImageSourceDialog(context: context);
    if (source == null) return;

    String? imageUrl;

    if (source == InsertImageSource.camera) {
      final status = await Permission.camera.status;
      if (status.isDenied || status.isRestricted) {
        final result = await Permission.camera.request();
        if (!result.isGranted) {
          _showPermissionDialog(context);
          return;
        }
      } else if (status.isPermanentlyDenied) {
        _showPermissionDialog(context);
        return;
      }
      imageUrl = (await ImagePicker().pickImage(source: ImageSource.camera))?.path;
    } else if (source == InsertImageSource.gallery) {
      imageUrl = (await ImagePicker().pickImage(source: ImageSource.gallery))?.path;
    } else if (source == InsertImageSource.link) {
      imageUrl = context.mounted ? await _typeLink(context) : null;
    }

    if (imageUrl == null) return;

    if (imageUrl.trim().isNotEmpty) {
      if (imageUrl.startsWith('https')) {
        return _handleImageInsert(imageUrl);
      }

      final tempDir = await getTemporaryDirectory();
      final targetPath = '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';

      final XFile? compressedFile = await FlutterImageCompress.compressAndGetFile(
        imageUrl,
        targetPath,
        quality: 70,
        keepExif: true,
      );

      final fileToUpload = File(compressedFile?.path ?? imageUrl);

      final box = GetStorage();
      String token = box.read('token');
      final url = Uri.parse('http://**************:3001/api/v1/general/files');

      print('token == $token');
      CustomDialogs().showCircularDialog(context);

      try {
        var request = http.MultipartRequest('POST', url);
        request.headers.addAll({
          'Authorization': 'Bearer $token',
        });

        request.files.add(await http.MultipartFile.fromPath(
          'file',
          fileToUpload.path,
        ));

        final response = await request.send();

        if (response.statusCode == 200) {
          final responseData = await response.stream.bytesToString();
          final data = jsonDecode(responseData);
          print('Upload Successful: ${data['data'][0]['link']}');
          CustomDialogs().hideCircularDialog(context);
          return await _handleImageInsert(data['data'][0]['link']);
        } else {
          CustomDialogs().hideCircularDialog(context);
          print('API Error: ${response.statusCode} - ${await response.stream.bytesToString()}');
        }
      } catch (e) {
        CustomDialogs().hideCircularDialog(context);
        print('Network Error: $e');
      }
    }
  }


  Future<String?> _typeLink(BuildContext context) async {
    final value = await showDialog<String>(
      context: context,
      builder: (_) => TypeLinkDialog(
        dialogTheme: options?.dialogTheme,
        linkRegExp: options?.linkRegExp,
        linkType: LinkType.image,
      ),
    );
    return value;
  }

  @override
  Widget buildButton(BuildContext context) {
    return QuillToolbarIconButton(
      icon: Icon(
        iconData(context),
        size: iconButtonFactor(context) * iconSize(context),
        color: Colors.grey.shade600,
      ),
      tooltip: tooltip(context),
      isSelected: false,
      onPressed: () => _sharedOnPressed(context),
      iconTheme: iconTheme(context),
    );
  }

  @override
  Widget? buildCustomChildBuilder(BuildContext context) {
    return childBuilder?.call(
      QuillToolbarImageButtonOptions(
        afterButtonPressed: afterButtonPressed(context),
        iconData: iconData(context),
        iconSize: iconSize(context),
        iconButtonFactor: iconButtonFactor(context),
        dialogTheme: options?.dialogTheme,
        iconTheme: options?.iconTheme,
        linkRegExp: options?.linkRegExp,
        tooltip: tooltip(context),
        imageButtonConfig: options?.imageButtonConfig,
      ),
      QuillToolbarImageButtonExtraOptions(
        context: context,
        controller: controller,
        onPressed: () => _sharedOnPressed(context),
      ),
    );
  }

  @override
  IconData Function(BuildContext context) get getDefaultIconData =>
      (context) => CupertinoIcons.photo;

  @override
  String Function(BuildContext context) get getDefaultTooltip =>
      (context) => context.loc.insertImage;
}

void _showPermissionDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Permission Required',style: TextStyle(fontSize: 18,fontWeight: FontWeight.w700),),
      content: const Text('Some permissions are permanently denied. Please enable them in the settings.',style: TextStyle(fontSize: 14,fontWeight: FontWeight.w500),),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel',style: TextStyle(color: Colors.red,fontSize: 14,fontWeight: FontWeight.w500),),
        ),
        TextButton(
          onPressed: () {
            openAppSettings(); // from permission_handler
            Navigator.of(context).pop();
          },
          child: const Text('Open Settings',style: TextStyle(color: Color(0xFF6D11D2),fontSize: 14,fontWeight: FontWeight.w500),),
        ),
      ],
    ),
  );
}

