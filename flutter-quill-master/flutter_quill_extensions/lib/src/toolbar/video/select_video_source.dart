import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_quill/internal.dart';
import 'package:get_storage/get_storage.dart';

import 'config/video.dart';

class SelectVideoSourceDialog extends StatelessWidget {
  const SelectVideoSourceDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final box = GetStorage();
    return Container(
      constraints: const BoxConstraints(minHeight: 150),
      width: double.infinity,
      decoration: BoxDecoration(
        color: box.read('isDarkMode') ? const Color(0xFF262A34) : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(40),
          topRight: Radius.circular(40),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // ListTile(
            //   title: Text(context.loc.gallery),
            //   subtitle: Text(
            //     context.loc.pickAVideoFromYourGallery,
            //   ),
            //   leading: const Icon(Icons.photo_sharp),
            //   onTap: () => Navigator.of(context).pop(InsertVideoSource.gallery),
            // ),
            // ListTile(
            //   title: Text(context.loc.camera),
            //   subtitle: Text(context.loc.recordAVideoUsingYourCamera),
            //   leading: const Icon(Icons.camera),
            //   enabled: !isDesktopApp,
            //   onTap: () => Navigator.of(context).pop(InsertVideoSource.camera),
            // ),
            const SizedBox(
              height: 8,
            ),
            Container(
              height: 5,
              width: 34,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.2),
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            ListTile(
              title: Text(context.loc.link),
              subtitle: Text(
                context.loc.pasteAVideoUsingALink,
              ),
              leading: const Icon(Icons.link),
              onTap: () => Navigator.of(context).pop(InsertVideoSource.link),
            ),
          ],
        ),
      ),
    );
  }
}

Future<InsertVideoSource?> showSelectVideoSourceDialog({
  required BuildContext context,
}) async {
  final imageSource = await showModalBottomSheet<InsertVideoSource>(context: context,
    backgroundColor: Colors.transparent,
    constraints: const BoxConstraints(maxWidth: 640),
    builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: const SelectVideoSourceDialog()),
  );
  return imageSource;
}
