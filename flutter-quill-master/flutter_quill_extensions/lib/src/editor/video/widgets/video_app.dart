import 'dart:io' show File;
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../common/utils/utils.dart';

class VideoApp extends StatefulWidget {
  const VideoApp({
    required this.videoUrl,
    required this.readOnly,
    super.key,
    this.onVideoInit,
  });

  final String videoUrl;
  final bool readOnly;
  final void Function(GlobalKey videoContainerKey)? onVideoInit;

  @override
  VideoAppState createState() => VideoAppState();
}

class VideoAppState extends State<VideoApp> {
  VideoPlayerController? _videoController;
  YoutubePlayerController? _youtubeController;

  GlobalKey videoContainerKey = GlobalKey();
  bool get isYouTube => getYoutubeVideoId(widget.videoUrl) != null;

  @override
  void initState() {
    super.initState();

    if (isYouTube) {
      final videoId = getYoutubeVideoId(widget.videoUrl)!;
      _youtubeController = YoutubePlayerController(
        initialVideoId: videoId,
        flags: const YoutubePlayerFlags(
          autoPlay: false,
          mute: false,
        ),
      );
    } else {
      _videoController = isHttpUrl(widget.videoUrl)
          ? VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl))
          : VideoPlayerController.file(File(widget.videoUrl));

      _videoController!.initialize().then((_) {
        setState(() {});
        widget.onVideoInit?.call(videoContainerKey);
      }).catchError((error) {
        setState(() {});
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final defaultStyles = DefaultStyles.getInstance(context);

    if (isYouTube) {
      return Container(
        key: videoContainerKey,
        height: 200,
        child: YoutubePlayer(
          controller: _youtubeController!,
          showVideoProgressIndicator: true,
          progressIndicatorColor: Colors.blueAccent,
        ),
      );
    }

    if (_videoController == null || !_videoController!.value.isInitialized) {
      return VideoProgressIndicator(
        _videoController!,
        allowScrubbing: true,
        colors: const VideoProgressColors(playedColor: Colors.blue),
      );
    }

    if (_videoController!.value.hasError) {
      if (widget.readOnly) {
        return RichText(
          text: TextSpan(
            text: widget.videoUrl,
            style: defaultStyles.link,
            // recognizer: TapGestureRecognizer()
            //   ..onTap = () => launchUrl(Uri.parse(widget.videoUrl)),
          ),
        );
      }
      return Text(widget.videoUrl, style: defaultStyles.link);
    }

    return Container(
      key: videoContainerKey,
      child: InkWell(
        onTap: () {
          setState(() {
            _videoController!.value.isPlaying
                ? _videoController!.pause()
                : _videoController!.play();
          });
        },
        child: Stack(
          alignment: Alignment.center,
          children: [
            Center(
              child: AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              ),
            ),
            if (!_videoController!.value.isPlaying)
              Container(
                color: const Color(0xfff5f5f5),
                child: const Icon(
                  Icons.play_arrow,
                  size: 60,
                  color: Colors.blueGrey,
                ),
              )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _videoController?.dispose();
    _youtubeController?.dispose();
    super.dispose();
  }

  String? getYoutubeVideoId(String url) {
    return YoutubePlayer.convertUrlToId(url);
  }
}
