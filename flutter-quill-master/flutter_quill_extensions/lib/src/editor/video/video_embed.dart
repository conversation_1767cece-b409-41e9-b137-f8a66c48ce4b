import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';

import '../../common/utils/element_utils/element_utils.dart';
import 'config/video_config.dart';
import 'widgets/video_app.dart';

class QuillEditorVideoEmbedBuilder extends EmbedBuilder {
  const QuillEditorVideoEmbedBuilder({
    required this.config,
  });

  final QuillEditorVideoEmbedConfig config;

  @override
  String get key => BlockEmbed.videoType;

  @override
  bool get expanded => false;

  @override
  Widget build(
      BuildContext context,
      EmbedContext embedContext,
      ) {
    final videoUrl = embedContext.node.value.data;

    final customVideoBuilder = config.customVideoBuilder;
    if (customVideoBuilder != null) {
      final videoWidget = customVideoBuilder(videoUrl, embedContext.readOnly);
      if (videoWidget != null) {
        return _buildVideoWithControls(
          context,
          embedContext,
          videoWidget,
        );
      }
    }

    final ((elementSize), margin, alignment) = getElementAttributes(
      embedContext.node,
      context,
    );

    final width = elementSize.width;
    final height = elementSize.height;

    final videoWidget = Container(
      width: width,
      height: height,
      margin: EdgeInsets.all(margin ?? 0.0),
      alignment: alignment,
      child: VideoApp(
        videoUrl: videoUrl,
        readOnly: embedContext.readOnly,
        onVideoInit: config.onVideoInit,
      ),
    );

    return _buildVideoWithControls(context, embedContext, videoWidget);
  }

  Widget _buildVideoWithControls(
      BuildContext context,
      EmbedContext embedContext,
      Widget videoWidget,
      ) {
    // If it's read-only, don't show controls
    if (embedContext.readOnly) {
      return videoWidget;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        videoWidget,
        // Remove button
        Positioned(
          top: -8,
          right: -8,
          child: GestureDetector(
            onTap: () => _removeVideo(context, embedContext),
            child: Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _removeVideo(BuildContext context, EmbedContext embedContext) {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: AlertDialog(
            title: const Text('Remove Video'),
            content: const Text('Are you sure you want to remove this video?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _deleteVideoEmbed(embedContext);
                },
                child: const Text(
                  'Remove',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Deletes the video embed from the document.
/// This method finds the correct offset of the video embed
  /// and removes it from the document.
  void _deleteVideoEmbed(EmbedContext embedContext) {
    final controller = embedContext.controller;
    final delta = controller.document.toDelta();

    int? correctOffset;
    var currentOffset = 0;

    // Loop through delta to find the correct video embed
    for (final op in delta.toList()) {
      if (op.data is Map<String, dynamic>) {
        final embed = op.data as Map<String, dynamic>;
        if (embed.containsKey(BlockEmbed.videoType) &&
            embed[BlockEmbed.videoType] == embedContext.node.value.data) {
          correctOffset = currentOffset;
          break;
        }
      }
      currentOffset += op.length!;
    }

    if (correctOffset == null) {
      debugPrint("Error: Embed node not found for video");
      return;
    }

    print("Removing video at offset: $correctOffset");

    // Remove video at correct offset
    controller.replaceText(
      correctOffset,
      1, // Length of the video embed
      '',
      TextSelection.collapsed(offset: correctOffset > 0 ? correctOffset - 1 : 0),
    );

  }
}