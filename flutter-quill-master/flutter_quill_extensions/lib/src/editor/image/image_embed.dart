import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';

import '../../common/utils/element_utils/element_utils.dart';
import 'config/image_config.dart';
import 'widgets/image.dart';

class QuillEditorImageEmbedBuilder extends EmbedBuilder {
  QuillEditorImageEmbedBuilder({
    required this.config,
  });

  final QuillEditorImageEmbedConfig config;

  @override
  String get key => BlockEmbed.imageType;

  @override
  bool get expanded => false;

  final ValueNotifier<String?> tappedImageId = ValueNotifier<String?>(null);

  @override
  Widget build(
      BuildContext context,
      EmbedContext embedContext,
      ) {
    final imageSource = standardizeImageUrl(embedContext.node.value.data);
    final ((imageSize), margin, alignment) = getElementAttributes(
      embedContext.node,
      context,
    );

    final width = imageSize.width;
    final height = imageSize.height;

    final imageWidget = getImageWidgetByImageSource(
      context: context,
      imageSource,
      imageProviderBuilder: config.imageProviderBuilder,
      imageErrorWidgetBuilder: config.imageErrorWidgetBuilder,
      alignment: alignment,
      height: height,
      width: width,
    );

    // Generate a unique ID for the image based on the offset
    final uniqueImageId = '${embedContext.node.offset}_${imageSource.hashCode}';

    return GestureDetector(
      onTap: () {
        tappedImageId.value = uniqueImageId;
        final onImageClicked = config.onImageClicked;
        print('image sorce == ${imageSource}');
        if (onImageClicked != null) {
          onImageClicked(imageSource);
          return;
        }
      },
      child: ValueListenableBuilder<String?>(
        valueListenable: tappedImageId,
        builder: (context, value, child) {
          final isSelected = value == uniqueImageId;

          return Stack(
            alignment: Alignment.topRight,
            children: [
              Builder(
                builder: (context) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 18),
                    child: Container(
                      // height: 220,
                      // width: double.infinity,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF6D11D2)
                              : Colors.transparent,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: imageWidget,
                      ),
                    ),
                  );
                },
              ),
              if (isSelected)
                Positioned(
                  top: 35,
                  right: 20,
                  child: InkWell(
                    onTap: () async {
                      if (await config.shouldRemoveImageCallback?.call(imageSource) == false) {
                        return;
                      }

                      final controller = embedContext.controller;
                      final delta = controller.document.toDelta();

                      int? correctOffset;
                      var currentOffset = 0;

                      // Loop through delta to find the correct image embed
                      for (final op in delta.toList()) {
                        if (op.data is Map<String, dynamic>) {
                          final embed = op.data as Map<String, dynamic>;
                          if (embed.containsKey(BlockEmbed.imageType) &&
                              standardizeImageUrl(embed[BlockEmbed.imageType]) == imageSource) {
                            correctOffset = currentOffset;
                            break;
                          }
                        }
                        currentOffset += op.length!;
                      }

                      if (correctOffset == null) {
                        debugPrint("Error: Embed node not found for image: $imageSource");
                        return;
                      }

                      print("Removing image at offset: $correctOffset");

                      // Remove image at correct offset
                      controller.replaceText(
                        correctOffset,
                        1,
                        '',
                        TextSelection.collapsed(offset: correctOffset > 0 ? correctOffset - 1 : 0),
                      );
                      // Callback for image removal
                      await config.onImageRemovedCallback.call(imageSource);
                    },
                    child: Image.asset(
                      "assets/create/cross.png",
                      height: 32,
                      width: 32,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
