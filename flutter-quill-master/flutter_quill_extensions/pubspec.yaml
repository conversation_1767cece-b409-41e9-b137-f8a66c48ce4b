name: flutter_quill_extensions
description: Embed extensions for flutter_quill including image, video, formula and etc.
version: 11.0.0-dev.4
homepage: https://github.com/singerdmx/flutter-quill/tree/master/flutter_quill_extensions/
repository: https://github.com/singerdmx/flutter-quill/tree/master/flutter_quill_extensions/
issue_tracker: https://github.com/singerdmx/flutter-quill/issues/
documentation: https://github.com/singerdmx/flutter-quill/tree/master/flutter_quill_extensions/
topics: [editor, text, rich-text-editor, quill, flutter-quill]

platforms:
  android:
  ios:
  linux:
  macos:
  web:
  windows:

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

# The versions listed here indicate the minimum required for dependencies 
# and do not affect those used in the client's application.
# For more information, visit:
# https://dart.dev/tools/pub/dependencies#version-constraints
# https://docs.flutter.dev/packages-and-plugins/using-packages#conflict-resolution
# https://dart.dev/tools/pub/versioning
# https://dart.dev/guides/libraries/private-files#pubspec-lock
# https://dart.dev/tools/pub/pubspec#sdk-constraints
dependencies:
  flutter:
    sdk: flutter

  # Dart Packages
  http: ^1.0.0
  path: ^1.8.0
  meta: ^1.7.0
  universal_html: ^2.2.4
  cross_file: ^0.3.3+4
  get_storage: ^2.1.1
  photo_view: ^0.15.0
  cached_network_image: ^3.4.1
  shimmer: ^2.0.0
  # Plugins
  video_player: ^2.8.0
  url_launcher: ^6.2.1
  gal: ^2.3.0
  gal_linux: ^0.1.0
  image_picker: ^1.1.2
  permission_handler: any
  youtube_player_flutter: any

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
