<!-- 
Briefly describe your changes and summarize in the title.
Contributor Guide: https://github.com/singerdmx/flutter-quill/blob/master/CONTRIBUTING.md
Package versioning is automated.
Add updates to `Unreleased` in `CHANGELOG.md` following [Keep a Changelog](https://keepachangelog.com/en/1.1.0/) format.
-->

## Description

*Describe what this PR does. If modifying behavior, explain the current and new behavior, along with the motivation.*

## Related Issues

<!--
Replace this paragraph with a list of issues related to this PR from the [issue database](https://github.com/singerdmx/flutter-quill/issues). Indicate, which of these issues are resolved or fixed by this PR.
*e.g.*
- *Fix #123*
- *Related #456*
-->

## Type of Change

<!---
Check the boxes that apply with x and leave the others empty. For example:
- [ ] ✨ **New feature:** Adds new functionality without breaking existing features.
- [x] 🛠️ **Bug fix:** Resolves an issue without changing current behavior.
-->

- [ ] ✨ **Feature:** New functionality without breaking existing features.
- [ ] 🛠️ **Bug fix:** Resolves an issue without altering current behavior.
- [ ] 🧹 **Refactor:** Code reorganization, no behavior change.
- [ ] ❌ **Breaking:** Alters existing functionality and requires updates.
- [ ] 🧪 **Tests:** New or modified tests
- [ ] 📝 **Documentation:** Updates or additions to documentation.
- [ ] 🗑️ **Chore:** Routine tasks, or maintenance.
- [ ] ✅ **Build configuration change:** Build/configuration changes.
