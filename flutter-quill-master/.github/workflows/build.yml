# TODO: Update the workflow to build on Android, iOS, desktop (macOS and Windows) instead of just Linux and Web

name: 🏗️ Build the example

on:
  pull_request:
    branches: [master, dev]

jobs:
  build_linux:
    name: 🐧 Build Linux and Web Apps
    runs-on: ubuntu-latest

    steps:
      - name: 📦 Checkout repository
        uses: actions/checkout@v4

      - name: 🛠️ Set up Flutter 
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          cache: true

      - name: 🔍 Verify Flutter installation
        run: flutter --version

      - name: 📥 Install Flutter dependencies
        run: flutter pub get

      - name: 🌐 Build Flutter Web Application
        run: flutter build web --release --verbose --dart-define=CI=true
        working-directory: ./example

      # TODO: Update the CI to not upgrade all the packages to save some time
      - name: 🔄 Update and Upgrade APT Packages
        run: sudo apt update -y && sudo apt upgrade -y

      - name: 🛠️ Install Flutter Linux Prerequisites
        run: sudo apt install -y curl git unzip xz-utils zip libglu1-mesa

      - name: 🧩 Install Flutter Linux Desktop Dependencies
        run: sudo apt install -y clang cmake git ninja-build pkg-config libgtk-3-dev liblzma-dev libstdc++-12-dev

      - name: 🐧 Build Flutter Linux Desktop Application
        run: flutter build linux --release --verbose --dart-define=CI=true
        working-directory: ./example
