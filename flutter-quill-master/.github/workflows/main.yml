name: 🧪 Run Tests

# TODO: Split the tests into tests.yml and quality checks into checks.yml once start using https://pub.dev/packages/melos

on:
  push:
    branches: [master, dev]
  pull_request:
    branches: [master, dev]

jobs:
  tests:
    name: Check lints and tests
    runs-on: ubuntu-latest

    steps:
      - name: 📦 Checkout repository
        uses: actions/checkout@v4

      - name: 🛠️ Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          cache: true

      - name: 🔍 Verify Flutter installation
        run: flutter --version

      - name: 📥 Install Flutter dependencies
        run: flutter pub get
      
      - name: 📦 Install flutter_quill_extensions dependencies
        run: flutter pub get -C flutter_quill_extensions

      - name: 📦 Install flutter_quill_test dependencies
        run: flutter pub get -C flutter_quill_test

      - name: 🔍 Run Flutter analysis
        run: flutter analyze

      - name: 🧹 Check Dart code formatting
        run: dart format --set-exit-if-changed .
      
      - name: 🔍 Preview Dart proposed changes
        run: dart fix --dry-run

      - name: 📦 Check if package is ready for publishing
        run: flutter pub publish --dry-run

      - name: 🧪 Run Flutter tests
        run: flutter test

      - name: 🔍 Check the translations
        run: dart ./scripts/translations_check.dart

      - name: 📥 Install cider
        run: dart pub global activate cider

      # TODO: Need a more strict way to validate the format that uses https://keepachangelog.com/en/1.1.0/
      - name: 🔍 Validate CHANGELOG.md format
        run: $HOME/.pub-cache/bin/cider list CHANGELOG.md
