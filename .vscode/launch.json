{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "incentiai-app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "incentiai-app (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "incentiai-app (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter-quill-master",
            "cwd": "flutter-quill-master",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter-quill-master (profile mode)",
            "cwd": "flutter-quill-master",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter-quill-master (release mode)",
            "cwd": "flutter-quill-master",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_quill_extensions",
            "cwd": "flutter-quill-master/flutter_quill_extensions",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_quill_extensions (profile mode)",
            "cwd": "flutter-quill-master/flutter_quill_extensions",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_quill_extensions (release mode)",
            "cwd": "flutter-quill-master/flutter_quill_extensions",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}