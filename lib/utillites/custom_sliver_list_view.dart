import 'package:flutter/material.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../constants/app_size_constant.dart';
import 'app_theme.dart';
import 'loader.dart';

class CustomSliverListView<T> extends StatefulWidget {
  final List<T>? items;
  final Widget Function(BuildContext, T, int index) itemBuilder;
  final Axis scrollDirection;
  final EdgeInsetsGeometry? padding;
  final Widget? emptyWidget;
  final Widget? maximumReachedWidget;
  final Future Function() onLoadMore;
  final EdgeInsetsGeometry loaderPadding;
  final bool hasMoreData;
  final bool isLoading;
  final Widget? pageOverWidget;

  const CustomSliverListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.scrollDirection = Axis.vertical,
    this.padding,
    this.emptyWidget,
    this.maximumReachedWidget,
    this.loaderPadding = const EdgeInsets.all(8.0),
    required this.onLoadMore,
    this.hasMoreData = true,
    this.isLoading = false,
    this.pageOverWidget,
  });

  @override
  _CustomSliverListViewState<T> createState() => _CustomSliverListViewState<T>();
}

class _CustomSliverListViewState<T> extends State<CustomSliverListView<T>> {
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore || !widget.hasMoreData || widget.isLoading) return;
    
    setState(() {
      _isLoadingMore = true;
    });

    try {
      await widget.onLoadMore();
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final itemCount = widget.items?.length ?? 0;

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == itemCount) {
            // Only trigger load more if we're near the end and not already loading
            if (widget.hasMoreData && !widget.isLoading && !_isLoadingMore) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _loadMore();
              });
            }

            if (index == 0 && !widget.hasMoreData) {
              return widget.emptyWidget ??
                  Center(
                    child: TypoGraphy(
                      text: "No any results found",
                      level: 3,
                      color: AppTheme.lightGrey,
                    ),
                  );
            }

            if (!widget.hasMoreData) {
              return Padding(
                padding: EdgeInsets.only(
                    top: MySize.size10 ?? 10,
                    bottom: MySize.size20 ?? 20,
                    left: paddingHoriZontal,
                    right: paddingHoriZontal),
                child: widget.maximumReachedWidget ??
                    Center(
                      child: TypoGraphy(
                        text: "You have reached maximum results",
                        level: 3,
                        color: AppTheme.lightGrey,
                      ),
                    ),
              );
            }

            return Center(
                child: Padding(
              padding: widget.loaderPadding,
              child: Loader(),
            ));
          }

          if (index >= itemCount) {
            return const SizedBox();
          }

          return widget.itemBuilder(context, widget.items![index], index);
        },
        childCount: itemCount + 1,
      ),
    );
  }
}
