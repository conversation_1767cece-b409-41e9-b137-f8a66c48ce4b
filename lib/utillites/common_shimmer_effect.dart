import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../constants/app_size_constant.dart';
import 'app_theme.dart';

class ShimmerPostCard extends StatelessWidget {
  final bool isPadding;
  const ShimmerPostCard({super.key,this.isPadding = false});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.baseShimmer,
      highlightColor: AppTheme.highlightShimmer,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: isPadding ? 0 : 10, vertical: 10),
        padding: EdgeInsets.all(10),
        // decoration: BoxDecoration(
        //   color: Colors.white,
        //   borderRadius: BorderRadius.circular(12),
        // ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Row
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.grey[400],
                ),
                SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 120,
                      height: 12,
                      color: Colors.grey[400],
                    ),
                    <PERSON>zed<PERSON><PERSON>(height: 5),
                    Container(
                      width: 80,
                      height: 10,
                      color: Colors.grey[400],
                    ),
                  ],
                ),
                Spacer(),
                Icon(Icons.more_horiz, color: Colors.grey[400]),
              ],
            ),
            SizedBox(height: 10),

            // Image Placeholder
            Container(
              height: MySize.getScaledSizeHeight(520),
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            SizedBox(height: 10),

            // Title Placeholder
            Container(
              width: double.infinity,
              height: 14,
              color: Colors.grey[400],
            ),
            SizedBox(height: 5),
            Container(
              width: 150,
              height: 14,
              color: Colors.grey[400],
            ),
            SizedBox(height: 10),

            // Description Placeholder
            Container(
              width: double.infinity,
              height: 12,
              color: Colors.grey[400],
            ),
            SizedBox(height: 5),
            Container(
              width: 250,
              height: 12,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
