import 'dart:ui';
import 'package:flutter/material.dart';

Widget commonBlurredContainer({
  required Widget child,
  double? height,
  double? width,
  double sigmaX = 10,
  double sigmaY = 10,
  double blurOpacity = 0.15,
  double borderRadius = 10,
  Color backgroundColor = Colors.white,
  bool centerChild = true,
  List<BoxShadow>? boxShadow,
  EdgeInsetsGeometry? padding,
  EdgeInsetsGeometry? margin,
  AlignmentGeometry? alignment,
}) {
  return Container(
    height: height,
    width: width,
    margin: margin,
    padding: padding,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: boxShadow ??
          [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 10,
              offset: Offset(0, 1),
            ),
          ],
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: sigmaX, sigmaY: sigmaY),
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor.withOpacity(blurOpacity),
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          alignment: alignment,
          child: centerChild ? Center(child: child) : child,
        ),
      ),
    ),
  );
}
