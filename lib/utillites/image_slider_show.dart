import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import 'network_image.dart';

class ImageSlideshow extends StatefulWidget {
  const ImageSlideshow({
    super.key,
    required this.children,
    this.width = double.infinity,
    this.height = 200,
    this.initialPage = 0,
    this.indicatorColor,
    this.indicatorBackgroundColor = Colors.grey,
    this.onPageChanged,
    this.autoPlayInterval,
    this.isLoop = false,
    this.indicatorRadius = 3,
    this.indicatorPadding = 4,
    this.indicatorBottomPadding = 10,
    this.disableUserScrolling = false,
  });

  final List<Widget> children;

  /// Width of the [ImageSlideshow].
  final double width;

  /// Height of the [ImageSlideshow].
  final double height;

  /// The page to show when first creating the [ImageSlideshow].
  final int initialPage;

  /// The color to paint the indicator.
  final Color? indicatorColor;

  /// The color to paint behind th indicator.
  final Color? indicatorBackgroundColor;

  /// Called whenever the page in the center of the viewport changes.
  final ValueChanged<int>? onPageChanged;

  /// Auto scroll interval.
  ///
  /// Do not auto scroll when you enter null or 0.
  final int? autoPlayInterval;

  /// Loops back to first slide.
  final bool isLoop;

  /// Radius of CircleIndicator.
  final double indicatorRadius;

  /// Padding of CircleIndicator.
  final double indicatorPadding;

  /// BottomPadding to Indicator.
  final double indicatorBottomPadding;

  /// Disable page changes by the user.
  final bool disableUserScrolling;

  @override
  ImageSlideshowState createState() => ImageSlideshowState();
}

class ImageSlideshowState extends State<ImageSlideshow> {
  late final ValueNotifier<int> _currentPageNotifier;
  late final PageController _pageController;
  late final ScrollBehavior _scrollBehavior;
  Timer? _timer;

  void _onPageChanged(int index) {
    _currentPageNotifier.value = index;
    if (widget.onPageChanged != null) {
      final correctIndex = index % widget.children.length;
      widget.onPageChanged!(correctIndex);
    }
  }

  void _autoPlayTimerStart() {
    _timer?.cancel();
    _timer = Timer.periodic(
      Duration(milliseconds: widget.autoPlayInterval!),
          (timer) {
        int nextPage;
        if (widget.isLoop) {
          nextPage = _currentPageNotifier.value + 1;
        } else {
          if (_currentPageNotifier.value < widget.children.length - 1) {
            nextPage = _currentPageNotifier.value + 1;
          } else {
            return;
          }
        }

        goToPage(nextPage);
      },
    );
  }

  void goToPage(int index) {
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeIn,
      );
    }
  }

  void stopAutoPlay() {
    _timer?.cancel();
  }

  @override
  void initState() {
    _scrollBehavior = widget.disableUserScrolling
        ? const ScrollBehavior().copyWith(
      scrollbars: false,
      dragDevices: {},
    )
        : const ScrollBehavior().copyWith(
      scrollbars: false,
      dragDevices: {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      },
    );

    _pageController = PageController(
      initialPage: widget.initialPage,
    );

    _currentPageNotifier = ValueNotifier(widget.initialPage);

      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final futures = <Future>[];
        for (final child in widget.children) {
          if (child is NetworkImageComponent &&
              child.imageUrl != null &&
              child.imageUrl!.isNotEmpty) {
            debugPrint('image url to precache: ${child.imageUrl}');
            final future = precacheImage(
              CachedNetworkImageProvider(child.imageUrl!),
              context,
            );
            futures.add(future);
          }
        }
        await Future.wait(futures);
        debugPrint('All images precached');
      });

    if (widget.autoPlayInterval != null && widget.autoPlayInterval != 0) {
      _autoPlayTimerStart();
    }
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _currentPageNotifier.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        children: [
          PageView.builder(
            scrollBehavior: _scrollBehavior,
            onPageChanged: _onPageChanged,
            itemCount: widget.isLoop ? null : widget.children.length,
            controller: _pageController,
            itemBuilder: (context, index) {
              final correctIndex = index % widget.children.length;
              return widget.children[correctIndex];
            },
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: widget.indicatorBottomPadding,
            child: ValueListenableBuilder<int>(
              valueListenable: _currentPageNotifier,
              builder: (context, value, child) {
                return Indicator(
                  count: widget.children.length,
                  currentIndex: value % widget.children.length,
                  activeColor: widget.indicatorColor,
                  backgroundColor: widget.indicatorBackgroundColor,
                  radius: widget.indicatorRadius,
                  padding: widget.indicatorPadding,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}


class Indicator extends StatelessWidget {
  const Indicator({
    super.key,
    required this.count,
    required this.currentIndex,
    this.activeColor,
    this.backgroundColor,
    required this.padding,
    required this.radius,
  });

  final int count;
  final int currentIndex;
  final Color? activeColor;
  final Color? backgroundColor;
  final double padding;
  final double radius;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: padding,
      runSpacing: padding,
      alignment: WrapAlignment.center,
      children: List.generate(
        count,
            (index) {
          return CircleAvatar(
            radius: radius,
            backgroundColor:
            currentIndex == index ? activeColor : backgroundColor,
          );
        },
      ),
    );
  }
}

