import 'package:flutter/material.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:lottie/lottie.dart';
import '../constants/app_size_constant.dart';
import 'app_theme.dart';
import 'buttons.dart';

class CustomPopup extends StatelessWidget {
  final VoidCallback? onClose;
  final String? svgImagePath;
  final String? title;
  final double? paddingTop;
  final String closeBtnText;
  final String? animationPath;
  final String? submitBtnText;
  final Color? closeBtnColor;
  final Color? submitBtnColor;
  final String? message;
  final Color? backGroundColor;
  final Color? borderColor;
  final Color? onCloseBtnTextColor;
  final Color? onSubmitBtnTextColor;
  final Color? bodyBgColor;
  final void Function()? onCloseBtnClick;
  final void Function()? onSubmitBtnClick;
  final Widget? child;
  final bool submitOnly;
  final bool isAnimation;
  final Widget? positionedChild;
  final EdgeInsets? bodyPadding;

  const CustomPopup({
    super.key,
    this.onClose,
    this.bodyPadding,
    this.paddingTop,
    this.submitOnly = false,
    this.title,
    this.message,
    this.closeBtnText = "Close",
    this.submitBtnText,
    this.svgImagePath,
    this.backGroundColor,
    this.borderColor,
    this.onCloseBtnClick,
    this.onSubmitBtnClick,
    this.child,
    this.closeBtnColor,
    this.submitBtnColor,
    this.onCloseBtnTextColor,
    this.onSubmitBtnTextColor,
    this.positionedChild,
    this.isAnimation = false,
    this.bodyBgColor,
    this.animationPath,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Close Icon
        Align(
          alignment: Alignment.center,
          child: InkWell(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            onTap: onClose ??
                () {
                  Navigator.of(context).pop();
                },
            child: Container(
              height: MySize.size30,
              width: MySize.size30,
              decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                  border: Border.all(width: MySize.size2 ?? 2, color: AppTheme.white)),
              child: Icon(Icons.close, color: AppTheme.white),
            ),
          ),
        ),
        Space.height(MySize.size15 ?? 15),
        // Popup body
        Stack(children: [
          Container(
            width: MediaQuery.of(context).size.width - (MySize.size15 ?? 15),
            padding: bodyPadding ?? EdgeInsets.only(
                top: paddingTop ?? MySize.size70 ?? 70,
                bottom: MySize.size20 ?? 20,
                right: MySize.size15 ?? 15,
                left: MySize.size15 ?? 15),
            decoration: BoxDecoration(
              color: bodyBgColor ?? Colors.white,
              borderRadius: BorderRadius.circular(MySize.size15 ?? 15),
            ),
            child: child ??
                Column(
                  children: [
                    SizedBox(height: MySize.size50 ?? 50),
                    TypoGraphy(
                      text: title,
                      level: 6,
                      color: AppTheme.darkGrey,
                    ),
                    SizedBox(height: MySize.size10 ?? 10),
                    // Line
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: MySize.size40 ?? 40),
                      child: TypoGraphy(
                        text: message,
                        level: 2,
                        textAlign: TextAlign.center,
                        fontWeight: FontWeight.w400,
                        color: AppTheme.darkGrey,
                      ),
                    ),
                    SizedBox(height: MySize.size80 ?? 80),
                    // Button
                  ],
                ),
          ),
          if (isAnimation)
            Positioned.fill(
              child: Lottie.asset(
                animationPath!,
                fit: BoxFit.fill,
                repeat: false,
              ),
            ),
          Positioned(
            bottom: MySize.size20 ?? 20,
            right: MySize.size20 ?? 20,
            left: MySize.size20 ?? 20,
            child: positionedChild ??
                Row(
                  children: [
                    if (!submitOnly)
                      Expanded(
                        child: Buttons(
                            textColor: onCloseBtnTextColor ?? AppTheme.red,
                            buttonTextWeight: FontWeight.w600,
                            height: MySize.size36 ?? 36,
                            buttonText: closeBtnText,
                            width: double.minPositive,
                            type: ButtonType.primary,
                            buttonTextLevel: 3,
                            color: closeBtnColor ?? AppTheme.red[50],
                            onTap: onCloseBtnClick ??
                                () {
                                  Navigator.pop(context);
                                }),
                      ),
                    if (!submitOnly) SizedBox(width: MySize.size20 ?? 20),
                    Expanded(
                      child: Buttons(
                          textColor: onCloseBtnTextColor ?? AppTheme.white,
                          height: MySize.size36 ?? 36,
                          buttonText: submitBtnText ?? "",
                          width: double.minPositive,
                          type: ButtonType.primary,
                          buttonTextLevel: 3,
                          buttonTextWeight: FontWeight.w600,
                          color: submitBtnColor ?? AppTheme.red,
                          onTap: onSubmitBtnClick ??
                              () {
                                Navigator.pop(context);
                              }),
                    ),
                  ],
                ),
          ),
        ]),
      ],
    );
  }
}
