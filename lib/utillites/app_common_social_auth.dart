import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:signin_with_linkedin/signin_with_linkedin.dart';
import 'dart:developer';
import '../constants/api.dart';
import '../main.dart';
import '../services/api_manager.dart';
import 'api_use_dialog.dart';
import 'app_global_variables.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;


class CommonAPIs {

  static Future<void> signInWithLinkedIn({
    required BuildContext context,
    required void Function(dynamic response, String message) successCallback,
    required void Function(dynamic message, String statusCode) failureCallback,
  }) async {
    try {
      ApiManager apiManager = ApiManager();
      SignInWithLinkedIn.signIn(
        context,
        config: linkedInConfig,
        onGetUserProfile: (tokenData, user) async {
          try {
            log('Auth token data: ${tokenData.toJson()}');
            log('LinkedIn User: ${user.toJson()}');

            // Save LinkedIn user data
            box.write("LinkedinPhotoUrl", user.picture);
            String? accessToken = tokenData.accessToken;
            log("value === ${accessToken}");
            if (accessToken != null) {
              // API call to authenticate with LinkedIn access token
              box.write("LinkedInAccessToken", accessToken);
              await apiManager.callApi(
                APIS.auth.linkedinAuth,
                params: {"accessToken": accessToken},
                successCallback: successCallback,
                failureCallback: failureCallback,
              );
            } else {
            }
          } catch (error) {
            log('Error: $error');
          }
        },
        onSignInError: (error) {
          log('Error on sign in: $error');
        },
      );

    } catch (error) {
      log('Error: $error');
    }
  }

  static Future<void> signInWithApple({
    required BuildContext context,
    required void Function(dynamic response, String message) successCallback,
    required void Function(dynamic message, String statusCode) failureCallback,
  }) async {
    try {
      ApiManager apiManager = ApiManager();
      final FirebaseAuth auth = FirebaseAuth.instance;
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final AuthCredential credential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );
      final UserCredential authResult =
          await auth.signInWithCredential(credential);
      final firebase_auth.User? user = authResult.user;
      print("user ==> ${appleCredential}");
      print("user givenName==> ${appleCredential.givenName}");
      print("user  familyName==> ${appleCredential.familyName}");
      box.write('firstName', appleCredential.givenName);
      box.write('lastName', appleCredential.familyName);
      // box.write('image', response['data']['image']);
      if (user != null) {
        final String? idToken = await user.getIdToken();
        return apiManager.callApi(
          APIS.auth.socialAuth,
          params: {
            "firebaseToken": idToken,
          },
          successCallback: successCallback,
          failureCallback: failureCallback,
        );
      }
    } catch (error) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log('Error: $error');
    }
  }

  static Future<void> signInWithGoogle({
    required BuildContext context,
    required void Function(dynamic response, String message) successCallback,
    required void Function(dynamic message, String statusCode) failureCallback,
  }) async {
    try {
      ApiManager apiManager = ApiManager();
      final GoogleSignIn googleSignIn = GoogleSignIn();
      final GoogleSignInAccount? googleSignInAccount =
          await googleSignIn.signIn();
      if (googleSignInAccount != null) {
        final GoogleSignInAuthentication googleSignInAuthentication =
        await googleSignInAccount!.authentication;
        final AuthCredential credential = GoogleAuthProvider.credential(
          accessToken: googleSignInAuthentication.accessToken,
          idToken: googleSignInAuthentication.idToken,
        );
        final UserCredential authResult =
        await FirebaseAuth.instance.signInWithCredential(credential);
        final firebase_auth.User? user = authResult.user;
        box.write("GooglePhotoUrl", user?.photoURL);

        if (user != null) {
          final String? idToken = await user.getIdToken();
          return apiManager.callApi(
            APIS.auth.socialAuth,
            params: {
              "firebaseToken": idToken,
            },
            successCallback: successCallback,
            failureCallback: failureCallback,
          );
        }
      }
      app.resolve<CustomDialogs>().hideCircularDialog(context);

    } catch (error) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log('Error: $error');
    }
  }
}
