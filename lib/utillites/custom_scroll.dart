import 'package:flutter/material.dart';

import '../constants/app_size_constant.dart';
import 'app_theme.dart';

class CustomHorizontalScrollbar extends StatefulWidget {
  final Widget child;
  final ScrollController? scrollController;

  CustomHorizontalScrollbar({
    super.key,
    required this.child,
    this.scrollController,
  });

  @override
  State<CustomHorizontalScrollbar> createState() =>
      _CustomHorizontalScrollbarState();
}

class _CustomHorizontalScrollbarState extends State<CustomHorizontalScrollbar> {
  late final ScrollController _internalScrollController;
  ScrollController get _controller => widget.scrollController ?? _internalScrollController;

  final ValueNotifier<double> _thumbOffset = ValueNotifier<double>(0.0);
  final double _thumbWidth = MySize.getScaledSizeWidth(130);

  bool _isExternalController = false;

  void _updateThumbPosition() {
    if (!_controller.hasClients) return;
    final double maxScroll = _controller.position.maxScrollExtent;
    final double scrollOffset = _controller.offset;
    final double viewWidth = _controller.position.viewportDimension;
    final double trackWidth = viewWidth - _thumbWidth;
    final double scrollFraction = maxScroll == 0 ? 0 : scrollOffset / maxScroll;
    _thumbOffset.value = trackWidth * scrollFraction;
  }

  @override
  void initState() {
    super.initState();

    _isExternalController = widget.scrollController != null;
    _internalScrollController = ScrollController();

    _controller.addListener(_updateThumbPosition);
  }

  @override
  void dispose() {
    _controller.removeListener(_updateThumbPosition);
    if (!_isExternalController) {
      _internalScrollController.dispose();
    }
    _thumbOffset.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SingleChildScrollView(
          controller: _controller,
          scrollDirection: Axis.horizontal,
          child: widget.child,
        ),
        Positioned(
          bottom: 4,
          left: MySize.getScaledSizeWidth(30),
          right: 0,
          child: SizedBox(
            height: 8,
            child: ValueListenableBuilder<double>(
              valueListenable: _thumbOffset,
              builder: (context, offset, child) {
                return Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    margin: EdgeInsets.only(left: offset > 0 ? offset : 0),
                    height: 4,
                    width: _thumbWidth,
                    decoration: BoxDecoration(
                      color: AppTheme.grey[50],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
