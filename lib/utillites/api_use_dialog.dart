// Common Dialogs

import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:url_launcher/url_launcher.dart';

import '../constants/app_size_constant.dart';
import 'app_theme.dart';

class CustomDialogs {
  static appAlertDialog(
      {required BuildContext context,
      String? description,
      required String question,
      required VoidCallback onTap,
      TextStyle? questionTextStyle}) async {
    try {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0)), //this right here
              child: Container(
                width: double.infinity,
                margin: const EdgeInsets.only(left: 8, right: 8, top: 20, bottom: 20),
                decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                padding: EdgeInsets.symmetric(
                    horizontal: MySize.size10 ?? 10,
                    vertical: MySize.size5 ?? 5),
                child: Column(mainAxisSize: MainAxisSize.min, children: [
                  TypoGraphy(
                    text: question,
                    textAlign: TextAlign.center,
                    level: 4,
                  ),
                  const Space.height(10),
                  if (description != null)
                    TypoGraphy(
                      text: description,
                      textAlign: TextAlign.center,
                      level: 2,
                      color: AppTheme.darkGrey,
                    ),
                  const Space.height(20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: InkWell(
                            highlightColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            onTap: onTap,
                            child: Container(
                              alignment: Alignment.center,
                              height: MySize.size40,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  color: AppTheme.primary1),
                              child: TypoGraphy(
                                text: "Yes",
                                textAlign: TextAlign.center,
                                level: 3,
                                color: AppTheme.white,
                              ),
                            )),
                      ),
                      const Space.width(12),
                      Expanded(
                        child: InkWell(
                            highlightColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            onTap: () {
                              Get.back();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: MySize.size40,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  border: Border.all(
                                      color: AppTheme.darkGrey[100]!)),
                              child: TypoGraphy(
                                text: "No",
                                textAlign: TextAlign.center,
                                level: 3,
                                color: AppTheme.black,
                              ),
                            )),
                      )
                    ],
                  )
                ]),
              ),
            );
          });
    } catch (e) {
    }
  }

  void showCircularDialog(
    BuildContext context,
  ) {
    CircularDialog.showLoadingDialog(
      context,
    );
  }

  void hideCircularDialog(
    BuildContext context,
  ) {
    Navigator.pop(
      context,
    );
  }

  getDialog(
      {String title = "Oops!",
      String desc = "Some Thing went wrong....",
      String? confirmationText,
      VoidCallback? onTap,
      EdgeInsets? contentPadding,
      bool isNeedRichText = false,
      Widget? richText}) {
    final emailRegex = RegExp(
        r'\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b'); // Regular expression to find email addresses
    final matches =
        emailRegex.allMatches(desc); // Find all emails in the description

    List<TextSpan> spans = [];
    int lastIndex = 0;

    for (var match in matches) {
      if (match.start > lastIndex) {
        // Add text before the email
        spans.add(
          TextSpan(
            text: '${desc.substring(lastIndex, match.start).trim()}\n',
            style: TextStyle(color: Colors.black,fontSize: MySize.size16,fontFamily: "Inter"),
          ),
        );
      }
      // Add the email as a clickable blue link
      spans.add(
        TextSpan(
          text: desc.substring(match.start, match.end),
          style: TextStyle(color: Colors.blue,fontSize: MySize.size16,fontFamily: "Inter"),
          recognizer: TapGestureRecognizer()
            ..onTap = () async {
              final email = desc.substring(match.start, match.end);
              final Uri emailLaunchUri = Uri(
                scheme: 'mailto',
                path: email,
              );
              await launchUrl(emailLaunchUri);
            },
        ),
      );
      lastIndex = match.end;
    }

    if (lastIndex < desc.length) {
      spans.add(
        TextSpan(
          text: '\n${desc.substring(lastIndex).trim()}',
          style: TextStyle(color: Colors.black,fontSize: MySize.size16,fontFamily: "Inter"),
        ),
      );
    }
    return Get.defaultDialog(
      contentPadding: contentPadding ??
          EdgeInsets.symmetric(vertical: Platform.isIOS ? 15 : 10,horizontal: 30),
      barrierDismissible: false,
      title: title,
      titlePadding: EdgeInsets.only(top: Platform.isIOS ? 15 : 10),
      content: isNeedRichText
          ? RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: spans,
              ),
            )
          : Text(
              desc,
              textAlign: TextAlign.center,
            ),
      buttonColor: isNeedRichText ? AppTheme.primary1 : AppTheme.grey,
      textConfirm: confirmationText ?? "Ok",
      confirmTextColor: Colors.white,
      onConfirm: onTap ??
          () {
            Get.back();
          },
    );
  }

  getConfirmDialog({
    String title = "Error",
    String desc = "Some Thing went wrong....",
    VoidCallback? onTap,
  }) {
    return Get.defaultDialog(
      barrierDismissible: false,
      title: title,
      content: Text(
        desc,
        textAlign: TextAlign.center,
      ),
      buttonColor: AppTheme.grey,
      textConfirm: "Ok",
      confirmTextColor: Colors.white,
      onConfirm: (onTap != null)
          ? onTap
          : () {
              Get.back();
            },
    );
  }
}

class CircularDialog {
  static Future<void> showLoadingDialog(
    BuildContext context,
  ) {
    return showDialog(
      context: context,
      builder: (
        BuildContext context,
      ) {
        // ignore: deprecated_member_use
        return WillPopScope(
          child: Center(
            child: Loader(),
          ),
          onWillPop: () async {
            return false;
          },
        );
      },
      barrierDismissible: false,
    );
  }
}
