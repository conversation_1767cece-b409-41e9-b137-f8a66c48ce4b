import 'dart:developer';
import 'dart:io';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:share_plus/share_plus.dart';

import 'app_theme.dart';
class FileDownloader {
  static Future<void> downloadFile(String url, String filename) async {
    try{
      final dio = Dio();
      if (Platform.isAndroid) {
        final downloadsDir = await getExternalStorageDirectory();
        final filePath = '${downloadsDir?.path}/$filename';
        // Download file
        await dio.download(url, filePath);
        print('File saved to: $filePath');
      } else if (Platform.isIOS) {
        // Save temporarily in app directory
        final dir = await getApplicationDocumentsDirectory();
        final filePath = '${dir.path}/$filename';
        await dio.download(url, filePath);
        print('File saved to: $filePath');
        // Open share sheet to export
        await Share.shareXFiles([XFile(filePath)], text: 'Save or share your file');
      }
      CommonFunction.showCustomSnackbar(message: 'File Downloaded Successfully');
    }catch(e){
      log(e.toString());
      CommonFunction.showCustomSnackbar(message: 'File Download Failed',isError: true,backgroundColor: AppTheme.red);
    }
  }
}