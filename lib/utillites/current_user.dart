import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import '../app/routes/app_pages.dart';
import '../constants/api.dart';
import '../main.dart';
import '../models/app_user_data_model.dart';
import '../services/api_manager.dart';
import 'api_use_dialog.dart';

Rx<UserData> userDetails = UserData().obs;
Rx<bool> isNewNotification = false.obs;

class CurrentUser {
  static final CurrentUser _instance = CurrentUser._internal();

  factory CurrentUser() {
    return _instance;
  }

  CurrentUser._internal();

  static UserData get user => userDetails.value;

  static set user(UserData user) => userDetails.value = user;

  static Future<void> getMe({Future<void> Function()? callback}) async {
    ApiManager apiManager = ApiManager();
    return await apiManager.callApi(APIS.user.getMe,
        successCallback: (response, message) async {
      UserData userData = handleApiResponse(response);
      userDetails.value = userData;

      if (callback != null) {
        callback();
      }
    }, failureCallback: (response, message) {
      if (message.isNotEmpty && message != "") {
        print("messag e: $message");
        app.resolve<CustomDialogs>().getDialog(
              title: "Oops!",
              desc: message,
              onTap: () {
                Get.toNamed(Routes.ONBOARDING);
              },
            );
      }
    });
  }

  static Future<void> updateMe(
      {Map<String, dynamic>? params,
      bool isGetMeCall = true,
      Null Function()? successCallback,
      Null Function(String message)? failCallback}) async {
    ApiManager apiManager = ApiManager();
    dio.FormData formData = dio.FormData.fromMap(params!);
    return await apiManager.callApi(APIS.user.updateMe, params: formData,
        successCallback: (response, message) async {
      if (isGetMeCall) getMe();
      if (successCallback != null) successCallback();
    }, failureCallback: (status, message) {
      if (message != "" && message != null) {
        app.resolve<CustomDialogs>().getDialog(title: "Oops!", desc: message);
      }
      if (failCallback != null) failCallback(message);
    });
  }

  static Future<void> deleteMe({Null Function()? callback}) async {
    ApiManager apiManager = ApiManager();
    return await apiManager.callApi(APIS.user.deleteMe,
        successCallback: (response, message) async {
      if (callback != null) {
        callback();
      }
    }, failureCallback: (status, message) {
      if (message.isNotEmpty && message != "") {
        app.resolve<CustomDialogs>().getDialog(title: "Oops!", desc: message);
      }
    });
  }

  static Future<void> clearMe({Null Function()? callback}) async {
    userDetails.value = UserData();
  }

  static Future<void> setFcm({required String fcm}) async {
    ApiManager apiManager = ApiManager();
    return await apiManager
        .callApi(APIS.user.updateMe, params: {"fcmToken": fcm});
  }
}
