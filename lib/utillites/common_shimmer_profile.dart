import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../constants/app_size_constant.dart';
import 'app_theme.dart';
import 'common_shimmer_effect.dart';

class ShimmerProfilePage extends StatelessWidget {
  final bool isProjectDetail;

  const ShimmerProfilePage({super.key, this.isProjectDetail = false});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Shimmer.fromColors(
        baseColor: AppTheme.baseShimmer,
        highlightColor: AppTheme.highlightShimmer,
        child: SingleChildScrollView(
          child: Column(
            children: [
              if (isProjectDetail)
                Container(
                  width: double.infinity,
                  height: MySize.getScaledSizeHeight(182),
                  color: Colors.grey[400], // Placeholder color
                ),
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: isProjectDetail ? 20 : 0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: isProjectDetail
                      ? CrossAxisAlignment.start
                      : CrossAxisAlignment.start,
                  children: [
                    if (!isProjectDetail) ...[
                      Stack(
                        children: [
                          // Cover image placeholder
                          Container(
                            width: double.infinity,
                            height: MySize.getScaledSizeHeight(182),
                            color: Colors.grey[400], // Placeholder color
                          ),

                          // Profile image placeholder
                          SizedBox(height: 10),
                          Padding(
                            padding: EdgeInsets.only(left: 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: MySize.getScaledSizeHeight(63)),

                                // Back Button
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: MySize.size30 ?? 30),
                                  child: Row(
                                    children: [
                                      Shimmer.fromColors(
                                        baseColor: AppTheme.baseShimmer,
                                        highlightColor: AppTheme.highlightShimmer,
                                        child: Container(
                                          width: 40,
                                          height: 40,
                                          decoration: BoxDecoration(
                                            color: Colors.grey[500],
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  height: MySize.getScaledSizeHeight(116),
                                  width: MySize.getScaledSizeWidth(116),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[500],
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 20),
                          // ShimmerTabBar(),
                          // SizedBox(height: 20),
                          // ShimmerProfileGrid(),
                        ],
                      ),
                    ],
                    SizedBox(height: 20),
                    // Name placeholder
                    Container(
                      margin: EdgeInsets.only(left: 20),
                      width: 150,
                      height: 14,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: 5),
                    // Followers & Following placeholder
                    Container(
                      margin: EdgeInsets.only(left: 20),
                      width: 180,
                      height: 12,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: 10),

                    // Bio placeholder
                    Container(
                      margin: EdgeInsets.only(left: 20),
                      width: 250,
                      height: 12,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: 30),

                    if (isProjectDetail) ...[
                      Center(
                        child: Container(
                          width: 100,
                          height: 18,
                          color: Colors.grey[400],
                        ),
                      ),
                      SizedBox(height: 30),
                      Container(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: 4,
                          itemBuilder: (context, index) {
                            return Column(
                              children: [
                                Shimmer.fromColors(
                                  baseColor: AppTheme.baseShimmer,
                                  highlightColor: AppTheme.highlightShimmer,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                      right: MySize.getScaledSizeWidth(12),
                                    ),
                                    height: MySize.getScaledSizeHeight(70),
                                    width:MySize.getScaledSizeHeight(70) ,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.grey[400],

                                    ),
                                  ),
                                ),
                                Space.height(10),
                                Shimmer.fromColors(
                                  baseColor: AppTheme.baseShimmer,
                                  highlightColor: AppTheme.highlightShimmer,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                      right: MySize.getScaledSizeWidth(12),
                                    ),
                                    height: MySize.getScaledSizeHeight(15),
                                    width:MySize.getScaledSizeHeight(60) ,
                                    decoration: BoxDecoration(
                                        color: Colors.grey[400],
                                        borderRadius: BorderRadius.circular(5)
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                    // Follow button placeholder
                    if (!isProjectDetail)
                      Container(
                        margin: EdgeInsets.only(left: 20),
                        width: 100,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(18),
                        ),
                      ),
                    SizedBox(height: 30),
                    if (!isProjectDetail) ...[
                      Container(
                        height: MySize.getScaledSizeHeight(100),
                        alignment: Alignment.centerLeft,
                        child: ListView.builder(
                          padding: EdgeInsets.only(
                            left: MySize.getScaledSizeWidth(30),
                          ),
                          shrinkWrap: true,
                          itemCount: 5,
                          scrollDirection: Axis.horizontal,
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (context, index) {
                            return Column(
                              children: [
                                Shimmer.fromColors(
                                  baseColor: AppTheme.baseShimmer,
                                  highlightColor: AppTheme.highlightShimmer,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                      right: MySize.getScaledSizeWidth(12),
                                    ),
                                    height: MySize.getScaledSizeHeight(70),
                                    width:MySize.getScaledSizeHeight(70) ,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.grey[400],

                                    ),
                                  ),
                                ),
                                Space.height(10),
                                Shimmer.fromColors(
                                  baseColor: AppTheme.baseShimmer,
                                  highlightColor: AppTheme.highlightShimmer,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                      right: MySize.getScaledSizeWidth(12),
                                    ),
                                    height: MySize.getScaledSizeHeight(15),
                                    width:MySize.getScaledSizeHeight(60) ,
                                    decoration: BoxDecoration(
                                        color: Colors.grey[400],
                                        borderRadius: BorderRadius.circular(5)
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                      SizedBox(height: 30),
                    ],
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Posts tab
                        Container(
                          width: 80,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.grey[400],
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        SizedBox(width: 10),

                        // Projects tab
                        Container(
                          width: 80,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.grey[400],
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    ShimmerPostCard(isPadding: !isProjectDetail ? false : true)
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
