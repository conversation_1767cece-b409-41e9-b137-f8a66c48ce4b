// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: prefer_initializing_formals, must_be_immutable

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:incenti_ai/utillites/typography.dart';

import 'app_theme.dart';
import 'loader.dart';

class Buttons extends StatefulWidget {
  final Widget? buttonWidget;
  final List<BoxShadow>? boxShadow;
  final Widget? buttonLoadingWidget;
  final String buttonText;
  final String? loadingText;
  final VoidCallback? onTap;
  final Color? textColor;
  final Color textDisableColor;
  final Color disableColor;
  final Color? textLoadingColor;
  final Color? loadingColor;
  final Color? boarderColor;
  final ButtonType type;
  final double height;
  final double loaderSize;
  final double? width;
  final EdgeInsetsGeometry padding;
  final int buttonTextLevel;
  final FontStyle? fontStyle;
  final FontWeight buttonTextWeight;
  late TextStyle activeTextStyle;
  late Decoration activeTabDecoration;
  late Color color;
  final Gradient? gradient;
  late BorderRadius borderRadius;
  late RxBool isLoading;
  late RxBool isDisable;
  final double dashWidth; // Length of each dash
  final double dashSpace; // Space between dashes
  final double strokeWidth;
  final Widget? icon;

  Buttons({super.key,
    bool isLoading = false,
    bool isDisable = false,
    Decoration? activeTabDecoration,
    Color? color,
    TextStyle? activeTextStyle,
    BorderRadius? borderRadius,
    this.buttonWidget,
    this.boxShadow,
    this.buttonLoadingWidget,
    required this.buttonText,
    this.loadingText,
    this.onTap,
    this.textColor,
    this.textDisableColor = AppTheme.buttonTextDisableColor,
    this.disableColor = AppTheme.buttonDisableColor,
    this.textLoadingColor,
    this.loadingColor,
    this.boarderColor,
    this.type = ButtonType.primary,
    this.height = AppTheme.buttonHight,
    this.loaderSize = 20,
    this.width,
    this.padding = const EdgeInsets.symmetric(vertical: 0),
    this.buttonTextLevel = AppTheme.buttonTextLevel,
    this.fontStyle,
    this.buttonTextWeight = AppTheme.buttonTextWeight,
    this.gradient,
    this.dashWidth = 4.0,
    this.dashSpace = 4.0,
    this.strokeWidth = 2.0,
    this.icon,
  }) {
    color ??= AppTheme.primary1;
    activeTextStyle ??= AppTheme.activeTextStyle;
    activeTabDecoration ??= AppTheme.activeTabDecoration;
    borderRadius ??= AppTheme.buttonBorderRadius;
    this.activeTabDecoration = activeTabDecoration;
    this.activeTextStyle = activeTextStyle;
    this.color = color;
    this.borderRadius = borderRadius;
    this.isLoading = isLoading.obs;
    this.isDisable = isDisable.obs;
  }

  @override
  State<Buttons> createState() => _MyButtonsState();
}

class _MyButtonsState extends State<Buttons> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Color color = widget.color;
    Color? textColor = widget.textColor ?? (widget.type == ButtonType.primary ? AppTheme.white : color);
    double? elevation;
    double? focusElevation;
    double? hoverElevation;
    double? highlightElevation;

    if (widget.isDisable.value) {
      color = widget.disableColor;
      textColor = widget.textDisableColor;
      elevation = 0;
      focusElevation = 0;
      hoverElevation = 0;
      highlightElevation = 0;
    }

    if (!widget.isDisable.value && widget.isLoading.value) {
      if (widget.textLoadingColor != null) {
        textColor = widget.textLoadingColor;
      }
      if (widget.loadingColor != null) {
        color = widget.loadingColor!;
      }
      elevation = 0;
      focusElevation = 0;
      hoverElevation = 0;
      highlightElevation = 0;
    }

    Widget buttonWidget = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.isLoading.value && widget.buttonLoadingWidget != null) widget.buttonLoadingWidget!,
        if (widget.isLoading.value && widget.buttonLoadingWidget == null)
          Loader(
            color: textColor,
            size: widget.loaderSize,
          ),
        if (widget.isLoading.value && widget.loadingText != null && widget.buttonLoadingWidget == null)
          Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: TypoGraphy(
              overflow: TextOverflow.ellipsis,
              text: widget.loadingText,
              color: textColor,
              level: widget.buttonTextLevel,
              fontWeight: widget.buttonTextWeight,
              fontStyle: widget.fontStyle,
            ),
          ),
        if (!widget.isLoading.value && widget.icon != null) widget.icon!,
        if (!widget.isLoading.value)
          widget.buttonWidget ??
              TypoGraphy(
                textAlign: TextAlign.center,
                text: widget.buttonText,
                color: textColor,
                level: widget.buttonTextLevel,
                fontWeight: widget.buttonTextWeight,
                fontStyle: widget.fontStyle,
                overflow: TextOverflow.ellipsis,
              )
      ],
    );

    BoxDecoration? buttonDecoration;

    if (widget.type == ButtonType.primary && widget.gradient != null) {
      buttonDecoration = BoxDecoration(
        gradient: widget.gradient,
        borderRadius: widget.borderRadius,
      );
    } else if (widget.type == ButtonType.outline && widget.gradient != null) {
      buttonDecoration = BoxDecoration(
          border: Border.all(color: widget.boarderColor ?? color, width: 2.0),
          gradient: widget.gradient,
          borderRadius: widget.borderRadius,
          boxShadow: widget.boxShadow);
    }
    switch (widget.type) {
      case ButtonType.primary:
        return Container(
          decoration: buttonDecoration,
          child: MaterialButton(
            enableFeedback: false,
            onPressed: onPressed,
            height: widget.height,
            minWidth: widget.width,
            elevation: elevation,
            focusElevation: focusElevation,
            hoverElevation: hoverElevation,
            highlightElevation: highlightElevation,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            color: widget.gradient == null ? color : null,
            padding: widget.padding,
            shape: RoundedRectangleBorder(borderRadius: widget.borderRadius),
            child: buttonWidget,
          ),
        );

      case ButtonType.text:
        return TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            splashFactory: null,
            shadowColor: Colors.transparent,
            backgroundColor: Colors.transparent,
            foregroundColor: const Color.fromARGB(0, 255, 255, 255),
            surfaceTintColor: const Color.fromARGB(0, 0, 0, 0),
          ),
          child: buttonWidget,
        );

      case ButtonType.outline:
        return Container(
          decoration: buttonDecoration,
          child: OutlinedButton(
            onPressed: onPressed,
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                width: 2.0,
                color: color,
              ),
              minimumSize: Size(widget.width ?? double.minPositive, widget.height),
              shape: RoundedRectangleBorder(borderRadius: widget.borderRadius),
              foregroundColor: color,
            ),
            child: buttonWidget,
          ),
        );

      case ButtonType.dashed:
        return InkWell(
          onTap: onPressed,
          child: Container(
            height: widget.height,
            width: widget.width,
            child: CustomPaint(
              painter: DottedBorderPainter(
                  color: widget.boarderColor ?? AppTheme.primary1,
                  fillColor: color,
                  borderRadius: widget.borderRadius,
                  dashWidth: widget.dashWidth,
                  dashSpace: widget.dashSpace,
                  strokeWidth: widget.strokeWidth),
              child: buttonWidget,
            ),
          ),
        );
    }
  }

  onPressed() {
    if (!widget.isLoading.value && !widget.isDisable.value && widget.onTap != null) {
      widget.onTap!();
    }
  }
}

class DottedBorderPainter extends CustomPainter {
  final Color color;
  final Color? fillColor;
  final BorderRadius? borderRadius;
  final double dashWidth; // Length of each dash
  final double dashSpace; // Space between dashes
  final double strokeWidth; // Width of the border

  DottedBorderPainter({
    required this.color,
    this.fillColor,
    this.borderRadius,
    this.dashWidth = 4.0,
    this.dashSpace = 4.0,
    this.strokeWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw the filled background

    final Paint fillPaint = Paint()
      ..color = fillColor ?? AppTheme.primary1
      ..style = PaintingStyle.fill;

    final RRect fillRRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(borderRadius?.topLeft.x ?? 10),
    );
    canvas.drawRRect(fillRRect, fillPaint);

    // Draw the dotted border
    final Paint borderPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final RRect borderRRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(borderRadius?.topLeft.x ?? 10),
    );

    final Path borderPath = Path()..addRRect(borderRRect);

    canvas.drawPath(
      _dashPath(borderPath),
      borderPaint,
    );
  }

  Path _dashPath(Path source) {
    final Path dashedPath = Path();
    final double dashWidthWithSpace = dashWidth + dashSpace;
    double distance = 0.0;

    for (PathMetric pathMetric in source.computeMetrics()) {
      final double length = pathMetric.length;
      while (distance < length) {
        final double start = distance;
        final double end = start + dashWidth;
        dashedPath.addPath(
          pathMetric.extractPath(start, end < length ? end : length),
          Offset.zero,
        );
        distance += dashWidthWithSpace;
      }
    }

    return dashedPath;
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

enum ButtonType { primary, outline, text, dashed }
