import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:incenti_ai/utillites/typography.dart';
import '../constants/app_size_constant.dart';
import '../main.dart';
import 'app_theme.dart';

Future<void> showCommonReportBottomSheet({
  required BuildContext context,
  required String title,
  required String subTitle,
  required String description,
  required List options,
  required void Function(String selectedOption) onOptionTap,
}) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: FractionallySizedBox(
          heightFactor: 0.50,
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark ? AppTheme.bottomBar : AppTheme.white,
              border: Border.all(color: AppTheme.borderColor),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40),
                topRight: Radius.circular(40),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: MySize.getScaledSizeWidth(20)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Space.height(8),
                  Container(
                    height: MySize.size5,
                    width: MySize.size34,
                    decoration: BoxDecoration(
                      color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  Space.height(30),
                  Align(
                    alignment: Alignment.center,
                    child: TypoGraphy(
                      text: title,
                      level: 5,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Space.height(5),
                  Divider(color: Theme.of(context).brightness == Brightness.dark ? AppTheme.borderColor : null,),
                  Space.height(20),
                  TypoGraphy(
                    text: subTitle,
                    level: 4,
                    fontWeight: FontWeight.w600,
                  ),
                  Space.height(20),
                  TypoGraphy(
                    text: description,
                    level: 3,
                    textAlign: TextAlign.center,
                    color: AppTheme.grey,
                  ),
                  Space.height(30),
                  Divider(color: Theme.of(context).brightness == Brightness.dark ? AppTheme.borderColor : null,),
                  Space.height(10),
                  ...List.generate(options.length, (index) {
                    final option = options[index];
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () {
                            onOptionTap(option);
                            Navigator.pop(context);
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.getScaledSizeWidth(10)),
                            child: Row(
                              children: [
                                TypoGraphy(
                                  text: option,
                                  level: 3,
                                ),
                                Spacer(),
                                Icon(Icons.arrow_forward_ios_rounded,
                                    size: MySize.getScaledSizeHeight(15)),
                              ],
                            ),
                          ),
                        ),
                        Space.height(10),
                        Divider(color: Theme.of(context).brightness == Brightness.dark ? AppTheme.borderColor : null,),
                        Space.height(10),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}
