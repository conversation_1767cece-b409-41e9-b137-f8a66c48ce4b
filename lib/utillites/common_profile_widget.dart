import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/rounded_network_image.dart';
import 'package:incenti_ai/utillites/typography.dart';

import 'app_theme.dart';
import 'current_user.dart';

Widget profileImage(
    {String url = "",
    String userName = "",
    double? height,
    double? width,
    double? iconHeight,
    double? iconWidth,
    double padding = 1.0,
    double? borderRadius,
    Color borderColor = Colors.grey,
    double? strokeWidth,
    Color? backgroundColor,
    TextStyle? textStyle,
    Color? color}) {
  iconHeight ??= height;
  iconWidth ??= width;
  return url != ""
      ? RoundedNetworkImage(
          url: url,
          width: width,
          height: height,
          strokeWidth: strokeWidth ?? 0,
          padding: padding,
          borderRadius: borderRadius,
          borderColor: borderColor,
          backgroundColor: backgroundColor)
      : Container(
          width: iconWidth,
          height: iconHeight,
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(500),
            color: AppTheme.primary1,
            border: Border.all(color: borderColor, width: strokeWidth ?? 0),
          ),
          alignment: Alignment.center,
          child: TypoGraphy(
            isTranslateDisabled: true,
            text: userName.isEmpty
                ? CurrentUser.user.firstName!.characters.isNotEmpty
                    ? CurrentUser.user.firstName?.characters.first.capitalize
                    : ""
                : userName.characters.isNotEmpty
                    ? userName.characters.first.capitalize
                    : "",
            color: AppTheme.white,
            textStyle: textStyle,
          ),
        );
}
