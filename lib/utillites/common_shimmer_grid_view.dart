import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../constants/app_size_constant.dart';
import 'app_theme.dart';
import 'common_grid_view.dart';

class ShimmerGridItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.baseShimmer,
      highlightColor: AppTheme.highlightShimmer,
      child: Container(
        // decoration: BoxDecoration(
        //   color: Colors.white,
        //   borderRadius: BorderRadius.circular(16),
        // ),
        height: MySize.size142,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
        ),
        // padding: EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image placeholder
            Container(
              height: MySize.size142,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            SizedBox(height: 8),

            // Sub-projects text
            Container(
              width: 60,
              height: 10,
              color: Colors.grey[400],
            ),
            SizedBox(height: 4),

            // Title placeholder
            Container(
              width: double.infinity,
              height: 14,
              color: Colors.grey[400],
            ),
            SizedBox(height: 4),
            Container(
              width: 120,
              height: 14,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}

class ShimmerGridView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      controller: ScrollController(),
      padding: EdgeInsets.zero,
      gridDelegate: FixedHeightGridDelegate(
        itemHeight: MySize.size220 ?? 200,
        crossAxisCount: 2,
        crossAxisSpacing: MySize.size20 ?? 20,
        mainAxisSpacing: MySize.size30 ?? 30,
      ),
      itemCount: 6, // Number of shimmer items
      itemBuilder: (context, index) => ShimmerGridItem(),
    );
  }
}
