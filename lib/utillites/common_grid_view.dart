import 'package:flutter/rendering.dart';

class FixedHeightGridDelegate extends SliverGridDelegate {
  final double itemHeight;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final int crossAxisCount;

  FixedHeightGridDelegate({
    required this.itemHeight,
    required this.crossAxisCount,
    this.crossAxisSpacing = 0.0,
    this.mainAxisSpacing = 0.0,
  });

  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    double itemWidth = (constraints.crossAxisExtent -
        (crossAxisSpacing * (crossAxisCount - 1))) /
        crossAxisCount;
    return SliverGridRegularTileLayout(
      crossAxisCount: crossAxisCount,
      mainAxisStride: itemHeight + mainAxisSpacing,
      crossAxisStride: itemWidth + crossAxisSpacing,
      childMainAxisExtent: itemHeight,
      childCrossAxisExtent: itemWidth,
      reverseCrossAxis: false,
    );
  }

  @override
  bool shouldRelayout(covariant SliverGridDelegate oldDelegate) => true;
}