import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../constants/app_size_constant.dart';
import 'buttons.dart';

void showDeleteConfirmationDialog({
  required BuildContext context,
  required String title,
  required String description,
  required VoidCallback onConfirm,
  required RxBool isLoading,
  VoidCallback? onCancel,
}) {
  Get.dialog(
    BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Dialog(
        insetPadding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(30.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Trash Icon
              Container(
                height: MySize.size100,
                width: MySize.size100,
                decoration: BoxDecoration(
                  color: AppTheme.red.shade50,
                  shape: BoxShape.circle,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: SvgPicture.asset(AppImage.trashIcon),
                ),
              ),

              Space.height(20),

              // Title
              TypoGraphy(
                text: title,
                level: 12,
                textAlign: TextAlign.center,
              ),
              Space.height(10),

              // Description
              TypoGraphy(
                text: description,
                level: 3,
                color: AppTheme.grey,
                textAlign: TextAlign.center,
              ),
              Space.height(40),

              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Cancel Button
                  Expanded(
                    child: InkWell(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: onCancel ?? () => Get.back(),
                      child: Container(
                        height: MySize.getScaledSizeHeight(70),
                        width: MySize.getScaledSizeHeight(170),
                        alignment: Alignment.center,
                        child: TypoGraphy(
                          text: "No",
                          level: 4,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 10),

                  // Confirm Delete Button
                  Expanded(
                    child: Obx(
                      () => Buttons(
                        buttonText: "Yes",
                        buttonTextLevel: 4,
                        color: AppTheme.red,
                        isLoading: isLoading.value,
                        height: MySize.getScaledSizeHeight(70),
                        onTap: () {
                          HapticFeedback.heavyImpact();
                          onConfirm(); // Execute Delete Action
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
