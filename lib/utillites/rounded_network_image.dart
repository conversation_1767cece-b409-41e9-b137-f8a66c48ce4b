import 'package:flutter/material.dart';


class RoundedNetworkImage extends StatelessWidget {
  final String url;
  final double? height;
  final double? width;
  final double? strokeWidth;
  final double? padding;
  final double? borderRadius;
  final Color borderColor;
  final Color? backgroundColor;

  const RoundedNetworkImage({
    super.key,
    required this.url,
    this.height,
    this.width,
    this.strokeWidth,
    this.padding,
    this.borderRadius,
    this.borderColor = Colors.grey,
    this.backgroundColor
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      padding: EdgeInsets.zero,
      decoration: BoxDecoration(
        // borderRadius: BorderRadius.circular(borderRadius ?? 500),
        border: Border.all(color: borderColor, width: strokeWidth ?? 0),
        color: backgroundColor,
        image: DecorationImage(image: NetworkImage(url),fit: BoxFit.cover),
        shape: BoxShape.circle
      ),
      // child: ClipRRect(
      //   borderRadius: BorderRadius.circular((borderRadius ?? 500) - (strokeWidth ?? 0)),
      //   child: NetworkImageComponent(
      //     imageUrl: url,
      //     height: height,
      //     width: width,
      //   ),
      // ),
    );
  }
}
