import 'dart:ui';
import 'package:get/get.dart';
import 'package:translator/translator.dart';
import 'package:html/parser.dart' as htmlParser;
import 'package:html/dom.dart' as dom;

import '../main.dart';

String selectedCountryCode = box.read("selectedCountryCode") ?? "US";
String selectedLanguageCode = box.read("selectedLanguageCode") ?? "en";

Map<String, dynamic> defaultWord = {
  "hi": {"Hindi": "हिंदी"},
};

class TranslationService extends GetxController {
  // Private constructor
  TranslationService._();

  // Static instance of the class
  static final TranslationService _instance = TranslationService._();

  // Factory constructor to return the instance
  factory TranslationService() => _instance;

  final Rx<Locale> _locale =
      Locale(selectedLanguageCode, selectedCountryCode).obs;

  Locale get locale => _locale.value;

  void setLocale(Locale locale) {
    _locale.value = locale;
    Get.updateLocale(locale);
    // Optionally, notify other parts of the app about locale change
  }

  bool isDefault() {
    return _locale.value.countryCode == "US" &&
        _locale.value.languageCode == "en";
  }

  Future<String> translate(String text) async {
    if (text == "") return text;

    if (_locale.value.countryCode == "US" &&
        _locale.value.languageCode == "en") {
      return text;
    }
    String? isDefaultWord = defaultWord[_locale.value.languageCode]?[text];
    if (isDefaultWord != null) return isDefaultWord;

    final GoogleTranslator translator = GoogleTranslator();
    try {
      final translation =
          await translator.translate(text, to: locale.languageCode);
      defaultWord[locale.languageCode][text] = translation.text;
      return translation.text;
    } catch (e) {
      return text;
    }
  }

  Future<String> translateHtml(String htmlContent) async {
    if (htmlContent.isEmpty) return htmlContent;

    dom.Document document = htmlParser.parse(htmlContent);
    List<dom.Text> textNodes = [];

    void collectTextNodes(dom.Node node) {
      if (node is dom.Text) {
        textNodes.add(node); // Add text nodes to list
      } else if (node is dom.Element) {
        node.nodes.forEach(collectTextNodes);
      }
    }

    document.body?.nodes.forEach(collectTextNodes);

    // Extract the actual text to be translated
    List<String> textsToTranslate = textNodes.map((node) => node.text).toList();
    List<String> translatedTexts = await translateBatch(textsToTranslate);
    for (int i = 0; i < textNodes.length; i++) {
      textNodes[i].text = translatedTexts[i];
    }

    return document.body?.outerHtml ?? htmlContent;
  }

  Future<List<String>> translateBatch(List<String> texts) async {
    return Future.wait(texts.map((text) async => await translate(text)));
  }
}
