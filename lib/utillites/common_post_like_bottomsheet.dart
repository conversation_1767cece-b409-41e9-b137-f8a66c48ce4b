
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/models/post_like_model.dart';
import 'package:intl/intl.dart';

import '../app/routes/app_pages.dart';
import '../constants/app_size_constant.dart';
import '../main.dart';
import 'app_theme.dart';
import 'common_profile_widget.dart';
import 'current_user.dart';
import 'loader.dart';
import '../utillites/typography.dart';

class CommonPostLikeBottomSheet extends StatelessWidget {
  final int index;
  final bool isBookMark;
  final RxBool isLoading;
  final List<PostLikeData> postLikeList;


  const CommonPostLikeBottomSheet({
    super.key,
    required this.index,
    this.isBookMark = false,
    required this.isLoading,
    required this.postLikeList,
  });

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: FractionallySizedBox(
        heightFactor: 0.85,
        child: Container(
          decoration: BoxDecoration(
            color: AppTheme.subBottom,
            border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40),
                topRight: Radius.circular(40)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Space.height(8),
                Container(
                  height: MySize.size5,
                  width: MySize.size34,
                  decoration: BoxDecoration(
                    color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
                Space.height(20),
                Align(
                  alignment: Alignment.centerLeft,
                  child: TypoGraphy(
                    text: "Likes",
                    level: 5,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Space.height(20),
                Expanded(
                  child: Obx(
                        () => isLoading.value
                        ? Loader()
                        : postLikeList.isNotEmpty
                        ? ListView.separated(
                      separatorBuilder: (context, index) => Space.height(30),
                      itemCount: postLikeList.length,
                      itemBuilder: (context, index) {
                        final like = postLikeList[index];
                        return InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            if(like.user?.id == CurrentUser.user.id) {
                              Get.toNamed(Routes.profile);
                            } else {
                              Get.offAndToNamed(Routes.other_user_profile,
                                  arguments: {"UserId": like.user?.id});
                            }
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              profileImage(
                                url: like.user?.image ?? "",
                                userName: like.user?.firstName ?? "",
                                width: MySize.size38 ?? 38,
                                height: MySize.size38 ?? 38,
                                iconHeight: MySize.size38,
                                iconWidth: MySize.size38,
                                strokeWidth: MySize.size1 ?? 1,
                                borderColor: Colors.transparent,
                                color: AppTheme.darkGrey[100],
                              ),
                              Space.width(14),
                              Expanded(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: "${like.user?.firstName} ${like.user?.lastName} ",
                                            style: GoogleFonts.inter(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w700,
                                                color: AppTheme.whiteWithBase
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Space.height(8),
                                    TypoGraphy(
                                      text: DateFormat('MMM d, y').format(
                                        DateTime.parse(like.createdAt.toString()),
                                      ),
                                      level: 2,
                                      fontWeight: FontWeight.w400,
                                      color: AppTheme.grey,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    )
                        : Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TypoGraphy(text: "No Likes yet", level: 5),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
