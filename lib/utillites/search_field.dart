import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';

class SearchAppTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final bool? errorMessage;
  final String? hintText;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final double borderRadius;
  final bool? readOnly;
  final bool? enabled;
  final InputBorder? enableBorder;
  final InputBorder? focusBorder;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final double? height;
  final EdgeInsets? padding;
  final EdgeInsets? suffixIconPadding;
  final String? suffixIconAsset;
  final BoxConstraints? suffixIconConstraints;
  final bool? obscureText;
  final List<String? Function(String?)> validators;
  final Function(String?)? onChangedValue;
  final Function(String?)? onSubmitted;
  final FocusNode? focusNode;
  final TextInputType? textInputType;
  final void Function(BuildContext context)? suffixOnTap;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization? textCapitalization;
  final int? maxLines;
  final int? hintTextSize;

  const SearchAppTextField(
      {super.key,
      this.labelText,
      this.hintTextSize,
      this.hintStyle,
      this.controller,
      this.errorMessage = false,
      this.enabled,
      this.height,
      this.labelStyle,
      this.textStyle,
      this.readOnly,
      this.suffixIcon,
      this.prefixIcon,
      this.textInputType,
      this.hintText,
      this.backgroundColor,
      this.enableBorder,
      this.focusBorder,
      this.borderRadius = 14,
      this.padding,
      this.focusNode,
      this.suffixIconPadding,
      this.suffixIconAsset,
      this.suffixIconConstraints,
      this.suffixOnTap,
      this.obscureText = false,
      this.validators = const [],
      this.onChangedValue,
      this.onSubmitted,
      this.maxLength,
      this.maxLines = 1,
      this.inputFormatters,
      this.textCapitalization});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ??
          EdgeInsets.only(
              top: MySize.size12 ?? 12,
              bottom: MySize.size12 ?? 12,
              left: MySize.size20 ?? 12),
      // Default padding
      decoration: BoxDecoration(
        color: backgroundColor ?? (AppTheme.appTextField),
        border: Border.all(
            color: errorMessage == true ? AppTheme.red : AppTheme.borderColor),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      alignment: Alignment.center,
      child: TextFormField(
        textCapitalization: textCapitalization ?? TextCapitalization.none,
        inputFormatters: inputFormatters,
        obscureText: obscureText ?? false,
        controller: controller,
        style: textStyle ??
            TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: MySize.size16,
            ),
        keyboardType: textInputType ?? TextInputType.text,
        enabled: enabled ?? true,
        onChanged: (value) {
          if (onChangedValue != null) {
            onChangedValue!(value);
          }

          if (validators.isEmpty) {
            return;
          }

          for (var validator in validators) {
            final result = validator(value);
            if (result != null) {
              return;
            }
          }
        },
        onFieldSubmitted: (value) {
          if (onSubmitted != null) onSubmitted!(value);

          if (validators.isEmpty) {
            return;
          }

          for (var validator in validators) {
            final result = validator(value);
            if (result != null) {
              return;
            }
          }
        },
        validator: (value) {
          if (validators.isEmpty) {
            return null;
          }

          for (var validator in validators) {
            final result = validator(value);
            if (result != null) {
              return null;
            }
          }

          return null;
        },
        focusNode: focusNode,
        cursorColor: AppTheme.primary1,
        maxLength: maxLength,
        maxLines: maxLines,
        buildCounter: (BuildContext context,
            {int? currentLength, bool? isFocused, int? maxLength}) {
          return null; // Hides the character counter
        },
        readOnly: readOnly ?? false,
        decoration: InputDecoration(
          isDense: true,
          contentPadding: hintText != null ? null : EdgeInsets.zero,
          enabledBorder: enableBorder ?? InputBorder.none,
          focusedBorder: focusBorder ?? InputBorder.none,
          disabledBorder: InputBorder.none,
          labelText: labelText,
          labelStyle: labelStyle ??
              TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: MySize.size16,
                  color: AppTheme.whiteWithBase,
                  fontFamily: "Inter"),
          hintText: hintText ?? "",
          hintStyle: hintStyle ??
              TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: MySize.size16,
                color: AppTheme.whiteWithBase,
                fontFamily: "Inter",
              ),
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon ??
              Padding(
                padding: suffixIconPadding ??
                    EdgeInsets.only(right: MySize.size20 ?? 20),
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: suffixOnTap != null
                      ? () => suffixOnTap!(context)
                      : null,
                  child: suffixIconAsset != null
                      ? SvgPicture.asset(
                          suffixIconAsset ?? "",
                        )
                      : SizedBox.shrink(),
                ),
              ),
          suffixIconConstraints: suffixIconConstraints ??
              BoxConstraints(
                  minWidth: MySize.size18 ?? 18,
                  minHeight: MySize.size18 ?? 18),
        ),
      ),
    );
  }
}
