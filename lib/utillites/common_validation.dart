// ignore_for_file: use_build_context_synchronously

class CommonValidation {
  static isEmpty({String value = "", String? validationMessage}) {
    if (value.isEmpty) {
      return validationMessage ?? "This field is required !";
    } else if (value == "0") {
      return validationMessage ?? "Enter Amount";
    }
    return null;
  }

  static isPasswordCompare({bool? value, String? validationMessage}) {
    if (value != null && value == false) {
      return validationMessage ?? "";
    }
    return null;
  }

  static isUrl({String value = "", String? validationMessage}) {
    if (!RegExp(r'(?:(?:https?|ftp):\/\/)?[\w/\-?=%.]+\.[\w/\-?=%.]+').hasMatch(
        value)) {
      return validationMessage ?? 'Please enter valid URL';
    }
    return null;
  }

  static isIFSCValid({String value = "", String? validationMessage}) {
    if (!RegExp(r'^[A-Z]{4}0[A-Z0-9]{6}$').hasMatch(
        value)) {
      return validationMessage ?? 'Please valid IFSC Code';
    }
    return null;
  }

  static isValidName({String value = "", String? validationMessage}) {
    if (!RegExp(r'^[a-zA-Z\s]*$').hasMatch(value)) {
      return validationMessage ?? 'Please enter valid Name';
    }
    return null;
  }

  static isUrlSpecialCharacter({String value = "", String? validationMessage}) {
    if (!RegExp(r'^[\ a-zA-Z0-9]+$').hasMatch(value.trim())) {
      return validationMessage ?? "Please enter a valid name";
    }
    return null;
  }

  static isOnlyAlphabet({String value = "", String? validationMessage}) {
    if (!RegExp(r"^[A-Za-z ]+$").hasMatch(value.trim())) {
      return validationMessage ?? "Please enter a valid name";
    }
    return null;
  }

  static isEmail({String value = "", String? validationMessage}) {
    if (!RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value)) {
      return validationMessage ?? 'Please enter a valid email';
    }
    return null;
  }

  static isPincode(
      {String value = "", String? validationMessage, int length = 6}) {
    if (value.length != length) {
      return validationMessage ??
          'Please enter a valid $length-digit pincode number';
    } else if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
      return validationMessage ?? "Please enter a valid Pincode";
    } else {
      return null;
    }
  }

  static isPhone(
      {String value = "", String? validationMessage, int length = 10}) {
    if (value.length != length) {
      return validationMessage ??
          'Please enter a valid $length-digit mobile number';
    } else if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
      return validationMessage ?? "Please enter a valid number";
    } else {
      return null;
    }
  }

  static String? isUpiId({String value = "", String? validationMessage}) {
    // UPI ID format usually includes alphabets, numbers, and special characters like . and @
    if (!RegExp(r"^[0-9A-Za-z.-]{2,256}@[A-Za-z]{2,64}$").hasMatch(value)) {
      return validationMessage ?? 'Please enter a valid UPI ID';
    }
    return null;
  }

  static isAdharCardNum(
      {String value = "", String? validationMessage, int length = 12}) {
    if (value.length != length) {
      return validationMessage ?? 'Required';
    } else if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
      return validationMessage ?? "Please enter a valid number";
    } else {
      return null;
    }
  }
}
