import 'package:flutter/material.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../constants/app_size_constant.dart';
import 'app_theme.dart';

class Empty extends StatelessWidget {
  final String? imagePath;
  final String? title;
  final String? message;
  final double? height;
  final double? titlePadding;
  final double? messagePadding;
  final Color? textColor;

  const Empty({
    super.key,
    this.imagePath,
    this.title,
    this.message,
    this.height,
    this.titlePadding,
    this.messagePadding,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (imagePath != null)
            Image.asset(
              imagePath!,
              height: height ?? MySize.size250, // Adjust height as needed
              width: height ?? MySize.size250, // Adjust width as needed
            ),
          if (title != null)
            Padding(
              padding: EdgeInsets.only(top: titlePadding ?? 16.0),
              child: TypoGraphy(
                text: title!,
                textAlign: TextAlign.center,
                level: 3,
                fontWeight: FontWeight.w700,
              ),
            ),
          if (message != null)
            Padding(
              padding: EdgeInsets.only(top: 8.0,left: messagePadding ?? 0,right: messagePadding ?? 0),
              child: TypoGraphy(
                text: message!,
                textAlign: TextAlign.center,
                level: 3,
                fontWeight: FontWeight.w500,
                color: textColor ?? AppTheme.darkGrey[200],
              ),
            ),
        ],
      ),
    );
  }
}
