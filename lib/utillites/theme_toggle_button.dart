import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/theme_controller.dart';

class ThemeToggleButton extends StatelessWidget {
  const ThemeToggleButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(
        Theme.of(context).brightness == Brightness.dark
            ? Icons.light_mode
            : Icons.dark_mode,
        color: AppTheme.getTextPrimaryColor(context),
      ),
      onPressed: () {
        Get.find<ThemeController>().changeTheme();
      },
    );
  }
} 