// ignore_for_file: prefer_initializing_formals, must_be_immutable

import 'dart:ui';

import 'package:flutter/material.dart';
import 'dart:math' as math show sin, pi;

import 'package:get/get_rx/src/rx_types/rx_types.dart';

import 'app_theme.dart';

class Loader extends StatelessWidget {
  late Color color;
  late Color loaderBackgroundColor;
  final double size;
  final Widget? child;
  final BorderRadiusGeometry? borderRadius;
  RxBool isLoading = true.obs;

  Loader(
      {super.key,
      Color? color,
      this.size = 30,
      this.child,
      Color? loaderBackgroundColor,
      bool? isLoading,
      this.borderRadius}) {
    this.isLoading.value = isLoading ?? false;
    color ??= AppTheme.primary1;
    loaderBackgroundColor ??= AppTheme.primary1.withOpacity(0.2);
    this.color = color;
    this.loaderBackgroundColor = loaderBackgroundColor;
  }

  @override
  Widget build(BuildContext context) {
    if (child != null) {
      return Stack(
        children: [
          child!, // Your original child widget
          if (isLoading.value)
            Positioned(
              top: 0.0, // Positioned from top
              left: 0.0, // Positioned from left
              right: 0.0, // Occupy full width
              bottom: 0.0, // Occupy full height
              child: ClipRRect(
                borderRadius: borderRadius ?? BorderRadius.circular(0.0),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: loaderBackgroundColor, // Background color
                    borderRadius: borderRadius ?? BorderRadius.circular(0.0), // Rounded corners
                  ),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 0.5, sigmaY: 0.5),
                    child: Center(
                      child: SpinKitFadingFour(color: color, size: size),
                    ),
                  ),
                ),
              ),
            ),
        ],
      );
    }
    return SpinKitFadingFour(color: color, size: size);
  }
}

class SpinKitFadingFour extends StatefulWidget {
  final Color? color;
  final BoxShape shape;
  final double size;
  final IndexedWidgetBuilder? itemBuilder;
  final Duration duration;
  final AnimationController? controller;

  const SpinKitFadingFour({
    super.key,
    this.color,
    this.shape = BoxShape.circle,
    this.size = 50.0,
    this.itemBuilder,
    this.duration = const Duration(milliseconds: 1200),
    this.controller,
  }) : assert(
          !(itemBuilder is IndexedWidgetBuilder && color is Color) && !(itemBuilder == null && color == null),
          'You should specify either a itemBuilder or a color',
        );

  @override
  State<SpinKitFadingFour> createState() => _SpinKitFadingFourState();
}

class _SpinKitFadingFourState extends State<SpinKitFadingFour> with SingleTickerProviderStateMixin {
  static const List<double> _delays = [.0, -0.9, -0.6, -0.3];
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = (widget.controller ?? AnimationController(vsync: this, duration: widget.duration))..repeat();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Transform.rotate(
        angle: math.pi / 4,
        child: SizedBox.fromSize(
          size: Size.square(widget.size),
          child: Stack(
            children: List.generate(4, (i) {
              final position = widget.size * .5;
              return Positioned.fill(
                top: position,
                left: position,
                child: Transform(
                  transform: Matrix4.rotationZ(30.0 * (i * 3) * 0.0174533),
                  child: Align(
                    alignment: Alignment.center,
                    child: FadeTransition(
                      opacity: DelayTween(
                        begin: 0.2,
                        end: 1.0,
                        delay: _delays[i],
                      ).animate(_controller),
                      child: SizedBox.fromSize(
                        size: Size.square(widget.size * 0.25),
                        child: _itemBuilder(i),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _itemBuilder(int index) => widget.itemBuilder != null
      ? widget.itemBuilder!(context, index)
      : DecoratedBox(
          decoration: BoxDecoration(color: widget.color, shape: widget.shape),
        );
}

class DelayTween extends Tween<double> {
  DelayTween({
    super.begin,
    super.end,
    required this.delay,
  });

  final double delay;

  @override
  double lerp(double t) {
    return super.lerp((math.sin((t - delay) * 2 * math.pi) + 1) / 2);
  }

  @override
  double evaluate(Animation<double> animation) => lerp(animation.value);
}
