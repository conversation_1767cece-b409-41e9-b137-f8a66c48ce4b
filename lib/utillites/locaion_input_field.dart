import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/search_field.dart';
import 'package:incenti_ai/utillites/typography.dart';
import '../constants/app_size_constant.dart';
import 'app_theme.dart';

class LocationInputField extends StatefulWidget {
  const LocationInputField({
    super.key,
    this.labelText = 'Location',
    this.initialValue,
    this.readOnly = false,
    this.textColor,
    this.onFocused,
    this.child,
    required this.selectedSuggestion,
    required this.controller,
  });

  final String labelText;
  final String? initialValue;
  final Color? textColor;
  final Function? onFocused;
  final bool readOnly;
  final Widget? child;
  final Rx<String?> selectedSuggestion;
  final TextEditingController controller;

  @override
  State<LocationInputField> createState() => _LocationInputFieldState();
}

class _LocationInputFieldState extends State<LocationInputField> {
  final GlobalKey<FormFieldState> formFieldKey = GlobalKey<FormFieldState>();
  // final controller = TextEditingController();
  final focusNode = FocusNode();

  final scrollController = ScrollController();

  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  final RxList<String> suggestions = RxList<String>([]);

  @override
  void initState() {
    onQueryChange(widget.controller.text);
    widget.selectedSuggestion.value = widget.controller.text;
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        HapticFeedback.lightImpact();
        widget.onFocused?.call();
        openDropdown();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    try {
      closeDropdown();
      _overlayEntry?.dispose();
      _overlayEntry = null;
      // ignore: empty_catches
    } catch (e) {}
    super.dispose();
  }

  Timer? _debouncer;

  onQueryChange(String query) async {
    widget.selectedSuggestion.value = null;
    _debouncer?.cancel();
    _debouncer = Timer(const Duration(milliseconds: 200), () async {
      if (query.trim().isEmpty) {
        suggestions.clear();
        return;
      }
      suggestions.value = await CommonFunction().getSuggestion(query.trim());
    });
  }

  void openDropdown() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry?.remove();
        _overlayEntry?.dispose();
      }
      _overlayEntry = _createOverlayEntry();

      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
    }
  }

  void closeDropdown() {
    try {
      if (_overlayEntry == null) {
        return;
      }
      _overlayEntry?.remove();
      _overlayEntry = null;
    } catch (e) {
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              closeDropdown();
            },
          ),
          Positioned(
            width: size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              targetAnchor: Alignment.bottomLeft,
              child: Padding(
                padding: EdgeInsets.only(top: MySize.size8 ?? 8),
                child: Material(
                  // color: Colors.transparent,
                  child: Obx(
                        () => Container(
                          height:
                          suggestions.length > 2 ? MySize.size200 ?? 200 : null,
                          decoration: BoxDecoration(
                            color: AppTheme.grey,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: suggestions.isEmpty
                              ? GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              closeDropdown();
                            },
                            child: Padding(
                              padding: EdgeInsets.all(MySize.size16 ?? 16),
                              child: TypoGraphy(
                                text: widget.controller.text.isEmpty
                                    ? 'Type to Search'
                                    : 'No results found, try for a different location',
                                level: 3,
                                color: AppTheme.white,
                              ),
                            ),
                          )
                              : Padding(
                            padding:
                            EdgeInsets.only(right: MySize.size6 ?? 6),
                            child: RawScrollbar(
                              interactive: true,
                              controller: scrollController,
                              mainAxisMargin: 0,
                              crossAxisMargin: 0,
                              padding: EdgeInsets.symmetric(
                                  vertical: MySize.size20 ?? 20),
                              // thumbColor: AppTheme.containerBorder,
                              scrollbarOrientation:
                              ScrollbarOrientation.right,
                              trackColor: Colors.transparent,
                              trackBorderColor: Colors.transparent,
                              thickness: 3,
                              thumbVisibility: true,
                              radius: const Radius.circular(10),
                              child: Obx(
                                    () => ListView(
                                  controller: scrollController,
                                  physics: const ClampingScrollPhysics(),
                                  padding:
                                  const EdgeInsets.symmetric(vertical: 8),
                                  shrinkWrap: true,
                                  children: suggestions
                                      .map(
                                        (item) => GestureDetector(
                                      onTap: () {
                                        widget.controller.text = item;
                                        widget.selectedSuggestion.value =
                                            item;
                                        formFieldKey.currentState
                                            ?.validate();

                                        HapticFeedback.lightImpact();
                                        closeDropdown();
                                      },
                                      child: Padding(
                                        padding:
                                        const EdgeInsets.symmetric(
                                          horizontal: 18,
                                          vertical: 12,
                                        ),
                                        child: TypoGraphy(
                                          text: item,
                                          level: 3,
                                          color: AppTheme.white,
                                        ),
                                      ),
                                    ),
                                  )
                                      .expand(
                                        (element) sync* {
                                      yield Divider(
                                        thickness: 1.5,
                                        height: 1.5,
                                        color: AppTheme.white
                                            .withOpacity(0.1),
                                        indent: MySize.size20,
                                        endIndent: MySize.size20,
                                      );
                                      yield element;
                                    },
                                  )
                                      .skip(1)
                                      .toList(),
                                ),
                              ),
                            ),
                          ),
                        ),
                  ),
                )
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (didPop) {
        closeDropdown();
      },
      child: FormField<String?>(
        key: formFieldKey,
        initialValue: widget.initialValue,
        builder: (field) => GestureDetector(
          onTap: () {
            if (_overlayEntry != null) {
              focusNode.unfocus();
              closeDropdown();
            } else {
              HapticFeedback.lightImpact();
              field.validate();
              focusNode.requestFocus();
              openDropdown();
            }
          },
          child: CompositedTransformTarget(
            link: _layerLink,
            child: SearchAppTextField(
              textCapitalization: TextCapitalization.sentences,
              controller: widget.controller,
              focusNode: focusNode,
              labelText: "Location",
              maxLines: null,
                onChangedValue: (value) {
                  onQueryChange(widget.controller.text);
                },
                onSubmitted: (p0) {
                  focusNode.unfocus();
                  closeDropdown();
                  field.validate();
                },
            )
            // AppTextField(
            //   maxLines: null,
            //   focusNode: focusNode,
            //   controller: controller,
            //   labelText: "Location",
            //   maxLength: 30,
            //   textCapitalization: TextCapitalization.words,
            //   onChangedValue: (value) {
            //     onQueryChange(controller.text);
            //   },
            //   onSubmitted: (p0) {
            //     focusNode.unfocus();
            //     closeDropdown();
            //     field.validate();
            //   },
            // ),
          ),
        ),
      ),
    );
  }

}
