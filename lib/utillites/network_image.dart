import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

import 'app_theme.dart';

class NetworkImageComponent extends StatelessWidget {
  final String? imageUrl;
  final String? errorImageUrl;
  final double? height;
  final double? aspectRatio;
  double? simmerHeight;
  final double? width;
  final bool? isTask;
  final BoxFit? boxFit;

  NetworkImageComponent(
      {super.key,
      required this.imageUrl,
      this.errorImageUrl,
      this.height,
      this.simmerHeight,
      this.width,
        this.boxFit,
      this.aspectRatio,this.isTask});

  @override
  Widget build(BuildContext context) {
    simmerHeight ??= height;
    if ((imageUrl == null || imageUrl!.isEmpty)) {
      Widget errorImage = Image.asset(
        errorImageUrl ?? "",
        height: height,
        width: width,
        fit: BoxFit.cover,
      );

      if (aspectRatio == null) return errorImage;
      return AspectRatio(aspectRatio: aspectRatio ?? 1, child: errorImage);
    }

    Widget image = CachedNetworkImage(
      maxHeightDiskCache: 1400,
      maxWidthDiskCache: 1400,
      height: height,
      width: width,
      imageUrl: imageUrl!,
      cacheManager: CachedNetworkImageProvider.defaultCacheManager,
      placeholder: (context, url) => Shimmer.fromColors(
        baseColor: AppTheme.baseShimmer,
        highlightColor: AppTheme.highlightShimmer,
        child: Container(
          height: simmerHeight,
          width: width,
          color: Colors.grey[300],
        ),
      ),
      errorWidget: (context, url, error) => Image.asset(
        errorImageUrl ?? "",
        height: height,
        width: width,
      ),
      fit: boxFit ?? BoxFit.cover,

    );

    if (aspectRatio == null) return image;

    return AspectRatio(aspectRatio: aspectRatio ?? 1, child: image);
  }
}
