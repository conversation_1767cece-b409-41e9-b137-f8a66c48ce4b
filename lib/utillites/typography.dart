import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/utillites/translation_service.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../models/theme_model/font_size.dart';
import 'app_theme.dart';
import 'common_function.dart';

TextStyle myTextStyle(
    {FontLevel? fontLevel,
    double? fontSize,
    Color? color,
    Color backgroundColor = AppTheme.textBackGroundPrimary,
    FontWeight? fontWeight,
    String? fontFamily,
    TextDecoration? textDecoration,
    FontStyle? fontStyle,
    BuildContext? context}) {
  fontLevel ??= AppTheme.fontSize.f1;
  
  // Use theme-specific color if context is provided
  Color effectiveColor = color ?? (context != null 
      ? AppTheme.getTextPrimaryColor(context) 
      : AppTheme.textPrimary);

  return GoogleFonts.inter(
      fontSize: fontSize ?? fontLevel.fontSize,
      color: effectiveColor,
      fontWeight: fontWeight ?? fontLevel.fontWeight,
      decoration: textDecoration ?? TextDecoration.none,
      backgroundColor: backgroundColor,
      fontStyle: fontStyle);
}

FontLevel getLevelFromNumber(int? level) {
  switch (level) {
    case 1:
      return AppTheme.fontSize.f1;
    case 2:
      return AppTheme.fontSize.f2;
    case 3:
      return AppTheme.fontSize.f3;
    case 4:
      return AppTheme.fontSize.f4;
    case 5:
      return AppTheme.fontSize.f5;
    case 6:
      return AppTheme.fontSize.f6;
    case 7:
      return AppTheme.fontSize.f7;
    case 8:
      return AppTheme.fontSize.f8;
    case 9:
      return AppTheme.fontSize.f9;
    case 10:
      return AppTheme.fontSize.f10;
    case 11:
      return AppTheme.fontSize.f11;
    case 12:
      return AppTheme.fontSize.f12;
    default:
      return AppTheme.fontSize.f5;
  }
}

class TypoGraphy extends StatelessWidget {
  final String? htmlText;
  final String? text;
  final int? level;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextStyle? textStyle;
  final FontStyle? fontStyle;
  final TextDecoration? textDecoration;
  final TextAlign? textAlign;
  final Color? color;
  final Color highlightTextColor;
  final Color backgroundColor;
  final int? maxLines;
  final TextOverflow? overflow;
  final List<String>? highlightKeywords;
  final List<String>? lineThroughWord;
  final List<String>? underLineWord;
  final List<String>? overLineWord;
  final bool isTranslateDisabled;
  final ShaderCallback? shaderCallback;

  const TypoGraphy({
    super.key,
    this.text,
    this.htmlText,
    this.level,
    this.fontWeight,
    this.fontSize,
    this.textStyle,
    this.fontStyle,
    this.textDecoration,
    this.color,
    this.highlightTextColor = Colors.yellow,
    this.backgroundColor = Colors.transparent,
    this.highlightKeywords,
    this.lineThroughWord,
    this.underLineWord,
    this.overLineWord,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.isTranslateDisabled = false,
    this.shaderCallback,
  });

  TextStyle myTextStyle({
    required FontLevel fontLevel,
    double? fontSize,
    Color? color,
    FontWeight? fontWeight,
    String? fontFamily,
    TextDecoration? textDecoration,
    FontStyle? fontStyle,
  }) {
    return GoogleFonts.inter(
      fontSize: fontSize ?? fontLevel.fontSize,
      color: color ?? AppTheme.getTextPrimaryColor(Get.context!),
      fontWeight: fontWeight ?? fontLevel.fontWeight,
      decoration: textDecoration,
      fontStyle: fontStyle,
    );
  }

  @override
  Widget build(BuildContext context) {
    TextStyle effectiveTextStyle = textStyle ??
        myTextStyle(
          fontLevel: getLevelFromNumber(level),
          color: color ?? AppTheme.getTextPrimaryColor(context),
          fontWeight: fontWeight,
          textDecoration: textDecoration,
          fontStyle: fontStyle,
        );

    if (highlightKeywords != null || lineThroughWord != null || underLineWord != null || overLineWord != null) {
      return HighlightedMarkdown(
        text: text ?? "",
        highlightKeywords: highlightKeywords ?? [],
        textStyle: effectiveTextStyle,
        lineThroughWord: lineThroughWord ?? [],
        textDecoration: textDecoration,
        underLineWord: underLineWord ?? [],
        overLineWord: overLineWord ?? [],
      );
    }

    String? contentToTranslate = text ?? htmlText;

    return FutureBuilder<String>(
        future: htmlText != null
            ? TranslationService().translateHtml(htmlText ?? "")
            : TranslationService().translate(contentToTranslate ?? ""),
        builder: (context, snapshot) {
          String translatedText = "";
          if (isTranslateDisabled || TranslationService().isDefault()) {
            translatedText = contentToTranslate ?? "";
          } else if (snapshot.connectionState == ConnectionState.done) {
            translatedText = snapshot.data ?? ' ' * translatedText.length;
          }
          if (htmlText != null) {
            return Html(
              data: translatedText,
              style: CommonFunction.style(),
              onLinkTap: (url, attributes, element) {
                if (url != null) {
                  launchUrlString(url.trim());
                }
              },
            );
          }

          if (shaderCallback != null) {
            return ShaderMask(
              shaderCallback: shaderCallback!,
              child: Text(
                translatedText,
                style: effectiveTextStyle.copyWith(color: Colors.white),
                // Neutral color for text
                textAlign: textAlign,
                maxLines: maxLines,
                overflow: overflow,
              ),
            );
          }

          return Text(
            translatedText,
            style: effectiveTextStyle,
            textAlign: textAlign,
            maxLines: maxLines,
            overflow: overflow,
          );
        });
  }
}

class HighlightedMarkdown extends StatelessWidget {
  final String text;

  final TextStyle? textStyle;
  final int? level;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;
  final TextDecoration? textDecoration;
  final TextAlign? textAlign;
  final Color? color;
  final Color highlightTextColor;
  final Color backgroundColor;
  final List<String> highlightKeywords;
  final List<String> lineThroughWord;
  final List<String> underLineWord;
  final List<String> overLineWord;

  const HighlightedMarkdown({
    super.key,
    required this.text,
    this.textStyle,
    this.level,
    this.fontWeight,
    this.fontStyle,
    this.textDecoration,
    this.color,
    this.highlightTextColor = AppTheme.textHighlight,
    this.backgroundColor = AppTheme.textHighlightBackground,
    this.highlightKeywords = const [],
    this.lineThroughWord = const [],
    this.underLineWord = const [],
    this.overLineWord = const [],
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    final textSpans = _parseMarkdown(text, highlightKeywords);
    return RichText(
      text: TextSpan(children: textSpans),
      textAlign: textAlign ?? TextAlign.start,
    );
  }

  List<TextSpan> _parseMarkdown(String text, List<String> highlightKeywords) {
    List<TextSpan> spans = [];

    int start = 0;

    for (int i = 0; i < text.length; i++) {
      List<TextDecoration> decorations = [];
      bool isHighLight = (highlightKeywords).contains(text.substring(start, i + 1));

      Color textColor = color ?? AppTheme.black;
      TextDecoration? textDecoration = this.textDecoration;
      Color backgroundColor = AppTheme.textBackGroundPrimary;
      if (isHighLight) {
        textColor = highlightTextColor;
        backgroundColor = this.backgroundColor;
      }

      if (textDecoration != null) {
        decorations.add(textDecoration);
      }

      if (underLineWord.contains(text.substring(start, i + 1))) {
        decorations.add(TextDecoration.underline);
      }

      if (overLineWord.contains(text.substring(start, i + 1))) {
        decorations.add(TextDecoration.overline);
      }

      if (lineThroughWord.contains(text.substring(start, i + 1))) {
        decorations.add(TextDecoration.lineThrough);
      }

      if (decorations.isNotEmpty) {
        textDecoration = TextDecoration.combine(decorations);
      }

      TextStyle highlightTextStyle = myTextStyle(
          fontLevel: getLevelFromNumber(level),
          color: textColor,
          backgroundColor: backgroundColor,
          fontWeight: fontWeight,
          textDecoration: textDecoration,
          fontStyle: fontStyle);

      if (text[i] == ' ') {
        spans.add(TextSpan(text: text.substring(start, i), style: textStyle));
        spans.add(TextSpan(text: ' ', style: textStyle)); // Add space here
        start = i + 1;
      } else if ([...overLineWord, ...lineThroughWord, ...underLineWord, ...highlightKeywords]
          .contains(text.substring(start, i + 1))) {
        spans.add(TextSpan(text: text.substring(start, i + 1), style: highlightTextStyle));
        start = i + 1;
      }
    }

// Add space after the last word if needed
    if (start < text.length) {
      spans.add(TextSpan(text: '${text.substring(start)} ', style: textStyle));
    }
    return spans;
  }
}
