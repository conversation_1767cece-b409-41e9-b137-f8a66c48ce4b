// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDy-PYYZlnw8xLVtsZYXc1bKdxf7imgASw',
    appId: '1:771366090530:web:a6fdc74c9b6610ac9f177b',
    messagingSenderId: '771366090530',
    projectId: 'incenti-ai',
    authDomain: 'incenti-ai.firebaseapp.com',
    storageBucket: 'incenti-ai.firebasestorage.app',
    measurementId: 'G-ECN7M1HWM3',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDUbz-0CJUsvInB54FDZi9sKEPl5bWmz3g',
    appId: '1:771366090530:android:4666e379b93b8c659f177b',
    messagingSenderId: '771366090530',
    projectId: 'incenti-ai',
    storageBucket: 'incenti-ai.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAsUEOk3g-qA55MLFClKWVhA3JHZVbY4TQ',
    appId: '1:771366090530:ios:3c58b67a80c353c19f177b',
    messagingSenderId: '771366090530',
    projectId: 'incenti-ai',
    storageBucket: 'incenti-ai.firebasestorage.app',
    iosBundleId: 'com.incentiai',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAsUEOk3g-qA55MLFClKWVhA3JHZVbY4TQ',
    appId: '1:771366090530:ios:3c58b67a80c353c19f177b',
    messagingSenderId: '771366090530',
    projectId: 'incenti-ai',
    storageBucket: 'incenti-ai.firebasestorage.app',
    iosBundleId: 'com.incentiai',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDy-PYYZlnw8xLVtsZYXc1bKdxf7imgASw',
    appId: '1:771366090530:web:1a756821f45762679f177b',
    messagingSenderId: '771366090530',
    projectId: 'incenti-ai',
    authDomain: 'incenti-ai.firebaseapp.com',
    storageBucket: 'incenti-ai.firebasestorage.app',
    measurementId: 'G-1DQEFDHBLE',
  );
}
