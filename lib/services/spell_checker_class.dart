import 'dart:convert';
import 'package:http/http.dart' as http;

class SpellCheckResult {
  final List<SpellError> errors;

  SpellCheckResult({required this.errors});

  factory SpellCheckResult.fromJson(Map<String, dynamic> json) {
    final List<dynamic> errorsList = json['errors'] ?? [];
    final errors = errorsList
        .map((error) => SpellError.fromJson(error as Map<String, dynamic>))
        .toList();
    return SpellCheckResult(errors: errors);
  }
}

class SpellError {
  final String word;
  final int offset;
  final int length;
  final List<String> suggestions;

  SpellError({
    required this.word,
    required this.offset,
    required this.length,
    required this.suggestions,
  });

  factory SpellError.fromJson(Map<String, dynamic> json) {
    final List<dynamic> suggestionsList = json['suggestions'] ?? [];
    final suggestions = suggestionsList.map((s) => s.toString()).toList();

    return SpellError(
      word: json['word'] as String,
      offset: json['offset'] as int,
      length: json['length'] as int,
      suggestions: suggestions,
    );
  }
}

class LanguageToolService {
  static const _endpoint = 'https://api.languagetoolplus.com/v2/check';
  static const String _apiKey = '';

  Future<SpellCheckResult> checkText(String text) async {
    final response = await http.post(
      Uri.parse(_endpoint),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
      body: {
        'text': text,
        'language': 'en-US',
        if (_apiKey.isNotEmpty) 'token': _apiKey,
      },
    );

    if (response.statusCode != 200) {
      throw Exception('LanguageTool call failed: ${response.body}');
    }

    final jsonResponse = jsonDecode(response.body);

    final errors = <SpellError>[];
    for (final m in jsonResponse['matches']) {
      if (m['rule']['issueType'] != 'misspelling') continue;

      errors.add(
        SpellError(
          word: m['context']['text'].substring(m['context']['offset'],
              m['context']['offset'] + m['context']['length']),
          offset: m['offset'],
          length: m['length'],
          suggestions: (m['replacements'] as List)
              .map((r) => r['value'].toString())
              .toList(),
        ),
      );
    }

    return SpellCheckResult(errors: errors);
  }
}