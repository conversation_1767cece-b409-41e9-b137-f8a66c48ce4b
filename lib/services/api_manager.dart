// ignore_for_file: constant_identifier_names, prefer_collection_literals, prefer_interpolation_to_compose_strings, unused_local_variable,

import 'dart:convert';
import 'dart:developer';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Response;
import '../app/modules/splash/controllers/splash_controller.dart';
import '../constants/config.dart';
import '../constants/constant.dart';
import '../main.dart';
import '../utillites/app_theme.dart';
import '../utillites/typography.dart';

class ApiManager {
  final RxBool _isLoading = false.obs;
  final RxDouble _progress = 0.0.obs;

  bool get isLoading => _isLoading.value;
  double get progress => _progress.value;

  RxDouble get liveProgress => _progress;

  final dio = Dio();
  String defaultBaseUrl = CONFIG().baseUrl;

  Future<Map<String, dynamic>> getAuthHeaders({String? tokenRegister}) async {
    Map<String, dynamic> authHeaders = <String, dynamic>{};
    String? token = box.read('token');

    if (tokenRegister != null) {
      dio.options.headers["Authorization"] = "Bearer $tokenRegister";
      authHeaders["Authorization"] = "Bearer $tokenRegister";
    } else {
      if (token != null) {
        dio.options.headers["Authorization"] = "Bearer $token";
        authHeaders["Authorization"] = "Bearer $token";
      }
    }
    return authHeaders;
  }

  Future callApi(ApiModel apiModel,
      {dynamic params,
      Map<String, dynamic>? headers,
      void Function(dynamic response, String message)? successCallback,
      void Function(dynamic message, String statusCode)? failureCallback,
      String? baseUrl,
      bool isLoadingDisable = false,
      bool showErrorDialog = true}) async {
    String url = (baseUrl ?? defaultBaseUrl) + apiModel.endpoint;
    try {
      if (!isConnected) {
        return 0;
      }
      if (!isLoadingDisable) _isLoading.value = true;
      var connectivityResult = await Connectivity().checkConnectivity();
      if (failureCallback != null &&
          connectivityResult == ConnectivityResult.none) {
        failureCallback("", "No Internet Connection");
        // getDialog(title: "Error", desc: "No Internet Connection.");
      }
      dio.options.validateStatus = (status) {
        return status! <= 505;
      };
      dio.options.connectTimeout = const Duration(seconds: 60); //5s
      dio.options.receiveTimeout = const Duration(seconds: 60);
      if (headers != null) {
        for (var key in headers.keys) {
          dio.options.headers[key] = headers[key];
        }
      }

  /*    dio.interceptors.add(PrettyDioLogger());

      dio.interceptors.add(PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 90,
          enabled: kDebugMode,
          filter: (options, args) {
            // don't print requests with uris containing '/posts'
            if (options.path.contains('/posts')) {
              return false;
            }
            // don't print responses with unit8 list data
            return !args.isResponse || !args.hasUint8ListData;
          }));*/

      // Set Barer Token
      String? token = headers?["token"] ?? box.read('token');
      // String? token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.S49mYBEbbaf82UpsSWkuYz4cZeJNvx6Fw-7f2UwjvdM";
      if (token != null) {
        dio.options.headers['Authorization'] = 'Bearer $token';
      }
      switch (apiModel.type) {
        case APIType.POST:
          var response = await dio.post(
            url,
            data: params,
            onSendProgress: (count, total) {
              _progress.value = count / total * 100;
            },
          );
          // ignore: use_build_context_synchronously
          if (successCallback != null || failureCallback != null) {
            parseResponse(response,
                successCallback: successCallback,
                failureCallback: failureCallback,
                showErrorDialog: showErrorDialog);
          } else {
            return response.data;
          }
          break;
        case APIType.PATCH:
          var response = await dio.patch(
            url,
            data: params,
            onSendProgress: (count, total) {
              _progress.value = count / total * 100;
            },
          );
          // ignore: use_build_context_synchronously
          if (successCallback != null || failureCallback != null) {
            parseResponse(response,
                successCallback: successCallback,
                failureCallback: failureCallback,
                showErrorDialog: showErrorDialog);
          } else {
            return response.data;
          }
          break;
        case APIType.GET:
          var response = await dio.get(
            url,
            queryParameters: params,
            onReceiveProgress: (count, total) {
              _progress.value = count / total * 100;
            },
          );
          // ignore: use_build_context_synchronously
          if (successCallback != null || failureCallback != null) {
            parseResponse(response,
                successCallback: successCallback,
                failureCallback: failureCallback,
                showErrorDialog: showErrorDialog);
          } else {
            return response.data;
          }
          break;

        case APIType.PUT:
          var response = await dio.put(
            url,
            data: params,
            onSendProgress: (count, total) {
              _progress.value = count / total * 100;
            },
          );
          // ignore: use_build_context_synchronously
          if (successCallback != null || failureCallback != null) {
            parseResponse(response,
                successCallback: successCallback,
                failureCallback: failureCallback,
                showErrorDialog: showErrorDialog);
          } else {
            return response.data;
          }
          break;

        case APIType.DELETE:
          var response = await dio.delete(url, queryParameters: params);
          // ignore: use_build_context_synchronously
          if (successCallback != null || failureCallback != null) {
            parseResponse(response,
                successCallback: successCallback,
                failureCallback: failureCallback,
                showErrorDialog: showErrorDialog);
          } else {
            return response.data;
          }
          break;
      }
    } finally {
      if (!isLoadingDisable) _isLoading.value = false;
    }
  }

  parseResponse(Response<dynamic> response,
      {Function(dynamic response, String message)? successCallback,
      Function(dynamic statusCode, String message)? failureCallback,
      bool showErrorDialog = true}) async {
    // app.resolve<CustomDialogs>().showCircularDialog();
    String statusCode = "response.data['code']";
    String message = "response.data['message']";

    if ((response.statusCode == 401 &&
            response.data?["message"] == "Access denied") ||
        (response.statusCode == 401 &&
            response.data?["message"] == "You are not authorized person") ||
        (response.statusCode == 500 &&
            response.data?["message"] == "invalid signature")) {
      // await Helper.getInstance.logoutPopup();
      // box.erase();
      // if (Get.currentRoute != Routes.USER_AUTH) {
      //   Get.offAllNamed(Routes.USER_AUTH);
      // }

      //CommonFunction.logOut();
    } else if (response.statusCode == 200 ||
        response.statusCode == 201 ||
        response.statusCode == 203) {
      // hideDialog(true, context);
      if (successCallback != null && isNullEmptyOrFalse(response.data)) {
        successCallback(response.statusCode, message);
        final prettyString =
            const JsonEncoder.withIndent('  ').convert(response.data);
        log(prettyString);
        return;
      }
      if (successCallback != null &&
          (response.data is Map<String, dynamic> ||
              response.data is List<dynamic>)) {
        successCallback(response.data, message);
        // final prettyString =
        //     const JsonEncoder.withIndent('  ').convert(response.data);
        // log(prettyString);
        return;
      } else if (successCallback != null &&
          response.data is List<Map<String, dynamic>>) {
        successCallback(response.data, response.statusMessage.toString());
        final prettyString =
            const JsonEncoder.withIndent('  ').convert(response.data);
        log(prettyString);
        return;
      } else if (failureCallback != null) {
        failureCallback(response.data, response.statusMessage.toString());
        return;
      } else {
        return;
      }
    } else {
      // hideDialog(true, context);
      if (failureCallback != null) {
        failureCallback(
          response.data['status'].toString(),
          (response.data["message"] != null || response.data["message"] != "")
              ? response.data["message"]
              : response.statusMessage.toString(),
        );

        if (response.statusCode == 409 &&
            response.data["message"] != null &&
            !response.data["message"]
                .toString()
                .contains("already registered.") &&
            showErrorDialog) {
          showDialog<bool>(
            context: Get.context!,
            builder: (context) => AlertDialog(
              backgroundColor: AppTheme.primary1,
              surfaceTintColor: AppTheme.primary1,
              content: TypoGraphy(
                  level: 3,
                  color: AppTheme.whiteF4F4F4,
                  text: response.data["message"]),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: TypoGraphy(
                    text: 'Ok',
                    level: 4,
                    color: AppTheme.white,
                  ),
                ),
              ],
            ),
          );
        }
      }
    }
  }
}

bool isNullEmptyOrFalse(dynamic o) {
  if (o is Map<String, dynamic> || o is List<dynamic>) {
    return o == null || o.length == 0;
  }
  return o == null || false == o || "" == o;
}
