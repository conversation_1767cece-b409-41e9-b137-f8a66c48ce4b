import 'dart:async';
import 'package:get/get.dart';
import 'package:app_links/app_links.dart';
import 'package:incenti_ai/app/routes/app_pages.dart';
import 'package:share_plus/share_plus.dart';

import '../app/modules/communities/controllers/communities_controller.dart';

class AppLinkService extends GetxService {
  static final AppLinkService instance = AppLinkService._internal();
  factory AppLinkService() => instance;
  AppLinkService._internal();

  StreamSubscription? linkSubscription;

  Future<AppLinkService> init() async {
    handleInitialLink();
    listenToIncomingLinks();
    return this;
  }

  Future<void> handleInitialLink() async {
    try {
      final initialLink = await AppLinks().getInitialLink();
      if (initialLink != null) {
        processLink(initialLink.toString());
        print('Initial link == ${initialLink.path}');
      }
    } catch (e) {
      print('Initial link error: $e');
    }
  }

  void listenToIncomingLinks() {
    linkSubscription = AppLinks().uriLinkStream.listen((Uri? link) {
      if (link != null) {
        processLink(link.toString());
      }
    }, onError: (err) {
      print('Link stream error: $err');
    });
  }

  @pragma("vm:entry-point")
  void processLink(String link) {
    print('Deep Link Received: $link');
    final uri = Uri.parse(link);
    final segments = uri.pathSegments;

    if (segments.isEmpty) return;

    final type = segments.first;
    final slug = segments.length > 1 ? segments[1] : null;

    if (slug == null) return;

    switch (type) {
      case 'posts':
        Get.toNamed(Routes.post_detail, arguments: {"postSlug": slug});
        break;
      case 'projects':
        Get.toNamed(Routes.project_detail, arguments: {'projectSlug': slug});
        break;
      case 'communities':
        Get.put(CommunitiesController());
        Get.toNamed(Routes.community_detail, arguments: {'communitySlug': slug});
        break;
    }
  }

  void shareMedia({
    required String slug,
    required ShareMediaType mediaType,
    String? title,
  }) {
    final uri = Uri(
      scheme: 'https',
      host: 'stag-webapp.incenti.ai',
      path: "${mediaType.name}/$slug",
    );

    final mediaLabel = {
      ShareMediaType.posts: 'post',
      ShareMediaType.projects: 'project',
      ShareMediaType.communities: 'community',
    }[mediaType]!;

    final shareText = (title != null && title.isNotEmpty)
        ? '*$title*\nCheck out this $mediaLabel: $uri'
        : 'Check out this $mediaLabel: $uri';

    Share.share(shareText);
  }
}

enum ShareMediaType { posts, projects, communities }