import 'package:get/get_navigation/src/routes/get_route.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';
import 'package:incenti_ai/app/modules/bottom_bar/bindings/bottom_bar_binding.dart';
import 'package:incenti_ai/app/modules/bottom_bar/views/bottom_bar_view.dart';
import 'package:incenti_ai/app/modules/chat_bot_view/bindings/chat_bot_view_binding.dart';
import 'package:incenti_ai/app/modules/chat_bot_view/views/chat_bot_view.dart';
import 'package:incenti_ai/app/modules/communities/bindings/communities_binding.dart';
import 'package:incenti_ai/app/modules/communities/views/communities_view.dart';
import 'package:incenti_ai/app/modules/community_detail_view/bindings/community_detail_view_binding.dart';
import 'package:incenti_ai/app/modules/community_detail_view/views/community_detail_view.dart';
import 'package:incenti_ai/app/modules/create_communities/bindings/create_communities_binding.dart';
import 'package:incenti_ai/app/modules/create_communities/views/create_communities_view.dart';
import 'package:incenti_ai/app/modules/create_project/bindings/create_project_binding.dart';
import 'package:incenti_ai/app/modules/create_project/views/create_project_view.dart';
import 'package:incenti_ai/app/modules/create_todo_view/bindings/create_todo_view_binding.dart';
import 'package:incenti_ai/app/modules/create_todo_view/views/create_todo_view.dart';
import 'package:incenti_ai/app/modules/explore/bindings/explore_binding.dart';
import 'package:incenti_ai/app/modules/explore/views/explore_view.dart';
import 'package:incenti_ai/app/modules/forgot_password/bindings/forgot_password_binding.dart';
import 'package:incenti_ai/app/modules/forgot_password/views/forgot_password_view.dart';
import 'package:incenti_ai/app/modules/global_search/bindings/global_search_view_binding.dart';
import 'package:incenti_ai/app/modules/global_search/views/global_search_view.dart';
import 'package:incenti_ai/app/modules/move_project/bindings/move_project_binding.dart';
import 'package:incenti_ai/app/modules/move_project/views/move_project_view.dart';
import 'package:incenti_ai/app/modules/notifications/views/notifications_view.dart';
import 'package:incenti_ai/app/modules/onboarding/bindings/onboarding_binding.dart';
import 'package:incenti_ai/app/modules/onboarding/views/onboarding_view.dart';
import 'package:incenti_ai/app/modules/other_user_profile/bindings/other_user_profile_binding.dart';
import 'package:incenti_ai/app/modules/other_user_profile/views/other_user_profile_view.dart';
import 'package:incenti_ai/app/modules/post/bindings/post_binding.dart';
import 'package:incenti_ai/app/modules/post/views/post_view.dart';
import 'package:incenti_ai/app/modules/post_detail_view/bindings/post_detail_view_binding.dart';
import 'package:incenti_ai/app/modules/profile_view/bindings/profile_view_binding.dart';
import 'package:incenti_ai/app/modules/profile_view/views/profile_view.dart';
import 'package:incenti_ai/app/modules/project_detail_view/bindings/project_detail_view_binding.dart';
import 'package:incenti_ai/app/modules/project_detail_view/views/project_detail_view.dart';
import 'package:incenti_ai/app/modules/project_detail_view/views/project_team_member_view.dart';
import 'package:incenti_ai/app/modules/projects/views/select_project_view.dart';
import 'package:incenti_ai/app/modules/reset_password/bindings/reset_password_binding.dart';
import 'package:incenti_ai/app/modules/reset_password/views/reset_password_view.dart';
import 'package:incenti_ai/app/modules/setting/bindings/setting_binding.dart';
import 'package:incenti_ai/app/modules/setting/views/setting_view.dart';
import 'package:incenti_ai/app/modules/sign_in/bindings/sign_in_binding.dart';
import 'package:incenti_ai/app/modules/sign_in/views/sign_in_view.dart';
import 'package:incenti_ai/app/modules/sign_up/bindings/sign_up_binding.dart';
import 'package:incenti_ai/app/modules/sign_up/views/sign_up_view.dart';
import 'package:incenti_ai/app/modules/story_cover_selector/binding/story_highlight_binding.dart';
import 'package:incenti_ai/app/modules/story_cover_selector/view/story_cover_selector_view.dart';
import 'package:incenti_ai/app/modules/story_editor/bindings/story_editor_bindings.dart';
import 'package:incenti_ai/app/modules/story_editor/views/story_editor_view.dart';
import 'package:incenti_ai/app/modules/story_highlight/binding/story_highlight_binding.dart';
import 'package:incenti_ai/app/modules/story_highlight/view/story_highlight_view.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/bindings/sub_project_detail_view_binding.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/views/sub_project_detail_view.dart';
import 'package:incenti_ai/app/modules/todo_view/bindings/todo_view_binding.dart';
import 'package:incenti_ai/app/modules/todo_view/views/todo_view.dart';
import 'package:incenti_ai/app/modules/tutorial_view/bindings/tutorial_view_binding.dart';
import 'package:incenti_ai/app/modules/tutorial_view/views/tutorial_view.dart';
import 'package:incenti_ai/app/modules/user_detail/bindings/user_detail_binding.dart';
import 'package:incenti_ai/app/modules/user_detail/views/user_detail_view.dart';
import 'package:incenti_ai/app/modules/verfiy_otp_view/bindings/verify_otp_view_binding.dart';
import 'package:incenti_ai/app/modules/verfiy_otp_view/views/verify_otp_view.dart';
import '../modules/app_under_maintenance/bindings/app_under_maintenance_binding.dart';
import '../modules/app_under_maintenance/views/app_under_maintenance_view.dart';
import '../modules/app_update_available/bindings/app_update_available_binding.dart';
import '../modules/app_update_available/views/app_update_available.dart';
import '../modules/no_internet/bindings/no_internet_binding.dart';
import '../modules/no_internet/views/no_internet_view.dart';
import '../modules/notifications/bindings/notifications_binding.dart';
import '../modules/post/views/post_publish_view.dart';
import '../modules/post_detail_view/views/post_detail_view.dart';
import '../modules/project_detail_view/bindings/project_team_member_binding.dart';
import '../modules/projects/bindings/projects_binding.dart';
import '../modules/projects/views/projects_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/splash/views/splash_view.dart';
import '../modules/story/binding/story_binding.dart';
import '../modules/story/view/story_view.dart';
import '../modules/video_trimmer/bindings/video_trim_binding.dart';
import '../modules/video_trimmer/view/video_trim_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;
  static final routes = [
    GetPage(
      name: _Paths.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.ONBOARDING,
      page: () => OnboardingView(),
      binding: OnboardingBinding(),
    ),
    GetPage(
      name: _Paths.Sign_In,
      page: () => SignInView(),
      binding: SignInBinding(),
    ),
    GetPage(
      name: _Paths.NO_INTERNET,
      page: () => const NoInternetView(),
      binding: NoInternetBinding(),
    ),
    GetPage(
      name: _Paths.Forgot_Password,
      page: () => const ForgotPasswordView(),
      binding: ForgotPasswordBinding(),
    ),
    GetPage(
      name: _Paths.Verify_Otp,
      page: () => const VerifyOtpView(),
      binding: VerifyOtpViewBinding(),
    ),
    GetPage(
      name: _Paths.Reset_Password,
      page: () => const ResetPasswordView(),
      binding: ResetPasswordBinding(),
    ),
    GetPage(
      name: _Paths.Sign_Up,
      page: () => const SignUpView(),
      binding: SignUpBinding(),
    ),
    GetPage(
      name: _Paths.User_Detail,
      page: () => const UserDetailView(),
      binding: UserDetailBinding(),
    ),
    GetPage(
      name: _Paths.Bottom_Bar,
      page: () => const BottomBarView(),
      binding: BottomBarBinding(),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: _Paths.explore_View,
      page: () => const ExploreView(),
      binding: ExploreBinding(),
    ),
    GetPage(
      name: _Paths.notifications,
      page: () => const NotificationsView(),
      binding: NotificationsBinding(),
    ),
    GetPage(
      name: _Paths.project,
      page: () => const ProjectsView(),
      binding: ProjectsBinding(),
    ),
    GetPage(
      name: _Paths.communities,
      page: () => const CommunitiesView(),
      binding: CommunitiesBinding(),
    ),
    GetPage(
      name: _Paths.todos,
      page: () => const TodosView(),
      binding: TodoViewBinding(),
    ),
    GetPage(
      name: _Paths.post,
      page: () => const PostView(),
      binding: PostBinding(),
    ),
    GetPage(
      name: _Paths.create_project,
      page: () => const CreateProjectView(),
      binding: CreateProjectBinding(),
    ),
    GetPage(
      name: _Paths.post_publish,
      page: () => const PostPublishView(),
      binding: PostBinding(),
    ),
    GetPage(
      name: _Paths.post_detail,
      page: () => const PostDetailView(),
      binding: PostDetailViewBinding(),
    ),
    GetPage(
      name: _Paths.APP_UNDER_MAINTENANCE,
      page: () => const AppUnderMaintenanceView(),
      binding: AppUnderMaintenanceBinding(),
    ),
    GetPage(
      name: _Paths.APP_UNDER_MAINTENANCE,
      page: () => const AppUnderMaintenanceView(),
      binding: AppUnderMaintenanceBinding(),
    ),
    GetPage(
      name: _Paths.APP_UPDATE_AVAILABLE,
      page: () => const AppUpdateAvailableView(),
      binding: AppUpdateAvailableBinding(),
    ),
    GetPage(
      name: _Paths.select_project,
      page: () => const SelectProjectView(),
      binding: ProjectsBinding(),
    ),
    GetPage(
      name: _Paths.profile,
      page: () => const ProfileView(),
      binding: ProfileViewBinding(),
        transition: Transition.noTransition
    ),
    GetPage(
      name: _Paths.setting,
      page: () => const SettingView(),
      binding: SettingBinding(),
      transition: Transition.downToUp,
    ),
    GetPage(
      name: _Paths.project_detail,
      page: () => const ProjectDetailView(),
      binding: ProjectDetailViewBinding(),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: _Paths.subProject_detail,
      page: () => const SubProjectDetailView(),
      binding: SubProjectDetailViewBinding(),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: _Paths.other_user_profile,
      page: () => const OtherUserProfileView(),
      binding: OtherUserProfileBinding(),
        transition: Transition.noTransition
    ),
    GetPage(
      name: _Paths.move_project,
      page: () => const MoveProjectView(),
      binding: MoveProjectBinding(),
    ),
    GetPage(
      name: _Paths.story_editor,
      page: () => const StoryEditorView(),
      binding: StoryEditorBindings(),
    ),
    GetPage(
      name: _Paths.video_trim,
      page: () => const VideoTrimView(),
      binding: VideoTrimBinding(),
    ),
    GetPage(
      name: _Paths.all_stories_view,
      page: () => AllStoryView(),
      binding: StoryViewBinding(),
      transition: Transition.downToUp
    ),
    GetPage(
      name: _Paths.chat_bot,
      page: () => const ChatBotView(),
      binding: ChatBotViewBinding(),
    ),
    GetPage(
      name: _Paths.create_community,
      page: () => const CreateCommunitiesView(),
      binding: CreateCommunitiesBinding(),
    ),
    GetPage(
      name: _Paths.community_detail,
      page: () => const CommunityDetailView(),
      binding: CommunityDetailViewBinding(),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: _Paths.story_highlight_selection,
      page: () => const StoryHighlightView(),
      binding: StoryHighlightBinding(),
    ),
    GetPage(
      name: _Paths.story_cover_selector,
      page: () => const StoryCoverSelectorView(),
      binding: StoryCoverSelectorBinding(),
    ),
    GetPage(
      name: _Paths.project_member,
      page: () => const ProjectTeamMemberView(),
      binding: ProjectTeamMemberBinding(),
    ),
    GetPage(
      name: _Paths.create_todo,
      page: () => const CreateTodoView(),
      binding: CreateTodoViewBinding(),
    ),
    GetPage(
      name: _Paths.global_search,
      page: () => const GlobalSearchView(),
      binding: GlobalSearchViewBinding(),
    ),
    GetPage(
      name: _Paths.tutorial,
      page: () => const TutorialView(),
      binding: TutorialViewBinding(),
    ),
  ];
}
