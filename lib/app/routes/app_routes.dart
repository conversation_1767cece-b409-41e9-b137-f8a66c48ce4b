// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';

abstract class Routes {
  Routes._();
  static const SPLASH = _Paths.SPLASH;
  static const NO_INTERNET = _Paths.NO_INTERNET;
  static const ONBOARDING = _Paths.ONBOARDING;
  static const Sign_In = _Paths.Sign_In;
  static const Forgot_Password = _Paths.Forgot_Password;
  static const Verify_Otp = _Paths.Verify_Otp;
  static const Reset_Password = _Paths.Reset_Password;
  static const Sign_Up = _Paths.Sign_Up;
  static const User_Detail = _Paths.User_Detail;
  static const Bottom_Bar = _Paths.Bottom_Bar;
  static const explore_View = _Paths.explore_View;
  static const notifications = _Paths.notifications;
  static const explore_jobs_View = _Paths.explore_jobs_View;
  static const project = _Paths.project;
  static const communities = _Paths.communities;
  static const todos = _Paths.todos;
  static const post = _Paths.post;
  static const create_project = _Paths.create_project;
  static const post_publish = _Paths.post_publish;
  static const APP_UNDER_MAINTENANCE = _Paths.APP_UNDER_MAINTENANCE;
  static const APP_UPDATE_AVAILABLE = _Paths.APP_UPDATE_AVAILABLE;
  static const post_detail = _Paths.post_detail;
  static const select_project = _Paths.select_project;
  static const profile = _Paths.profile;
  static const setting = _Paths.setting;
  static const project_detail = _Paths.project_detail;
  static const subProject_detail = _Paths.subProject_detail;
  static const other_user_profile = _Paths.other_user_profile;
  static const move_project = _Paths.move_project;
  static const story_editor = _Paths.story_editor;
  static const video_trim = _Paths.video_trim;
  static const all_stories_view = _Paths.all_stories_view;
  static const chat_bot = _Paths.chat_bot;
  static const create_community = _Paths.create_community;
  static const community_detail = _Paths.community_detail;
  static const story_highlight_selection = _Paths.story_highlight_selection;
  static const story_cover_selector = _Paths.story_cover_selector;
  static const project_member = _Paths.project_member;
  static const create_todo = _Paths.create_todo;
  static const global_search = _Paths.global_search;
  static const tutorial = _Paths.tutorial;
}

abstract class _Paths {
  _Paths._();
  static const SPLASH = '/splash';
  static const NO_INTERNET = '/no-internet';
  static const ONBOARDING = '/onboarding';
  static const Sign_In = '/sign_in';
  static const Forgot_Password = '/forgot_password';
  static const Verify_Otp = '/verify_otp';
  static const Reset_Password = '/reset_password';
  static const Sign_Up = '/sign_up';
  static const User_Detail = '/user_detail';
  static const Bottom_Bar = '/bottom_bar';
  static const explore_View = '/explore_view';
  static const notifications = '/notifications';
  static const explore_jobs_View = '/explore_jobs_view';
  static const project = '/project';
  static const communities = '/communities';
  static const todos = '/todos';
  static const post = '/post';
  static const create_project = '/create_project';
  static const post_publish = '/post_publish';
  static const APP_UNDER_MAINTENANCE = '/app_under_maintenance';
  static const APP_UPDATE_AVAILABLE = '/app_update_available';
  static const post_detail = '/post_detail';
  static const select_project = '/select_project';
  static const move_project = '/move_project';
  static const profile = '/profile';
  static const setting = '/setting';
  static const project_detail = '/project_detail';
  static const subProject_detail = '/subProject_detail';
  static const other_user_profile = '/other_user_profile';
  static const story_editor = '/story_editor';
  static const video_trim = '/video_trim';
  static const all_stories_view = '/all_stories_view';
  static const chat_bot = '/chat_bot';
  static const create_community = '/create_community';
  static const community_detail = '/community_detail';
  static const story_highlight_selection = '/story_highlight_selection';
  static const story_cover_selector = '/story_cover_selector';
  static const project_member = '/project_member';
  static const create_todo = '/create_todo';
  static const global_search = '/global_search';
  static const tutorial = '/tutorial';
}
