import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/models/app_todo_model.dart';
import 'package:incenti_ai/utillites/app_theme.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_project_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';

class TodoViewController extends GetxController with WidgetsBindingObserver {
  DateTime? _backgroundTime;
  ApiManager apiManager = ApiManager();
  RxBool isLoading = false.obs;
  RxBool hasMoreData = true.obs;
  RxBool isPostLoading = false.obs;
  int limit = 20;
  RxInt page = 1.obs;
  RxInt projectPage = 1.obs;
  RxInt subProjectPage = 1.obs;
  RxList<ToDo> todoList = <ToDo>[].obs;
  RxBool isDownloading = false.obs;
  final downloadingFileIds = <int>{}.obs;
  RxList<Project> createdProject = <Project>[].obs;
  Rx<TextEditingController> searchController = TextEditingController().obs;
  RxInt selectedIndex = (-1).obs;
  RxBool isFilterApply = false.obs;
  RxBool isButtonClick = false.obs;
  // RxnString selectedProjectId = RxnString();
  RxList<String> selectedProjectIds = <String>[].obs;
  RxList<String> selectedSubProjectId = <String>[].obs;
  FocusNode searchFocusNode = FocusNode();
  Timer? debounceTimer;
  RxList<SubProjectData> subProjectList = <SubProjectData>[].obs;
  final ScrollController scrollController = ScrollController();
  RxMap<String, List<SubProjectData>> projectSubProjectsMap = <String, List<SubProjectData>>{}.obs;
  // final globalKey = GlobalKey();

  RxList filter = [
    null,
    "My To Do",
    null,
    "Assigned to Others",
    "Assigned by Others"
  ].obs;

  RxList priority = ["Low", "Medium", "High"].obs;

  pullRefresh() {
    projectPage.value = 1;
    hasMoreData.value = true;
    callApiForProjectData(context: Get.context!);
  }

  void _scrollToBottom() {
    Future.delayed(Duration(milliseconds: 100), () {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  var selectedPriorities = <String>[].obs;

  @override
  void onInit() {
    WidgetsBinding.instance.addObserver(this);
    callApiForToDo(
      context: Get.context!,
    );
    super.onInit();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      _backgroundTime = DateTime.now();
    } else if (state == AppLifecycleState.resumed) {
      if (_backgroundTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_backgroundTime!);
        if (difference.inMinutes >= 3) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            scrollController.animateTo(0, duration: Duration(milliseconds: 700), curve: Curves.easeInOut).then((value) =>  pullRefreshToDo(),);
          });
        }
      }
    }
  }

  pullRefreshToDo() {
    page.value = 1;
    hasMoreData.value = true;
    todoList.clear();
    callApiForToDo(context: Get.context!);
  }

  Future<void> callApiForDeleteOneToDo(
      {required BuildContext context, String? todoId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/todos/$todoId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForCompleteToDo(
      {required BuildContext context, required String todoId, required int index}) {
    FocusScope.of(context).unfocus();
    todoList[index].status?.value = !(todoList[index].status?.value ?? false);
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/todos/status/$todoId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            isLoading.value = false;
            // page.value = 1;
            // hasMoreData.value = true;
            // todoList.clear();
            // callApiForToDo(context: Get.context!).then((value) {
            //   _scrollToBottom();
            // },);

          }
        } catch (error) {
          isLoading.value = false;
          log("error === $error");
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
        CommonFunction.showCustomSnackbar(message: statusCode,isError: true,backgroundColor: AppTheme.red);
        todoList[index].status?.value = !(todoList[index].status?.value ?? false);
      },
    );
  }

  pullRefreshForUserSubProject() {
    page.value = 1;
    hasMoreData.value = true;
    callApiForSubProject(context: Get.context!, projectId: selectedProjectIds);
  }

  Future<void> callApiForSubProject(
      {required BuildContext context, List<String>? projectId, bool storeInMap = false, String? parentProjectId}) async {
    isLoading.value = true;
    log("value === ${projectId?.join(',').toString()}");
    return apiManager.callApi(
      APIS.project.getAllSubProject,
      params: {
        "ProjectIds": projectId?.join(',').toString(),
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text,
        // 'page': storeInMap ? 1 : projectPage.value,
        // 'limit': limit,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            SubProjectResponse subProjectResponse =
                SubProjectResponse.fromJson(response);
            final newData = subProjectResponse.data.data;
            final isLastPage = newData.length < limit;
            hasMoreData.value = !isLastPage;
            projectSubProjectsMap[parentProjectId!] = newData;
            subProjectList.addAll(newData);

            subProjectPage.value++;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future loadSubProjectsInParallel(BuildContext context, List<Project> projects) async {

    List<Future> subProjectFutures = projects.map((project) =>
        callApiForSubProject(
          context: context,
          projectId: [project.id.toString()],
          storeInMap: true,
          parentProjectId: project.id.toString(),
        )
    ).toList();

    await Future.wait(subProjectFutures);
  }


  Future<void> callApiForProjectData({required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    if (!hasMoreData.value) return;
    isLoading.value = true;
    final params = {
      "UserId": CurrentUser.user.id,
      'page': projectPage.value,
      'limit': limit,
      if (searchController.value.text.isNotEmpty)
        "searchQuery": searchController.value.text,
    };

    apiManager.callApi(
      APIS.project.getAllMyProject,
      params: params,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse =
                ProjectResponse.fromJson(response);
            final newData = projectResponse.data.data;
            final isLastPage = newData.length < limit;
            hasMoreData.value = !isLastPage;

            createdProject.addAll(newData);
            await loadSubProjectsInParallel(context, newData);

            projectPage.value++;
          }
        } catch (error) {
          log("Error processing response: $error");
          hasMoreData.value = false;
        } finally {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  Future<void> callApiForToDo({required BuildContext context}) async {
    isLoading.value = true;

    try {
      Map<String, dynamic> dict = {
        "limit": limit, "page": page.value
      };
      if(selectedIndex.value == 1){
        dict["created"] = 1;
      } /*else if(selectedIndex.value == 2) {
        dict["status"] = 1;
      }*/else if(selectedIndex.value == 3) {
        dict["assignedToFriends"] = 1;
      } else if(selectedIndex.value == 4) {
        dict["assignedToMe"] = 1;
      }

      if(selectedPriorities.isNotEmpty) {
        dict["priorities"] = selectedPriorities.map((element) {
          if(element == "Low"){
            return 1;
          } else if(element == "Medium") {
            return 2;
          } else if(element == "High") {
            return 3;
          }
        },).join(',').toString();
      }

      if(selectedProjectIds.isNotEmpty || selectedSubProjectId.isNotEmpty) {
        dict["ProjectIds"] = "${selectedProjectIds.join(',').toString()},${selectedSubProjectId.join(',').toString()}";
      }
      log("dict==$dict");
      await apiManager.callApi(
        APIS.todo.getTodo,
        params: dict,
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            log("dict==$response");
            TodoResponse todoResponse = TodoResponse.fromJson(response);
            if (todoResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if(selectedIndex.value == 0) {
              selectedIndex.value = -1;
            }
            if (page.value == 1) {
              todoList.clear();
            }
            todoList.addAll(todoResponse.data.data);
            page++;
          }
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;
        },
      );
    } catch (error) {
      hasMoreData.value = false;
    } finally {
      isLoading.value = false;
    }
  }
}
