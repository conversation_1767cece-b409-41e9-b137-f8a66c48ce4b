import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/typography.dart';

class CommonPriorityBottomSheet extends StatelessWidget {
  final RxList priorities;
  final RxList<String> selectedPriorities;
  final dynamic originalPriorities;
  final VoidCallback? onDone;

  const CommonPriorityBottomSheet({
    super.key,
    required this.priorities,
    required this.selectedPriorities,
    required this.originalPriorities,
    this.onDone,
  });

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        selectedPriorities.value = originalPriorities;
        return true;
      },
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: FractionallySizedBox(
          heightFactor: 0.55,
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppTheme.subBottom,
              border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40),
                topRight: Radius.circular(40),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.getScaledSizeWidth(30),
              ),
              child: Column(
                children: [
                  Space.height(36),
                  Row(
                    children: [
                      InkWell(
                        child: SvgPicture.asset(AppImage.backArrow,color: AppTheme.whiteWithNull,),
                        onTap: () {
                          selectedPriorities.value = originalPriorities;
                          Navigator.pop(context);
                        },
                      ),
                      Spacer(),
                      TypoGraphy(text: "Priority", level: 12),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          onDone?.call();
                          Navigator.pop(context);
                        },
                        child: TypoGraphy(
                          text: "Done",
                          level: 12,
                          color: AppTheme.primaryIconDark,
                        ),
                      )
                    ],
                  ),
                  Space.height(30),
                  Obx(() => Column(
                    children: priorities.map((element) {
                      bool isSelected = selectedPriorities.contains(element);
                      return Padding(
                        padding: EdgeInsets.only(
                          bottom: MySize.getScaledSizeHeight(30),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TypoGraphy(
                              text: element,
                              level: 5,
                              color: isSelected ? AppTheme.primary1 : null,
                            ),
                            GestureDetector(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                if (isSelected) {
                                  selectedPriorities.remove(element);
                                } else {
                                  selectedPriorities.add(element);
                                }
                              },
                              child: Container(
                                height: MySize.getScaledSizeHeight(28),
                                width: MySize.getScaledSizeWidth(28),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? AppTheme.primary1
                                      : Colors.transparent,
                                  border: Border.all(
                                    color: isSelected
                                        ? AppTheme.primary1
                                        :  AppTheme.whiteWithBase,
                                  ),
                                  borderRadius: BorderRadius.circular(6.14),
                                ),
                                child: isSelected
                                    ? Padding(
                                  padding: EdgeInsets.all(4),
                                  child: SvgPicture.asset(
                                    AppImage.checkIcon,
                                    color: AppTheme.white,
                                  ),
                                )
                                    : null,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ))
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
