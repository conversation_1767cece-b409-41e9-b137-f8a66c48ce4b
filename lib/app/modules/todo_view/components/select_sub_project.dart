import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/typography.dart';
import '../controllers/todo_view_controller.dart';

Widget filterSubProject(
    {required BuildContext context,
    required TodoViewController controller,
    required List<String> originalSelectedProjects}) {
  return BackdropFilter(
    filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
    child: FractionallySizedBox(
      heightFactor: 0.70,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppTheme.subBottom,
          border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(40),
            topRight: Radius.circular(40),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: MySize.getScaledSizeWidth(30),
          ),
          child: Column(
            children: [
              Space.height(36),
              Row(
                children: [
                  InkWell(
                    child: SvgPicture.asset(AppImage.backArrow,color: AppTheme.whiteWithNull,),
                    onTap: () {
                      controller.selectedSubProjectId.value =
                          originalSelectedProjects;
                      Navigator.pop(context);
                    },
                  ),
                  Spacer(),
                  TypoGraphy(text: "Sub-Projects", level: 12),
                  Spacer(),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: TypoGraphy(
                      text: "Done",
                      level: 12,
                      color: AppTheme.primaryIconDark,
                    ),
                  )
                ],
              ),
              Space.height(30),
              Obx(
                () => controller.subProjectList.isNotEmpty ?
                  ValueListenableBuilder(
                  valueListenable: controller.searchController.value,
                  builder: (context, value, child) {
                    return AppTextField(
                      controller: controller.searchController.value,
                      focusNode: controller.searchFocusNode,
                      padding: EdgeInsets.only(
                        top: MySize.size12 ?? 12,
                        bottom: MySize.size10 ?? 12,
                      ),
                      onChangedValue: (text) {
                        if ((text?.trim() ?? "").isNotEmpty) {
                          controller.debounceTimer?.cancel();
                          controller.debounceTimer =
                              Timer(Duration(milliseconds: 500), () {
                            controller.page.value = 1;
                            controller.subProjectList.clear();
                            controller.pullRefreshForUserSubProject();
                          });
                        }
                      },
                      height: MySize.getScaledSizeHeight(50),
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(4),
                        child: SvgPicture.asset(
                          AppImage.searchIcon,
                          height: MySize.size24,
                          width: MySize.size24,
                          color: AppTheme.grey,
                        ),
                      ),
                      suffixIcon: (controller
                              .searchController.value.text.isNotEmpty)
                          ? GestureDetector(
                              onTap: () {
                                controller.searchController.value.clear();
                                controller.page.value = 1;
                                controller.subProjectList.clear();
                                controller.pullRefreshForUserSubProject();
                              },
                              child: Padding(
                                padding:
                                    EdgeInsets.only(right: MySize.size15 ?? 20),
                                child: SvgPicture.asset(
                                  AppImage.textFieldClear,
                                  color: AppTheme.grey,
                                ),
                              ),
                            )
                          : null,
                      hintText: "Search",
                      hintStyle: TextStyle(
                        color: AppTheme.grey,
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w400,
                      ),
                    );
                  },
                ) : SizedBox.shrink(),
              ),
              Space.height(30),
              Expanded(
                child: Obx(
                  () => controller.isLoading.value &&
                          controller.subProjectList.isEmpty
                      ? Loader()
                      : controller.searchController.value.text.isNotEmpty &&
                              controller.subProjectList.isEmpty
                          ? Padding(
                              padding: EdgeInsets.only(
                                top: MySize.getScaledSizeHeight(150),
                                left: paddingHoriZontal,
                                right: paddingHoriZontal,
                              ),
                              child: Column(
                                // mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    "assets/images/icon_search.svg",
                                    height: MySize.size50,
                                    color: AppTheme.whiteWithNull,
                                  ),
                                  Empty(
                                    title: "Search Result Not Found !",
                                  ),
                                ],
                              ),
                            )
                          : CustomScrollView(
                              keyboardDismissBehavior:
                                  ScrollViewKeyboardDismissBehavior.onDrag,
                              slivers: [
                                CustomSliverListView(
                                  emptyWidget: Padding(
                                      padding: EdgeInsets.only(
                                        top: MySize.getScaledSizeHeight(150),
                                        left: paddingHoriZontal,
                                        right: paddingHoriZontal,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          TypoGraphy(
                                            text: "No Sub-Projects",
                                            level: 5,
                                          ),
                                          Space.height(5),
                                          TypoGraphy(
                                            text: "No Sub-Projects yet!",
                                            level: 3,
                                            color: AppTheme.grey,
                                          )
                                        ],
                                      )),
                                  maximumReachedWidget: const SizedBox(),
                                  itemBuilder: (p0, p1, index) {
                                    return Obx(
                                      () {
                                        final userId =
                                            controller.subProjectList[index];
                                        final isSelected = controller
                                            .selectedSubProjectId
                                            .contains(userId.id.toString());
                                        // final isSelected =
                                        //     controller.selectedSubProjectId.value == userId.id.toString();
                                        return Padding(
                                          padding: EdgeInsets.only(
                                              bottom:
                                                  MySize.getScaledSizeHeight(
                                                      32)),
                                          child: Row(
                                            children: [
                                              TypoGraphy(
                                                text: controller
                                                    .subProjectList[index].name,
                                                level: 5,
                                                color: isSelected
                                                    ? AppTheme.primary1
                                                    : null,
                                              ),
                                              Spacer(),
                                              GestureDetector(
                                                onTap: () {
                                                  HapticFeedback.lightImpact();
                                                  final id =
                                                      userId.id.toString();
                                                  if (controller
                                                      .selectedSubProjectId
                                                      .contains(id)) {
                                                    controller
                                                        .selectedSubProjectId
                                                        .remove(id);
                                                  } else {
                                                    controller
                                                        .selectedSubProjectId
                                                        .add(id);
                                                  }
                                                },
                                                child: Container(
                                                  height: MySize
                                                      .getScaledSizeHeight(28),
                                                  width:
                                                      MySize.getScaledSizeWidth(
                                                          28),
                                                  decoration: BoxDecoration(
                                                    color: isSelected
                                                        ? AppTheme.primary1
                                                        : Colors.transparent,
                                                    border: Border.all(
                                                      color: isSelected
                                                          ? AppTheme.primary1
                                                          : AppTheme.whiteWithBase,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6.14),
                                                  ),
                                                  child: isSelected
                                                      ? Padding(
                                                          padding:
                                                              EdgeInsets.all(4),
                                                          child:
                                                              SvgPicture.asset(
                                                            AppImage.checkIcon,
                                                            color:
                                                                AppTheme.white,
                                                          ),
                                                        )
                                                      : null,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    );
                                  },
                                  items: controller.subProjectList,
                                  isLoading: controller.isLoading.value,
                                  hasMoreData: controller.hasMoreData.value,
                                  onLoadMore: () {
                                    return controller.callApiForSubProject(
                                        context: context,
                                        projectId:
                                            controller.selectedProjectIds);
                                  },
                                )
                              ],
                            ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
