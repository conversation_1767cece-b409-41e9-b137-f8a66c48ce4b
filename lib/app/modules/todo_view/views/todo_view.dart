import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../bottom_bar/controllers/bottom_bar_controller.dart';
import '../../create_todo_view/components/common_todo_shimmer.dart';
import '../components/common_prority_view.dart';
import '../components/filter_project.dart';
import '../components/todo_card_view.dart';
import '../controllers/todo_view_controller.dart';

class TodosView extends StatefulWidget {
  const TodosView({super.key});

  @override
  State<TodosView> createState() => _TodosViewState();
}

class _TodosViewState extends State<TodosView> {
  final TodoViewController controller = Get.put(TodoViewController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: AppTheme.white,
      body: Column(
        children: [
          Space.height(40),
          SizedBox(
            height: MySize.getScaledSizeHeight(65),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding:
                          EdgeInsets.only(left: MySize.getScaledSizeWidth(34)),
                      child: InkWell(
                        onTap: () {
                          Get.find<BottomBarController>().currentIndex.value =
                              0;
                        },
                        child: Image.asset(
                          AppImage.appShortIcon,
                          color: AppTheme.whiteWithBase,
                          height: MySize.size31,
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        Obx(
                          () => Padding(
                            padding: EdgeInsets.only(
                                right: MySize.getScaledSizeWidth(15)),
                            child: InkWell(
                              onTap: () {
                                mainBottomSheet();
                              },
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  SvgPicture.asset(AppImage.filter,color: AppTheme.whiteWithNull,),
                                  if (controller.isFilterApply.value)
                                    Positioned(
                                      right: -4,
                                      bottom: 0,
                                      child: Container(
                                        height: MySize.getScaledSizeHeight(12),
                                        width: MySize.getScaledSizeWidth(12),
                                        decoration: BoxDecoration(
                                          color: AppTheme.red,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                              color: AppTheme.white, width: 1),
                                        ),
                                      ),
                                    )
                                ],
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            Get.toNamed(Routes.profile,arguments: {"isNeedBottom": true});
                            CurrentUser.getMe(callback: () async {});
                          },
                          child: Obx(
                            () => profileImage(
                              url: CurrentUser.user.image ?? "",
                              width: MySize.size34 ?? 25,
                              height: MySize.size34 ?? 25,
                              iconHeight: MySize.size34 ?? 25,
                              iconWidth: MySize.size34 ?? 25,
                              borderColor: Colors.transparent,
                              color: AppTheme.darkGrey[100],
                            ),
                          ),
                        ),
                        Space.width(30),
                      ],
                    ),
                  ],
                ),
                TypoGraphy(
                  text: "To Do",
                  level: 11,
                ),
              ],
            ),
          ),
          // Space.height(30),
          Expanded(
            child: Obx(
              () => (controller.isLoading.value &&
                          controller.todoList.isEmpty) ||
                      controller.isPostLoading.value
                  ? ListView.builder(
                      physics: AlwaysScrollableScrollPhysics(),
                      // shrinkWrap: true,
                      itemCount: 5,
                      padding: EdgeInsets.zero,
                      itemBuilder: (context, index) => TodoShimmerView(),
                    )
                  : RefreshIndicator(
                      onRefresh: () async {
                        controller.pullRefreshToDo();
                      },
                      child: controller.todoList.isEmpty &&
                          controller.isFilterApply.value
                          ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            text: "No matching To Do",
                            level: 6,
                            textAlign: TextAlign.center,
                          ),
                          Space.height(16),
                          TypoGraphy(
                            text:
                            "Try adjusting or clearing your filters to\nsee more To Do.",
                            level: 3,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      )
                          : controller.todoList.isEmpty
                              ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            text: "You're all caught up!",
                            level: 6,
                            textAlign: TextAlign.center,
                          ),
                          Space.height(16),
                          TypoGraphy(
                            text: "You have no To Do at the moment.",
                            level: 3,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      )
                              : CustomScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        controller: controller.scrollController,
                        slivers: [
                          SlidableAutoCloseBehavior(
                            child: CustomSliverListView(
                              emptyWidget: SizedBox(),
                              maximumReachedWidget: const SizedBox(),
                              itemBuilder: (context, p1, index) {
                                final incompleteTodos = controller.todoList
                                    .where((todo) => !(todo.status?.value ?? false))
                                    .toList();
                                final completedTodos = controller.todoList
                                    .where((todo) => todo.status?.value ?? false)
                                    .toList();

                                if (index < incompleteTodos.length) {
                                  final originalIndex = controller.todoList.indexOf(incompleteTodos[index]);
                                  return Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.getScaledSizeWidth(30))
                                        .copyWith(
                                        bottom: MySize.getScaledSizeHeight(18)),
                                    child: toDoCard(
                                        controller: controller,
                                        index: originalIndex),
                                  );
                                } else if (index == incompleteTodos.length && completedTodos.isNotEmpty) {
                                  return Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.getScaledSizeWidth(20))
                                        .copyWith(
                                        bottom: MySize.getScaledSizeHeight(20)),
                                    child: ExpansionTile(
                                      backgroundColor: Colors.transparent,
                                      collapsedBackgroundColor: Colors.transparent,
                                      // initiallyExpanded: true,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      collapsedShape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      title: TypoGraphy(
                                        text: "Completed (${completedTodos.length})",
                                        level: 4,
                                        fontWeight: FontWeight.w600,
                                        color: AppTheme.whiteWithBase,
                                      ),
                                      iconColor: AppTheme.whiteWithBase,
                                      collapsedIconColor: AppTheme.whiteWithBase,
                                      children: completedTodos.map((todo) {
                                        final originalIndex = controller.todoList.indexOf(todo);
                                        return Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: MySize.getScaledSizeWidth(10))
                                              .copyWith(
                                              bottom: MySize.getScaledSizeHeight(18)),
                                          child: toDoCard(
                                              controller: controller,
                                              index: originalIndex),
                                        );
                                      }).toList(),
                                    ),
                                  );
                                }
                                return SizedBox.shrink();
                              },
                              items: controller.todoList,
                              isLoading: controller.isLoading.value,
                              hasMoreData: controller.hasMoreData.value,
                              onLoadMore: () {
                                return controller.callApiForToDo(
                                  context: context,
                                );
                              },
                            ),
                          )
                        ],
                      ),
                    ),
            ),
          )
        ],
      ),
    );
  }

  void mainBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
      ),
      builder: (context) {
        final originalSelectedIndex = controller.selectedIndex.value;
        return WillPopScope(
          onWillPop: () async {
            controller.selectedIndex.value = originalSelectedIndex;
            return true;
          },
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: FractionallySizedBox(
              heightFactor: 0.68,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
                  color: AppTheme.subBottom,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(40),
                      topRight: Radius.circular(40)),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: MySize.getScaledSizeWidth(30)),
                  child: Obx(
                    () => Column(
                      children: [
                        Space.height(8),
                        Container(
                          height: MySize.size5,
                          width: MySize.size34,
                          decoration: BoxDecoration(
                            color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(100),
                          ),
                        ),
                        Space.height(26),
                        TypoGraphy(
                          text: "Filter by",
                          level: 12,
                        ),
                        Space.height(25),
                        ...List.generate(
                          controller.filter.length,
                          (index) {
                            return Obx(
                              () => InkWell(
                                onTap: () {
                                  controller.selectedIndex.value = index;
                                },
                                child: controller.filter[index] == null ? SizedBox() : Padding(
                                  padding: EdgeInsets.only(
                                      bottom: MySize.getScaledSizeHeight(30)),
                                  child: Row(
                                    children: [
                                      TypoGraphy(
                                        text: controller.filter[index],
                                        level: 5,
                                        color: controller.selectedIndex.value ==
                                                index
                                            ? AppTheme.primary1
                                            : null,
                                      ),
                                      Spacer(),
                                      controller.selectedIndex.value == index
                                          ? SvgPicture.asset(
                                              AppImage.checkIcon,
                                              color: AppTheme.primary1,
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      25),
                                            )
                                          : SizedBox(),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        InkWell(
                          onTap: () {
                            controller.isButtonClick.value = true;
                            Navigator.pop(context);
                            showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(40)),
                              ),
                              builder: (context) {
                                final originalPriorities = List<String>.from(
                                    controller.selectedPriorities);
                                return CommonPriorityBottomSheet(
                                  priorities: controller.priority,
                                  selectedPriorities:
                                      controller.selectedPriorities,
                                  originalPriorities: originalPriorities,
                                  onDone: () {
                                    // Optional: Handle any logic after pressing "Done"
                                  },
                                );
                              },
                            ).then(
                              (value) {
                                controller.isButtonClick.value = false;
                                mainBottomSheet();
                              },
                            );
                          },
                          child: Row(
                            children: [
                              TypoGraphy(
                                text: "Priority",
                                level: 5,
                              ),
                              Spacer(),
                              if (controller.selectedPriorities.isNotEmpty)
                                Container(
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppTheme.primary1),
                                  height: MySize.getScaledSizeHeight(30),
                                  width: MySize.getScaledSizeWidth(30),
                                  alignment: Alignment.center,
                                  child: TypoGraphy(
                                    text:
                                        "${controller.selectedPriorities.length}",
                                    level: 5,
                                    color: AppTheme.white,
                                  ),
                                ),
                              Space.width(18),
                              Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: MySize.getScaledSizeHeight(20),
                              )
                            ],
                          ),
                        ),
                        Space.height(30),
                        InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            controller.searchController.value.clear();
                            controller.hasMoreData.value = true;
                            controller.createdProject.clear();
                            controller.isButtonClick.value = true;
                            controller.callApiForProjectData(context: context);
                            showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              isScrollControlled: true,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(40)),
                              ),
                              builder: (context) {
                                final originalSelectedProjects =
                                    List<String>.from(
                                        controller.selectedProjectIds);
                                return WillPopScope(
                                  onWillPop: () async {
                                    controller.selectedProjectIds.value =
                                        originalSelectedProjects;
                                    return true;
                                  },
                                  child: filterProject(
                                      controller: controller,
                                      context: context,
                                      originalSelectedProjects:
                                          originalSelectedProjects),
                                );
                              },
                            ).then(
                              (value) {
                                controller.projectPage.value = 1;
                                controller.isButtonClick.value = false;
                                mainBottomSheet();
                              },
                            );
                          },
                          child: Row(
                            children: [
                              TypoGraphy(
                                text: "Projects",
                                level: 5,
                              ),
                              Spacer(),
                              if (controller.selectedProjectIds.isNotEmpty)
                                Container(
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppTheme.primary1),
                                  height: MySize.getScaledSizeHeight(30),
                                  width: MySize.getScaledSizeWidth(30),
                                  alignment: Alignment.center,
                                  child: TypoGraphy(
                                    text:
                                        "${controller.selectedProjectIds.length}",
                                    level: 5,
                                    color: AppTheme.white,
                                  ),
                                ),
                              Space.width(18),
                              Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: MySize.getScaledSizeHeight(20),
                              )
                            ],
                          ),
                        ),
                       /* Space.height(30),
                        InkWell(
                          onTap: () {
                            controller.isButtonClick.value = true;
                            if (controller.selectedProjectIds.isEmpty) {
                              CommonFunction.showCustomSnackbar(
                                  message: "Please Select Project First",
                                  isError: true,
                                  backgroundColor: AppTheme.red);
                            } else {
                              Navigator.pop(context);
                              controller.hasMoreData.value = true;
                              controller.searchController.value.clear();
                              controller.subProjectList.clear();
                              controller.callApiForSubProject(
                                  context: context,
                                  projectId: controller.selectedProjectIds);
                              showModalBottomSheet(
                                context: context,
                                backgroundColor: Colors.transparent,
                                isScrollControlled: true,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(40)),
                                ),
                                builder: (context) {
                                  final originalSelectedProjects =
                                      List<String>.from(
                                          controller.selectedSubProjectId);
                                  return WillPopScope(
                                      onWillPop: () async {
                                        controller.selectedSubProjectId.value =
                                            originalSelectedProjects;
                                        return true;
                                      },
                                      child: filterSubProject(
                                          context: context,
                                          controller: controller,
                                          originalSelectedProjects:
                                              originalSelectedProjects));
                                },
                              ).then(
                                (value) {
                                  controller.page.value = 1;
                                  controller.isButtonClick.value = false;
                                  mainBottomSheet();
                                },
                              );
                            }
                          },
                          child: Row(
                            children: [
                              TypoGraphy(
                                text: "Sub-Projects",
                                level: 5,
                              ),
                              Spacer(),
                              if (controller.selectedSubProjectId.isNotEmpty)
                                Container(
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppTheme.primary1),
                                  height: MySize.getScaledSizeHeight(30),
                                  width: MySize.getScaledSizeWidth(30),
                                  alignment: Alignment.center,
                                  child: TypoGraphy(
                                    text:
                                        "${controller.selectedSubProjectId.length}",
                                    level: 5,
                                    color: AppTheme.white,
                                  ),
                                ),
                              Space.width(18),
                              Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: MySize.getScaledSizeHeight(20),
                              )
                            ],
                          ),
                        ),*/
                        Space.height(40),
                        Obx(
                          () => Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Buttons(
                                buttonText: "Apply",
                                buttonTextLevel: 4,
                                isLoading: controller.isLoading.value,
                                height: MySize.getScaledSizeHeight(70),
                                width: MySize.getScaledSizeWidth(156),
                                onTap: () {
                                  HapticFeedback.lightImpact();
                                  if (controller.selectedPriorities.isEmpty &&
                                      controller.selectedSubProjectId.isEmpty &&
                                      controller.selectedProjectIds.isEmpty && controller.selectedIndex.value == -1) {
                                    controller.isFilterApply.value = false;
                                  } else {
                                    controller.isFilterApply.value = true;
                                    print("filter value -==> ${controller.isFilterApply.value}");
                                  }
                                  Navigator.pop(context);
                                  controller.page.value = 1;
                                  controller.hasMoreData.value = true;
                                  controller.todoList.clear();
                                  controller.callApiForToDo(context: context);
                                },
                              ),
                            ],
                          ),
                        ),
                        Space.height(30),
                        InkWell(
                          onTap: () {
                            HapticFeedback.lightImpact();
                            Navigator.pop(context);
                            controller.selectedProjectIds.clear();
                            controller.selectedPriorities.clear();
                            controller.selectedSubProjectId.clear();
                            controller.selectedIndex.value = -1;
                            controller.isFilterApply.value = false;
                            controller.page.value = 1;
                            controller.hasMoreData.value = true;
                            controller.todoList.clear();
                            controller.callApiForToDo(context: context);
                          },
                          child: TypoGraphy(
                            text: "Clear All",
                            level: 4,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    ).then(
      (value) {
        if (controller.isFilterApply.value == false &&
            controller.isButtonClick.value == false) {
          controller.selectedPriorities.clear();
          controller.selectedSubProjectId.clear();
          controller.selectedProjectIds.clear();
        }
      },
    );
  }
}
