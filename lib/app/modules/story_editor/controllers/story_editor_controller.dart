
import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit_config.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/bottom_bar/controllers/bottom_bar_controller.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import 'package:incenti_ai/services/api_manager.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import '../../../../constants/api.dart';
import '../../../../constants/app_argument_key.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../constants/config.dart';
import '../../../../models/app_project_model.dart';
import '../../../../models/story_model.dart';
import '../../../../utillites/current_user.dart';
import '../../video_trimmer/view/utils/trimmer_utils.dart';
import 'package:dio/dio.dart' as dio;

class StoryEditorController extends GetxController {
  var argument = Get.arguments;
  File? pickedFile;
  RxBool isChange = false.obs;
  RxBool showIndicator = false.obs;
  var text = ''.obs;
  var projectId = 0.obs;
  RxString slug = ''.obs;
  var subProjectId = 0.obs;
  var textList = <String>[].obs;
  var textColor = 0.obs;
  var textSize = 45.0.obs;
  var fontFamilyIndex = 0.obs;
  var textAlign = TextAlign.center.obs;
  var backGroundColor = Colors.transparent.obs;
  var isFontFamily = false.obs;
  GlobalKey textKey = GlobalKey();
  GlobalKey mentionKey = GlobalKey();

  var fontFamilyController = PageController(viewportFraction: .125).obs;
  var textAnimationController = PageController(viewportFraction: .125).obs;
  var textController = TextEditingController().obs;
  RxBool isReadOnly = false.obs;

  var currentColorBackground = 0.obs;
  var textColorBackGround = [Colors.transparent, Colors.black, Colors.white];

  var currentAlign = 0.obs;
  var texAlignment = [TextAlign.center, TextAlign.right, TextAlign.left];

  var currentAnimation = 0.obs;

  var gradientIndex = 0.obs;
  var isTextEditing = false.obs;
  var fontList = [
    'Roboto',
    'Lato',
    'Open Sans',
    'Montserrat',
    'Poppins',
    'Raleway',
    'Ubuntu',
    'Playfair Display',
    'Quicksand',
    'Dancing Script',
    'Alegreya',
    'B612',
    'Varela',
    'Vollkorn',
    'Rakkas',
    'Neonderthaw',
    'Sacramento',
    'UnifrakturMaguntia'
  ].obs;

  var isCustomFontList = false.obs;
  var gradientColors =  [
    [
      const Color.fromRGBO(31, 179, 237, 1),
      const Color.fromRGBO(17, 106, 197, 1)
    ],
    [
      const Color.fromRGBO(240, 19, 77, 1),
      const Color.fromRGBO(228, 0, 124, 1)
    ],
    [
      const Color.fromRGBO(255, 190, 32, 1),
      const Color.fromRGBO(251, 112, 71, 1)
    ],
    [
      const Color.fromRGBO(255, 255, 255, 1),
      const Color.fromRGBO(234, 236, 255, 1)
    ],
    [HexColor.fromHex('#2AA8F2'), HexColor.fromHex('#A800FF')],
    [HexColor.fromHex('#A800FF'), HexColor.fromHex('#2AA8F2')],
    [HexColor.fromHex('#A800FF'), HexColor.fromHex('#FF0900')],
    [
      HexColor.fromHex('#33CD11'),
      HexColor.fromHex('#33CD75'),
      HexColor.fromHex('#0099FF')
    ],
    [HexColor.fromHex('#FF0043'), HexColor.fromHex('#FFAA20')],
    [HexColor.fromHex('#9911AA'), HexColor.fromHex('#FF0013')],
    [HexColor.fromHex('#323232'), HexColor.fromHex('#354047')],
    [HexColor.fromHex('#A9AAAE'), HexColor.fromHex('#C8CACA')],
    [
      HexColor.fromHex('#FF0033'),
      HexColor.fromHex('#FF8800'),
      HexColor.fromHex('#FFAA00'),
      HexColor.fromHex('#009508'),
      HexColor.fromHex('#0078FF'),
      HexColor.fromHex('#8001AA')
    ],
    [const Color(0xFFee9ca7), const Color(0xFFffdde1)],
    [const Color(0xFF2193b0), const Color(0xFF6dd5ed)],
    [const Color(0xFFb92b27), const Color(0xFF1565C0)],
    [const Color(0xFF373B44), const Color(0xFF4286f4)],
    [const Color(0xFFbdc3c7), const Color(0xFF2c3e50)],
    [const Color(0xFF00416A), const Color(0xFFE4E5E6)],
    [const Color(0xFFFFE000), const Color(0xFF799F0C)],
    [const Color(0xFF4364F7), const Color(0xFF6FB1FC)],
    [const Color(0xFF799F0C), const Color(0xFFACBB78)],
    [const Color(0xFFffe259), const Color(0xFFffa751)],
    [const Color(0xFF536976), const Color(0xFF292E49)],
    [const Color(0xFF1488CC), const Color(0xFF2B32B2)],
    [const Color(0xFFec008c), const Color(0xFFfc6767)],
    [const Color(0xFFcc2b5e), const Color(0xFF753a88)],
    [const Color(0xFF2193b0), const Color(0xFF6dd5ed)],
    [const Color(0xFF2b5876), const Color(0xFF4e4376)],
    [const Color(0xFFff6e7f), const Color(0xFFbfe9ff)],
    [const Color(0xFFe52d27), const Color(0xFFb31217)],
    [const Color(0xFF603813), const Color(0xFFb29f94)],
    [const Color(0xFF16A085), const Color(0xFFF4D03F)],
    [const Color(0xFFD31027), const Color(0xFFEA384D)],
    [const Color(0xFFEDE574), const Color(0xFFE1F5C4)],
    [const Color(0xFF02AAB0), const Color(0xFF00CDAC)],
    [const Color(0xFFDA22FF), const Color(0xFF9733EE)],
    [const Color(0xFF348F50), const Color(0xFF56B4D3)],
    [const Color(0xFFF09819), const Color(0xFFEDDE5D)],
    [const Color(0xFFFF512F), const Color(0xFFDD2476)],
    [const Color(0xFF1A2980), const Color(0xFF26D0CE)],
    [const Color(0xFFFF512F), const Color(0xFFF09819)],
    [const Color(0xFFEB3349), const Color(0xFFF45C43)],
    [const Color(0xFF1D976C), const Color(0xFF93F9B9)],
    [const Color(0xFFFF8008), const Color(0xFFFFC837)],
    [const Color(0xFF16222A), const Color(0xFF3A6073)],
    [const Color(0xFF4776E6), const Color(0xFF8E54E9)],
    [const Color(0xFF232526), const Color(0xFF414345)],
    [const Color(0xFF00c6ff), const Color(0xFF0072ff)],
    [const Color(0xFFe6dada), const Color(0xFF274046)],
    [const Color(0xFFece9e6), const Color(0xFFffffff)],
    [const Color(0xFF11998e), const Color(0xFF38ef7d)],
    [const Color(0xFFff9a9e), const Color(0xFFfad0c4)],
    [const Color(0xFFa18cd1), const Color(0xFFfbc2eb)],
    [const Color(0xFFfad0c4), const Color(0xFFffd1ff)],
    [const Color(0xFFffecd2), const Color(0xFFfcb69f)],
    [const Color(0xFFff9a9e), const Color(0xFFfecfef)],
    [const Color(0xFFf6d365), const Color(0xFFfda085)],
    [const Color(0xFFfbc2eb), const Color(0xFFa6c1ee)],
    [const Color(0xFFfdcbf1), const Color(0xFFe6dee9)],
    [const Color(0xFFa1c4fd), const Color(0xFFc2e9fb)],
    [const Color(0xFFd4fc79), const Color(0xFF96e6a1)],
  ].obs;
  var middleBottomWidget = Rx<Widget?>(null);
  var exitDialogWidget = Rx<Future<bool>?>(null);
  var colorList = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.pink,
    Colors.purple,
    Colors.deepPurple,
    Colors.indigo,
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
    Colors.teal,
    Colors.green,
    Colors.lightGreen,
    Colors.lime,
    Colors.yellow,
    Colors.amber,
    Colors.orange,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
    Colors.blueGrey,
  ].obs;
  var mediaPath = ''.obs;
  var isPhotoFilter = false.obs;
  RxList<EditableItem> draggableWidget = <EditableItem>[].obs;
  RxList<EditableItem> draggableMentionedWidget = <EditableItem>[].obs;
  List<String> splitList = [];
  String sequenceList = '';
  String lastSequenceList = '';
  Rx<Color> color1 = Color(0xff000000).obs;
  Rx<Color> color2 = Color(0xff000000).obs;

  var initPos = Offset.zero.obs;
  var currentPos = Offset.zero.obs;
  var currentScale = 1.0.obs;
  var currentRotation = 0.0.obs;
  var inAction = false.obs;
  var activeItem = Rxn<EditableItem>();
  var isDeletePosition = false.obs;
  RxBool isVideo = false.obs;
  RxBool isText = false.obs;
  late VideoPlayerController videoPlayerController;
  String? _compressedVideoPath;
  RxList<Project> userProject = <Project>[].obs;
  RxList<SubProjectData> subProjectList = <SubProjectData>[].obs;
  FocusNode textNode = FocusNode();
  RxBool isCompressing = false.obs;
  RxString buttonText = 'Share'.obs;
  RxBool isInitializingVideo = false.obs;


  @override
  Future<void> onInit() async {
    if (argument != null) {
      if(argument[AppArguments.file] != null)pickedFile = argument[AppArguments.file];
      mediaPath.value = pickedFile?.path ?? '';
      isVideo.value = argument[AppArguments.isVideo] ?? false;
      isText.value = argument['isText'] ?? false;
      if(pickedFile != null && !isVideo.value)draggableWidget.add(EditableItem(type: StoryType.image));
      if (isVideo.value && pickedFile != null) {
        _compressVideoInBackground(pickedFile!.path);
        videoPlayerController = VideoPlayerController.file(pickedFile!)
          ..initialize().then((_) {
            log('videoPlayerController.value.aspectRatio ===> ${videoPlayerController.value.aspectRatio}');
            videoPlayerController.play();
            videoPlayerController.setLooping(true);
            isInitializingVideo.value = true;
          });
      }
    }
    await callApiForGetAllProjectOfUser(context: Get.context!);
    super.onInit();
  }


  void onBackGroundChange() {
    if (currentColorBackground.value < textColorBackGround.length - 1) {
      currentColorBackground.value += 1;
    } else {
      currentColorBackground.value = 0;
    }
    backGroundColor.value = textColorBackGround[currentColorBackground.value];
  }

  void onAlignmentChange() {
    if (currentAlign.value < texAlignment.length - 1) {
      currentAlign.value += 1;
    } else {
      currentAlign.value = 0;
    }
    textAlign.value = texAlignment[currentAlign.value];
  }

  void setDefaults() {
    text.value = '';
    textController.value.text = '';
    textColor.value = 0;
    textSize.value = 45.0;
    fontFamilyIndex.value = 0;
    textAlign.value = TextAlign.center;
    backGroundColor.value = Colors.transparent;
    fontFamilyController.value = PageController(viewportFraction: .125);
    textAnimationController.value = PageController(viewportFraction: .125);
    isFontFamily.value = true;
    textList.value = [];
    projectId.value = 0;
    slug.value = '';
    subProjectId.value = 0;
    isReadOnly.value = false;
  }

  void disposeController() {
    if (isVideo.value) {
      videoPlayerController.pause();
      videoPlayerController.dispose();
    }
    textController.value.dispose();
    fontFamilyController.value.dispose();
    textAnimationController.value.dispose();
  }

  void onScaleStart(ScaleStartDetails details) {
    if (activeItem.value == null) {
      return;
    }
    initPos.value = details.focalPoint;
    currentPos.value = activeItem.value!.position;
    currentScale.value = activeItem.value!.scale;
    currentRotation.value = activeItem.value!.rotation;
  }

  void onScaleUpdate(ScaleUpdateDetails details,RxList<EditableItem> draggableWidget) {
    if (activeItem.value == null) {
      return;
    }
    final delta = details.focalPoint - initPos.value;
    final index = draggableWidget.indexOf(activeItem.value!);
    if (index != -1) {
      final left = (delta.dx / MySize.screenWidth) + currentPos.value.dx;
      final top = (delta.dy / MySize.screenHeight) + currentPos.value.dy;

      draggableWidget[index].position = Offset(left, top);
      draggableWidget[index].rotation = details.rotation + currentRotation.value;
      draggableWidget[index].scale = details.scale * currentScale.value;
      activeItem.value = draggableWidget[index];
      draggableWidget.refresh();
    }
  }

  void deletePosition(EditableItem item, PointerMoveEvent details) {
    if (item.type == StoryType.text &&
        item.position.dy >= 0.3 &&
        item.position.dx >= -0.2 &&
        item.position.dx <= 0.2) {
      isDeletePosition.value = true;
      item.deletePosition = true;
    } else {
      isDeletePosition.value = false;
      item.deletePosition = false;
    }
  }

  void deleteItemOnCoordinates(EditableItem item, PointerUpEvent details,RxList<EditableItem> draggableWidget) {
    inAction.value = false;
    if (item.type == StoryType.text &&
        item.position.dy >= 0.3 &&
        item.position.dx >= -0.2 &&
        item.position.dx <= 0.2) {
      draggableWidget.removeAt(draggableWidget.indexOf(item));
      HapticFeedback.heavyImpact();
    } else {
      activeItem.value = null;
    }
    activeItem.value = null;
  }

  void updateItemPosition(EditableItem item, PointerDownEvent details) {
    if (inAction.value) {
      return;
    }
    inAction.value = true;
    activeItem.value = item;
    initPos.value = details.position;
    currentPos.value = item.position;
    currentScale.value = item.scale;
    currentRotation.value = item.rotation;
    HapticFeedback.lightImpact();
  }

  void onDoneTap(List<EditableItem> draggableWidget) {
    if (text.trim().isNotEmpty) {
      draggableWidget.add(EditableItem(
          type: StoryType.text,
          text: text.trim(),
          projectId: projectId.value,
          slug: slug.value,
          subProjectId: subProjectId.value,
          backGroundColor: backGroundColor.value,
          textColor: colorList[textColor.value],
          fontFamily: fontFamilyIndex.value,
          fontSize: textSize.value,
          textAlign: textAlign.value,
          position: Offset(0, 0)));

      textList.clear();
      setDefaults();
      isTextEditing.value = !isTextEditing.value;
    } else {
      setDefaults();
      isTextEditing.value = !isTextEditing.value;
    }
  }

  void onTextEditTap(BuildContext context, EditableItem item,
  List<EditableItem> draggableWidgets) {

   textController.value.text = item.text.trim();
   text.value = item.text.trim();
   projectId.value = item.projectId;
   slug.value = item.slug;
   subProjectId.value = item.subProjectId;
   fontFamilyIndex.value = item.fontFamily;
   textSize.value = item.fontSize;
   backGroundColor.value = item.backGroundColor;
   textAlign.value = item.textAlign;
   textColor.value =
        colorList.indexOf(item.textColor);
    draggableWidgets
        .removeAt(draggableWidgets.indexOf(item));
    fontFamilyController.value = PageController(
      initialPage: item.fontFamily,
      viewportFraction: .1,

    );

    if(item.text.startsWith('@')) isReadOnly.value = true;
    isTextEditing.value = !isTextEditing.value;
  }

  Future<void> callApiForGetAllProjectOfUser({required BuildContext context}) async {
    ApiManager().callApi(
      APIS.project.getAllMyProject,
      params: {
        "UserId": CurrentUser.user.id,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse = ProjectResponse.fromJson(response);
            userProject.value = projectResponse.data.data;
            userProject.value = userProject
                .where((project) =>
            (project.projectMembers.isEmpty ||
                project.projectMembers.any((member) =>
                member.access != "read")) && !project.isPrivate)
                .toList();
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  void callApiForGetAllSubProject({required BuildContext context, required int projectId}) {
    ApiManager().callApi(
      APIS.project.getAllMyProject,
      params: {
        "UserId": CurrentUser.user.id,
        "ParentId" : projectId.toString(),
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            subProjectList.value = List<SubProjectData>.from(response["data"]["data"].map((x) => SubProjectData.fromJson(x)));
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  Future<void> _compressVideoInBackground(String videoPath) async {
    if (isCompressing.value) return;
    isCompressing.value = true;
    try {

      final directory = await getTemporaryDirectory();
      final compressedPath = '${directory.path}/${DateTime.now().millisecondsSinceEpoch}_compressed.mp4';
      await FFmpegKitConfig.init();
      final session = await FFmpegKit.execute(
          "-i $videoPath -vcodec mpeg4 -q:v 3 -b:v 1500k -acodec aac -b:a 128k $compressedPath"
      );

      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        _compressedVideoPath = compressedPath;
        isCompressing.value = false;
      } else {
      }
    } catch (e) {
    } finally {
      isCompressing.value = false;
    }
  }

  Future<void> shareStory({
    required BuildContext context,
  }) async {
    CommonFunction.showCustomSnackbar(
      message: 'Story upload started...',
    );

    List<OneStory> existingStories = [];



    if (isVideo.value && pickedFile != null) {
      String? overlayImagePath;
      String? overlayImagePath1;
      if (textKey.currentContext != null) {
        RenderRepaintBoundary boundary =
        textKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
        ui.Image image = await boundary.toImage(pixelRatio: 3.0);
        ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List pngBytes = byteData!.buffer.asUint8List();
        final directory = await getTemporaryDirectory();
        File overlayFile = File('${directory.path}/${DateTime.now().millisecondsSinceEpoch}_captured_image.png');
        await overlayFile.writeAsBytes(pngBytes);
        overlayImagePath = overlayFile.path;
      }

      if (mentionKey.currentContext != null) {
        RenderRepaintBoundary boundary =
        mentionKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
        ui.Image image = await boundary.toImage(pixelRatio: 3.0);
        ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List pngBytes = byteData!.buffer.asUint8List();
        final directory = await getTemporaryDirectory();
        File overlayFile = File('${directory.path}/${DateTime.now().millisecondsSinceEpoch}_captured_image.png');
        await overlayFile.writeAsBytes(pngBytes);
        overlayImagePath1 = overlayFile.path;
      }

      bool isCurrentUserStoryMissing = !Get.find<ExploreController>().storyDataList.any((story) =>
      story.id == CurrentUser.user.id
      );
      if (isCurrentUserStoryMissing) {
        Story currentUserStory = Story(
            id: CurrentUser.user.id,
            firstName: CurrentUser.user.firstName,
            lastName: CurrentUser.user.lastName,
            image: CurrentUser.user.image,
            stories: []
        );
        Get.find<ExploreController>().storyDataList.insert(0, currentUserStory);
      }

      (Get.find<ExploreController>().storyDataList[0].stories ?? []).add(OneStory(
        userId: CurrentUser.user.id,
        mediaLink: _compressedVideoPath ?? pickedFile!.path,
        overlayImage: overlayImagePath ?? '',
        mentionedPath: overlayImagePath1 ?? '',
        properties: draggableMentionedWidget,
        seen: false,
        mediaType: StoryType.video.name,
        createdAt: DateTime.now(),
        thumbnailPath: firstThumbnailPath ?? '',
        showUploading: true,
        duration: videoPlayerController.value.duration.inSeconds,
      ));
      Get.find<ExploreController>().storyDataList.refresh();
      log('here called ===> 6 ${(Get.find<ExploreController>().storyDataList[0].stories ?? []).length}');

      await StoryBackgroundUploadService.uploadVideoStory(
        videoPath: _compressedVideoPath ?? pickedFile!.path,
        overlayImagePath: overlayImagePath,
        properties: jsonEncode(draggableMentionedWidget.map((e) => e.toJson()).toList()),
        thumbNailImage: firstThumbnailPath ?? '',
        duration: videoPlayerController.value.duration.inSeconds,
      );

      existingStories.add(OneStory(
        mediaLink: _compressedVideoPath ?? pickedFile!.path,
        overlayImage: overlayImagePath ?? '',
        mentionedPath: overlayImagePath1 ?? '',
        properties: draggableMentionedWidget,
        seen: false,
        mediaType: StoryType.video.name,
        createdAt: DateTime.now(),
        thumbnailPath: firstThumbnailPath ?? '',
      ));


    } else {
      List<String> imagePathList = [];
      String? overlayImagePath;
      String? overlayImagePath1;

      if (textKey.currentContext != null) {
        RenderRepaintBoundary boundary =
        textKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
        ui.Image image = await boundary.toImage(pixelRatio: 3.0);
        ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List pngBytes = byteData!.buffer.asUint8List();
        final directory = await getTemporaryDirectory();
        File overlayFile = File('${directory.path}/${DateTime.now().millisecondsSinceEpoch}_captured_image.png');
        await overlayFile.writeAsBytes(pngBytes);
        overlayImagePath = overlayFile.path;
        imagePathList.add(overlayImagePath);
      }

      if (mentionKey.currentContext != null) {
        RenderRepaintBoundary boundary =
        mentionKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
        ui.Image image = await boundary.toImage(pixelRatio: 3.0);
        ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List pngBytes = byteData!.buffer.asUint8List();
        final directory = await getTemporaryDirectory();
        File overlayFile = File('${directory.path}/${DateTime.now().millisecondsSinceEpoch}_captured_image.png');
        await overlayFile.writeAsBytes(pngBytes);
        overlayImagePath1 = overlayFile.path;
      }

      bool isCurrentUserStoryMissing = !Get.find<ExploreController>().storyDataList.any((story) =>
      story.id == CurrentUser.user.id
      );
      if (isCurrentUserStoryMissing) {
        Story currentUserStory = Story(
            id: CurrentUser.user.id,
            firstName: CurrentUser.user.firstName,
            lastName: CurrentUser.user.lastName,
            image: CurrentUser.user.image,
            stories: []
        );
        Get.find<ExploreController>().storyDataList.insert(0, currentUserStory);
      }

      (Get.find<ExploreController>().storyDataList[0].stories ?? []).add(OneStory(
        userId: CurrentUser.user.id,
        mediaLink:  imagePathList.isNotEmpty ? imagePathList.last : '',
        overlayImage: overlayImagePath ?? '',
        mentionedPath: overlayImagePath1 ?? '',
        properties: draggableMentionedWidget,
        seen: false,
        mediaType: argument['isText'] != null ? StoryType.text.name : StoryType.image.name,
        createdAt: DateTime.now(),
        thumbnailPath: (argument['isText'] != null ? overlayImagePath : imagePathList.isNotEmpty ? imagePathList.last : '') ?? '',
        showUploading: true,
      ));
      Get.find<ExploreController>().storyDataList.refresh();
      log('here called ===> 71 ${(Get.find<ExploreController>().storyDataList[0].stories ?? []).length}');
      log('here called ===> 72 ${(Get.find<ExploreController>().storyDataList[0].stories?.last.toJson() ?? [])}');

      await StoryBackgroundUploadService.uploadImageTextStory(
        imagePaths: imagePathList,
        properties: jsonEncode(draggableMentionedWidget.map((e) => e.toJson()).toList()),
        mediaType: argument['isText'] != null ? StoryType.text.name : StoryType.image.name,
      );

      existingStories.add(OneStory(
        mediaLink: imagePathList.isNotEmpty ? imagePathList.last : '',
        overlayImage: overlayImagePath ?? '',
        mentionedPath: overlayImagePath1 ?? '',
        properties: draggableMentionedWidget,
        seen: false,
        mediaType: argument['isText'] != null ? StoryType.text.name : StoryType.image.name,
        createdAt: DateTime.now(),
        thumbnailPath: (argument['isText'] != null ? overlayImagePath : imagePathList.isNotEmpty ? imagePathList.last : '') ?? '',
      ));

    }

    Navigator.of(context).pop();
    Get.find<BottomBarController>().currentIndex.value = 0;
  }

  void shareOnTap({required BuildContext context}) async {
    if (isCompressing.value) {
      buttonText.value = 'Preparing a Story';
      ever(isCompressing, (bool value) {
        if (!value) {
          HapticFeedback.lightImpact();
          Get.find<ExploreController>().startUploadStatusCheck();
          shareStory(context: context);
        }
      });
    } else {
      HapticFeedback.lightImpact();
      Get.find<ExploreController>().startUploadStatusCheck();
      await shareStory(context: context);
    }
  }

  @override
  void dispose() {
    disposeController();
    super.dispose();
  }

  @override
  void onClose() {
    disposeController();
    super.onClose();
  }

}

extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
      '${alpha.toRadixString(16).padLeft(2, '0')}'
      '${red.toRadixString(16).padLeft(2, '0')}'
      '${green.toRadixString(16).padLeft(2, '0')}'
      '${blue.toRadixString(16).padLeft(2, '0')}';
}

/*class StoryBackgroundUploadService{
  static final FlutterLocalNotificationsPlugin notificationsPlugin = FlutterLocalNotificationsPlugin();
  static final FlutterBackgroundService backgroundService = FlutterBackgroundService();
  static const String notificationChannelId = 'story_upload_channel';
  static const String notificationChannelName = 'Story Upload Notifications';
  static final int notificationId = 77777;
  static const String serviceId = 'story_upload_service';

  static Future<void> initialize() async {
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );
    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    await notificationsPlugin.initialize(initSettings);

    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      notificationChannelId,
      notificationChannelName,
      importance: Importance.high,
    );
    await notificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    await backgroundService.configure(
      androidConfiguration: AndroidConfiguration(
        foregroundServiceTypes: [AndroidForegroundType.dataSync],
        onStart: onStart,
        autoStart: false,
        autoStartOnBoot: false,
        isForegroundMode: false,
        notificationChannelId: notificationChannelId,
        initialNotificationTitle: 'Story Upload',
        initialNotificationContent: 'Preparing to upload',
        foregroundServiceNotificationId: notificationId,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
    );
  }

  static Future<bool> onIosBackground(ServiceInstance service) async {
    return true;
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    ui.DartPluginRegistrant.ensureInitialized();

    if (service is AndroidServiceInstance) {
      service.setAsForegroundService();
    }

    service.on('uploadVideo').listen((event) async {
      if (event == null) return;

      final videoPath = event['videoPath'] as String;
      final overlayImagePath = event['overlayImagePath'] as String?;
      final properties = event['properties'] as String?;
      final thumbnailPath = event['thumbNailImage'] as String?;
      final duration = event['duration'] as String?;

      await handleVideoUpload(service, videoPath, overlayImagePath, thumbnailPath,duration,properties);
    });

    service.on('uploadImageAndTextStory').listen((event) async {
      if (event == null) return;

      final List<String> imagePaths = (event['imagePaths'] as List).cast<String>();
      final properties = event['properties'] as String?;
      final mediaType = event['mediaType'] as String?;

      await handleImageStoryUpload(service, imagePaths, properties, mediaType);
    });
  }

  static Future<void> handleVideoUpload(
      ServiceInstance service,
      String videoPath,
      String? overlayImagePath,
      String? thumbNailImagePath,
      String? duration,
      String? properties
      ) async {
    try {
      updateNotification(service, 'Preparing video upload...', 10);

      updateNotification(service, 'Uploading video...', 20);

      List<String> overlayImageUrls = [];
      bool isOnlyThumbnail = overlayImagePath == null && thumbNailImagePath != null;

      if (overlayImagePath != null || thumbNailImagePath != null) {
        try {
          List<String> uploadPaths = [];

          if (overlayImagePath != null) {
            uploadPaths.add(overlayImagePath);
          }
          if (thumbNailImagePath != null) {
            uploadPaths.add(thumbNailImagePath);
          }

          overlayImageUrls = await uploadMedia(uploadPaths, progressCallback: (progress) {
            updateNotification(service, 'Uploading video...', 20 + (progress * 30).round());
          });
        } catch (e) {
        }
      }

      String? overlayUrl;
      String? thumbnailUrl;

      if (isOnlyThumbnail && overlayImageUrls.isNotEmpty) {
        thumbnailUrl = overlayImageUrls[0];
      } else {
        overlayUrl = overlayImageUrls.isNotEmpty ? overlayImageUrls[0] : null;
        thumbnailUrl = overlayImageUrls.length > 1 ? overlayImageUrls[1] : null;
      }

      await uploadVideo(
        videoPath,
        overlayUrl,
        duration,
        thumbnailUrl,
        properties,
        progressCallback: (progress) {
          updateNotification(service, 'Uploading video...', 50 + (progress * 45).round());
        },
      );

      updateNotification(service, 'Upload complete!', 100);
      await Future.delayed(const Duration(seconds: 2));
      service.stopSelf();
      await notificationsPlugin.cancel(notificationId);
    } catch (e) {
      updateNotification(service, 'Upload failed: $e', 0);
      await Future.delayed(const Duration(seconds: 3));
      service.stopSelf();
      await notificationsPlugin.cancel(notificationId);
    }
  }

  static Future<void> handleImageStoryUpload(
      ServiceInstance service,
      List<String> imagePaths,
      String? properties,
      String? mediaType
      ) async {
    try {

      updateNotification(service, 'Uploading story...', 10);

      final List<String> imageUrls = await uploadMedia(imagePaths,
          progressCallback: (progress) {
            updateNotification(service, 'Uploading media...', 10 + (progress * 70).round());
          }
      );

      await postImageTextStory(imageUrls, properties, mediaType,
          progressCallback: (progress) {
            updateNotification(service, 'Upload complete!', 80 + (progress * 20).round());
          }
      );

      updateNotification(service, 'Upload complete!', 100);
      await Future.delayed(const Duration(seconds: 2));
      service.stopSelf();
      await notificationsPlugin.cancel(notificationId);
    } catch (e) {
      updateNotification(service, 'Upload failed: $e', 0);
      await Future.delayed(const Duration(seconds: 3));
      service.stopSelf();
      await notificationsPlugin.cancel(notificationId);
    }
  }

  static void updateNotification(ServiceInstance service, String message, int progress) {
    if(Platform.isAndroid || progress==100) {
      notificationsPlugin.show(
        notificationId,
        'Story Upload',
        '$message ($progress%)',
        NotificationDetails(
          android: AndroidNotificationDetails(
            notificationChannelId,
            notificationChannelName,
            channelShowBadge: false,
            importance: Importance.high,
            priority: Priority.high,
            onlyAlertOnce: true,
            silent: true,
            playSound: false,
            showProgress: true,
            maxProgress: 100,
            progress: progress,
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: false,
            presentSound: false,
          ),
        ),
      );
    }
  }

  static Future<List<String>> uploadMedia(
      List<String> filePaths,
      {Function(double progress)? progressCallback}
      ) async {
    final completer = Completer<List<String>>();
    try {
      List<dio.MultipartFile> multipartFiles = [];

      for (String path in filePaths) {
        multipartFiles.add(
          await dio.MultipartFile.fromFile(
            path,
            filename: path.split('/').last.trim(),
          ),
        );
      }
      dio.FormData formData = dio.FormData.fromMap({
        "file": multipartFiles,
      });
      log('dio form data ===> ${formData}');
      final dioInstance = dio.Dio();
      dioInstance.post(
        CONFIG().baseUrl+APIS.generalImageUpload.imageUpload.endpoint,
        data: formData,
        onSendProgress: (sent, total) {
          if (progressCallback != null && total > 0) {
            final progress = sent / total;
            progressCallback(progress);
          }
        },
        options: dio.Options(
          headers:  await ApiManager().getAuthHeaders(),
        ),
      ).then((response) {
        if (response.data['status'] == 'success') {
          List<String> uploadedUrls = [];
          List<dynamic> uploadedImages = response.data['data'];

          for (var imageData in uploadedImages) {
            String imageUrl = imageData['link'];
            uploadedUrls.add(imageUrl);
          }
          log('uploaded url ==> $uploadedUrls');
          completer.complete(uploadedUrls);
        } else {
          completer.completeError("API returned non-success status");
        }
      }).catchError((error) {
        completer.completeError("API Error: $error");
        log('uploaded url ==> $error');
      });

      return completer.future;
    } catch (e) {
      completer.completeError(e);
      return completer.future;
    }
  }

  static Future<void> uploadVideo(
      String videoPath,
      String? overlayImageUrl,
      String? duration,
      String? thumbNailImage,
      String? properties,
      {Function(double progress)? progressCallback}
      ) async {
    final completer = Completer<void>();

    try {

      List<dio.MultipartFile> multipartFiles = [];

      multipartFiles.add(
        await dio.MultipartFile.fromFile(
          videoPath,
          filename: videoPath.split('/').last.trim(),
        ),
      );

      dio.FormData formData = dio.FormData.fromMap({
        "file": multipartFiles,
        "overlayImage": overlayImageUrl ?? "",
        "properties": properties ?? "[]",
        "duration": duration ?? "0",
        "thumbNailImage": thumbNailImage ?? "",
      });


      final dioInstance = dio.Dio();

      var header = await ApiManager().getAuthHeaders();

      dioInstance.post(
        CONFIG().baseUrl +  APIS.story.postVideoStory.endpoint,
        data: formData,
        onSendProgress: (sent, total) {
          if (progressCallback != null && total > 0) {
            final progress = sent / total;
            progressCallback(progress);
          }
        },
        options: dio.Options(
          headers: await ApiManager().getAuthHeaders(),
        ),
      ).then((response) {
        completer.complete();
      }).catchError((error) {
        completer.completeError("API Error: $error");
      });

      return completer.future;
    } catch (e) {
      completer.completeError(e);
      return completer.future;
    }
  }

  static Future<void> postImageTextStory(
      List<String> imageUrls,
      String? properties,
      String? mediaType,
      {Function(double progress)? progressCallback}
      ) async {
    final completer = Completer<void>();

    try {
      var dict = {};

      if (imageUrls.isNotEmpty) {
        dict['overlayImage'] = imageUrls[0];
        if (imageUrls.length > 1) {
          dict['mediaLink'] = imageUrls[1];
        }
      }

      dict['mediaType'] = mediaType ?? StoryType.text.name;
      dict['properties'] = properties ?? "[]";

      log('dict is ==> ${dict}');

      final dioInstance = dio.Dio();

      if (progressCallback != null) {
        progressCallback(0.5);
      }

      dioInstance.post(
        CONFIG().baseUrl+APIS.story.postStory.endpoint,
        data: dict,
        options: dio.Options(
          headers: await ApiManager().getAuthHeaders(),
        ),
      ).then((response) {
        if (progressCallback != null) {
          progressCallback(1.0);
        }
        log('api response ===> $response');
        completer.complete();
      }).catchError((error) {
        completer.completeError("API Error: $error");
        log('api error ===> ${error}');
      });

      return completer.future;
    } catch (e) {
      completer.completeError(e);
      log('api error ===> 2 ${e}');
      return completer.future;
    }
  }

  static Future<void> uploadVideoStory({
    required String videoPath,
    String? overlayImagePath,
    String? properties,
    String? thumbNailImage,
    int? duration,
  }) async {
    await backgroundService.startService();

    backgroundService.invoke('uploadVideo', {
      'videoPath': videoPath,
      'overlayImagePath': overlayImagePath,
      'properties': properties,
      'thumbNailImage': thumbNailImage,
      'duration': duration.toString(),
    });

    return;
  }

  static Future<void> uploadImageTextStory({
    required List<String> imagePaths,
    String? properties,
    String? mediaType,
  }) async {
    await backgroundService.startService();

    backgroundService.invoke('uploadImageAndTextStory', {
      'imagePaths': imagePaths,
      'properties': properties,
      'mediaType': mediaType,
    });
    return;
  }
}*/

class StoryBackgroundUploadService{
  static final AwesomeNotifications awesomeNotifications = AwesomeNotifications();
  static final FlutterBackgroundService backgroundService = FlutterBackgroundService();
  static const String notificationChannelId = 'story_upload_channel';
  static const String notificationChannelName = 'Story Upload Notifications';
  static final int notificationId = 77777;
  static const String serviceId = 'story_upload_service';

  static Future<void> initialize() async {
    await awesomeNotifications.initialize(
      'resource://mipmap/ic_launcher',
      [
        NotificationChannel(
          channelKey: notificationChannelId,
          channelName: notificationChannelName,
          channelDescription: 'Notifications for story upload progress',
          defaultColor: const Color(0xFF9D50DD),
          ledColor: Colors.white,
          importance: NotificationImportance.High,
          channelShowBadge: false,
          playSound: false,
          enableVibration: false,
        ),
      ],
    );

    await backgroundService.configure(
      androidConfiguration: AndroidConfiguration(
        foregroundServiceTypes: [AndroidForegroundType.dataSync],
        onStart: onStart,
        autoStart: false,
        autoStartOnBoot: false,
        isForegroundMode: false,
        notificationChannelId: notificationChannelId,
        initialNotificationTitle: 'Story Upload',
        initialNotificationContent: 'Preparing to upload',
        foregroundServiceNotificationId: notificationId,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
    );
  }

  static Future<bool> onIosBackground(ServiceInstance service) async {
    return true;
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    ui.DartPluginRegistrant.ensureInitialized();

    if (service is AndroidServiceInstance) {
      service.setAsForegroundService();
    }

    service.on('uploadVideo').listen((event) async {
      if (event == null) return;

      final videoPath = event['videoPath'] as String;
      final overlayImagePath = event['overlayImagePath'] as String?;
      final properties = event['properties'] as String?;
      final thumbnailPath = event['thumbNailImage'] as String?;
      final duration = event['duration'] as String?;

      await handleVideoUpload(service, videoPath, overlayImagePath, thumbnailPath,duration,properties);
    });

    service.on('uploadImageAndTextStory').listen((event) async {
      if (event == null) return;

      final List<String> imagePaths = (event['imagePaths'] as List).cast<String>();
      final properties = event['properties'] as String?;
      final mediaType = event['mediaType'] as String?;

      await handleImageStoryUpload(service, imagePaths, properties, mediaType);
    });
  }

  static Future<void> handleVideoUpload(
      ServiceInstance service,
      String videoPath,
      String? overlayImagePath,
      String? thumbNailImagePath,
      String? duration,
      String? properties
      ) async {
    try {
      updateNotification(service, 'Preparing video upload...', 10);

      updateNotification(service, 'Uploading video...', 20);

      List<String> overlayImageUrls = [];
      bool isOnlyThumbnail = overlayImagePath == null && thumbNailImagePath != null;

      if (overlayImagePath != null || thumbNailImagePath != null) {
        try {
          List<String> uploadPaths = [];

          if (overlayImagePath != null) {
            uploadPaths.add(overlayImagePath);
          }
          if (thumbNailImagePath != null) {
            uploadPaths.add(thumbNailImagePath);
          }

          overlayImageUrls = await uploadMedia(uploadPaths, progressCallback: (progress) {
            updateNotification(service, 'Uploading video...', 20 + (progress * 30).round());
          });
        } catch (e) {
        }
      }

      String? overlayUrl;
      String? thumbnailUrl;

      if (isOnlyThumbnail && overlayImageUrls.isNotEmpty) {
        thumbnailUrl = overlayImageUrls[0];
      } else {
        overlayUrl = overlayImageUrls.isNotEmpty ? overlayImageUrls[0] : null;
        thumbnailUrl = overlayImageUrls.length > 1 ? overlayImageUrls[1] : null;
      }

      await uploadVideo(
        videoPath,
        overlayUrl,
        duration,
        thumbnailUrl,
        properties,
        progressCallback: (progress) {
          updateNotification(service, 'Uploading video...', 50 + (progress * 45).round());
        },
      );

      updateNotification(service, 'Upload complete!', 100);
      await Future.delayed(const Duration(seconds: 2));
      service.stopSelf();
      await awesomeNotifications.cancel(notificationId);
    } catch (e) {
      updateNotification(service, 'Upload failed: $e', 0);
      await Future.delayed(const Duration(seconds: 3));
      service.stopSelf();
      await awesomeNotifications.cancel(notificationId);
    }
  }

  static Future<void> handleImageStoryUpload(
      ServiceInstance service,
      List<String> imagePaths,
      String? properties,
      String? mediaType
      ) async {
    try {

      updateNotification(service, 'Uploading story...', 10);

      final List<String> imageUrls = await uploadMedia(imagePaths,
          progressCallback: (progress) {
            updateNotification(service, 'Uploading media...', 10 + (progress * 70).round());
          }
      );

      await postImageTextStory(imageUrls, properties, mediaType,
          progressCallback: (progress) {
            updateNotification(service, 'Upload complete!', 80 + (progress * 20).round());
          }
      );

      updateNotification(service, 'Upload complete!', 100);
      await Future.delayed(const Duration(seconds: 2));
      service.stopSelf();
      await awesomeNotifications.cancel(notificationId);
    } catch (e) {
      updateNotification(service, 'Upload failed: $e', 0);
      await Future.delayed(const Duration(seconds: 3));
      service.stopSelf();
      await awesomeNotifications.cancel(notificationId);
    }
  }

  static void updateNotification(ServiceInstance service, String message, int progress) {
    if(Platform.isAndroid || progress==100) {
      awesomeNotifications.createNotification(
        content: NotificationContent(
          id: notificationId,
          channelKey: notificationChannelId,
          title: 'Story Upload',
          body: '$message ($progress%)',
          notificationLayout: NotificationLayout.ProgressBar,
          progress: progress.toDouble(),
          locked: true,
          criticalAlert: false,
          wakeUpScreen: false,
          showWhen: false,
        ),
      );
    }
  }

  static Future<List<String>> uploadMedia(
      List<String> filePaths,
      {Function(double progress)? progressCallback}
      ) async {
    final completer = Completer<List<String>>();
    try {
      List<dio.MultipartFile> multipartFiles = [];

      for (String path in filePaths) {
        multipartFiles.add(
          await dio.MultipartFile.fromFile(
            path,
            filename: path.split('/').last.trim(),
          ),
        );
      }
      dio.FormData formData = dio.FormData.fromMap({
        "file": multipartFiles,
      });
      log('dio form data ===> ${formData}');
      final dioInstance = dio.Dio();
      dioInstance.post(
        CONFIG().baseUrl+APIS.generalImageUpload.imageUpload.endpoint,
        data: formData,
        onSendProgress: (sent, total) {
          if (progressCallback != null && total > 0) {
            final progress = sent / total;
            progressCallback(progress);
          }
        },
        options: dio.Options(
          headers:  await ApiManager().getAuthHeaders(),
        ),
      ).then((response) {
        if (response.data['status'] == 'success') {
          List<String> uploadedUrls = [];
          List<dynamic> uploadedImages = response.data['data'];

          for (var imageData in uploadedImages) {
            String imageUrl = imageData['link'];
            uploadedUrls.add(imageUrl);
          }
          log('uploaded url ==> $uploadedUrls');
          completer.complete(uploadedUrls);
        } else {
          completer.completeError("API returned non-success status");
        }
      }).catchError((error) {
        completer.completeError("API Error: $error");
        log('uploaded url ==> $error');
      });

      return completer.future;
    } catch (e) {
      completer.completeError(e);
      return completer.future;
    }
  }

  static Future<void> uploadVideo(
      String videoPath,
      String? overlayImageUrl,
      String? duration,
      String? thumbNailImage,
      String? properties,
      {Function(double progress)? progressCallback}
      ) async {
    final completer = Completer<void>();

    try {

      List<dio.MultipartFile> multipartFiles = [];

      multipartFiles.add(
        await dio.MultipartFile.fromFile(
          videoPath,
          filename: videoPath.split('/').last.trim(),
        ),
      );

      dio.FormData formData = dio.FormData.fromMap({
        "file": multipartFiles,
        "overlayImage": overlayImageUrl ?? "",
        "properties": properties ?? "[]",
        "duration": duration ?? "0",
        "thumbNailImage": thumbNailImage ?? "",
      });


      final dioInstance = dio.Dio();

      var header = await ApiManager().getAuthHeaders();

      dioInstance.post(
        CONFIG().baseUrl +  APIS.story.postVideoStory.endpoint,
        data: formData,
        onSendProgress: (sent, total) {
          if (progressCallback != null && total > 0) {
            final progress = sent / total;
            progressCallback(progress);
          }
        },
        options: dio.Options(
          headers: await ApiManager().getAuthHeaders(),
        ),
      ).then((response) {
        completer.complete();
      }).catchError((error) {
        completer.completeError("API Error: $error");
      });

      return completer.future;
    } catch (e) {
      completer.completeError(e);
      return completer.future;
    }
  }

  static Future<void> postImageTextStory(
      List<String> imageUrls,
      String? properties,
      String? mediaType,
      {Function(double progress)? progressCallback}
      ) async {
    final completer = Completer<void>();

    try {
      var dict = {};

      if (imageUrls.isNotEmpty) {
        dict['overlayImage'] = imageUrls[0];
        if (imageUrls.length > 1) {
          dict['mediaLink'] = imageUrls[1];
        }
      }

      dict['mediaType'] = mediaType ?? StoryType.text.name;
      dict['properties'] = properties ?? "[]";

      log('dict is ==> ${dict}');

      final dioInstance = dio.Dio();

      if (progressCallback != null) {
        progressCallback(0.5);
      }

      dioInstance.post(
        CONFIG().baseUrl+APIS.story.postStory.endpoint,
        data: dict,
        options: dio.Options(
          headers: await ApiManager().getAuthHeaders(),
        ),
      ).then((response) {
        if (progressCallback != null) {
          progressCallback(1.0);
        }
        log('api response ===> $response');
        completer.complete();
      }).catchError((error) {
        completer.completeError("API Error: $error");
        log('api error ===> ${error}');
      });

      return completer.future;
    } catch (e) {
      completer.completeError(e);
      log('api error ===> 2 ${e}');
      return completer.future;
    }
  }

  static Future<void> uploadVideoStory({
    required String videoPath,
    String? overlayImagePath,
    String? properties,
    String? thumbNailImage,
    int? duration,
  }) async {
    await backgroundService.startService();

    backgroundService.invoke('uploadVideo', {
      'videoPath': videoPath,
      'overlayImagePath': overlayImagePath,
      'properties': properties,
      'thumbNailImage': thumbNailImage,
      'duration': duration.toString(),
    });

    return;
  }

  static Future<void> uploadImageTextStory({
    required List<String> imagePaths,
    String? properties,
    String? mediaType,
  }) async {
    await backgroundService.startService();

    backgroundService.invoke('uploadImageAndTextStory', {
      'imagePaths': imagePaths,
      'properties': properties,
      'mediaType': mediaType,
    });
    return;
  }
}