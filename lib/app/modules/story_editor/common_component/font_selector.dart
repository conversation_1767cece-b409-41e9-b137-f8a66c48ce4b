
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/app/modules/story_editor/controllers/story_editor_controller.dart';

import 'animated_onTap_button.dart';


class FontSelector extends StatelessWidget {
  const FontSelector({super.key});

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    StoryEditorController editorNotifier = Get.find<StoryEditorController>();
    return Obx(()=> Container(
        height: size.width * 0.1,
        width: size.width,
        alignment: Alignment.center,
        child: PageView.builder(
          controller: editorNotifier.fontFamilyController.value,
          itemCount: editorNotifier.fontList.length,
          onPageChanged: (index) {
            editorNotifier.fontFamilyIndex.value = index;
            HapticFeedback.lightImpact();
          },
          physics: const BouncingScrollPhysics(),
          allowImplicitScrolling: true,
          pageSnapping: false,
          itemBuilder: (context, index) {
            return Obx(()=>AnimatedOnTapButton(
                onTap: () {
                  editorNotifier.fontFamilyIndex.value = index;
                  editorNotifier.fontFamilyController.value.jumpToPage(index);
                },
                child: Container(
                  height: size.width * 0.1,
                  width: size.width * 0.1,
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                      color: index == editorNotifier.fontFamilyIndex.value
                          ? Colors.white
                          : Colors.black.withOpacity(0.4),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white)),
                  child: Center(
                    child: Text(
                      'Aa',
                      style: GoogleFonts.getFont(
                               editorNotifier.fontList[index],)
                          .copyWith(
                              color: index == editorNotifier.fontFamilyIndex.value
                                  ? Colors.red
                                  : Colors.white,
                              fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
