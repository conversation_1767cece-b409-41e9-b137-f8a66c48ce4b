import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:incenti_ai/constants/app_size_constant.dart';

class ColorDetection {
  final GlobalKey? currentKey;
  final StreamController<Color>? stateController;
  final GlobalKey? paintKey;

  img.Image? photo;

  ColorDetection({
    required this.currentKey,
    required this.stateController,
    required this.paintKey,
  });

  Future<dynamic> searchPixel(Offset globalPosition) async {
    if (photo == null) {
      await loadSnapshotBytes();
    }
    return _calculatePixel(globalPosition);
  }

  _calculatePixel(Offset globalPosition) {
    try {
      RenderBox box = currentKey!.currentContext!.findRenderObject() as RenderBox;
      Offset localPosition = box.globalToLocal(globalPosition);

      double px = localPosition.dx;
      double py = localPosition.dy;

      px = px.clamp(0, photo!.width - 1);
      py = py.clamp(0, photo!.height - 1);

      final pixel32 = photo!.getPixelSafe(px.toInt(), py.toInt()).toList().cast<int>();

      if (pixel32.length == 4) {
        final hex = pixel32ToArgb(pixel32);
        Color color = Color(hex).withOpacity(.95);
        stateController?.add(color);
        return color;
      } else {
        Color fallbackColor = Colors.grey.withOpacity(.95);
        stateController?.add(fallbackColor);
        return fallbackColor;
      }
    } catch (e) {
      Color fallbackColor = Colors.grey.withOpacity(.95);
      stateController?.add(fallbackColor);
      return fallbackColor;
    }
  }

  Future<void> loadSnapshotBytes() async {
    RenderRepaintBoundary? boxPaint =
    paintKey!.currentContext!.findRenderObject() as RenderRepaintBoundary?;
    ui.Image capture = await boxPaint!.toImage();
    ByteData? imageBytes =
    await capture.toByteData(format: ui.ImageByteFormat.png);
    setImageBytes(imageBytes!);
    capture.dispose();
  }

  void setImageBytes(ByteData imageBytes) {
    Uint8List values = imageBytes.buffer.asUint8List();
    photo = null;
    photo = img.decodeImage(values);
  }
}

int abgrToArgb(int argbColor) {
  int r = (argbColor >> 16) & 0xFF;
  int b = argbColor & 0xFF;
  return (argbColor & 0xFF00FF00) | (b << 16) | r;
}

int pixel32ToArgb(List<int> pixel) {
  if (pixel.length != 4) {
    throw ArgumentError('Pixel must have four components.');
  }

  return (pixel[3] << 24) | (pixel[0] << 16) | (pixel[1] << 8) | pixel[2];
}

class FileImageBG extends StatefulWidget {
  final File? filePath;
  final void Function(Color color1, Color color2) generatedGradient;
  const FileImageBG({
    super.key,
    required this.filePath,
    required this.generatedGradient,
  });

  @override
  State<FileImageBG> createState() => _FileImageBGState();
}

class _FileImageBGState extends State<FileImageBG>  {
  GlobalKey imageKey = GlobalKey();
  GlobalKey paintKey = GlobalKey();

  GlobalKey? currentKey;

  final StreamController<Color> stateController = StreamController<Color>();
  var color1 = Colors.grey[350]!;
  var color2 = Colors.grey[350]!;

  Timer? _timer;

  @override
  void initState() {
    currentKey = paintKey;
    if (mounted) {
      _timer =
          Timer.periodic(const Duration(milliseconds: 500), _periodicFunction);
    }
    super.initState();
  }

  Future<void> _periodicFunction(Timer callback) async {
    if (imageKey.currentState?.context.size?.height == 0.0) {
      return;
    }

    try {
      var cd = ColorDetection(
        currentKey: currentKey,
        paintKey: paintKey,
        stateController: stateController,
      );
      if (cd.photo == null) {
        await cd.loadSnapshotBytes();
        if (cd.photo == null) return;
      }
      var cd1 = await cd.searchPixel(
          Offset(imageKey.currentState!.context.size!.width / 2, 480));
      var cd12 = await cd.searchPixel(
          Offset(imageKey.currentState!.context.size!.width / 2.03, 530));
      color1 = cd1;
      color2 = cd12;
      setState(() {});
      widget.generatedGradient(color1, color2);
      callback.cancel();
      stateController.close();
    } catch (e) {
      print("Error in color detection: $e");
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    stateController.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return SizedBox(
      height: MySize.screenHeight,
      width: MySize.screenWidth,
      child: RepaintBoundary(
        key: paintKey,
        child: FittedBox(
          fit: BoxFit.cover,
          clipBehavior: Clip.none,
          child: Image.file(
            File(widget.filePath!.path),
            key: imageKey,
            filterQuality: FilterQuality.high,
          ),
        ),
      ),
    );
  }
}