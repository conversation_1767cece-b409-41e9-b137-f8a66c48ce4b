import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_project_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_blur_container.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../controllers/story_editor_controller.dart';
import 'animated_onTap_button.dart';

class ToolBar extends StatelessWidget {
  final StoryEditorController editorNotifier;

  const ToolBar({super.key, required this.editorNotifier});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.getScaledSizeWidth(30),
                vertical: MySize.getScaledSizeHeight(15),
              ),
              child: commonBlurredContainer(
                blurOpacity: 0.1,
                child: Padding(
                  padding: EdgeInsets.all(MySize.getScaledSizeHeight(5)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildIconButton(
                        isSelected: editorNotifier.isFontFamily.value,
                        onTap: () {
                          editorNotifier.isFontFamily.value = true;
                          _animateToPage();
                        },
                        child: TypoGraphy(
                          text: "Aa",
                          color: Colors.white,
                          textAlign: TextAlign.center,
                          fontSize: MySize.size26 ?? 26,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      _buildIconButton(
                        isSelected: !editorNotifier.isFontFamily.value,
                        onTap: () {
                          editorNotifier.isFontFamily.value = false;
                          _animateToPage();
                        },
                        child: Container(
                            height: MySize.getScaledSizeHeight(34),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(color: AppTheme.white, width: 2),
                            ),
                            child: SvgPicture.asset(AppImage.icGradient)),
                      ),
                      editorNotifier.mediaPath.value.isEmpty
                          ? _buildIconButton(
                              onTap: _changeGradient,
                              child: Container(
                                height: MySize.getScaledSizeHeight(34),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(color: AppTheme.white, width: 2),
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: editorNotifier.gradientColors[editorNotifier.gradientIndex.value],
                                  ),
                                ),
                              ),
                            )
                          : _buildIconButton(
                              onTap: editorNotifier.onBackGroundChange,
                              child: Container(
                                height: MySize.getScaledSizeHeight(34),
                                decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: AppTheme.white, width: 2), color: editorNotifier.backGroundColor.value),
                              ),
                            ),
                      _buildIconButton(
                        onTap: editorNotifier.onAlignmentChange,
                        child: Icon(
                          _getAlignmentIcon(),
                          color: Colors.white,
                          size: MySize.getScaledSizeHeight(34),
                        ),
                      ),
                      if (editorNotifier.mediaPath.value.isEmpty)
                        _buildIconButton(
                          onTap: editorNotifier.onBackGroundChange,
                          child: Container(
                            height: MySize.getScaledSizeHeight(34),
                            decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: AppTheme.white, width: 2), color: editorNotifier.backGroundColor.value),
                          ),
                        )
                    ],
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                editorNotifier.textNode.unfocus();
                ImagePickerBottomSheet.show(
                  context: context,
                  child: _projectBottomSheet(),
                );
              },
              child: commonBlurredContainer(
                blurOpacity: 0.1,
                borderRadius: 0,
                child: Padding(
                  padding: EdgeInsets.all(MySize.getScaledSizeHeight(10)),
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildTagButton(
                          text: "@ Tag Project",
                        ),
                      ),
                      /*      _buildVerticalDivider(),
                  Expanded(
                    child: _buildTagButton(text: "@ Tag Sub-Project",
                      onTap: () {
                      if(editorNotifier.draggableMentionedWidget.isEmpty){
                        CommonFunction.showCustomSnackbar(
                            message: "Please first select the Parent Project",
                            backgroundColor: AppTheme.red,
                            isError: true);
                      }else{
                        editorNotifier.textNode.unfocus();
                        ImagePickerBottomSheet.show(
                        context: context,
                        child: _subProjectBottomSheet(),
                      );}}),
                  ),*/
                    ],
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  Widget _buildIconButton({
    required VoidCallback onTap,
    required Widget child,
    bool isSelected = false,
  }) {
    return Expanded(
      child: AnimatedOnTapButton(
        onTap: onTap,
        child: isSelected
            ? commonBlurredContainer(
                blurOpacity: 0.1,
                borderRadius: MySize.size8 ?? 8,
                centerChild: true,
                child: SizedBox(
                  height: MySize.getScaledSizeHeight(50),
                  child: Center(child: child),
                ),
              )
            : SizedBox(
                height: MySize.getScaledSizeHeight(50),
                child: Center(child: child),
              ),
      ),
    );
  }

  Widget _buildTagButton({required String text}) {
    return Center(
      child: TypoGraphy(
        text: text,
        level: 4,
        textStyle: TextStyle(color: AppTheme.white),
      ),
    );
  }

/*  Widget _buildVerticalDivider() {
    return Container(
      height: MySize.getScaledSizeHeight(28),
      width: 2,
      color: AppTheme.white,
    );
  }*/

  Widget _projectBottomSheet() {
    return SizedBox(
      height: MySize.getScaledSizeHeight(300),
      child: Column(
        children: [
          SizedBox(height: MySize.getScaledSizeHeight(30)),
          editorNotifier.userProject.isNotEmpty
              ? Expanded(
                  child: ListView.builder(
                    itemCount: editorNotifier.userProject.length,
                    itemBuilder: (context, index) {
                      Project project = editorNotifier.userProject[index];
                      return ListTile(
                        title: TypoGraphy(
                          text: project.name,
                          level: 4,
                          textStyle: TextStyle(color: editorNotifier.draggableMentionedWidget.any((element) => element.projectId == project.id) ? AppTheme.primary1 : null),
                        ),
                        onTap: () {
                          // editorNotifier.callApiForGetAllSubProject(context: context, projectId: project.id);
                          editorNotifier.text.value = "@${project.name}";
                          editorNotifier.textController.value.text = "@${project.name}";
                          editorNotifier.projectId.value = project.id;
                          editorNotifier.slug.value = project.slug ?? '';
                          editorNotifier.isReadOnly.value = true;
                          Get.back();
                        },
                      );
                    },
                  ),
                )
              : Empty(
                  title: "No Project available!",
                )
        ],
      ),
    );
  }

/*  Widget _subProjectBottomSheet() {
    return SizedBox(
      height: MySize.getScaledSizeHeight(300),
      child: Column(
        children: [
          SizedBox(height: MySize.getScaledSizeHeight(30)),
          editorNotifier.subProjectList.isNotEmpty ? Expanded(
            child: ListView.builder(
              itemCount: editorNotifier.subProjectList.length,
              itemBuilder: (context, index) {
                SubProjectData subProject = editorNotifier.subProjectList[index];
                return ListTile(
                  title: TypoGraphy(
                    text: subProject.name,
                    level: 4,
                    textStyle: TextStyle(color: editorNotifier.draggableMentionedWidget.any((element) => element.subProjectId == subProject.id) ? AppTheme.primary1 : AppTheme.black),
                  ),
                  onTap: () {
                    editorNotifier.text.value = "@${subProject.name}";
                    editorNotifier.textController.value.text = "@${subProject.name}";
                    editorNotifier.subProjectId.value = subProject.id;
                    editorNotifier.isReadOnly.value = true;
                    Get.back();
                  },
                );
              },
            ),
          ) :
          Empty(
            title: "No Project available!",
          )
        ],
      ),
    );
  }*/

  void _animateToPage() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (editorNotifier.fontFamilyController.value.hasClients) {
        editorNotifier.fontFamilyController.value.animateToPage(
          editorNotifier.fontFamilyIndex.value,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeIn,
        );
      }
    });
  }

  void _changeGradient() {
    editorNotifier.gradientIndex.value = (editorNotifier.gradientIndex.value + 1) % editorNotifier.gradientColors.length;
  }

  IconData _getAlignmentIcon() {
    switch (editorNotifier.textAlign.value) {
      case TextAlign.right:
        return Icons.format_align_right;
      case TextAlign.left:
        return Icons.format_align_left;
      default:
        return Icons.format_align_center;
    }
  }
}
