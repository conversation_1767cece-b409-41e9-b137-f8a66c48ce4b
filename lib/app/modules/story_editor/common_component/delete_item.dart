import 'package:flutter/material.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';

import '../../../../models/story_model.dart';

class DeleteItem extends StatelessWidget {
  const DeleteItem({
    super.key,
    required EditableItem? activeItem,
    required this.isDeletePosition,
    required this.animationsDuration,
    this.deletedItem,
  }) : _activeItem = activeItem;

  final EditableItem? _activeItem;
  final bool isDeletePosition;
  final Duration animationsDuration;
  final Widget? deletedItem;

  @override
  Widget build(BuildContext context) {

    return Positioned(
        bottom: 0,
        right: 0,
        left: 0,
        child: AnimatedScale(
          curve: Curves.easeIn,
          duration: const Duration(milliseconds: 200),
          scale: _activeItem != null && _activeItem.type != StoryType.image
              ? 1.0
              : 0.0,
          child: SizedBox(
            width: MySize.screenWidth,
            child: Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: MySize.size90,
                    height: MySize.size90,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: deletedItem == null
                        ? Transform.scale(
                            scale: 1,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(360),
                              child: deletedItem,
                            ),
                          )
                        : const SizedBox(),
                  ),
                  AnimatedContainer(
                    alignment: Alignment.center,
                    duration: animationsDuration,
                    height: isDeletePosition ? 55.0 : 45,
                    width: isDeletePosition ? 55.0 : 45,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.35),
                      border: Border.all(color: isDeletePosition ? Colors.red :Colors.white),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child:  Icon(
                       Icons.delete,
                      color: isDeletePosition ? Colors.red :Colors.white,
                      size: 23,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
