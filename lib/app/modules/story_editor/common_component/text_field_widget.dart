// ignore_for_file: unrelated_type_equality_checks

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/app/modules/story_editor/controllers/story_editor_controller.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';


class TextFieldWidget extends StatelessWidget {
  const TextFieldWidget({super.key});

  @override
  Widget build(BuildContext context) {
    StoryEditorController storyController =  Get.find<StoryEditorController>();
    MySize().init(context);
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MySize.screenWidth - (MySize.size100 ?? 100),
        ),
        child: IntrinsicWidth(
            child: Stack(
              alignment: Alignment.center,
              children: [
                _textField(
                  editorNotifier: storyController,
                  textNode: storyController.textNode,
                  paintingStyle: PaintingStyle.stroke,
                )
              ],
            )),
      ),
    );
  }

  Widget _textField({
    required StoryEditorController editorNotifier,
    required FocusNode textNode,
    required PaintingStyle paintingStyle,
  }) {
    return Obx(()=>Container(
      color: editorNotifier.backGroundColor.value,
      child: TextField(
        readOnly: editorNotifier.isReadOnly.value,
          focusNode: textNode,
          autofocus: true,
          textInputAction: TextInputAction.newline,
          controller: editorNotifier.textController.value,
          textAlign: editorNotifier.textAlign.value,
          style: GoogleFonts.getFont(
                      editorNotifier.fontList[editorNotifier.fontFamilyIndex.value],
            fontWeight: FontWeight.w500
          )
              .copyWith(
            color: editorNotifier.colorList[editorNotifier.textColor.value],
            fontSize: editorNotifier.textSize.value,
            background: Paint()
              ..strokeWidth = 20.0
              ..color = editorNotifier.backGroundColor.value
              ..style = paintingStyle
              ..strokeJoin = StrokeJoin.round
              ..filterQuality = FilterQuality.high
              ..maskFilter = const MaskFilter.blur(BlurStyle.solid, 1)
              ..strokeCap = StrokeCap.round,
          ),
          cursorColor: editorNotifier.colorList[editorNotifier.textColor.value],
          minLines: 1,
          keyboardType: TextInputType.multiline,
          maxLines: null,
          decoration: null,
          onChanged: (value) {
            editorNotifier.text.value = value;
          },
        ),
    ),
    );
  }
}
