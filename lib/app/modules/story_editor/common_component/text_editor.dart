import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/story_editor/common_component/size_slider_selector.dart';
import 'package:incenti_ai/app/modules/story_editor/common_component/text_field_widget.dart';
import 'package:incenti_ai/app/modules/story_editor/common_component/tool_bar.dart';
import 'package:incenti_ai/app/modules/story_editor/controllers/story_editor_controller.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'color_selector.dart';
import 'font_selector.dart';

class TextEditor extends StatefulWidget {
  final BuildContext context;
  final StoryEditorController controller;
  const TextEditor({super.key, required this.context, required this.controller});

  @override
  State<TextEditor> createState() => _TextEditorState();
}

class _TextEditorState extends State<TextEditor> {
  List<String> splitList = [];
  String sequenceList = '';
  String lastSequenceList = '';

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      widget.controller
        ..textController.value.text = widget.controller.text.value
        ..fontFamilyController.value = PageController(viewportFraction: .125);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Obx(
      () => Material(
        color: Colors.transparent,
        child: GestureDetector(
          onTap: (){
            widget.controller.onDoneTap(
                widget.controller.text.startsWith('@')
                    ? widget.controller.draggableMentionedWidget
                    : widget.controller.draggableWidget);
            widget.controller.isTextEditing.value = false;
          },
          child: Container(
            decoration: BoxDecoration(color: Colors.black.withValues(alpha: 0.2)),
            height: MySize.screenHeight,
            width: MySize.screenWidth,
            child: Stack(
              children: [
                const Align(
                  alignment: Alignment.center,
                  child: TextFieldWidget(),
                ),
                 Align(
                  alignment: MediaQuery.of(context).viewInsets.bottom > 0 ? Alignment.topRight : Alignment.centerRight,
                  child: Padding(
                    padding: EdgeInsets.only(right: 8.0),
                    child: SizeSliderWidget(),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  left: 0,
                  child: ToolBar(editorNotifier: widget.controller),
                ),
                Positioned(
                  bottom: MySize.getScaledSizeHeight(110),
                  child: Visibility(
                    visible: widget.controller.isFontFamily.value,
                    child: const Align(
                      alignment: Alignment.bottomCenter,
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 20),
                        child: FontSelector(),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: MySize.getScaledSizeHeight(90),
                  child: Visibility(
                      visible: !widget.controller.isFontFamily.value,
                      child:  Align(
                        alignment: Alignment.bottomCenter,
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 20),
                          child: ColorSelector(controller: widget.controller,),
                        ),
                      )),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
