import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/story_editor/controllers/story_editor_controller.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'animated_onTap_button.dart';


class ColorSelector extends StatelessWidget {
  final StoryEditorController controller;
  const ColorSelector({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(()=>Container(
        height: MySize.screenHeight * 0.1,
        width: MySize.screenWidth,
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 5, right: 5),
        child: Row(
          children: [

            Container(
              height: MySize.getScaledSizeHeight(50),
              width: MySize.getScaledSizeWidth(50),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: controller.colorList[controller.textColor.value],
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1.5)),
              child: Icon(
                Icons.format_color_text,
                color: (controller.textColor.value == 0
                        ? Colors.black
                        : Colors.white),
              ),
            ),

            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    ...controller.colorList.map((color) {
                      final int index =
                      controller.colorList.indexOf(color);
                      return AnimatedOnTapButton(
                        onTap: () {
                            controller.textColor.value = index;
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(5)),
                          child: Container(
                            height: MySize.getScaledSizeHeight(35),
                            width: MySize.getScaledSizeWidth(35),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: controller.colorList[index],
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: Colors.white, width: 1.5)),
                          ),
                        ),
                      );
                    })
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
