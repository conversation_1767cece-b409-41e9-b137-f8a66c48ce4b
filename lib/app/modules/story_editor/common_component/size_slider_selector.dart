import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/story_editor/controllers/story_editor_controller.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';

class SizeSliderWidget extends StatefulWidget {
  const SizeSliderWidget({super.key});

  @override
  State<SizeSliderWidget> createState() => _SizeSliderWidgetState();
}

class _SizeSliderWidgetState extends State<SizeSliderWidget> {

  @override
  Widget build(BuildContext context) {
    StoryEditorController editorNotifier = Get.find<StoryEditorController>();
    return Stack(
      alignment: Alignment.center,
      children: [
        Obx(()=>AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: EdgeInsets.only(right: editorNotifier.isChange.value ? 0 : 15),
              width: editorNotifier.isChange.value ? 39 : 10,
              height: 250,
              child: CustomPaint(
                painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ain<PERSON>(),
                size: Size(MySize.screenHeight, (MySize.screenWidth).toDouble()),
              )),
        ),

        Obx(()=>AnimatedContainer(
            padding: EdgeInsets.only(left: editorNotifier.isChange.value ? 1 : 1, right: 2.1),
            duration: const Duration(milliseconds: 300),
            width: editorNotifier.isChange.value ? 39 : 15,
            height: 250,
            decoration: const BoxDecoration(),
            child: RotatedBox(
              quarterTurns: 3,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: 200,
                    height: !editorNotifier.showIndicator.value ? 2 : 0,
                    decoration: BoxDecoration(color: !editorNotifier.showIndicator.value ? Colors.white.withOpacity(0.2) : Colors.transparent, borderRadius: BorderRadius.circular(30)),
                  ),
                  Padding(
                    padding: editorNotifier.isChange.value ? const EdgeInsets.only(top: 2) : const EdgeInsets.all(0),
                    child: Slider(
                      value: editorNotifier.textSize.value,
                      min: 22,
                      max: 70,
                      activeColor: Colors.transparent,
                      thumbColor: Colors.white,
                      inactiveColor: Colors.transparent,
                      onChanged: (value) {
                        editorNotifier.textSize.value = value;
                      },
                      onChangeStart: (start) {
                        editorNotifier.isChange.value = true;
                        editorNotifier.showIndicator.value = true;
                      },
                      onChangeEnd: (end) {
                        editorNotifier.isChange.value = false;
                        editorNotifier.showIndicator.value = false;
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class RPSCustomPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint0 = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.fill
      ..strokeWidth = 1.0;

    Path path0 = Path();
    path0.moveTo(size.width * 0.0980139, size.height * 0.0651296);
    path0.lineTo(size.width * 0.9040833, size.height * 0.0646574);
    path0.lineTo(size.width * 0.5000139, size.height * 0.9537037);
    path0.lineTo(size.width * 0.0980139, size.height * 0.0651296);
    path0.close();

    canvas.drawPath(path0, paint0);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
