import 'dart:io';

import 'package:align_positioned/align_positioned.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import '../../../../models/story_model.dart';
import 'animated_onTap_button.dart';
import 'file_image_bg.dart';


class DraggableWidget extends StatelessWidget {
  final EditableItem draggableWidget;
  final Function(PointerDownEvent)? onPointerDown;
  final Function(PointerUpEvent)? onPointerUp;
  final Function(PointerMoveEvent)? onPointerMove;
  final void Function() onTap;
  final List<String> fontList;
  final String? mediaPath;
  final void Function(Color, Color)? onGeneratedGradient;
  final double? upperBound;
  final double? lowerBound;
  const DraggableWidget({
    super.key,
    required this.draggableWidget,
    this.onPointerDown,
    this.onPointerUp,
    this.onPointerMove,
    required this.onTap,
    required this.fontList,
    this.mediaPath,
    this.onGeneratedGradient,
    this.upperBound,
    this.lowerBound,
  });

  @override
  Widget build(BuildContext context) {
    Widget? overlayWidget;
    MySize().init(context);
    switch (draggableWidget.type) {
      case StoryType.text:
        overlayWidget = IntrinsicWidth(
          child: IntrinsicHeight(
            child: Container(
              constraints: BoxConstraints(
                minHeight: MySize.size200 ?? 50,
                minWidth: MySize.size200 ?? 50,
                maxWidth: MySize.screenWidth - (MySize.size35 ?? 35),
              ),
              width: draggableWidget.deletePosition ? 100 : null,
              height: draggableWidget.deletePosition ? 100 : null,
              child: AnimatedOnTapButton(
                lowerBound: lowerBound ?? 0.95,
                upperBound: upperBound ?? 1,
                onTap: onTap,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    _text(paintingStyle: PaintingStyle.stroke),
                    _text(paintingStyle: PaintingStyle.fill),
                  ],
                ),
              ),
            ),
          ),
        );
        break;

      case StoryType.video:
        throw UnimplementedError();

      case StoryType.image:
        if (mediaPath != null && mediaPath!.isNotEmpty) {
          overlayWidget = SizedBox(
            width: MySize.screenWidth,
            height: MySize.screenHeight,
            child: FileImageBG(
              filePath: File(mediaPath!),
              generatedGradient: onGeneratedGradient ?? ((color1, color2) {}),
            ),
          );
        } else {
          overlayWidget = Container();
        }
        break;
    }

    return AnimatedAlignPositioned(
      duration: const Duration(milliseconds: 10),
      dy: (draggableWidget.deletePosition
          ? _deleteTopOffset()
          : (draggableWidget.position.dy * MySize.screenHeight)),
      dx: (draggableWidget.deletePosition
          ? 0
          : (draggableWidget.position.dx * MySize.screenWidth)),
      alignment: Alignment.center,
      child: Transform.scale(
        scale: draggableWidget.deletePosition
            ? _deleteScale()
            : draggableWidget.scale,
        child: Transform.rotate(
          angle: draggableWidget.rotation,
          child: Listener(
            onPointerDown: onPointerDown,
            onPointerUp: onPointerUp,
            onPointerMove: onPointerMove,
            behavior: HitTestBehavior.opaque,
            child: overlayWidget,
          ),
        ),
      ),
    );
  }

  Widget _text({ required PaintingStyle paintingStyle }) {
    return Text(
      draggableWidget.text,
      textAlign: draggableWidget.textAlign,
      style: _textStyle(paintingStyle:paintingStyle),
    );
  }

  TextStyle _textStyle({ required PaintingStyle paintingStyle }) {
    return GoogleFonts.getFont(
      fontList[draggableWidget.fontFamily],
      fontWeight: FontWeight.w500,
      fontSize: draggableWidget.deletePosition ? 8 : draggableWidget.fontSize,
      color: draggableWidget.textColor,
      background: Paint()
        ..strokeWidth = 20.0
        ..color = draggableWidget.backGroundColor
        ..style = paintingStyle
        ..strokeJoin = StrokeJoin.round
        ..filterQuality = FilterQuality.high
        ..strokeCap = StrokeCap.round
        ..maskFilter = const MaskFilter.blur(BlurStyle.solid, 1)
    );
  }

  _deleteTopOffset() {
    double top = 0.0;
    if (draggableWidget.type == StoryType.text) {
      top = MySize.screenWidth / 1.2;
      return top;
    }
  }

  _deleteScale() {
    double scale = 0.0;
    if (draggableWidget.type == StoryType.text) {
      scale = 0.4;
      return scale;
    }
  }
}
