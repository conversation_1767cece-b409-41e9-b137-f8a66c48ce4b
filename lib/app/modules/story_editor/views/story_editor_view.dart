import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/app/modules/story_editor/common_component/animated_onTap_button.dart';
import 'package:incenti_ai/app/modules/story_editor/common_component/text_editor.dart';
import 'package:incenti_ai/app/modules/story_editor/controllers/story_editor_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_blur_container.dart';
import 'package:video_player/video_player.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../common_component/delete_item.dart';
import '../common_component/draggable_widget.dart';

class StoryEditorView extends GetWidget<StoryEditorController> {
  const StoryEditorView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.black,
        body: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Stack(
                children: [
                  Obx(() => ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(MySize.size25 ?? 25),
                          topRight: Radius.circular(MySize.size25 ?? 25),
                        ),
                        child: controller.mediaPath.isNotEmpty &&
                                controller.isVideo.value &&
                                controller.isInitializingVideo.value
                            ? Center(
                                child: AspectRatio(
                                  aspectRatio: controller
                                      .videoPlayerController.value.aspectRatio,
                                  child: VideoPlayer(
                                      controller.videoPlayerController),
                                ),
                              )
                            : SizedBox(),
                      )),
                  Obx(
                    () => GestureDetector(
                      onScaleStart: controller.onScaleStart,
                      onScaleUpdate: (scale) {
                        controller.onScaleUpdate(
                          scale,
                          (controller.activeItem.value?.text ?? '')
                                  .startsWith('@')
                              ? controller.draggableMentionedWidget
                              : controller.draggableWidget,
                        );
                      },
                      onTap: () {
                        controller.isTextEditing.value =
                            !controller.isTextEditing.value;
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(MySize.size25 ?? 25),
                          topRight: Radius.circular(MySize.size25 ?? 25),
                        ),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: RepaintBoundary(
                            key: controller.textKey,
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              decoration: BoxDecoration(
                                gradient: controller.mediaPath.isEmpty
                                    ? LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: controller.gradientColors[
                                            controller.gradientIndex.value])
                                    : null,
                              ),
                              child: GestureDetector(
                                onScaleStart: controller.onScaleStart,
                                onScaleUpdate: (scale) {
                                  controller.onScaleUpdate(
                                    scale,
                                    (controller.activeItem.value?.text ?? '')
                                            .startsWith('@')
                                        ? controller.draggableMentionedWidget
                                        : controller.draggableWidget,
                                  );
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      gradient: controller.isVideo.value ||
                                              (controller.isText.value)
                                          ? null
                                          : LinearGradient(
                                              colors: [
                                                controller.color1.value,
                                                controller.color2.value
                                              ],
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                            )),
                                  child: Stack(
                                    children: [
                                      ...controller.draggableWidget
                                          .map((editableItem) {
                                        return DraggableWidget(
                                          draggableWidget: editableItem,
                                          mediaPath: controller.mediaPath.value,
                                          onGeneratedGradient: (color1, color2) {
                                            controller.color1.value = color1;
                                            controller.color2.value = color2;
                                          },
                                          onPointerDown: (details) {
                                            controller.updateItemPosition(
                                              editableItem,
                                              details,
                                            );
                                          },
                                          onPointerUp: (details) {
                                            controller.deleteItemOnCoordinates(
                                              editableItem,
                                              details,
                                              (controller.activeItem.value
                                                              ?.text ??
                                                          '')
                                                      .startsWith('@')
                                                  ? controller
                                                      .draggableMentionedWidget
                                                  : controller.draggableWidget,
                                            );
                                          },
                                          onPointerMove: (details) {
                                            controller.deletePosition(
                                              editableItem,
                                              details,
                                            );
                                          },
                                          onTap: () {
                                            controller.onTextEditTap(context,editableItem,controller.draggableWidget);
                                          }, fontList: controller.fontList,
                                        );
                                      }),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => GestureDetector(
                      onScaleStart: controller.onScaleStart,
                      onScaleUpdate: (scale) {
                        controller.onScaleUpdate(
                          scale,
                          (controller.activeItem.value?.text ?? '')
                                  .startsWith('@')
                              ? controller.draggableMentionedWidget
                              : controller.draggableWidget,
                        );
                      },
                      onTap: () {
                        controller.isTextEditing.value =
                            !controller.isTextEditing.value;
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(MySize.size25 ?? 25),
                          topRight: Radius.circular(MySize.size25 ?? 25),
                        ),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: RepaintBoundary(
                            key: controller.mentionKey,
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              child: GestureDetector(
                                onScaleStart: controller.onScaleStart,
                                onScaleUpdate: (scale) {
                                  controller.onScaleUpdate(
                                    scale,
                                    (controller.activeItem.value?.text ?? '')
                                            .startsWith('@')
                                        ? controller.draggableMentionedWidget
                                        : controller.draggableWidget,
                                  );
                                },
                                child: SizedBox(
                                  child: Stack(
                                    children: [
                                      ...controller.draggableMentionedWidget
                                          .map((editableItem) {
                                        return DraggableWidget(
                                          draggableWidget: editableItem,
                                          onPointerDown: (details) {
                                            controller.updateItemPosition(
                                              editableItem,
                                              details,
                                            );
                                          },
                                          onPointerUp: (details) {
                                            controller.deleteItemOnCoordinates(
                                              editableItem,
                                              details,
                                              (controller.activeItem.value
                                                              ?.text ??
                                                          '')
                                                      .startsWith('@')
                                                  ? controller
                                                      .draggableMentionedWidget
                                                  : controller.draggableWidget,
                                            );
                                          },
                                          onPointerMove: (details) {
                                            controller.deletePosition(
                                              editableItem,
                                              details,
                                            );
                                          }, onTap: () {
                                          controller.onTextEditTap(context,editableItem,controller.draggableMentionedWidget);
                                        }, fontList: controller.fontList,
                                        );
                                      }),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => Align(
                      alignment: Alignment.center,
                      child: Visibility(
                        visible: controller.draggableWidget.isEmpty &&
                            controller.draggableMentionedWidget.isEmpty &&
                            !controller.isTextEditing.value,
                        child: IgnorePointer(
                          ignoring: true,
                          child: Align(
                            alignment: const Alignment(0, -0.1),
                            child: Text(
                              'Tap to add text',
                              style: GoogleFonts.getFont(
                                'Alegreya',
                                fontWeight: FontWeight.w500,
                                fontSize: 30,
                                color: Colors.white.withOpacity(0.5),
                                shadows: <Shadow>[
                                  Shadow(
                                      offset: const Offset(1.0, 1.0),
                                      blurRadius: 3.0,
                                      color: Colors.black45.withOpacity(0.3))
                                ],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => DeleteItem(
                      activeItem: controller.activeItem.value,
                      animationsDuration: const Duration(milliseconds: 300),
                      isDeletePosition: controller.isDeletePosition.value,
                    ),
                  ),
                  Obx(
                    () => Padding(
                      padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      child: Visibility(
                        visible: controller.isTextEditing.value,
                        child: TextEditor(
                          context: context,
                          controller: controller,
                        ),
                      ),
                    ),
                  ),
                  Obx(() => controller.isTextEditing.value
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AnimatedOnTapButton(
                              onTap: () {
                                controller.isTextEditing.value = false;
                                controller.setDefaults();
                                HapticFeedback.lightImpact();
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size25 ?? 25,
                                    vertical: MySize.size10 ?? 10),
                                child: commonBlurredContainer(
                                  height: MySize.size40,
                                  width: MySize.size40,
                                  borderRadius: MySize.size50 ?? 50,
                                  blurOpacity: 0.1,
                                  centerChild: true,
                                  child: Icon(
                                    Icons.close_outlined,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            InkWell(
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {
                                controller.onDoneTap(
                                    controller.text.startsWith('@')
                                        ? controller.draggableMentionedWidget
                                        : controller.draggableWidget);
                                controller.isTextEditing.value = false;
                                HapticFeedback.lightImpact();
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size25 ?? 25,
                                    vertical: MySize.size10 ?? 10),
                                child: TypoGraphy(
                                  level: 18,
                                  text: "Done",
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AnimatedOnTapButton(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                showBackDialogue(context: context);
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size25 ?? 25,
                                    vertical: MySize.size10 ?? 10),
                                child: commonBlurredContainer(
                                  height: MySize.size40,
                                  width: MySize.size40,
                                  borderRadius: MySize.size50 ?? 50,
                                  blurOpacity: 0.1,
                                  centerChild: true,
                                  child: SvgPicture.asset(AppImage.backArrow,
                                      color:  AppTheme.white ),
                                ),
                              ),
                            ),
                            AnimatedOnTapButton(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                  controller.isTextEditing.value =
                                  !controller.isTextEditing.value;
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size25 ?? 25,
                                    vertical: MySize.size10 ?? 10),
                                child: commonBlurredContainer(
                                  height: MySize.size40,
                                  width: MySize.size40,
                                  borderRadius: MySize.size50 ?? 50,
                                  blurOpacity: 0.1,
                                  centerChild: true,
                                  child: TypoGraphy(text: 'A',color: AppTheme.white,),
                                ),
                              ),
                            ),
                          ],
                        )),
                ],
              ),
            ),
          ],
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: Obx(() => controller.activeItem.value != null ||
                controller.isTextEditing.value
            ? SizedBox()
            : Padding(
                padding:
                    EdgeInsets.only(bottom: MySize.getScaledSizeHeight(40)),
                child: SizedBox(
                  width: MySize.getScaledSizeWidth(198),
                  child: Buttons(
                    buttonText: controller.buttonText.value,
                    buttonTextLevel: 4,
                    buttonTextWeight: FontWeight.w600,
                    height: MySize.getScaledSizeHeight(70),
                    onTap: () => controller.shareOnTap(context: context),
                  ),
                ),
              )),
      ),
    );
  }

  void showBackDialogue({
    required BuildContext context,
  }) {
    showDialog(
        context: context,
        builder: (context) {
          return BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Dialog(
              insetPadding: EdgeInsets.symmetric(
                  horizontal: MySize.getScaledSizeWidth(70)),
              backgroundColor: Color(0xff25282d),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding: EdgeInsets.all(MySize.getScaledSizeHeight(20))
                    .copyWith(top: MySize.getScaledSizeHeight(30)),
                child: Column(
                  spacing: MySize.getScaledSizeHeight(10),
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TypoGraphy(
                      text: 'Discard edits?',
                      level: 4,
                      fontWeight: FontWeight.w600,
                      color: Color(0xfff2f4f4),
                    ),
                    TypoGraphy(
                      textAlign: TextAlign.center,
                      text:
                          'If you go back now, you\'ll loose all the edits you\'ve made.',
                      level: 4,
                      fontWeight: FontWeight.w400,
                      color: Color(0xfff2f4f4),
                    ),
                    Space.height(15),
                    InkWell(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        Get.back();
                        Get.back();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            TypoGraphy(
                              textAlign: TextAlign.center,
                              text: 'Discard',
                              level: 3,
                              fontWeight: FontWeight.w500,
                              color: Color(0xfff56475),
                            ),
                          ],
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        Get.back();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            TypoGraphy(
                              textAlign: TextAlign.center,
                              text: 'Keep editing',
                              level: 3,
                              fontWeight: FontWeight.w500,
                              color: Color(0xfff2f4f4),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }
}
