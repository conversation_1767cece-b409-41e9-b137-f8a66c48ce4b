
import 'dart:io';

import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../main.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/app_global_variables.dart';
import '../../../../utillites/current_user.dart';
import '../../../routes/app_pages.dart';

class AppUpdateAvailableController extends GetxController {
  var args = Get.arguments;
  RxBool isSoftUpdate = false.obs;
  @override
  void onInit() {
    if (args != null) {
      isSoftUpdate.value = args["softUpdate"];
    }
    super.onInit();
  }

  onTapUpdate() async {
    try {
      if (Platform.isAndroid) {
        if (!await launchUrl(Uri.parse(playStoreLink))) {
          throw Exception('Could not launch $playStoreLink');
        }
      } else {
        if (!await launchUrl(Uri.parse(appStoreLink))) {
          throw Exception('Could not launch $appStoreLink');
        }
      }
    } catch (error) {
      app.resolve<CustomDialogs>().getDialog(title: "Oops!", desc: error.toString());
    }
  }

  onSkipNow() {
    if (box.read("token") != null) {
      CurrentUser.getMe(callback: () async {
        Get.offAllNamed(Routes.Bottom_Bar); // Replace with your desired route
      });
    } else {
      Get.offNamed(Routes.ONBOARDING);
    }
  }
}
