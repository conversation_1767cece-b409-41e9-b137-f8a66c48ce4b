// ignore_for_file: unused_import

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../controllers/app_update_available_controller.dart';

class AppUpdateAvailableView extends GetWidget<AppUpdateAvailableController> {
  const AppUpdateAvailableView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      backgroundColor: box.read('isDarkMode') ? null : AppTheme.subPrimary,
      body: SafeArea(
        bottom: false,
        child: LayoutBuilder(
          builder: (context, constraints) {
            double screenWidth = constraints.maxWidth;
            double screenHeight = constraints.maxHeight;
            double verticalSpacing = screenHeight * 0.05; // 5% of screen height
            return Stack(
              children: [
                Column(
                  children: [
                    SizedBox(
                      height: screenHeight * 0.60, // 60% of the screen height
                      child: Image.asset(
                        AppImage.appUpdateBackground,
                        width: MySize.safeWidth,
                        // height: 20,
                        fit: BoxFit.cover,
                        alignment: Alignment.bottomCenter,
                        color: box.read('isDarkMode') ? AppTheme.baseBlack : null,
                      ),
                    ),
                    Container(
                      height: screenHeight * 0.40,
                      decoration: BoxDecoration(color: box.read('isDarkMode') ? null : AppTheme.primary1),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(18)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: verticalSpacing,
                            ),
                            TypoGraphy(
                              text: "New update is available",
                              color: AppTheme.white,
                              level: 5,
                            ),
                            SizedBox(height: verticalSpacing),
                            SizedBox(
                              width: MySize.getScaledSizeWidth(281),
                              child: TypoGraphy(
                                textAlign: TextAlign.center,
                                text:
                                    "a new version is release, please update to\nget new feature",
                                color: AppTheme.white,
                                level: 3,
                              ),
                            ),
                            SizedBox(
                              height: verticalSpacing,
                            ),
                            Obx(
                              () => controller.isSoftUpdate.value == false
                                  ? Buttons(
                                      buttonTextLevel: 4,
                                      height: MySize.getScaledSizeHeight(40),
                                      buttonText: "Update Now",
                                      textColor: AppTheme.primary1,
                                      color: Color(0XFFF5EEFC),
                                      borderRadius: BorderRadius.circular(9),
                                      onTap: controller.onTapUpdate,
                                    )
                                  : Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: screenWidth * 0.025),
                                      child: Column(
                                        children: [
                                          Buttons(
                                            buttonTextLevel: 4,
                                            height: MySize.getScaledSizeHeight(40),
                                            buttonText: "Update Now",
                                            textColor: AppTheme.primary1,
                                            color: Color(0XFFF5EEFC),
                                            borderRadius: BorderRadius.circular(9),
                                            onTap: controller.onTapUpdate,
                                          ),
                                          const Space.height(20),
                                          InkWell(
                                            highlightColor: Colors.transparent,
                                            splashColor: Colors.transparent,
                                            onTap: controller.onSkipNow,
                                            child: TypoGraphy(
                                              text: "skip for now",
                                              level: 3,
                                              color: AppTheme.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                // Positioned image
                Positioned(
                  top: screenHeight *
                      0.33, // Positioned to overlap the lower section by 5%
                  left: 0,
                  right: 0,
                  child: Image.asset(
                    AppImage.rocket,
                    height: screenHeight * 0.3,
                  ),
                ),
                Positioned(
                  top: 20, // Positioned to overlap the lower section by 5%
                  left: 0,
                  right: 0,
                  child: Align(
                    alignment: Alignment.topLeft,
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20),
                      child: Image.asset(AppImage.appLogo,
                          width: MySize.getScaledSizeHeight(120),
                          color: AppTheme.whiteWithBase,
                          fit: BoxFit.contain),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
