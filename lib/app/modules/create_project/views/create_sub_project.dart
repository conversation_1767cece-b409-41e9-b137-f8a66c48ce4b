import 'dart:io';
import 'dart:ui';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../models/app_project_model.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/locaion_input_field.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../post/components/image_select_dialog.dart';
import '../../user_detail/components/profile_image_view.dart';
import '../controllers/create_project_controller.dart';

Widget subProjectVisibility(BuildContext context, bool isEdit,
    {SubProjectData? selectedSubProject,
    int? subProjectIndex,
    String? projectId,
    bool isSubCreate = false,int? isVisibilityValue,bool? mainSubProjectVisibility}) {
  return GetBuilder<CreateProjectController>(
    init: CreateProjectController()..setIsEdit(isEdit, projectId.toString(),isVisibilityValue ?? -1,mainSubProjectVisibility ?? false),
    initState: (state) {
      if (isEdit) {
        state.controller?.callApiForGetOneProject(context: context);
      }
    },
    builder: (controller) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
        child: FractionallySizedBox(
          heightFactor: 0.80,
          child: Container(
            decoration: BoxDecoration(
              color: AppTheme.subBottom,
              border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(40), topRight: Radius.circular(40)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Space.height(8),
                Align(
                  alignment: Alignment.center,
                  child: Container(
                    height: MySize.size5,
                    width: MySize.size34,
                    decoration: BoxDecoration(
                      color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                ),
                Space.height(30),
                Align(
                  alignment: Alignment.center,
                  child: TypoGraphy(
                    text: controller.isEdit.value
                        ? "Edit sub-project"
                        : "Create sub-project",
                    level: 8,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                  child: Align(
                    alignment: Alignment.center,
                    child: TypoGraphy(
                      text:
                          "The sub-project helps you categorize the main project.",
                      level: 3,
                      fontWeight: FontWeight.w400,
                      color: AppTheme.grey,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                Space.height(10),
                Expanded(
                  child: SingleChildScrollView(
                    keyboardDismissBehavior:
                        ScrollViewKeyboardDismissBehavior.onDrag,
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: MySize.size0 ?? 25),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Space.height(20),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.size45 ?? 25),
                            child: AppTextField(
                              controller:
                                  controller.subProjectNameController.value,
                              labelText: "Sub-Project Name",
                              maxLength: 50,
                              onChangedValue: (p0) {
                                controller.subProjectNameController.refresh();
                                controller.update();
                              },
                              textCapitalization: TextCapitalization.sentences,
                            ),
                          ),
                          Obx(
                                () => (controller.subProjectNameController.value.text
                                .length >=
                                50)
                                ? Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal:
                                  MySize.getScaledSizeWidth(45)).copyWith(top: MySize.getScaledSizeWidth(10)),
                              child: TypoGraphy(
                                text: "Maximum 50 characters allowed",
                                level: 2,
                                fontWeight: FontWeight.w400,
                                color: AppTheme.red,
                              ),
                            )
                                : SizedBox.shrink(),
                          ),
                          ExpansionTile(
                            childrenPadding: EdgeInsets.symmetric(
                                horizontal: MySize.size45 ?? 25),
                            iconColor: AppTheme.whiteWithBase,
                            collapsedIconColor: AppTheme.whiteWithBase,
                            tilePadding: EdgeInsets.symmetric(
                                horizontal: MySize.size45 ?? 25),
                            shape: const RoundedRectangleBorder(
                              side: BorderSide.none,
                            ),
                            title: TypoGraphy(
                              text: "Add More (Optional)",
                              level: 12,
                              fontWeight: FontWeight.w500,
                            ),
                            children: [
                              GetBuilder<CreateProjectController>(
                                builder: (controller) => AppTextField(
                                  textInputAction: TextInputAction.newline,
                                  textInputType: TextInputType.multiline,
                                  textCapitalization:
                                      TextCapitalization.sentences,
                                  height: MySize.size140,
                                  focusNode: controller
                                      .subProjectDescriptionFocusNode.value,
                                  controller: controller
                                      .subProjectDescriptionController.value,
                                  labelText: controller
                                              .subProjectDescriptionFocusNode
                                              .value
                                              .hasFocus ||
                                          controller
                                              .subProjectDescriptionController
                                              .value
                                              .text
                                              .isNotEmpty
                                      ? "Description"
                                      : null,
                                  hintText: controller
                                          .subProjectDescriptionFocusNode
                                          .value
                                          .hasFocus
                                      ? ""
                                      : "Description",
                                  maxLines: 5,
                                  maxLength: 250,
                                ),
                              ),
                              Space.height(6),
                              Obx(
                                () => Align(
                                  alignment: Alignment.centerRight,
                                  child: TypoGraphy(
                                    text:
                                        "${controller.charCountSubProject}/${controller.maxCharsSubProject}",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                              Space.height(20),
                              LocationInputField(
                                controller: controller.subLocationController,
                                key: controller.subLocationFieldKey,
                                selectedSuggestion: controller.subMeetLocation,
                                onFocused: () {
                                  controller.scrollToSubLocationField(context);
                                },
                              ),
                              Space.height(5),
                              TypoGraphy(
                                text:
                                "Add a location to find specific sub project easily.",
                                level: 3,
                                fontWeight: FontWeight.w300,
                                fontStyle: FontStyle.italic,
                                color: AppTheme.grey,
                              ),
                              Space.height(30),
                              Obx(
                                () => GestureDetector(
                                  onTap: () {
                                    if (controller
                                            .selectedImageSubProject.value ==
                                        "") {
                                      // Get.dialog(
                                      ImageTypeSelectPopup(
                                        onCamera: () async {
                                          Get.back();
                                          File? pick = await CommonFunction
                                              .pickImageFromCamera();

                                          if (pick != null) {
                                            if (await controller
                                                .isValidImage(pick)) {
                                              controller.callApiForProjectImage(
                                                  context: context,
                                                  imageFile: pick.path,
                                                  isForSub: true);

                                              // controller.selectedImage.value = pick;
                                            } else {
                                              CommonFunction.showCustomSnackbar(
                                                  message:
                                                      "Please upload a JPEG or PNG file under 2MB.",
                                                  backgroundColor: AppTheme.red,
                                                  isError: true);
                                            }
                                            // controller.selectedImageSubProject.value = pick;
                                          }
                                        },
                                        onGallery: () async {
                                          Get.back();
                                          File? pick = await CommonFunction
                                              .pickImageFromGallery();
                                          if (pick != null) {
                                            if (await controller
                                                .isValidImage(pick)) {
                                              controller.callApiForProjectImage(
                                                  context: context,
                                                  imageFile: pick.path,
                                                  isForSub: true);

                                              // controller.selectedImage.value = pick;
                                            } else {
                                              CommonFunction.showCustomSnackbar(
                                                  message:
                                                      "Please upload a JPEG or PNG file under 2MB.",
                                                  backgroundColor: AppTheme.red,
                                                  isError: true);
                                            }
                                            // controller.selectedImageSubProject.value = pick;
                                          }
                                        },
                                      );
                                      // );
                                    }
                                  },
                                  child: controller
                                              .selectedImageSubProject.value !=
                                          ""
                                      ? ProfileImageWithEditIcon(
                                          top: -8,
                                          right: -10,
                                          clipOval: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(14),
                                              child: NetworkImageComponent(
                                                imageUrl: controller
                                                    .selectedImageSubProject
                                                    .value,
                                                simmerHeight:
                                                    MySize.getScaledSizeHeight(
                                                        220),
                                                height: MySize.size220,
                                                width: double.infinity,
                                                // aspectRatio: 430/183,
                                              )),
                                          editIconPath: AppImage.closeImage,
                                          color: AppTheme.baseBlack,
                                          padding: 10,
                                          imageSize: MySize.size120 ?? 120,
                                          iconSize: MySize.size32 ?? 34,
                                          blurSigma: MySize.size35 ?? 30,
                                          onEditIconTap: () {
                                            controller.selectedImageSubProject
                                                .value = "";
                                          },
                                        )
                                      : Container(
                                          height: MySize.size220,
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(14),
                                            color: Theme.of(context).brightness == Brightness.dark ? Color(0xFF181A20) : AppTheme.lightGrey,
                                          ),
                                          child: DottedBorder(
                                            borderType: BorderType.RRect,
                                            dashPattern: const [8, 6],
                                            color: AppTheme.grey,
                                            borderPadding: EdgeInsets.all(
                                                MySize.size1 ?? 1),
                                            radius: Radius.circular(14),
                                            child: Center(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                    AppImage.uploadImage,
                                                    height: MySize.size50,
                                                    width: MySize.size50,
                                                  ),
                                                  Space.height(12),
                                                  TypoGraphy(
                                                    text: "Upload Image",
                                                    level: 12,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                  Space.height(4),
                                                  TypoGraphy(
                                                    text:
                                                        "Formats: JPEG, PNG",
                                                    level: 3,
                                                    fontWeight: FontWeight.w300,
                                                    color: AppTheme.grey,
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                ),
                              ),
                              Space.height(30),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.size45 ?? 25),
                            child: TypoGraphy(
                              text: "Select Sub-project Visibility*",
                              level: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Space.height(6),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.size45 ?? 25),
                            child: TypoGraphy(
                              text:
                                  "Public sub-projects are visible to everyone, while private sub-projects are limited to you and your team. ",
                              level: 3,
                              fontWeight: FontWeight.w400,
                              color: AppTheme.grey,
                            ),
                          ),
                          Obx(
                            () {
                              // Auto-update subproject value when visibilityValue changes
                              if (controller.visibilityValue.value == 1) {
                                controller.visibilityValueSubProject.value = 1;
                                controller.visibilityNameSubProject.value =
                                    "Private";
                              }

                              return Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size30 ?? 25),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Transform.scale(
                                      scale: 1.2,
                                      child: Radio(
                                        activeColor: AppTheme.primary1,
                                        value: 0,
                                        onChanged: (controller
                                                    .visibilityValue.value ==
                                                1)
                                            ? null
                                            : (value) {
                                                controller
                                                    .visibilityValueSubProject
                                                    .value = value as int;
                                                controller
                                                    .visibilityNameSubProject
                                                    .value = "Public";
                                                controller.update();
                                              },
                                        groupValue: controller
                                            .visibilityValueSubProject.value,
                                      ),
                                    ),
                                    TypoGraphy(
                                      text: "Public",
                                      textStyle: TextStyle(
                                        fontSize:
                                            MySize.getScaledSizeHeight(16),
                                        fontWeight: FontWeight.w400,
                                        color: (controller
                                                    .visibilityValue.value ==
                                                1)
                                            ? Colors
                                                .grey // Grey out if disabled
                                            : Theme.of(context).brightness == Brightness.dark ? AppTheme.white : Colors.black,
                                      ),
                                    ),
                                    SizedBox(
                                        width: MySize.getScaledSizeWidth(40)),
                                    Transform.scale(
                                      scale: 1.2,
                                      child: Radio(
                                        activeColor: AppTheme.primary1,
                                        value: 1,
                                        onChanged: (value) {
                                          controller.visibilityValueSubProject
                                              .value = value as int;
                                          controller.visibilityNameSubProject
                                              .value = "Private";
                                          controller.update();
                                        },
                                        groupValue: controller
                                            .visibilityValueSubProject.value,
                                      ),
                                    ),
                                    TypoGraphy(
                                      text: "Private",
                                      textStyle: TextStyle(
                                        fontSize:
                                            MySize.getScaledSizeHeight(16),
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                          Obx(
                                () => (controller.visibilityNameSubProject.value != controller.originalVisibilityValue.value && controller.isEdit.value) ?
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(45)),
                              child: TypoGraphy(
                                text: "* Are you sure you want to change the visibility to ${controller.visibilityNameSubProject.value}?  All your content will also be visible to ${controller.visibilityNameSubProject.value}",
                                level: 3,
                                color: AppTheme.red,
                              ),
                            ) : SizedBox(),
                          ),
                          Space.height(20),
                          Obx(
                            () => Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Buttons(
                                  width: MySize.size198,
                                  buttonText: "Save",
                                  buttonTextLevel: 4,
                                  isLoading: controller.isLoading.value,
                                  onTap: () {
                                    HapticFeedback.lightImpact();

                                    String subProjectName = controller
                                        .subProjectNameController.value.text
                                        .trim();
                                    bool isVisibilityValid = controller
                                            .visibilityValueSubProject.value !=
                                        -1;
                                    bool isLocationFilled = controller
                                        .subLocationController.value.text
                                        .trim()
                                        .isNotEmpty;
                                    bool isLocationValid = !isLocationFilled ||
                                        controller.subMeetLocation.value ==
                                            controller
                                                .subLocationController.value.text;
                                    print("isEdit == ${controller.isEdit.value}");
                                    print("only isEdit == ${isEdit}");
                                    if (isVisibilityValid &&
                                        subProjectName.isNotEmpty) {
                                      if (controller.isEdit.value) {
                                        controller.callApiForUpdateOneProject(
                                            context: context);
                                      } else if (isEdit) {
                                        if (isLocationValid) {
                                          controller.callApiForCreateProject(
                                              context: context);
                                        } else {
                                          CommonFunction.showCustomSnackbar(
                                            message:
                                                "Please Choose Correct Location",
                                            backgroundColor: AppTheme.red,
                                            isError: true,
                                          );
                                        }
                                      } else if (isSubCreate) {
                                        if (isLocationValid) {
                                          controller.callApiForCreateSubProject(
                                              context: context);
                                        } else {
                                          CommonFunction.showCustomSnackbar(
                                            message:
                                                "Please Choose Correct Location",
                                            backgroundColor: AppTheme.red,
                                            isError: true,
                                          );
                                        }
                                      } else {
                                        if (subProjectIndex != null) {
                                          // Update existing subproject
                                          controller
                                                  .subProjects[subProjectIndex] =
                                              SubProjectData(
                                                  name: subProjectName,
                                                  description: controller
                                                      .subProjectDescriptionController
                                                      .value
                                                      .text,
                                                  image: controller
                                                      .selectedImageSubProject
                                                      .value,
                                                  isPrivate: controller
                                                          .visibilityValueSubProject
                                                          .value ==
                                                      1,
                                                  location: controller
                                                      .subMeetLocation.value);

                                          // Update only the related controller
                                          controller
                                              .subProjectNameControllers[
                                                  subProjectIndex]
                                              .text = subProjectName;
                                          controller.update();
                                          // Get.back();
                                          Navigator.pop(context);
                                        } else {
                                          if (isLocationValid) {

                                            var newController =
                                                TextEditingController(
                                                    text: subProjectName);

                                            controller.subProjects.add(SubProjectData(
                                                name: subProjectName,
                                                description: controller
                                                    .subProjectDescriptionController
                                                    .value
                                                    .text,
                                                image: controller
                                                    .selectedImageSubProject.value
                                                    .toString(),
                                                isPrivate: controller
                                                        .visibilityValueSubProject
                                                        .value ==
                                                    1,
                                                location: controller
                                                    .subMeetLocation.value));

                                            // Add the new controller to the list
                                            controller.subProjectNameControllers
                                                .add(newController);
                                            // Get.back();
                                            Navigator.pop(context);
                                          } else {
                                            CommonFunction.showCustomSnackbar(
                                              message:
                                                  "Please Choose Correct Location",
                                              backgroundColor: AppTheme.red,
                                              isError: true,
                                            );
                                          }
                                        }
                                        // Get.back();
                                      }
                                    } else {
                                      if (controller.subProjectNameController
                                              .value.text.isNotEmpty &&
                                          subProjectName.isEmpty) {
                                        CommonFunction.showCustomSnackbar(
                                          message: "Space not allowed",
                                          isError: true,
                                          backgroundColor: Color(0xFFEF3B41),
                                        );
                                      } else {
                                        CommonFunction.showCustomSnackbar(
                                          message:
                                              "Visibility and name are required.",
                                          isError: true,
                                          backgroundColor: Color(0xFFEF3B41),
                                        );
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                          Space.height(30),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}
