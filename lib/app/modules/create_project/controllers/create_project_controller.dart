import 'dart:developer';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:incenti_ai/utillites/app_theme.dart';
import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../main.dart';
import '../../../../models/app_project_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/common_function.dart';
import '../../../routes/app_pages.dart';

class CreateProjectController extends GetxController {
  Rx<TextEditingController> projectNameController = TextEditingController().obs;
  Rx<TextEditingController> subProjectNameController =
      TextEditingController().obs;
  TextEditingController subLocationController = TextEditingController();
  Rx<TextEditingController> locationController = TextEditingController().obs;
  Rxn<String> subMeetLocation = Rxn(null);
  Rxn<String> meetLocation = Rxn(null);
  final GlobalKey subLocationFieldKey = GlobalKey();
  final GlobalKey locationFieldKey = GlobalKey();
  var subProjectNameControllers =
      <TextEditingController>[].obs; // List of controllers for text fields

  Rx<TextEditingController> projectDescriptionController =
      TextEditingController().obs;
  Rx<TextEditingController> subProjectDescriptionController =
      TextEditingController().obs;
  Rx<TextEditingController> subProjectController = TextEditingController().obs;
  Rx<FocusNode> projectDescriptionFocusNode = FocusNode().obs;
  FocusNode projectNameFocusNode = FocusNode();
  Rx<FocusNode> subProjectDescriptionFocusNode = FocusNode().obs;
  RxInt charCount = 0.obs;
  RxInt maxChars = 250.obs;
  RxInt charCountSubProject = 0.obs;
  RxInt maxCharsSubProject = 250.obs;
  RxInt visibilityValue = (-1).obs;
  RxString visibilityName = "".obs;
  RxString originalVisibilityValue = "".obs;
  var subProjects = <SubProjectData>[].obs;
  RxBool isEdit = false.obs;
  // var subProjectsData = <SubProjectData>[].obs;
  RxInt visibilityValueSubProject = (-1).obs;
  RxString projectId = "-1".obs;
  RxString visibilityNameSubProject = "".obs;
  var arg = Get.arguments;
  RxString selectedImage = "".obs;
  RxString selectedImageSubProject = "".obs;
  ApiManager apiManager = ApiManager();
  RxBool isLoading = false.obs;
  Rx<Project> getProjectDetailData = Project().obs;

  void setIsEdit(bool value,String projectID,int projectVisibilityValue,bool mainVisibilityValue) {
    isEdit.value = value;
    projectId.value = projectID;
    if(mainVisibilityValue) {
      visibilityValue.value = projectVisibilityValue;
    } else {
      visibilityValueSubProject.value = projectVisibilityValue;
    }
  }

  void scrollToSubLocationField(BuildContext context) {
    Scrollable.ensureVisible(
      subLocationFieldKey.currentContext!,
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOut,
    );
  }

  void scrollToLocationField(BuildContext context) {
    Scrollable.ensureVisible(
      locationFieldKey.currentContext!,
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOut,
    );
  }

  @override
  void onInit() {
    // TODO: implement onInit
    projectDescriptionFocusNode.value.addListener(() {
      update(); // Calls update() to refresh the UI
    });
    subProjectDescriptionFocusNode.value.addListener(() {
      update(); // Calls update() to refresh the UI
    });
    projectDescriptionController.value.addListener(
      () {
        updateCharCount();
        update();
      },
    );
    subProjectDescriptionController.value
        .addListener(updateCharCountSubProject);
    if (arg != null && arg["projectId"] != null) {
      projectId.value = arg["projectId"].toString();
    }
    if (isEdit.value) {
      callApiForGetOneProject(context: Get.context!);
    }
    if (arg != null && arg["isEdit"] != null) {
      callApiForGetOneProject(context: Get.context!).then(
        (value) {
          projectNameController.value.text = getProjectDetailData.value.name;
          projectDescriptionController.value.text =
              getProjectDetailData.value.description;
          selectedImage.value = getProjectDetailData.value.image;
          meetLocation.value = getProjectDetailData.value.location;
          locationController.value.text = getProjectDetailData.value.location ?? "";
          subProjects.value = getProjectDetailData.value.subProjects;
          if (getProjectDetailData.value.isPrivate == true) {
            visibilityName.value = "Private";
            originalVisibilityValue.value = "Private";
            visibilityValue.value = 1;
          } else {
            visibilityName.value = "Public";
            originalVisibilityValue.value = "Public";
            visibilityValue.value = 0;
          }
          // **Initialize controllers for existing subprojects**
          for (var subProject in subProjects) {
            var controller = TextEditingController();
            controller.text = subProject.name; // Set the existing name
            subProjectNameControllers.add(controller);
          }
        },
      );
    }
    super.onInit();
  }

  Future<bool> isValidImage(File file) async {
    // Get file size in bytes
    // int fileSize = await file.length();
    // if (fileSize > 2 * 1024 * 1024) {
    //   // 2MB limit
    //   return false;
    // }

    // Get file extension
    String fileExtension = file.path.split('.').last.toLowerCase();
    if (fileExtension != 'jpg' &&
        fileExtension != 'jpeg' &&
        fileExtension != 'png') {
      return false;
    }

    return true;
  }

  void updateCharCount() {
    charCount.value =
        projectDescriptionController.value.text.length.clamp(0, maxChars.value);
  }

  void updateCharCountSubProject() {
    charCountSubProject.value = subProjectDescriptionController
        .value.text.length
        .clamp(0, maxCharsSubProject.value);
  }

  @override
  void dispose() {
    subProjectDescriptionController.value.removeListener(updateCharCount);
    projectDescriptionController.value.removeListener(updateCharCount);
    projectDescriptionController.value.dispose();
    subProjectDescriptionController.value.dispose();
    super.dispose();
  }

  callApiForCreateProject({required BuildContext context}) async {
    isLoading.value = true;
    bool isPrivate = visibilityName.value.toLowerCase() == "private";
    bool subIsPrivate =
        visibilityNameSubProject.value.toLowerCase() == "private";
    try {
      Map<String, dynamic> dict = {};
      if (arg != null && arg["isEdit"] != null) {
        dict = {
          "name": subProjectNameController.value.text.trim(),
          "isPrivate": isPrivate ? isPrivate : subIsPrivate,
          "ParentId": projectId.value
        };
        if(selectedImageSubProject.value != "" && selectedImageSubProject.value.isNotEmpty){
          dict['image'] = selectedImageSubProject.value;
        }
        if(subMeetLocation.value != null && (subMeetLocation.value ?? "").isNotEmpty){
          dict['location'] = subMeetLocation.value;
        }
        if(subProjectDescriptionController.value.text.isNotEmpty){
          dict['description'] = subProjectDescriptionController.value.text;
        }

      } else {
        dict = {
          "name": projectNameController.value.text.trim(),
          "isPrivate": isPrivate,
        };
        if (projectDescriptionController.value.text.isNotEmpty) {
          dict['description'] = projectDescriptionController.value.text;
        }
        if (selectedImage.value.isNotEmpty) {
          dict['image'] = selectedImage.value;
        }
        if(meetLocation.value != null && (meetLocation.value ?? "").isNotEmpty){
          dict['location'] = meetLocation.value;
        }
        // Adding subProjects if not empty
        if (subProjects.isNotEmpty) {
          dict["subProjects"] = subProjects.map((subProject) {
            Map<String, dynamic> subProjectDict = {
              "name": subProject.name,
              "isPrivate": isPrivate ? isPrivate : subProject.isPrivate,
            };

            if(subProject.location != "" && subProject.location != null){
              subProjectDict['location'] = subProject.location;
            }

            if (subProject.image != null && subProject.image != "") {
              subProjectDict["image"] = subProject.image;
            }

            if (subProject.description != null &&
                subProject.description!.isNotEmpty) {
              subProjectDict["description"] = subProject.description;
            }

            return subProjectDict;
          }).toList();
        }
      }
      print("dict => ${dict}");
      apiManager.callApi(
        APIS.project.createMyProject,
        params: dict,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            isLoading.value = false;
            if(arg != null && arg["isEdit"] != null) {
              callApiForGetOneProject(context: Get.context!).then((value) {
                Get.back();
              },);
            } else {
              if(arg != null && arg["isFromSignUp"] != null) {
                Get.offAllNamed(Routes.Bottom_Bar, arguments: {"index": 1,"isFirstTime": true});
              } else {
                // if (arg != null &&
                //     arg['isBottom'] != null) {
                //   print("is bottom => ${arg['isBottom']}");
                //   Get.offAllNamed(Routes.Bottom_Bar, arguments: {"index": 1});
                // } else {
                //   print("else part");
                //
                //   Get.offAndToNamed(Routes.project_detail, arguments: {
                //     "projectId": response["project"]["id"]
                //   });
                //   // Get.back();
                // }
                Get.offAndToNamed(Routes.project_detail, arguments: {
                  "projectId": response["project"]["id"],
                if (arg != null && arg['isBottom'] != null)
                  "isBottom": arg['isBottom']
                });
              }
            }
            CommonFunction.showCustomSnackbar(message: response['message']);
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          isLoading.value = false;
        },
      );
    } catch (e) {
      log("Exception: $e");
      isLoading.value = false;
    }
  }

  callApiForCreateSubProject({required BuildContext context}) async {
    isLoading.value = true;
    bool isPrivate = visibilityName.value.toLowerCase() == "private";
    bool subIsPrivate =
        visibilityNameSubProject.value.toLowerCase() == "private";
    try {
      Map<String, dynamic> dict = {};
        dict = {
          "name": subProjectNameController.value.text.trim(),
          "isPrivate": isPrivate ? isPrivate : subIsPrivate,
          "ParentId": projectId.value
        };
        if(selectedImageSubProject.value != "" && selectedImageSubProject.value.isNotEmpty){
          dict['image'] = selectedImageSubProject.value;
        }
      if(subMeetLocation.value != null && (subMeetLocation.value ?? "").isNotEmpty){
        dict['location'] = subMeetLocation.value;
      }
        if(subProjectDescriptionController.value.text.isNotEmpty){
          dict['description'] = subProjectDescriptionController.value.text;
        }
      apiManager.callApi(
        APIS.project.createMyProject,
        params: dict,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            isLoading.value = false;
           // Get.back();
            subProjectNameController.value.clear();
            subProjectDescriptionController.value.clear();
            subMeetLocation.value = null;
            subLocationController.clear();
            selectedImageSubProject.value = "";
            Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          log("Error: $statusCode");
          isLoading.value = false;
        },
      );
    } catch (e) {
      log("Exception: $e");
      isLoading.value = false;
    }
  }


  callApiForProjectImage(
      {required BuildContext context,
      required String imageFile,
      bool isForSub = false}) async {
    app.resolve<CustomDialogs>().showCircularDialog(context);

    try {
      Map<String, dynamic> dict = {
        "file": await MultipartFile.fromFile(imageFile,
            filename: imageFile.split('/').last.trim())
      };

      FormData formData = FormData.fromMap(dict);

      apiManager.callApi(
        APIS.generalImageUpload.imageUpload,
        params: formData,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            String imageUrl =
                response['data'][0]['link']; // Extract URL from response
            if (isForSub) {
              selectedImageSubProject.value = imageUrl;
            } else {
              selectedImage.value = imageUrl;
            }
            app.resolve<CustomDialogs>().hideCircularDialog(context);
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          app.resolve<CustomDialogs>().hideCircularDialog(context);
        },
      );
    } catch (e) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log("Exception: $e");
    }
  }

  Future<void> callApiForGetOneProject({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/projects/${projectId.value}", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            getProjectDetailData.value = Project.fromJson(response["data"]);
            if(isEdit.value) {
              subProjectNameController.value.text = getProjectDetailData.value.name;
              subProjectDescriptionController.value.text = getProjectDetailData.value.description;
              selectedImageSubProject.value = getProjectDetailData.value.image;
              subMeetLocation.value = getProjectDetailData.value.location;
              subLocationController.text = getProjectDetailData.value.location ?? "";
              locationController.value.text = getProjectDetailData.value.location ?? "";

              if (getProjectDetailData.value.isPrivate == true) {
                visibilityNameSubProject.value = "Private";
                originalVisibilityValue.value = "Private";
                visibilityValueSubProject.value = 1;
              } else {
                visibilityNameSubProject.value = "Public";
                originalVisibilityValue.value = "Public";
                visibilityValueSubProject.value = 0;
              }
            }
            subProjects.clear();
            subProjectNameControllers.clear();
            subProjects.assignAll(getProjectDetailData.value.subProjects);
            subProjects.refresh();
            for (var subProject in subProjects) {
              var controller = TextEditingController();
              controller.text = subProject.name; // Set the existing name
              subProjectNameControllers.add(controller);
            }
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForUpdateOneProject({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/projects/${projectId.value}", APIType.PATCH);
    bool isPrivate = visibilityName.value.toLowerCase() == "private";
    bool subIsPrivate =
        visibilityNameSubProject.value.toLowerCase() == "private";
    Map<String, dynamic> dict = {
      "isPrivate": isEdit.value ? subIsPrivate : isPrivate,
    };
    if(isEdit.value) {
      if (subProjectNameController.value.text.isNotEmpty) {
        dict['name'] = subProjectNameController.value.text.trim();
      }
      if (subProjectDescriptionController.value.text.isNotEmpty) {
        dict['description'] = subProjectDescriptionController.value.text;
      } else {
        dict['description'] = null;
      }
      if (selectedImageSubProject.value.isNotEmpty) {
        dict['image'] = selectedImageSubProject.value;
      } else {
        dict['image'] = null;
      }
      if (subMeetLocation.value != null && (subMeetLocation.value ?? "").isNotEmpty) {
        dict['location'] = subMeetLocation.value;
      } else {
        dict['location'] = null;
      }
    } else {
      if (projectNameController.value.text.isNotEmpty) {
        dict['name'] = projectNameController.value.text.trim();
      }
      if (projectDescriptionController.value.text.isNotEmpty) {
        dict['description'] = projectDescriptionController.value.text;
      } else {
        dict['description'] = null;
      }
      if (meetLocation.value != null && (meetLocation.value ?? "").isNotEmpty) {
        dict['location'] = meetLocation.value;
      } else {
        dict['location'] = null;
      }
      if (selectedImage.value.isNotEmpty) {
        dict['image'] = selectedImage.value;
      } else {
        dict['image'] = null;
      }
    }

    return apiManager.callApi(
      getPost,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            if(isEdit.value) {
              CommonFunction.showCustomSnackbar(
                  message: "Sub Project Updated Successfully");
            } else {
              CommonFunction.showCustomSnackbar(
                  message: "Project Updated Successfully");
            }
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
            message: statusCode,
            isError: true,
            backgroundColor: AppTheme.red);
        isLoading.value = false;
      },
    );
  }
}
