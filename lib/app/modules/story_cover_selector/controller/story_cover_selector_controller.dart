import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:crop_your_image/crop_your_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'package:incenti_ai/app/modules/profile_view/controllers/profile_view_controller.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';
import 'package:incenti_ai/models/get_all_highlights_model.dart';
import 'package:incenti_ai/services/api_manager.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:path_provider/path_provider.dart';
import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../models/story_model.dart';
import 'package:dio/dio.dart' as dio;

import '../../../../utillites/current_user.dart';

class ImageType {
  final File? file;
  final String? networkUrl;
  final Uint8List? bytes;

  ImageType({this.file, this.networkUrl, this.bytes});

  bool get isFile => file != null;
  bool get isNetwork => networkUrl != null;
  bool get hasBytes => bytes != null;
  bool get isValid => isFile || isNetwork;
}

class StoryCoverSelectorController extends GetxController {
  var args = Get.arguments;
  final Rx<ImageType?> selectedImageSource = Rx<ImageType?>(null);
  final Rx<ImageType?>  finalSelectedImageSource = Rx<ImageType?>(null);
  final RxMap<String, Uint8List> imageByteCache = RxMap<String, Uint8List>();
  final Rx<Uint8List?> croppedImageData = Rxn<Uint8List>();
  final RxList<OneStory> selectedStories = <OneStory>[].obs;
  Rx<Highlight> highlight = Highlight().obs;
  final CropController cropController = CropController();
  Rx<TextEditingController> highlightController = TextEditingController().obs;
  final RxBool isLoading = true.obs;
   RxBool showTitleView = true.obs;
   RxBool isUploading = false.obs;
  final RxString loadingError = ''.obs;
  RxInt projectId = 0.obs;
  RxInt subProjectId = 0.obs;
  RxBool isFromHighlight = false.obs;
   RxInt selectedTabIndex = 0.obs;
  RxList<OneStory> userStories = <OneStory>[].obs;
  ScrollController scrollController = ScrollController();

  @override
  Future<void> onInit() async {
    super.onInit();
    projectId.value = args['projectId'] ?? 0;
    subProjectId.value = args['subProjectId'] ?? 0;

    log('id ===> ${projectId.value} && ${args['projectId'] }  && ${subProjectId.value}&& ${ args['subProjectId'] }');
    isFromHighlight.value = args['isFromHighlight'] ?? false;
    selectedStories.value = args['selectedStories'] ?? [];
    highlight.value = args['highlight'] ?? Highlight();

    if(selectedStories.isNotEmpty){
      final image = selectedStories.first.mediaType == StoryType.video.name ? selectedStories.first.thumbnailPath ?? '' : selectedStories.first.overlayImage ?? '';
      setNetworkImage(image);
      croppedImageData.value =  await loadImageBytes(image) ;
      isLoading.value = false;
    }

    if((highlight.value.stories?? []).isNotEmpty && isFromHighlight.value){
      final image = (highlight.value.stories?? []).first.story?.mediaType == StoryType.video.name ? (highlight.value.stories?? []).first.story?.thumbnailPath ?? '' : (highlight.value.stories?? []).first.story?.overlayImage ?? '';
      croppedImageData.value = await loadImageBytes(highlight.value.image ?? '');
      setNetworkImage(image);
      highlightController.value.text = highlight.value.title ?? '';
      await callApiForGetCurrentUserStory();
      isLoading.value = false;
    }
    isLoading.value = false;

  }

  Future<Uint8List?> loadImageBytes(String imagePath) async {
    try {
      if (imageByteCache.containsKey(imagePath)) {
        return imageByteCache[imagePath];
      }

      if (imagePath.startsWith('http')) {
        final response = await http.get(Uri.parse(imagePath));
        if (response.statusCode == 200) {
          return response.bodyBytes;
        }
      } else {
        final file = File(imagePath);
        if (await file.exists()) {
          return await file.readAsBytes();
        }
      }
    } catch (e) {
    }
    return null;
  }

  Future<void> pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      final file = File(image.path);
      final bytes = await file.readAsBytes();

      selectedImageSource.value = ImageType(
          file: file,
          bytes: bytes,
          networkUrl: file.path
      );
    }
  }

  Future<void> setNetworkImage(String imageUrl) async {
    if (imageByteCache.containsKey(imageUrl)) {
      selectedImageSource.value = ImageType(
          networkUrl: imageUrl,
          bytes: imageByteCache[imageUrl]
      );
      return;
    }

    final bytes = await loadImageBytes(imageUrl);
    if (bytes != null) {
      imageByteCache[imageUrl] = bytes;
      selectedImageSource.value = ImageType(
          networkUrl: imageUrl,
          bytes: bytes
      );
    }
  }

  void onCropComplete(CropResult result) {
    switch (result) {
      case CropSuccess(:final croppedImage):
        croppedImageData.value = null;
        croppedImageData.value = croppedImage;
       showTitleView.value = true;
      case CropFailure(:final cause):
        Get.snackbar(
          'Error',
          'Failed to crop image: $cause',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
    }
  }

  Future<List<String>> callApiForUploadMedia({
    required BuildContext context,
    required List<Uint8List> imageDataList,
  }) async {
    if (imageDataList.isEmpty) return [];

    Completer<List<String>> completer = Completer<List<String>>();
    List<String> tempFilePaths = [];

    try {
      for (int i = 0; i < imageDataList.length; i++) {
        final tempDir = await getTemporaryDirectory();
        final file = File('${tempDir.path}/image_${DateTime.now().millisecondsSinceEpoch}_$i.jpg');
        await file.writeAsBytes(imageDataList[i]);
        tempFilePaths.add(file.path);
      }

      List<dio.MultipartFile> multipartFiles = [];

      for (String imagePath in tempFilePaths) {
        if (!File(imagePath).existsSync()) {
          throw Exception("File does not exist: $imagePath");
        }

        log('Preparing to upload file: $imagePath');
        multipartFiles.add(
          await dio.MultipartFile.fromFile(
            imagePath,
            filename: imagePath.split('/').last.trim(),
          ),
        );
      }

      dio.FormData formData = dio.FormData.fromMap({
        "file": multipartFiles,
      });

      ApiManager().callApi(
        APIS.generalImageUpload.imageUpload,
        params: formData,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            List<String> uploadedUrls = [];
            List<dynamic> uploadedImages = response['data'];
            for (var imageData in uploadedImages) {
              String imageUrl = imageData['link'];
              uploadedUrls.add(imageUrl);
              log('Uploaded file link: $imageUrl');
            }

            completer.complete(uploadedUrls);
          } else {
            completer.completeError("API returned non-success status");
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          completer.completeError("API Error: $message");
        },
      );

      return completer.future;
    } catch (e) {
      log("Exception in upload: $e");
      if (!completer.isCompleted) {
        completer.completeError(e);
      }
      return completer.future;
    } finally {
      completer.future.whenComplete(() async {
        for (String path in tempFilePaths) {
          try {
            await File(path).delete();
          } catch (e) {
            log("Error deleting temporary file: $e");
          }
        }
      });
    }
  }

  void toggleSelection(OneStory story) {
    story.isSelected.value = !story.isSelected.value;
  }

  Future<void> callApiForCreateHighlight({
    required BuildContext context,
    required Uint8List croppedImage,
  }) async {
    FocusScope.of(context).unfocus();
    try {
      List<String> uploadedImageUrls = await callApiForUploadMedia(
        context: context,
        imageDataList: [croppedImage],
      );

      Map<String, dynamic> dict = {
        "title": highlightController.value.text.trim(),
        "StoryIds": selectedStories.map((e) => e.id).toList(),
      };

      if (uploadedImageUrls.isNotEmpty) {
        dict["image"] = uploadedImageUrls.first;
      }

      if (subProjectId.value != 0) {
        dict["ProjectId"] = subProjectId.value;
      } else if (projectId.value != 0) {
        dict["ProjectId"] = projectId.value;
      }

      ApiManager().callApi(
        APIS.story.highlights,
        params: dict,
        successCallback: (response, message) async {
          if(Get.isRegistered<SubProjectDetailViewController>()){
            await Get.find<SubProjectDetailViewController>().callApiForGetHighlightsOfSubProject(context: context);
          }else if(Get.isRegistered<ProjectDetailViewController>(tag: projectId.value.toString())){
            await Get.find<ProjectDetailViewController>(tag: projectId.value.toString()).callApiForGetHighlightsOfProject(context: context);
          }else {
            await Get
                .find<ProfileViewController>()
                .callApiForGetHighlightsOfProfile(context: context);
          }
          isUploading.value = false;
          Get.back();
          Get.back();
          CommonFunction.showCustomSnackbar(message: response['message']);
        },
        failureCallback: (message, statusCode) {
          isUploading.value = false;
          CommonFunction.showCustomSnackbar(message: statusCode,isError: true,backgroundColor: AppTheme.red);
        },
      );
    } catch (e) {
      log("Error creating highlight: $e");
    }
  }

  Future<void> callApiForGetCurrentUserStory(){
    final ApiModel getStories =
    ApiModel("/stories/users/${CurrentUser.user.id}", APIType.GET);
    return ApiManager().callApi(
      getStories,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            userStories.value = List<OneStory>.from(response['data']['data']!.map((x) => OneStory.fromJson(x)));
            userStories.value = userStories.reversed.toList();
            for (var element in userStories) {
           if((highlight.value.stories ?? []).any((e) => e.story?.id == element.id)){
              element.isSelected.value = true;
            }
            }
            log('sdsds ===> ${userStories.toJson()}');
          }
        } catch (error) {
          log('error === $error');
        }
      },
      failureCallback: (message, statusCode) {
        log('error === $message');
      },
    );
  }

  Future<void> callApiForUpdateHighlight({
    required BuildContext context,
    required Uint8List croppedImage,
  }) async {
    FocusScope.of(context).unfocus();
    try {
      List<String> uploadedImageUrls = await callApiForUploadMedia(
        context: context,
        imageDataList: [croppedImage],
      );

      final oldStoryIds = highlight.value.stories?.map((e) => e.story?.id).whereType<int>().toList() ?? [];
      final currentSelectedIds = userStories.where((element) => element.isSelected.value,).map((e) => e.id).toList();

      final addedStoryIds = getDifference(oldStoryIds, currentSelectedIds);
      final removedStoryIds = getDifference(currentSelectedIds, oldStoryIds);

      Map<String, dynamic> dict = {
        "title": highlightController.value.text.trim(),
        "AddStoriesIds": addedStoryIds,
        "RemoveStoriesIds": removedStoryIds,
      };

      if (uploadedImageUrls.isNotEmpty) {
        dict["image"] = uploadedImageUrls.first;
      }

      if (subProjectId.value != 0) {
        dict["ProjectId"] = subProjectId.value;
      } else if (projectId.value != 0) {
        dict["ProjectId"] = projectId.value;
      }

    ApiManager().callApi(
      ApiModel("/highlights/${highlight.value.id.toString()}", APIType.PATCH),
        params: dict,
        successCallback: (response, message) async {
          if(Get.isRegistered<SubProjectDetailViewController>()){
            await Get.find<SubProjectDetailViewController>().callApiForGetHighlightsOfSubProject(context: context);
            Get.find<SubProjectDetailViewController>().update();
          }else if(Get.isRegistered<ProjectDetailViewController>(tag: projectId.value.toString())){
            await Get.find<ProjectDetailViewController>(tag: projectId.value.toString()).callApiForGetHighlightsOfProject(context: context);
            Get.find<ProjectDetailViewController>(tag: projectId.value.toString()).update();
          }else {
            await Get
                .find<ProfileViewController>()
                .callApiForGetHighlightsOfProfile(context: context);
          }       isUploading.value = false;
          Get.back();
          Get.back();
          CommonFunction.showCustomSnackbar(message: response['message']);
        },
        failureCallback: (message, statusCode) {
          isUploading.value = false;
          CommonFunction.showCustomSnackbar(message: statusCode,isError: true,backgroundColor: AppTheme.red);
        },
      );
    } catch (e) {
      log("Error creating highlight: $e");
    }
  }

  List<T> getDifference<T>(List<T> original, List<T> updated) {
    return updated.where((e) => !original.contains(e)).toList();
  }
}