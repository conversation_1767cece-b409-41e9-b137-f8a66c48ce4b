import 'package:crop_your_image/crop_your_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/story_cover_selector/controller/story_cover_selector_controller.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import '../../../../constants/app_image.dart';
import '../../../../models/story_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/typography.dart';
import '../../story_editor/common_component/animated_onTap_button.dart';

class StoryCoverSelectorView extends GetWidget<StoryCoverSelectorController> {
  const StoryCoverSelectorView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: appBar(context: context),
      body: Obx(
          () => controller.showTitleView.value ? titleBody() : editorBody()),
    );
  }

  AppBar appBar({required BuildContext context}) {
    return AppBar(
      backgroundColor: Colors.black,
      elevation: 0,
      leadingWidth: 50,
      leading: Obx(
        () => InkWell(
          onTap: controller.showTitleView.value
              ? controller.isUploading.value ? (){} :() => Get.back()
              : () => controller.showTitleView.value = true,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: SvgPicture.asset(
              AppImage.backArrow,
              color:  AppTheme.white ,
            ),
          ),
        ),
      ),
      title: Row(
        children: [
          Obx(() => TypoGraphy(
                text: controller.isFromHighlight.value
                    ? 'Edit'
                    : controller.showTitleView.value
                        ? 'Title'
                        : 'Edit cover',
                level: 5,
                color: AppTheme.white,
                fontWeight: FontWeight.w600,
              )),
        ],
      ),
      actions: [
        Obx(
          () => controller.isUploading.value
              ? Loader(
                  size: MySize.getScaledSizeWidth(25),
                )
              : TextButton(
                  onPressed: controller.userStories
                      .where((p0) => p0.isSelected.value)
                      .toList().isEmpty && controller.isFromHighlight.value ? (){} :controller.showTitleView.value
                      ? () async {
                    HapticFeedback.lightImpact();
                          if (controller.highlightController.value.text
                              .trim()
                              .isEmpty) {
                            CommonFunction.showCustomSnackbar(
                                message:
                                    'Please first enter the highlight name',
                                isError: true,
                                backgroundColor: AppTheme.red);
                          } else {
                            controller.isUploading.value = true;
                            HapticFeedback.lightImpact();
                            if(controller.isFromHighlight.value){
                              await controller.callApiForUpdateHighlight(
                                  context: context,
                                  croppedImage:
                                  controller.croppedImageData.value!);
                            }else {
                              await controller.callApiForCreateHighlight(
                                  context: context,
                                  croppedImage:
                                  controller.croppedImageData.value!);
                            }
                          }
                        }
                      : () {
                          HapticFeedback.lightImpact();
                          controller.cropController.crop();
                        },
                  child: Obx(()=>TypoGraphy(
                      text: 'Done',
                      level: 1,
                      color: controller.userStories
                          .where((p0) => p0.isSelected.value)
                          .toList().isEmpty && controller.isFromHighlight.value ? AppTheme.grey : AppTheme.primary1,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        )
      ],
    );
  }

  Widget editorBody() {
    return Column(
      children: [
        Expanded(
          child: Obx(() {
            final imagePath = controller.selectedImageSource.value?.networkUrl;
            if (imagePath == null) return const SizedBox();
            return FutureBuilder<Uint8List?>(
              future: controller.loadImageBytes(imagePath),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: Loader());
                }

                if (!snapshot.hasData || snapshot.data == null) {
                  return const Center(
                      child: Text('Failed to load image',
                          style: TextStyle(color: Colors.white)));
                }

                return Crop(
                  controller: controller.cropController,
                  image: snapshot.data!,
                  onCropped: controller.onCropComplete,
                  aspectRatio: MySize.getScaledSizeWidth(94) /
                      MySize.getScaledSizeHeight(126),
                  baseColor: AppTheme.black,
                  maskColor: AppTheme.black.withValues(alpha: 0.8),
                  radius: MySize.getScaledSizeHeight(20),
                  interactive: true,
                  fixCropRect: true,
                  cornerDotBuilder: (size, edgeAlignment) =>
                      const SizedBox.shrink(),
                  initialRectBuilder: InitialRectBuilder.withSizeAndRatio(
                    size: 1,
                    aspectRatio: MySize.getScaledSizeWidth(94) /
                        MySize.getScaledSizeHeight(126),
                  ),
                );
              },
            );
          }),
        ),
        Space.height(20),
        SizedBox(
          height: MySize.getScaledSizeHeight(100),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: (controller.isFromHighlight.value ? controller.userStories
                .where((p0) => p0.isSelected.value)
                .toList()
                .length : controller.selectedStories.length) + 1 ,
            itemBuilder: (context, index) {
              if (index == 0) {
                return InkWell(
                  onTap: controller.pickImage,
                  child: Container(
                    padding: EdgeInsets.all(MySize.size8 ?? 8),
                    alignment: Alignment.center,
                    height: MySize.getScaledSizeHeight(100),
                    width: MySize.getScaledSizeWidth(90),
                    child: const Icon(Icons.photo_library, color: Colors.white),
                  ),
                );
              } else {
                final story = controller.isFromHighlight.value ? controller.userStories
                    .where((p0) => p0.isSelected.value)
                    .toList()[index - 1] : controller.selectedStories[index - 1];
                final imageUrl = (story.mediaType == StoryType.image.name ||
                        story.mediaType == StoryType.text.name)
                    ? story.overlayImage ?? ''
                    : story.thumbnailPath ?? '';

                return InkWell(
                  onTap: () => controller.setNetworkImage(imageUrl),
                  child: Obx(() {
                    final isSelected =
                        controller.selectedImageSource.value?.networkUrl ==
                            imageUrl;
                    return Container(
                      height: MySize.getScaledSizeHeight(100),
                      width: MySize.getScaledSizeWidth(90),
                      padding: EdgeInsets.all(MySize.size5 ?? 5),
                      decoration: isSelected
                          ? BoxDecoration(
                              border: Border.all(
                                  color: AppTheme.primary1,
                                  width: MySize.getScaledSizeWidth(1.5)),
                              borderRadius: BorderRadius.circular(15),
                            )
                          : null,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: CachedNetworkImage(
                          imageUrl: imageUrl,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => const Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          ),
                          errorWidget: (context, url, error) => const Center(
                            child: Icon(Icons.error, color: Colors.red),
                          ),
                        ),
                      ),
                    );
                  }),
                );
              }
            },
          ),
        ),
        Space.height(20),
      ],
    );
  }

  Widget titleBody() {
    return controller.isLoading.value
        ? Center(
            child: Loader(),
          )
        : Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Space.height(20),
                Obx(
                  () => Container(
                    height: MySize.getScaledSizeHeight(126) * 1.3,
                    width: MySize.getScaledSizeWidth(94) * 1.3,
                    padding: EdgeInsets.all(MySize.getScaledSizeWidth(3)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                        MySize.getScaledSizeWidth(20),
                      ),
                      border: Border.all(color: AppTheme.whiteF4F4F4),
                    ),
                    child: ClipRRect(
                        borderRadius: BorderRadius.circular(
                          MySize.getScaledSizeWidth(17),
                        ),
                        child: Image.memory(
                          controller.croppedImageData.value!,
                          fit: BoxFit.cover,
                        )),
                  ),
                ),
                InkWell(
                  onTap: () {
                    controller.showTitleView.value = false;
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0)
                        .copyWith(top: 12)
                        .copyWith(bottom: 0),
                    child: TypoGraphy(
                      text: 'Edit Cover',
                      level: 4,
                      color: AppTheme.primary1,
                    ),
                  ),
                ),
                Obx(() => controller.isFromHighlight.value
                    ? Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 15),
                        child: TextField(
                          cursorColor: AppTheme.primary1,
                          controller: controller.highlightController.value,
                          style: TextStyle(color: AppTheme.white,fontSize: MySize.getScaledSizeHeight(17)),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.zero,
                            labelText: "Title",
                            labelStyle: TextStyle(color: AppTheme.grey),
                            hintText: "Highlights",
                            hintStyle: TextStyle(color: AppTheme.grey,fontSize: MySize.getScaledSizeHeight(16)),
                            fillColor: Colors.transparent,
                            filled: true,
                            border: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppTheme.grey),
                            ),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppTheme.grey),
                            ),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppTheme.primary1),
                            ),
                          ),
                        ),
                      )
                    : TextField(
                        cursorColor: AppTheme.primary1,
                        controller: controller.highlightController.value,
                        autofocus: true,
                        textAlign: TextAlign.center,
                        style: TextStyle(color: AppTheme.white,fontSize: MySize.getScaledSizeHeight(20)),
                        decoration: InputDecoration(
                          hintText: "Highlights",
                          hintStyle: TextStyle(color: AppTheme.grey,fontSize: MySize.getScaledSizeHeight(19)),
                          fillColor: Colors.transparent,
                          filled: true,
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                        ),
                      )),
                if (controller.isFromHighlight.value)
                  Expanded(child: buildTabBar()),
              ],
            ),
          );
  }

  Widget buildTabBar() {
    return Obx(() => Column(
          children: [
            DefaultTabController(
              length: 2,
              initialIndex: controller.selectedTabIndex.value,
              child: TabBar(
                onTap: (index) {
                  controller.selectedTabIndex.value = index;
                  if (index == 1) {
                    Future.delayed(Duration(milliseconds: 100), () {
                      if (controller.scrollController.hasClients) {
                        controller.scrollController.animateTo(
                          controller.scrollController.position.maxScrollExtent,
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    });
                  }
                },
                indicator: UnderlineTabIndicator(
                  borderSide: BorderSide(color: AppTheme.primary1, width: 1.5),
                  insets: const EdgeInsets.symmetric(horizontal: 10.0),
                ),
                labelColor: Colors.white,
                unselectedLabelColor: Colors.grey,
                tabs: const [
                  Tab(text: 'Selected'),
                  Tab(text: 'Stories'),
                ],
              ),
            ),
            Expanded(
              child: controller.selectedTabIndex.value == 0
                  ? GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        childAspectRatio: 9 / 16,
                        crossAxisSpacing: 2,
                        mainAxisSpacing: 2,
                      ),
                      itemCount: controller.userStories
                          .where((p0) => p0.isSelected.value)
                          .toList()
                          .length,
                      itemBuilder: (context, index) {
                        final List<OneStory> storyList = controller.userStories
                            .where((p0) => p0.isSelected.value)
                            .toList();
                        final story = storyList[index];
                        return _buildStoryItem(story);
                      },
                    )
                  : RawScrollbar(
                      thickness: 4,
                      thumbColor: AppTheme.white,
                      controller: controller.scrollController,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 2),
                        child: GridView.builder(
                          controller: controller.scrollController,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            childAspectRatio: 9 / 16,
                            crossAxisSpacing: 2,
                            mainAxisSpacing: 2,
                          ),
                          itemCount: controller.userStories.length,
                          itemBuilder: (context, index) {
                            final story = controller.userStories[index];
                            return _buildStoryItem(story);
                          },
                        ),
                      ),
                    ),
            ),
          ],
        ));
  }

  Widget _buildStoryItem(OneStory story) {
    return AnimatedOnTapButton(
      lowerBound: 0.98,
      upperBound: 1,
      onTap: () => controller.toggleSelection(story),
      child: Stack(
        fit: StackFit.expand,
        children: [
          ClipRRect(
            child: CachedNetworkImage(
              imageUrl: (story.mediaType == StoryType.image.name ||
                      story.mediaType == StoryType.text.name)
                  ? story.overlayImage ?? ''
                  : story.thumbnailPath ?? '',
              fit: BoxFit.cover,
              errorWidget: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
          ),
          Positioned(
            top: 8,
            left: 8,
            child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Column(
                  children: [
                    TypoGraphy(
                      text: DateFormat('d').format(
                          (story.createdAt ?? DateTime.now()).toLocal()),
                      color: Colors.white,
                      level: 4,
                    ),
                    TypoGraphy(
                      text: DateFormat('MMM').format(
                          (story.createdAt ?? DateTime.now()).toLocal()),
                      color: Colors.white,
                      level: 2,
                    )
                  ],
                )),
          ),
          Obx(() => story.isSelected.value
              ? Container(
                  decoration: BoxDecoration(
                      color: AppTheme.white.withValues(alpha: 0.6)),
                )
              : SizedBox()),
          Positioned(
            right: 8,
            bottom: 8,
            child: Obx(() => InkWell(
                  onTap: () => controller.toggleSelection(story),
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      color: story.isSelected.value
                          ? AppTheme.whiteF4F4F4
                          : Colors.transparent,
                      border: Border.all(
                        color: story.isSelected.value
                            ? AppTheme.whiteF4F4F4
                            : Color(0xff99a1ab),
                        width: 2,
                      ),
                    ),
                    child: story.isSelected.value
                        ? const Icon(
                            Icons.check,
                            color: Colors.black,
                            size: 20,
                          )
                        : null,
                  ),
                )),
          ),
        ],
      ),
    );
  }
}
