
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../../constants/app_image.dart';
import '../../../../../constants/app_size_constant.dart';
import '../../../../../models/section_base.dart';
import '../../../../../utillites/app_theme.dart';
import '../../../../../utillites/custom_sliver_list_view.dart';
import '../../../../../utillites/empty.dart';
import '../../../../../utillites/typography.dart';
import '../../../../routes/app_pages.dart';
import '../../../create_todo_view/components/common_todo_shimmer.dart';
import '../../controllers/sub_project_detail_view_controller.dart';
import '../sub_project_todo_view.dart';

class TodoSection extends SectionBase<SubProjectDetailViewController> {
  TodoSection({required super.controller});

  @override
  String get title => 'To Do';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();
        Get.toNamed(Routes.create_todo, arguments: {
          "isProjectCreate": true,
          "ProjectId": controller.projectId.value
        })?.then(
              (value) {
            controller.page.value = 1;
            controller.hasMoreData.value = true;
            controller.todoList.clear();
            controller.callApiForToDo(context: context);
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }


  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(
        child: Space.height(25),
      ),
      Obx(
            () =>
        controller.isLoading.value &&
            controller.todoList.isEmpty
            ? SliverToBoxAdapter(
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: 5,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) =>
                TodoShimmerView(),
          ),
        )
            : SlidableAutoCloseBehavior(
          child: CustomSliverListView(
            emptyWidget: Padding(
              padding: EdgeInsets.only(
                left: paddingHoriZontal,
                right: paddingHoriZontal,
              ),
              child: Padding(
                padding: EdgeInsets.only(
                    top: MySize.size50 ?? 50),
                child: const Empty(
                  title: "No To Do available!",
                ),
              ),
            ),
            maximumReachedWidget:
            const SizedBox(),
            itemBuilder: (p0, p1, index) {
              final incompleteTodos = controller.todoList
                  .where((todo) => !(todo.status?.value ?? false))
                  .toList();
              final completedTodos = controller.todoList
                  .where((todo) => todo.status?.value ?? false)
                  .toList();
              if (index < incompleteTodos.length) {
                final originalIndex = controller.todoList.indexOf(incompleteTodos[index]);
                return Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: MySize.getScaledSizeWidth(30))
                      .copyWith(
                      bottom: MySize.getScaledSizeHeight(18)),
                  child: subProjectToDoCard(
                      controller: controller,
                      index: originalIndex),
                );
              } else if (index == incompleteTodos.length && completedTodos.isNotEmpty) {
                return Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: MySize.getScaledSizeWidth(20))
                      .copyWith(
                      bottom: MySize.getScaledSizeHeight(20)),
                  child: ExpansionTile(
                    backgroundColor: Colors.transparent,
                    collapsedBackgroundColor: Colors.transparent,
                    // initiallyExpanded: true,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    collapsedShape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    title: TypoGraphy(
                      text: "Completed (${completedTodos.length})",
                      level: 4,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.whiteWithBase,
                    ),
                    iconColor: AppTheme.whiteWithBase,
                    collapsedIconColor: AppTheme.whiteWithBase,
                    children:
                    completedTodos.map((todo) {
                      final originalIndex = controller.todoList.indexOf(todo);
                      return Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal:
                            MySize.getScaledSizeWidth(10))
                            .copyWith(
                            bottom:
                            MySize.getScaledSizeHeight(18)),
                        child: subProjectToDoCard(
                            controller: controller,
                            index: originalIndex),
                      );
                    }).toList(),
                  ),
                );
              }
              return SizedBox.shrink();
            },
            items: controller.todoList,
            isLoading:
            controller.isLoading.value,
            hasMoreData:
            controller.hasMoreData.value,
            onLoadMore: () {
              return controller.callApiForToDo(
                context: context,
              );
            },
          ),
        ),
      ),
    ];
  }

  @override
  void onCategorySelected() {
    controller.page.value = 1;
    controller.hasMoreData.value =
    true;
    controller.todoList.clear();
    controller.callApiForToDo(
        context: Get.context!);
  }

}