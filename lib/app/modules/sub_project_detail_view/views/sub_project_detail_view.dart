import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/common_shimmer_profile.dart';
import '../../../../utillites/custom_scroll.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../bottom_bar/views/bottom_bar_view.dart';
import '../../explore/components/common_widget_view.dart';
import '../../profile_view/components/profile_widget_view.dart';
import '../../project_detail_view/components/project_detail_about_view.dart';
import '../../project_detail_view/components/sub_project_edit.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../controllers/sub_project_detail_view_controller.dart';

class SubProjectDetailView extends GetWidget<SubProjectDetailViewController> {
  const SubProjectDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      bottomNavigationBar: customBottomNavigation(),
      floatingActionButton: Obx(
        () => ((CurrentUser.user.id ==
                    controller.getProjectDetailData.value.userId) ||
                CurrentUser.user.id ==
                    controller
                        .getProjectDetailData.value.parentProjectData?.userId ||
                (controller
                        .getProjectDetailData.value.projectMembers.isNotEmpty &&
                    controller.getProjectDetailData.value.projectMembers[0]
                            .access ==
                        "write"))
            ? controller.sections[controller.currentSelectedIndex.value]
                .floatingActionButtonBuilder(context)
            : SizedBox(),
      ),
      body: Obx(
        () => controller.isUserLoading.value
            ? ShimmerProfilePage(
                isProjectDetail: true,
              )
            : controller.isProjectNotFound.value
                ? Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeWidth(30)),
                    child: SafeArea(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildIcon(AppImage.backArrow, () {
                            print("route == ${Get.previousRoute}");
                            Get.back();
                          }, padding: 7, color: AppTheme.whiteWithBase),
                          // Space.height(200),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Center(
                                child: Image.asset(
                                  "assets/images/not_folder.png",
                                  width: MySize.getScaledSizeWidth(200),
                                  height: MySize.getScaledSizeHeight(200),
                                ),
                              ),
                              Space.height(20),
                              Center(
                                child: TypoGraphy(
                                  text: "Sub-Project not found!",
                                  level: 4,
                                ),
                              ),
                            ],
                          ),
                          SizedBox.shrink(),
                        ],
                      ),
                    ),
                  )
                : Obx(
                    () => CustomScrollView(
                      physics: ClampingScrollPhysics(),
                      slivers: [
                        SliverToBoxAdapter(
                          child: Stack(
                            children: [
                              Obx(
                                () => controller
                                            .getProjectDetailData.value.image !=
                                        ""
                                    ? NetworkImageComponent(
                                        imageUrl: controller
                                            .getProjectDetailData.value.image,
                                        height: MySize.getScaledSizeHeight(182),
                                        width: double.infinity,
                                        simmerHeight:
                                            MySize.getScaledSizeHeight(182),
                                      )
                                    : controller.isUserLoading.value
                                        ? Shimmer.fromColors(
                                            baseColor: Colors.grey[300]!,
                                            highlightColor: Colors.grey[100]!,
                                            child: Container(
                                              width: double.infinity,
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      182),
                                              color: Colors.grey[400],
                                            ),
                                          )
                                        : Image.asset(
                                            AppImage.defaultBannerImage,
                                            width: double.infinity,
                                            height:
                                                MySize.getScaledSizeHeight(182),
                                            fit: BoxFit.fill,
                                          ),
                              ),
                              Container(
                                width: double.infinity,
                                height: MySize.getScaledSizeHeight(182),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      AppTheme.black.withOpacity(0.8),
                                      AppTheme.black.withOpacity(0),
                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                ),
                              ),
                              Column(
                                children: [
                                  Space.height(55),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.size30 ?? 30),
                                    child: Row(
                                      children: [
                                        InkWell(
                                          child: buildIcon(AppImage.backArrow,
                                              () => Get.back(),
                                              padding: 7),
                                        ),
                                        Spacer(),
                                        buildIcon(AppImage.moreVertIcon, () {
                                          HapticFeedback.lightImpact();
                                          ImagePickerBottomSheet.show(
                                            context: context,
                                            child: (CurrentUser.user.id ==
                                                        controller
                                                            .getProjectDetailData
                                                            .value
                                                            .userId ||
                                                    CurrentUser.user.id ==
                                                        controller
                                                            .getProjectDetailData
                                                            .value
                                                            .parentProjectData
                                                            ?.userId)
                                                ? showSubProjectEditOption(
                                                    projectName: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .name,
                                                    mainProjectVisibility:
                                                        controller
                                                            .getProjectDetailData
                                                            .value
                                                            .isPrivate,
                                                    isHideDelete: (controller
                                                            .getProjectDetailData
                                                            .value
                                                            .projectMembers
                                                            .isNotEmpty &&
                                                        controller
                                                                .getProjectDetailData
                                                                .value
                                                                .projectMembers[
                                                                    0]
                                                                .access ==
                                                            "write"),
                                                    isSubProject: true,
                                                    projectId: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .id
                                                        .toString(),
                                                    isPrivate: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .isPrivate,
                                                    isHide: controller
                                                            .getProjectDetailData
                                                            .value
                                                            .hide ??
                                                        false,
                                                    hidePrivate: () {
                                                      controller.callApiForHideProject(
                                                          context: context,
                                                          projectId: controller
                                                              .getProjectDetailData
                                                              .value
                                                              .id
                                                              .toString(),
                                                          isHide: controller
                                                                      .getProjectDetailData
                                                                      .value
                                                                      .hide ==
                                                                  false
                                                              ? true
                                                              : false);
                                                    },
                                                  )
                                                : showPostOption(
                                                    isProject: true,
                                                    onTap: () {
                                                      Navigator.pop(context);
                                                      showCommonReportBottomSheet(
                                                        context: context,
                                                        title: "Report",
                                                        subTitle:
                                                            "Why are you reporting this project?",
                                                        description:
                                                            "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                                        options: controller
                                                            .repostData,
                                                        onOptionTap:
                                                            (selectedOption) async {
                                                          controller
                                                                  .selectedReason
                                                                  .value =
                                                              selectedOption;
                                                          await controller
                                                              .callApiForReportProject(
                                                            context: context,
                                                            projectId:
                                                                controller
                                                                    .projectId
                                                                    .value
                                                                    .toString(),
                                                          );
                                                        },
                                                      );
                                                    },
                                                    title: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .name,
                                                    projectId: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .id,
                                                    projectSlug: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .slug),
                                          );
                                        }, padding: 9)
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SliverToBoxAdapter(child: Space.height(20)),
                        SliverToBoxAdapter(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.getScaledSizeWidth(30)),
                                child: Row(
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        if (controller.args != null &&
                                            controller.args["fromPost"] !=
                                                null) {
                                          Get.offAndToNamed(
                                              Routes.project_detail,
                                              arguments: {
                                                "projectId": controller
                                                    .getProjectDetailData
                                                    .value
                                                    .parentProjectData
                                                    ?.id
                                              });
                                        } else {
                                          Get.back();
                                        }
                                      },
                                      child: TypoGraphy(
                                        text: (controller
                                                            .getProjectDetailData
                                                            .value
                                                            .parentProjectData
                                                            ?.name ??
                                                        "")
                                                    .length >
                                                20
                                            ? '${controller.getProjectDetailData.value.parentProjectData?.name.substring(0, 20)}...'
                                            : controller.getProjectDetailData
                                                .value.parentProjectData?.name,
                                        level: 3,
                                      ),
                                    ),
                                    TypoGraphy(
                                      text: " > ",
                                      level: 3,
                                    ),
                                    TypoGraphy(
                                      text: controller.getProjectDetailData
                                                  .value.name.length >
                                              20
                                          ? '${controller.getProjectDetailData.value.name.substring(0, 20)}...'
                                          : controller
                                              .getProjectDetailData.value.name,
                                      level: 3,
                                    ),
                                  ],
                                ),
                              ),
                              Space.height(10),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.getScaledSizeWidth(30)),
                                child: InkWell(
                                  onTap: () {
                                    ImagePickerBottomSheet.show(
                                      context: context,
                                      child: ProjectDetailsWidget(
                                        projectName: controller
                                            .getProjectDetailData.value.name,
                                        projectDescription: controller
                                            .getProjectDetailData
                                            .value
                                            .description,
                                        userImage: controller
                                                .getProjectDetailData
                                                .value
                                                .userData
                                                ?.image ??
                                            "",
                                        userName: controller
                                                .getProjectDetailData
                                                .value
                                                .userData
                                                ?.firstName ??
                                            "",
                                        userLastName: controller
                                                .getProjectDetailData
                                                .value
                                                .userData
                                                ?.lastName ??
                                            "",
                                        createdAt: DateFormat('MMM d, y')
                                            .format(DateTime.parse(controller
                                                .getProjectDetailData
                                                .value
                                                .createdAt
                                                .toString())),
                                        isCurrentUser: CurrentUser.user.id ==
                                            controller.getProjectDetailData
                                                .value.userData?.id,
                                        currentUseId: controller
                                                .getProjectDetailData
                                                .value
                                                .userData
                                                ?.id ??
                                            0,
                                      ),
                                    );
                                  },
                                  child: RichText(
                                    text: TextSpan(
                                      children: [
                                        if (controller.getProjectDetailData
                                            .value.isPrivate) ...[
                                          WidgetSpan(
                                              child: Padding(
                                            padding: EdgeInsets.only(
                                                right:
                                                    MySize.getScaledSizeWidth(
                                                        8)),
                                            child: Image.asset(
                                              AppImage.lockProject,
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      32),
                                              width:
                                                  MySize.getScaledSizeWidth(32),
                                            ),
                                          )),
                                        ],
                                        TextSpan(
                                          text: controller
                                              .getProjectDetailData.value.name,
                                          style: TextStyle(
                                            fontSize: 30,
                                            fontWeight: FontWeight.w700,
                                            color: AppTheme.whiteWithBase,
                                          ),
                                        ),
                                        WidgetSpan(
                                          // alignment: PlaceholderAlignment.middle,
                                          child: Padding(
                                            padding: EdgeInsets.only(
                                                left: 10, bottom: 5),
                                            child: SvgPicture.asset(
                                              AppImage.back,
                                              height: MySize.size16,
                                              color: AppTheme.whiteWithNull,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              Space.height(10),
                              if (controller
                                      .getProjectDetailData.value.postsCount !=
                                  "0") ...[
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: MySize.size30 ?? 30),
                                  child: TypoGraphy(
                                    text:
                                        "${controller.getProjectDetailData.value.postsCount} Posts",
                                    level: 2,
                                    color: AppTheme.grey,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                              Space.height(12),
                              buildStoryHighlightSection(
                                  context: context,
                                  highlightList: controller.highlights,
                                  fontSize: MySize.size13 ?? 13,
                                  subProjectId: controller.projectId.value,
                                  userId: controller
                                      .getProjectDetailData.value.userId,
                                  isLoading:
                                      controller.isHighlightLoading.value),
                              Space.height(30),
                              if (CurrentUser.user.id ==
                                      controller.getProjectDetailData.value.userData
                                          ?.id ||
                                  CurrentUser.user.id ==
                                      controller.getProjectDetailData.value
                                          .parentProjectData?.userId ||
                                  controller.getProjectDetailData.value
                                      .projectMembers.isNotEmpty) ...[
                                Builder(builder: (context) {
                                  final labelList = buildPostCategorySection(
                                    isProjectMembers: controller
                                        .getProjectDetailData
                                        .value
                                        .projectMembers
                                        .isNotEmpty,
                                    userPostList: controller.sections
                                        .map((e) => e.title)
                                        .toList(),
                                    currentSelectedIndex:
                                        controller.currentSelectedIndex.value,
                                    onCategorySelected: (index) {
                                      controller.currentSelectedIndex.value =
                                          index;
                                      controller.sections[index]
                                          .onCategorySelected();
                                    },
                                  );
                                  return controller.sections.length >= 4
                                      ? CustomHorizontalScrollbar(
                                          scrollController:
                                              controller.scrollController,
                                          child: labelList,
                                        )
                                      : labelList;
                                })
                              ]
                            ],
                          ),
                        ),
                        ...controller
                            .sections[controller.currentSelectedIndex.value]
                            .viewBuilder(context)
                      ],
                    ),
                  ),
      ),
    );
  }
}
