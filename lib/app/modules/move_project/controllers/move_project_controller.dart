import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_project_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';


class MoveProjectController extends GetxController {
  Rx<TextEditingController> searchController = TextEditingController().obs;
  Timer? debounceTimer;
  RxBool isLoading = false.obs;
  ApiManager apiManager = ApiManager();
  int limit = 10;
  RxInt page = 1.obs;
  RxString postId = "-1".obs;
  RxList<Project> userProject = <Project>[].obs;
  RxBool hasMoreData = true.obs;
  RxList<SubProjectData> subProjectList = <SubProjectData>[].obs;
  RxInt selectedSubProjectIndex = (-1).obs;
  RxBool isChecked = false.obs;
  RxInt selectedIndex = (-1).obs;
  var args = Get.arguments;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    callApiForProjectData(context: Get.context!);
    if(args != null && args["postId"] != null) {
      postId.value = args["postId"].toString();

    }
  }

  void updateSelectedIndex(int index) {
    selectedIndex.value = index;
    update(); // Notify UI to rebuild
  }

  callApiForProjectData({required BuildContext context}) {
    if (searchController.value.text.isNotEmpty) {
      // Cancel previous timer if exists
      debounceTimer?.cancel();

      // Start new debounce timer (500ms delay) for searching
      debounceTimer = Timer(Duration(milliseconds: 500), () {
        fetchProjectData(context);
      });
    } else {
      fetchProjectData(context);
    }
  }

  void fetchProjectData(BuildContext context) {
    isLoading.value = true;

    apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {
        "UserId": CurrentUser.user.id,
        'page': page.value,
        'limit': limit,
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse =
            ProjectResponse.fromJson(response);
            if (projectResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            userProject.value = projectResponse.data.data;
            userProject.value = userProject.where(
                    (element) =>
                (CurrentUser.user.id == element.userId || (element.projectMembers.isNotEmpty && element.projectMembers[0].access != "read"))).toList();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  Future<void> callApiForOneSubProject({required BuildContext context,String? projectId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;

    return apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {
        "UserId": CurrentUser.user.id,
        "ParentId": projectId
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            subProjectList.value = List<SubProjectData>.from(response["data"]["data"].map((x) => SubProjectData.fromJson(x)));
            subProjectList.value = subProjectList.where(
                    (element) =>
                (CurrentUser.user.id == element.userId || (element.projectMembers.isNotEmpty && element.projectMembers[0].access != "read"))).toList();

            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForUpdatePost(
      {required BuildContext context,required int projectId}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost = ApiModel("/posts/${postId.value}", APIType.PATCH);

    Map<String, dynamic> dict = {
      "ProjectId": projectId,
    };


    return apiManager.callApi(
      updatePost,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isLoading.value = false;
            Get.back();
            CommonFunction.showCustomSnackbar(
              message: "Post moved successfully to this project.",
            );
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

}
