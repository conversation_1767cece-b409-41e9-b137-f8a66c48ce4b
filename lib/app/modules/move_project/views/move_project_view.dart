import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_grid_view.dart';
import '../../../../utillites/common_shimmer_grid_view.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../projects/components/project_card.dart';
import '../../projects/components/show_sub_project_bottomsheet.dart';
import '../controllers/move_project_controller.dart';

class MoveProjectView extends GetWidget<MoveProjectController> {
  const MoveProjectView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: box.read('isDarkMode') ? AppTheme.darkBackground : AppTheme.white,
        elevation: 0,
        title: TypoGraphy(
          text: "Move to Project",
          level: 12,
        ),
        leading: SizedBox(),
        actions: [
          InkWell(
            onTap: () {
              Get.back();
            },
            child: SvgPicture.asset(
              AppImage.closeImage,
              width: MySize.size16,
              height: MySize.size16,
              color: AppTheme.whiteWithNull,
            ),
          ),
          Space.width(25),
        ],
      ),
      body: SafeArea(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.size25 ?? 20),
        child: Column(
          children: [
            ValueListenableBuilder(
              valueListenable: controller.searchController.value,
              builder: (context, value, child) {
                return AppTextField(
                  controller: controller.searchController.value,
                  padding: EdgeInsets.only(
                    top: MySize.size12 ?? 12,
                    bottom: MySize.size10 ?? 12,
                  ),
                  onChangedValue: (text) {
                    if ((text?.trim() ?? "").isNotEmpty) {
                      controller.callApiForProjectData(context: context);
                    }
                  },
                  height: MySize.getScaledSizeHeight(50),
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(4),
                    child: SvgPicture.asset(
                      AppImage.searchIcon,
                      height: MySize.size24,
                      width: MySize.size24,
                      color: AppTheme.grey,
                    ),
                  ),
                  suffixIcon: (controller
                          .searchController.value.text.isNotEmpty)
                      ? GestureDetector(
                          onTap: () {
                            controller.searchController.value.clear();
                            controller.callApiForProjectData(context: context);
                          },
                          child: Padding(
                            padding:
                                EdgeInsets.only(right: MySize.size15 ?? 20),
                            child: SvgPicture.asset(
                              AppImage.textFieldClear,
                              // height: MySize.size20,
                              // width: MySize.size20,
                              color: AppTheme.grey,
                            ),
                          ),
                        )
                      : null,
                  hintText: "Search",
                  hintStyle: TextStyle(
                    color: AppTheme.grey,
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w400,
                  ),
                );
              },
            ),
            Space.height(30),
            Obx(
              () => controller.isLoading.value
                  ? Expanded(
                      child: ShimmerGridView(),
                    )
                  : controller.userProject.isNotEmpty
                      ? Expanded(
                          child: GridView.builder(
                            keyboardDismissBehavior:
                                ScrollViewKeyboardDismissBehavior.onDrag,
                            gridDelegate: FixedHeightGridDelegate(
                              itemHeight: MySize.size220 ?? 200,
                              crossAxisCount: 2,
                              crossAxisSpacing: MySize.size20 ?? 20,
                              mainAxisSpacing: MySize.size10 ?? 30,
                            ),
                            shrinkWrap: true,
                            controller: ScrollController(),
                            itemCount: controller.userProject.length,
                            itemBuilder: (context, index) {
                              var project = controller.userProject[index];

                              return GestureDetector(
                                onTap: () {
                                  controller.updateSelectedIndex(index);
                                  controller.selectedSubProjectIndex.value = -1;
                                  controller.callApiForOneSubProject(
                                      context: context,
                                      projectId: project.id.toString());
                                  showMoveToProjectBottomSheet(
                                      context: context,
                                      project: project,
                                      isChecked: controller.isChecked,
                                      isSubLoading: controller.isLoading,
                                      selectedSubProjectIndex:
                                          controller.selectedSubProjectIndex,
                                      subProjectList: controller.subProjectList,
                                      isMoveProject: true);
                                },
                                child: ProjectCard(project: project),
                              );
                            },
                          ),
                        )
                      : Center(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Space.height(50),
                              SvgPicture.asset(AppImage.emptyProject),
                              Space.height(30),
                              TypoGraphy(
                                text:
                                    "Let’s get started, create your project today!",
                                level: 12,
                                textAlign: TextAlign.center,
                              ),
                              Space.height(50),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Buttons(
                                    buttonText: "+ Create ",
                                    buttonTextLevel: 4,
                                    height: MySize.size70 ?? 70,
                                    width: MySize.size198,
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                      Get.toNamed(Routes.create_project)?.then(
                                        (value) {
                                          controller.callApiForProjectData(
                                            context: context,
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
            ),
          ],
        ),
      )),
    );
  }
}
