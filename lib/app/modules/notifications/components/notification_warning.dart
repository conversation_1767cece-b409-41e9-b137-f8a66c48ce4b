
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/typography.dart';
class LocationWarning extends StatelessWidget {
  const LocationWarning({super.key});
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          height: MySize.size44,
          padding: EdgeInsets.all(MySize.size10 ?? 10),
          decoration: BoxDecoration(
            border: Border.all(
              color: AppTheme.red[400]!,
              width: MySize.getScaledSizeWidth(1.2),
            ),
            // image: DecorationImage(
            //   // image: AssetImage(AppImage.bluerBackground),
            //   fit: BoxFit.cover,
            // ),
            borderRadius: BorderRadius.circular(MySize.size10 ?? 10),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    AppImage.notificationIcon,
                    width: MySize.size20,
                    height: MySize.size20,
                    color: AppTheme.whiteWithNull,
                  ),
                  const Space.width(7),
                  TypoGraphy(
                    text: "Notification Permission",
                    level: 3,
                  ),
                ],
              ),
              Row(
                children: [
                  InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () async {
                      HapticFeedback.lightImpact();
                      await showDialog<bool>(
                        context: context,
                        builder: (context) => DefaultPopup(
                          text:
                          'Enable notifications to make sure you never miss important activity—like project invites, new followers, or community updates.\nThis helps you stay engaged and respond quickly to what matters most in Incenti.',
                          buttonText: 'OKAY',
                        ),
                      );
                    },
                    child: Container(
                      height: MySize.getScaledSizeHeight(20),
                      width: MySize.getScaledSizeWidth(20),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppTheme.red[400]!,
                          width: MySize.getScaledSizeWidth(1.2),
                        ),
                        borderRadius:
                        BorderRadius.circular(MySize.size100 ?? 10),
                      ),
                      child: Center(
                        child: TypoGraphy(
                          text: "i",
                          level: 2,
                        ),
                      ),
                    ),
                  ),
                  const Space.width(7),
                  InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      HapticFeedback.lightImpact();
                      openAppSettings();
                    },
                    child: Container(
                      height: MySize.getScaledSizeHeight(24),
                      decoration: BoxDecoration(
                        color: AppTheme.red,
                        borderRadius: BorderRadius.circular(MySize.size4 ?? 10),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: MySize.size10 ?? 10,
                      ),
                      child: Center(
                        child: TypoGraphy(
                          text: "Allow",
                          level: 3,
                          color: AppTheme.white,
                        ),
                      ),
                    ),
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}

class DefaultPopup extends StatelessWidget {
  const DefaultPopup({
    super.key,
    required this.text,
    this.textAlign = TextAlign.start,
    this.buttonText = "GOT IT",
  });
  final String text;
  final TextAlign textAlign;
  final String buttonText;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TypoGraphy(
            level: 4,
            text: "Stay Connected with Real-Time Updates",
            fontWeight: FontWeight.w600,
            textAlign: textAlign,
          ),
          Space.height(20),
          TypoGraphy(
            level: 3,
            text: text,
            textAlign: textAlign,
          ),
        ],
      ),
      actions: <Widget>[
        CupertinoButton(
          padding: EdgeInsets.symmetric(vertical: MySize.size4 ?? 4),
          child: TypoGraphy(
            text: buttonText,
            color: Colors.blue,
            level: 3,
            // fontWeight: FontWeight.w600,
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            Navigator.of(context).pop(false);
          },
        )
      ],
    );
  }
}
