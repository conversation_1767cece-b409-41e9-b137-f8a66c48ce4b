// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_profile_widget.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:intl/intl.dart';

import '../../../routes/app_pages.dart';
import '../controllers/notifications_controller.dart';

class NotificationItem extends StatelessWidget {
  const NotificationItem({
    super.key,
    required this.notification,
    required this.controller,
  });

  final NotificationTypes notification;
  final NotificationsController controller;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
      child: InkWell(
        onTap: () {
          notification.onTap(context, controller);
        },
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () {
                    notification.onImageTap(context, controller);
                  },
                  child: profileImage(
                    url: notification.imageUrl ?? '',
                    height: MySize.size50,
                    width: MySize.size50,
                    userName: notification.userSlug,
                    // frameBuilder:
                    //     (context, child, frame, wasSynchronouslyLoaded) =>
                    //         ClipOval(
                    //   child: child,
                    // ),
                  ),
                ),
                Space.width(20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Space.height(4),
                      notification.buildStyledMessage(),
                      Space.height(8),
                      TypoGraphy(
                        text: DateFormat('MMM dd, yyyy')
                            .format(notification.dateTime),
                        color: AppTheme.grey,
                        level: 2,
                      ),
                      if (notification.actionStatus == null) ...[
                        Space.height(10),
                        Row(
                          children: [
                            InkWell(
                              onTap: () async {
                                HapticFeedback.lightImpact();
                                await notification.onAccept(
                                    context, controller);
                              },
                              child: Container(
                                decoration: ShapeDecoration(
                                  shape: StadiumBorder(),
                                  color: AppTheme.success,
                                ),
                                padding: EdgeInsets.symmetric(
                                  vertical: MySize.size11 ?? 11,
                                  horizontal: MySize.size16 ?? 16,
                                ),
                                child: TypoGraphy(
                                  text: 'Accept',
                                  level: 4,
                                  color: AppTheme.white,
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                notification.onReject(context, controller);
                              },
                              child: Container(
                                decoration: ShapeDecoration(
                                  shape: StadiumBorder(),
                                  color: Colors.transparent,
                                ),
                                padding: EdgeInsets.symmetric(
                                  vertical: MySize.size11 ?? 11,
                                  horizontal: MySize.size16 ?? 16,
                                ),
                                child: TypoGraphy(
                                  text: 'Reject',
                                  level: 4,
                                  color: AppTheme.red,
                                ),
                              ),
                            ),
                            Spacer(),
                            if (notification is CreatedPostNotification)
                              InkWell(
                                onTap: () async {
                                  HapticFeedback.lightImpact();
                                  Get.toNamed(Routes.post_detail, arguments: {
                                    'postID':
                                    (notification as CreatedPostNotification).postId,
                                    "isFromNotification": true
                                  });
                                },
                                child: TypoGraphy(
                                  text: "Preview",
                                  level: 4,
                                  color: AppTheme.primary1,
                                )
                              ),
                          ],
                        )
                      ]
                    ],
                  ),
                ),
                // if (notification is CreatedPostNotification)
                //   Padding(
                //     padding: const EdgeInsets.all(4),
                //     child: InkWell(
                //       onTap: () async {
                //         HapticFeedback.lightImpact();
                //         Get.toNamed(Routes.post_detail, arguments: {
                //           'postID':
                //           (notification as CreatedPostNotification).postId
                //         });
                //       },
                //       child: Container(
                //         decoration: ShapeDecoration(
                //           shape: StadiumBorder(
                //               side: BorderSide(color: AppTheme.baseBlack)),
                //         ),
                //         padding: EdgeInsets.symmetric(
                //           vertical: MySize.size6 ?? 6,
                //           horizontal: MySize.size8 ?? 8,
                //         ),
                //         child: Icon(
                //           Icons.visibility_rounded,
                //           color: AppTheme.primary1,
                //         ),
                //       ),
                //     ),
                //   ),
              ],
            ),
            Space.height(20),
            Divider(
              thickness: MySize.size1,
              color: AppTheme.grey[0],
            )
          ],
        ),
      ),
    );
  }
}
