import 'dart:convert';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/app/modules/bottom_bar/controllers/bottom_bar_controller.dart';
import 'package:incenti_ai/app/routes/app_pages.dart';
import 'package:incenti_ai/constants/api.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/services/api_manager.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../communities/controllers/communities_controller.dart';

class NotificationsController extends GetxController
    with WidgetsBindingObserver {
  DateTime? _backgroundTime;
  final apiManager = ApiManager();
  final ScrollController scrollController = ScrollController();

  final isLoading = false.obs;
  final isNotificationEnable = true.obs;

  RxList<NotificationTypes> notifications = <NotificationTypes>[].obs;

  @override
  void onInit() async {
    isLoading.value = true;
    isNotificationEnable.value = await Permission.notification.status.isGranted;
    AwesomeNotifications().resetGlobalBadge();
    WidgetsBinding.instance.addObserver(this);
    getNotifications();
    super.onInit();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      final permission = await Permission.notification.status.isGranted ||
          await Permission.notification.status.isProvisional;
      print("Notification ==> $permission");
      isNotificationEnable.value = permission;
      if (_backgroundTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_backgroundTime!);
        if (difference.inMinutes >= 3) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            scrollController
                .animateTo(0,
                    duration: Duration(seconds: 1), curve: Curves.easeInOut)
                .then(
                  (value) => refreshNotifications(),
                );
          });
        }
      }

      // Add your logic here to handle when app comes to foreground
    } else if (state == AppLifecycleState.paused) {
      _backgroundTime = DateTime.now();
      print("App is now in the background!");
      // Add your logic here to handle when app goes to background
    }
  }

  Future<void> getNotifications() async {
    await apiManager.callApi(
      APIS.notification.getNotifications,
      successCallback: (response, message) {
        final notificationList = response['data']['data'] as List;
        print('notificationList === $message $notificationList');
        isLoading.value = false;
        notifications.value =
            notificationList.map((e) => NotificationTypes.classify(e)).toList();
        isLoading.value = false;
      },
      failureCallback: (message, statusCode) {
        print('NOTIFICATIONS ERROR === $message $statusCode');
        isLoading.value = false;
      },
    );
  }

  refreshNotifications() async => await getNotifications();
}

sealed class NotificationTypes {
  final int id;
  final String? imageUrl;
  final String title;
  final String userSlug;
  final String body;
  final DateTime dateTime;
  final int userId;
  final bool? actionStatus;

  NotificationTypes({
    required this.id,
    required this.imageUrl,
    required this.title,
    required this.userSlug,
    required this.body,
    required this.dateTime,
    required this.userId,
    required this.actionStatus,
  });

  static NotificationTypes classify(Map<String, dynamic> json) {
    return switch (json['type']) {
      'community-joined' => JoinedCommunityNotification.fromJson(json),
      'post-created' => CreatedPostNotification.fromJson(json),
      'user-followed' => FollowedUserNotification.fromJson(json),
      'team-invitation' => TeamInvitationNotification.fromJson(json),
      'team-invitation-response' =>
        TeamInvitationResponseNotification.fromJson(json),
      'Post Commented' => PostCommentNotification.fromJson(json),
      'Liked Post' => PostLikeNotification.fromJson(json),
      'team-member-left' => TeamLeaveNotification.fromJson(json),
      'Project-mentioned' => ProjectMentionedNotification.fromJson(json),
      'Post-mentioned' => UserMentionedNotification.fromJson(json),
      'Todo-DueDate' => ToDoNotification.fromJson(json),
      _ => throw Exception('Unknown notification type'),
    };
  }

  onTap(BuildContext context, NotificationsController controller) {
    if (userId != CurrentUser.user.id) {
      Get.toNamed(
        Routes.other_user_profile,
        arguments: {
          "UserId": userId,
        },
      );
    } else {
      Get.toNamed(Routes.profile);
    }
  }

  onImageTap(BuildContext context, NotificationsController controller) {
    if (userId != CurrentUser.user.id) {
      Get.toNamed(
        Routes.other_user_profile,
        arguments: {
          "UserId": userId,
        },
      );
    } else {
      Get.toNamed(Routes.profile);
    }
  }

  RichText buildStyledMessage() {
    return RichText(text: TextSpan(text: body));
  }

  onAccept(BuildContext context, NotificationsController controller) {}

  onReject(BuildContext context, NotificationsController controller) {}
}

class JoinedCommunityNotification extends NotificationTypes {
  JoinedCommunityNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required super.userSlug,
    required this.communityId,
    super.actionStatus = true,
  });

  final int communityId;

  factory JoinedCommunityNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return JoinedCommunityNotification(
      id: json['id'],
      imageUrl: clickAction['image'],
      userSlug: clickAction['userSlug'],
      title: json['title'],
      body: json['body'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      communityId: clickAction['CommunityId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp = RegExp(r"^(.*?) has joined your (.*?) community\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;
    final community = match.group(2)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(
            text: user,
            style: textStyle.copyWith(fontWeight: FontWeight.w600),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                if (userId != CurrentUser.user.id) {
                  Get.toNamed(
                    Routes.other_user_profile,
                    arguments: {
                      "UserId": userId,
                    },
                  );
                } else {
                  Get.toNamed(Routes.profile);
                }
              },
          ),
          TextSpan(text: ' has joined your ', style: textStyle),
          TextSpan(
              text: community,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.put(CommunitiesController());
                  Get.toNamed(Routes.community_detail,
                      arguments: {'communityId': communityId});
                }),
          TextSpan(text: ' community.', style: textStyle),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    Get.put(CommunitiesController());
    Get.toNamed(
        Routes.community_detail, arguments: {'communityId': communityId});
  }
}

class FollowedUserNotification extends NotificationTypes {
  FollowedUserNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.userSlug,
    required super.dateTime,
    required super.userId,
    super.actionStatus = true,
  });

  factory FollowedUserNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return FollowedUserNotification(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      userSlug: clickAction['userSlug'],
      imageUrl: clickAction['image'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp = RegExp(r"^(.*?) started following you\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(
            text: user,
            style: textStyle.copyWith(fontWeight: FontWeight.w600),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                if (userId != CurrentUser.user.id) {
                  Get.toNamed(
                    Routes.other_user_profile,
                    arguments: {
                      "UserId": userId,
                    },
                  );
                } else {
                  Get.toNamed(Routes.profile);
                }
              },
          ),
          TextSpan(text: ' started following you.', style: textStyle),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    if (userId != CurrentUser.user.id) {
      Get.toNamed(
        Routes.other_user_profile,
        arguments: {
          "UserId": userId,
        },
      );
    } else {
      Get.toNamed(Routes.profile);
    }
  }
}

class CreatedPostNotification extends NotificationTypes {
  CreatedPostNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userSlug,
    required super.userId,
    required super.actionStatus,
    required this.postId,
    required this.communityId,
  });

  final int? postId;
  final int communityId;

  factory CreatedPostNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return CreatedPostNotification(
      id: json['id'],
      title: json['title'],
      userSlug: clickAction['userSlug'],
      body: json['body'],
      imageUrl: clickAction['image'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      postId: clickAction['PostId'],
      communityId: clickAction['CommunityId'],
      actionStatus: json['actionStatus'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp =
        RegExp(r"^(.*?) wants to add a post in your (.*?) community\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;
    final community = match.group(2)!;
    return switch (actionStatus) {
      true || false => RichText(
          text: TextSpan(
            style: TextStyle(color: Colors.black),
            children: [
              TextSpan(
                  text: 'You have ${actionStatus! ? 'accepted' : 'rejected'} ',
                  style: textStyle),
              TextSpan(
                text: user,
                style: textStyle.copyWith(fontWeight: FontWeight.w600),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    if (userId != CurrentUser.user.id) {
                      Get.toNamed(
                        Routes.other_user_profile,
                        arguments: {
                          "UserId": userId,
                        },
                      );
                    } else {
                      Get.toNamed(Routes.profile);
                    }
                  },
              ),
              TextSpan(text: '\'s post in your ', style: textStyle),
              TextSpan(
                  text: community,
                  style: textStyle.copyWith(fontWeight: FontWeight.w600),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Get.put(CommunitiesController());
                      Get.toNamed(Routes.community_detail,
                          arguments: {'communityId': communityId});
                    }),
              TextSpan(text: ' community.', style: textStyle),
            ],
          ),
        ),
      null => RichText(
          text: TextSpan(
            style: TextStyle(color: Colors.black),
            children: [
              TextSpan(
                text: user,
                style: textStyle.copyWith(fontWeight: FontWeight.w600),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    if (userId != CurrentUser.user.id) {
                      Get.toNamed(
                        Routes.other_user_profile,
                        arguments: {
                          "UserId": userId,
                        },
                      );
                    } else {
                      Get.toNamed(Routes.profile);
                    }
                  },
              ),
              TextSpan(text: ' wants to add a post in your ', style: textStyle),
              TextSpan(
                  text: community,
                  style: textStyle.copyWith(fontWeight: FontWeight.w600),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Get.put(CommunitiesController());
                      Get.toNamed(Routes.community_detail,
                          arguments: {'communityId': communityId});
                    }),
              TextSpan(text: ' community.', style: textStyle),
            ],
          ),
        )
    };
  }

  @override
  onAccept(BuildContext context, NotificationsController controller) async {
    await handleCreatePost(true, controller);
    Get.put(CommunitiesController());
    Get.toNamed(Routes.community_detail,
        arguments: {'communityId': communityId});
  }

  @override
  onReject(BuildContext context, NotificationsController controller) async {
    await handleCreatePost(false, controller);
  }

  @override
  onTap(BuildContext context, NotificationsController controller) {
    if (postId != null) {
      Get.toNamed(Routes.post_detail,
          arguments: {'postID': postId, "source": "Notification"});
    }
  }

  handleCreatePost(bool isAccepted, NotificationsController controller) async {
    final apiManager = ApiManager();
    await apiManager.callApi(
      CommonFunction.prepareApi(
        APIS.post.approvePost,
        {'id': postId.toString()},
      ),
      params: {
        'NotificationId': id.toString(),
        'isApproved': isAccepted,
      },
      successCallback: (response, message) async {
        await controller.refreshNotifications();
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
            message: statusCode, isError: true, backgroundColor: AppTheme.red);
      },
    );
  }
}

class TeamInvitationNotification extends NotificationTypes {
  TeamInvitationNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required this.projectId,
    required super.actionStatus,
    required this.access,
    required this.parentId,
    required this.isPrivateProject,
    required this.isPrivateSubProject,
    required super.userSlug,
  });

  final int projectId;
  final String access;
  final int? parentId;
  final bool isPrivateProject;
  final bool isPrivateSubProject;

  factory TeamInvitationNotification.fromJson(Map<String, dynamic> json) {
    print("value === $json");
    final clickAction = jsonDecode(json['clickAction']);
    return TeamInvitationNotification(
        id: json['id'],
        title: json['title'],
        body: json['body'],
        userSlug: clickAction['userSlug'],
        imageUrl: clickAction['image'],
        parentId: clickAction['ParentId'],
        dateTime: DateTime.parse(json['createdAt']),
        userId: clickAction['UserId'],
        projectId: clickAction['ProjectId'],
        actionStatus: json['actionStatus'],
        access: clickAction['access'] ?? '',
        isPrivateProject: clickAction['isPrivateProject'] ?? false,
        isPrivateSubProject: clickAction['isPrivateSubProject'] ?? false
    );
  }

  @override
  RichText buildStyledMessage() {
    // Ekta lakhani invited you to this Test public which belongs to Demo
    final regExp = parentId != null
        ? RegExp(r"^(.*?) invited you to (.*?) which belongs to (.*?)\.?$")
        : RegExp(r"^(.*?) invited you to (.*?) project\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;
    final subProject = match.group(2)!;
    final project = parentId != null ? match.group(3)! : match.group(2)!;

    return switch (actionStatus) {
      true || false => parentId != null
          ? RichText(
              text: TextSpan(
                style: TextStyle(color: Colors.black),
                children: [
                  TextSpan(
                      text:
                          'You have ${actionStatus! ? 'accepted' : 'rejected'} ',
                      style: textStyle),
                  TextSpan(
                    text: user,
                    style: textStyle.copyWith(fontWeight: FontWeight.w600),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (userId != CurrentUser.user.id) {
                          Get.toNamed(
                            Routes.other_user_profile,
                            arguments: {
                              "UserId": userId,
                            },
                          );
                        } else {
                          Get.toNamed(Routes.profile);
                        }
                      },
                  ),
                  TextSpan(text: '\'s invitation for ', style: textStyle),
                  TextSpan(
                      text: subProject,
                      style: textStyle.copyWith(fontWeight: FontWeight.w600),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          if ((actionStatus == null || actionStatus == false) && isPrivateSubProject) {
                            CommonFunction.showCustomSnackbar(
                                message:
                                "This project is private. Please accept the invitation to view this project",
                                isError: true,
                                backgroundColor: AppTheme.red);
                          } else {
                            Get.toNamed(Routes.subProject_detail,
                                arguments: {'projectId': projectId});
                          }
                        }),
                  TextSpan(text: ' project.', style: textStyle),
                ],
              ),
            )
          : RichText(
              text: TextSpan(
                style: TextStyle(color: Colors.black),
                children: [
                  TextSpan(
                      text:
                          'You have ${actionStatus! ? 'accepted' : 'rejected'} ',
                      style: textStyle),
                  TextSpan(
                    text: user,
                    style: textStyle.copyWith(fontWeight: FontWeight.w600),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (userId != CurrentUser.user.id) {
                          Get.toNamed(
                            Routes.other_user_profile,
                            arguments: {
                              "UserId": userId,
                            },
                          );
                        } else {
                          Get.toNamed(Routes.profile);
                        }
                      },
                  ),
                  TextSpan(text: '\'s invitation for ', style: textStyle),
                  TextSpan(
                      text: project,
                      style: textStyle.copyWith(fontWeight: FontWeight.w600),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          if ((actionStatus == null || actionStatus == false) && isPrivateProject) {
                            CommonFunction.showCustomSnackbar(
                                message:
                                "This project is private. Please accept the invitation to view this project",
                                isError: true,
                                backgroundColor: AppTheme.red);
                          } else {
                            Get.toNamed(Routes.project_detail,
                                arguments: {'projectId': projectId});
                          }
                        }),
                  TextSpan(text: ' project.', style: textStyle),
                ],
              ),
            ),
      null => parentId != null
          ? RichText(
              text: TextSpan(
                style: TextStyle(color: Colors.black),
                children: [
                  TextSpan(
                    text: user,
                    style: textStyle.copyWith(fontWeight: FontWeight.w600),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (userId != CurrentUser.user.id) {
                          Get.toNamed(
                            Routes.other_user_profile,
                            arguments: {
                              "UserId": userId,
                            },
                          );
                        } else {
                          Get.toNamed(Routes.profile);
                        }
                      },
                  ),
                  TextSpan(text: ' invited you to ', style: textStyle),
                  TextSpan(
                      text: subProject,
                      style: textStyle.copyWith(fontWeight: FontWeight.w600),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          if ((actionStatus == null || actionStatus == false) && isPrivateSubProject) {
                            CommonFunction.showCustomSnackbar(
                                message:
                                "This project is private. Please accept the invitation to view this project",
                                isError: true,
                                backgroundColor: AppTheme.red);
                          } else {
                            Get.toNamed(Routes.subProject_detail,
                                arguments: {'projectId': projectId});
                          }
                        }),
                  TextSpan(text: ' which belongs to ', style: textStyle),
                  TextSpan(
                      text: project,
                      style: textStyle.copyWith(fontWeight: FontWeight.w600),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          if ((actionStatus == null || actionStatus == false) && isPrivateProject) {
                            CommonFunction.showCustomSnackbar(
                                message:
                                "This project is private. Please accept the invitation to view this project",
                                isError: true,
                                backgroundColor: AppTheme.red);
                          } else {
                            Get.toNamed(Routes.project_detail,
                                arguments: {'projectId': parentId});
                          }
                        }),
                  TextSpan(text: ' project.', style: textStyle),
                ],
              ),
            )
          : RichText(
              text: TextSpan(
                style: TextStyle(color: Colors.black),
                children: [
                  TextSpan(
                    text: user,
                    style: textStyle.copyWith(fontWeight: FontWeight.w600),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (userId != CurrentUser.user.id) {
                          Get.toNamed(
                            Routes.other_user_profile,
                            arguments: {
                              "UserId": userId,
                            },
                          );
                        } else {
                          Get.toNamed(Routes.profile);
                        }
                      },
                  ),
                  TextSpan(text: ' invited you to ', style: textStyle),
                  TextSpan(
                      text: project,
                      style: textStyle.copyWith(fontWeight: FontWeight.w600),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          if ((actionStatus == null || actionStatus == false) && isPrivateProject) {
                            CommonFunction.showCustomSnackbar(
                                message:
                                    "This project is private. Please accept the invitation to view this project",
                                isError: true,
                                backgroundColor: AppTheme.red);
                          } else {
                            Get.toNamed(Routes.project_detail,
                                arguments: {'projectId': projectId});
                          }
                        }),
                  TextSpan(text: ' project.', style: textStyle),
                ],
              ),
            ),
    };
  }

  @override
  onAccept(BuildContext context, NotificationsController controller) async {
    await handleInvitation(true, controller);
    Get.put(CommunitiesController());
    if (parentId != null) {
      Get.toNamed(Routes.subProject_detail,
          arguments: {'projectId': projectId});
    } else {
      Get.toNamed(Routes.project_detail, arguments: {'projectId': projectId});
    }
  }

  @override
  onReject(BuildContext context, NotificationsController controller) async {
    await handleInvitation(false, controller);
  }

  handleInvitation(bool isAccepted, NotificationsController controller) async {
    final apiManager = ApiManager();
    await apiManager.callApi(
      CommonFunction.prepareApi(
        APIS.projectMember.acceptMember,
        {'id': projectId.toString()},
      ),
      params: {
        'status': isAccepted,
        'NotificationId': id.toString(),
        'access': access,
        "UserId": userId,
      },
      successCallback: (response, message) async {
        await controller.refreshNotifications();
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
            message: statusCode, isError: true, backgroundColor: AppTheme.red);
      },
    );
  }

  @override
  onTap(BuildContext context, NotificationsController controller) {
    if ((actionStatus == null || actionStatus == false) && (isPrivateSubProject || isPrivateProject)) {
      CommonFunction.showCustomSnackbar(
          message:
          "This project is private. Please accept the invitation to view this project",
          isError: true,
          backgroundColor: AppTheme.red);
    } else {
      if (parentId != null) {
        Get.toNamed(Routes.subProject_detail,
            arguments: {'projectId': projectId});
      } else {
        Get.toNamed(Routes.project_detail, arguments: {'projectId': projectId});
      }
    }
  }
}

class TeamInvitationResponseNotification extends NotificationTypes {
  TeamInvitationResponseNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userSlug,
    required super.userId,
    required this.projectId,
  }) : super(actionStatus: true);

  final int projectId;

  factory TeamInvitationResponseNotification.fromJson(
      Map<String, dynamic> json) {
    print("value === $json");
    final clickAction = jsonDecode(json['clickAction']);
    return TeamInvitationResponseNotification(
      id: json['id'],
      title: json['title'],
      userSlug: clickAction['userSlug'],
      body: json['body'],
      imageUrl: clickAction['image'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      projectId: clickAction['ProjectId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp = RegExp(
        r"^(.*?) has (.*?) the invitation to join your project (.*?)\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;
    final isAccepted = match.group(2) == 'accepted' ? true : false;
    final project = match.group(3)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(
            text: user,
            style: textStyle.copyWith(fontWeight: FontWeight.w600),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                if (userId != CurrentUser.user.id) {
                  Get.toNamed(
                    Routes.other_user_profile,
                    arguments: {
                      "UserId": userId,
                    },
                  );
                } else {
                  Get.toNamed(Routes.profile);
                }
              },
          ),
          TextSpan(
              text: ' has ${isAccepted ? 'accepted' : 'declined'} ',
              style: textStyle),
          TextSpan(
              text: 'the invitation to join your project ', style: textStyle),
          TextSpan(
              text: project,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.toNamed(Routes.project_detail,
                      arguments: {'projectId': projectId});
                }),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    Get.toNamed(Routes.project_detail, arguments: {'projectId': projectId});
  }
}

class PostLikeNotification extends NotificationTypes {
  PostLikeNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required this.postId,
    super.actionStatus = true,
    required super.userSlug,
  });

  final int postId;

  factory PostLikeNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return PostLikeNotification(
      id: json['id'],
      userSlug: clickAction['userSlug'],
      imageUrl: clickAction['image'],
      title: json['title'],
      body: json['body'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      postId: clickAction['PostId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp = RegExp(r"^(.*?) liked your post: (.*?)\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;
    final post = match.group(2)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(
            text: user,
            style: textStyle.copyWith(fontWeight: FontWeight.w600),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                if (userId != CurrentUser.user.id) {
                  Get.toNamed(
                    Routes.other_user_profile,
                    arguments: {
                      "UserId": userId,
                    },
                  );
                } else {
                  Get.toNamed(Routes.profile);
                }
              },
          ),
          TextSpan(text: ' liked your post: ', style: textStyle),
          TextSpan(
              text: post,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  print("post id ==> ${postId}");
                  Get.toNamed(Routes.post_detail,
                      arguments: {'postID': postId, "source": "Notification"});
                }),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    Get.toNamed(Routes.post_detail,
        arguments: {'postID': postId, "source": "Notification"});
  }
}

class PostCommentNotification extends NotificationTypes {
  PostCommentNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required this.postId,
    super.actionStatus = true,
    required super.userSlug,
  });

  final int postId;

  factory PostCommentNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return PostCommentNotification(
      id: json['id'],
      imageUrl: clickAction['image'],
      title: json['title'],
      body: json['body'],
      userSlug: clickAction['userSlug'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      postId: clickAction['PostId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp = RegExp(r"^(.*?) commented on your post: (.*?)\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;
    final post = match.group(2)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(
            text: user,
            style: textStyle.copyWith(fontWeight: FontWeight.w600),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                if (userId != CurrentUser.user.id) {
                  Get.toNamed(
                    Routes.other_user_profile,
                    arguments: {
                      "UserId": userId,
                    },
                  );
                } else {
                  Get.toNamed(Routes.profile);
                }
              },
          ),
          TextSpan(text: ' commented on your post: ', style: textStyle),
          TextSpan(
              text: post,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  print("post id ==> ${postId}");
                  Get.toNamed(Routes.post_detail,
                      arguments: {'postID': postId, "source": "Notification"});
                }),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    Get.toNamed(Routes.post_detail,
        arguments: {'postID': postId, "source": "Notification"});
  }
}

class TeamLeaveNotification extends NotificationTypes {
  TeamLeaveNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required this.projectId,
    required this.parentId,
    super.actionStatus = true,
    required super.userSlug,
  });

  final int projectId;
  final int? parentId;

  factory TeamLeaveNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return TeamLeaveNotification(
      id: json['id'],
      imageUrl: clickAction['image'],
      title: json['title'],
      body: json['body'],
      userSlug: clickAction['userSlug'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      projectId: clickAction['ProjectId'],
      parentId: clickAction['ParentId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp = RegExp(r"^(.*?) exited from your Project: (.*?)\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final user = match.group(1)!;
    final post = match.group(2)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(
            text: user,
            style: textStyle.copyWith(fontWeight: FontWeight.w600),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                if (userId != CurrentUser.user.id) {
                  Get.toNamed(
                    Routes.other_user_profile,
                    arguments: {
                      "UserId": userId,
                    },
                  );
                } else {
                  Get.toNamed(Routes.profile);
                }
              },
          ),
          TextSpan(text: ' exited from your Project: ', style: textStyle),
          TextSpan(
              text: post,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  if (parentId != null) {
                    Get.toNamed(Routes.subProject_detail,
                        arguments: {'projectId': projectId});
                  } else {
                    Get.toNamed(Routes.project_detail,
                        arguments: {'projectId': projectId});
                  }
                }),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    if (parentId != null) {
      Get.toNamed(Routes.subProject_detail,
          arguments: {'projectId': projectId});
    } else {
      Get.toNamed(Routes.project_detail, arguments: {'projectId': projectId});
    }
  }
}

class ProjectMentionedNotification extends NotificationTypes {
  ProjectMentionedNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required this.projectId,
    required this.postId,
    super.actionStatus = true,
    required super.userSlug,
  });

  final int projectId;
  final int? postId;

  factory ProjectMentionedNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return ProjectMentionedNotification(
      id: json['id'],
      userSlug: clickAction['userSlug'],
      imageUrl: clickAction['image'],
      title: json['title'],
      body: json['body'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      projectId: clickAction['ProjectId'],
      postId: clickAction['PostId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp =
        RegExp(r"^Your Project:(.*?) is mentioned in a Post:(.*?)\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final project = match.group(1)!;
    final post = match.group(2)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(text: 'Your Project: ', style: textStyle),
          TextSpan(
              text: project,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.toNamed(Routes.project_detail,
                      arguments: {'projectId': projectId});
                }),
          TextSpan(text: ' is mentioned in a Post: ', style: textStyle),
          TextSpan(
              text: post,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.toNamed(Routes.post_detail,
                      arguments: {'postID': postId});
                }),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    Get.toNamed(Routes.post_detail,
        arguments: {'postID': postId});
  }
}

class UserMentionedNotification extends NotificationTypes {
  UserMentionedNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required this.projectId,
    required this.postId,
    super.actionStatus = true,
    required super.userSlug,
  });

  final int projectId;
  final int? postId;

  factory UserMentionedNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return UserMentionedNotification(
      id: json['id'],
      imageUrl: clickAction['image'],
      title: json['title'],
      userSlug: clickAction['userSlug'],
      body: json['body'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'],
      projectId: clickAction['ProjectId'] ?? 0,
      postId: clickAction['PostId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    final regExp = RegExp(r"^You were mentioned on this Post:(.*?)\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final post = match.group(1)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(text: 'You were mentioned on this Post: ', style: textStyle),
          TextSpan(
              text: post,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.toNamed(Routes.post_detail,
                      arguments: {'postID': postId, "source": "Notification"});
                }),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    Get.toNamed(Routes.post_detail,
        arguments: {'postID': postId, "source": "Notification"});
  }
}

class ToDoNotification extends NotificationTypes {
  ToDoNotification({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.body,
    required super.dateTime,
    required super.userId,
    required this.projectId,
    required this.postId,
    super.actionStatus = true,
    required super.userSlug,
  });

  final int projectId;
  final int? postId;

  factory ToDoNotification.fromJson(Map<String, dynamic> json) {
    final clickAction = jsonDecode(json['clickAction']);
    return ToDoNotification(
      id: json['id'],
      userSlug: clickAction['userSlug'] ?? "",
      imageUrl: clickAction['image'],
      title: json['title'],
      body: json['body'],
      dateTime: DateTime.parse(json['createdAt']),
      userId: clickAction['UserId'] ?? 0,
      projectId: clickAction['ProjectId'] ?? 0,
      postId: clickAction['PostId'],
    );
  }

  @override
  RichText buildStyledMessage() {
    // "You have this To Do due today: Another to do"
    final regExp = RegExp(r"^You have this To Do due today:(.*?)\.?$");
    final match = regExp.firstMatch(body);

    final textStyle = TextStyle(
      fontFamily: GoogleFonts.inter().fontFamily,
      fontSize: MySize.size16,
      fontWeight: FontWeight.w400,
      color: AppTheme.whiteWithBase,
    );

    if (match == null) {
      // fallback if the pattern doesn't match
      return RichText(
          text: TextSpan(text: body, style: TextStyle(color: Colors.black)));
    }

    final post = match.group(1)!;

    return RichText(
      text: TextSpan(
        style: TextStyle(color: Colors.black),
        children: [
          TextSpan(text: 'You have this To Do due today: ', style: textStyle),
          TextSpan(
              text: post,
              style: textStyle.copyWith(fontWeight: FontWeight.w600),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.find<BottomBarController>().currentIndex.value = 4;
                  Get.toNamed(Routes.Bottom_Bar, arguments: {
                    'index': 4,
                  });
                }),
        ],
      ),
    );
  }
  @override
  onTap(BuildContext context, NotificationsController controller) {
    Get.find<BottomBarController>().currentIndex.value = 4;
    Get.toNamed(Routes.Bottom_Bar, arguments: {
      'index': 4,
    });
  }
}
