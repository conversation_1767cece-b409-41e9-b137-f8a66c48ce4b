import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/notifications/components/notification_item.dart';
import 'package:incenti_ai/app/modules/notifications/controllers/notifications_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:shimmer/shimmer.dart';

import '../components/notification_warning.dart';

class NotificationsView extends GetWidget<NotificationsController> {
  const NotificationsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Space.height(48),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size30 ?? 30,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(7.0),
                    child: SvgPicture.asset(
                      AppImage.backArrow,
                      height: MySize.size28,
                      width: MySize.size28,
                      color: AppTheme.whiteWithNull,
                    ),
                  ),
                ),
                TypoGraphy(
                  text: 'Notifications',
                  level: 12,
                ),
                Space.width(MySize.size42 ?? 42),
              ],
            ),
          ),
          Space.height(10),
          Obx(
            () => !controller.isNotificationEnable.value ?
            Padding(
              padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(30)).copyWith(bottom: MySize.getScaledSizeHeight(20)),
              child: LocationWarning(),
            ) : SizedBox.shrink(),
          ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                controller.refreshNotifications();
              },
              child: Obx(
                () => controller.isLoading.value
                    ? ListView.builder(
                        controller: controller.scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        itemCount: 10,
                        itemBuilder: (context, index) => _loadingItem(),
                      )
                    : controller.notifications.isEmpty
                        ? _emptyList()
                        : ListView.builder(
                            controller: controller.scrollController,
                            physics: const AlwaysScrollableScrollPhysics(),
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            itemCount: controller.notifications.length,
                            itemBuilder: (context, index) {
                              final notification =
                                  controller.notifications[index];
                              return NotificationItem(
                                notification: notification,
                                controller: controller,
                              );
                            },
                          ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _emptyList() {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TypoGraphy(
            text: "You're all caught up!",
            level: 6,
            textAlign: TextAlign.center,
          ),
          Space.height(16),
          TypoGraphy(
            text: "You have no new notifications.",
            level: 3,
            textAlign: TextAlign.center,
          ),
          Space.height(70),
        ],
      ),
    );
  }

  Widget _loadingItem() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: AppTheme.baseShimmer,//300
                highlightColor: AppTheme.highlightShimmer,//100
                child: Container(
                  height: MySize.size50,
                  width: MySize.size50,
                  decoration: BoxDecoration(
                    color: Colors.grey[400],
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              Space.width(20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Space.height(4),
                    Shimmer.fromColors(
                      baseColor: AppTheme.baseShimmer,
                      highlightColor: AppTheme.highlightShimmer,
                      child: Container(
                        height: MySize.size32,
                        width: MySize.safeWidth,
                        decoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius:
                              BorderRadius.circular(MySize.size12 ?? 12),
                        ),
                      ),
                    ),
                    Space.height(8),
                    Shimmer.fromColors(
                      baseColor: AppTheme.baseShimmer,
                      highlightColor: AppTheme.highlightShimmer,
                      child: Container(
                        height: MySize.size12,
                        width: MySize.safeWidth,
                        decoration: ShapeDecoration(
                          color: Colors.grey[400],
                          shape: StadiumBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Space.height(20),
          Divider(
            thickness: MySize.size1,
            color: AppTheme.grey[0],
          )
        ],
      ),
    );
  }
}
