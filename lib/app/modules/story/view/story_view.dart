import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import 'package:incenti_ai/app/modules/profile_view/components/profile_widget_view.dart';
import 'package:incenti_ai/app/modules/story/view/story_video_player.dart';
import 'package:incenti_ai/app/modules/user_detail/components/image_picker_bottom_sheet.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import '../../../../constants/app_image.dart';
import '../../../../models/story_model.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../story_editor/common_component/draggable_widget.dart';
import '../controller/story_controller.dart';
import '../controller/story_utils.dart';
import 'cube_transform_animaion.dart';
import 'custom_story_view.dart';
import 'package:cached_network_image/cached_network_image.dart';

class AllStoryView extends GetWidget<StoryViewController> {
  const AllStoryView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: StoryViewController(),
      tag: (Get.arguments['isHighlight'] ?? false).toString(),
      builder: (controller) {
        final CarouselSliderController sliderController =
            CarouselSliderController();
        return Scaffold(
          backgroundColor: AppTheme.black,
          body: SafeArea(
            child: CarouselSlider.builder(
              autoSliderTransitionTime: Duration(milliseconds: 500),
              scrollPhysics: ClampingScrollPhysics(),
              unlimitedMode: false,
              controller: sliderController,
              slideBuilder: (userIndex) {
                final userId = controller.isHighlight.value
                    ? controller.highLightedStoriesList[userIndex].id
                    : controller.storiesList[userIndex].id;

                final lastViewedIndex =
                    userId != null ? controller.getLastStoryIndex(userId) : 0;
                return ClipRRect(
                  clipBehavior: Clip.hardEdge,
                  borderRadius: BorderRadius.circular(10),
                  child: Stack(
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        child: StoryView(
                          storyItems: controller.isHighlight.value
                              ? (controller.highLightedStoriesList[userIndex]
                                          .stories ??
                                      [])
                                  .map((story) {
                                  final storyIndex = controller
                                          .highLightedStoriesList[userIndex]
                                          .stories
                                          ?.indexOf(story) ??
                                      0;
                                  final shouldMarkAsShown =
                                      storyIndex < lastViewedIndex;

                                  return StoryItem(
                                      buildStoryItemFromApi(
                                        sliderController: sliderController,
                                        story: story.story!,
                                        controller: controller,
                                        showUploading:
                                            story.story?.showUploading ?? false,
                                        context: context,
                                      ),
                                      duration: Duration(
                                          seconds: story.story?.duration ?? 8),
                                      shown: shouldMarkAsShown);
                                }).toList()
                              : (controller.storiesList[userIndex].stories ??
                                      [])
                                  .map((story) {
                                  final storyIndex = controller
                                          .storiesList[userIndex].stories
                                          ?.indexOf(story) ??
                                      0;
                                  final shouldMarkAsShown =
                                      storyIndex < lastViewedIndex;

                                  return StoryItem(
                                      buildStoryItemFromApi(
                                        story: story,
                                        controller: controller,
                                        showUploading:
                                            story.showUploading ?? false,
                                        context: context,
                                        sliderController: sliderController,
                                      ),
                                      duration: Duration(
                                          seconds: story.duration ?? 8),
                                      shown: shouldMarkAsShown);
                                }).toList(),
                          onComplete: () {
                            if (userIndex <
                                (controller.isHighlight.value
                                        ? controller
                                            .highLightedStoriesList.length
                                        : controller.storiesList.length) -
                                    1) {
                              sliderController.nextPage();
                            } else {
                              Navigator.pop(context);
                            }
                          },
                          onVerticalSwipeComplete: (direction) {
                            if (direction == Direction.down) {
                              Navigator.pop(context);
                            } else if (direction == Direction.previous &&
                                userIndex > 0) {
                              controller.currentStoryIndex.value =
                                  controller.isHighlight.value
                                      ? (controller
                                                  .highLightedStoriesList[
                                                      userIndex - 1]
                                                  .stories
                                                  ?.length ??
                                              1) -
                                          1
                                      : (controller.storiesList[userIndex - 1]
                                                  .stories?.length ??
                                              1) -
                                          1;
                              sliderController.previousPage();
                            }
                          },
                          inline: true,
                          indicatorColor:
                              AppTheme.white.withValues(alpha: 0.40),
                          indicatorForegroundColor: AppTheme.white,
                          indicatorHeight: IndicatorHeight.medium,
                          onStoryShow: (storyItem, index) {
                            if (!controller.isChangingSlide.value) {
                              if(controller.isHighlight.value){
                                controller.highLightedStoriesList[userIndex].stories?[index].story?.isSeen.value = true;
                              }else {
                                controller.storiesList[userIndex]
                                    .stories?[index]
                                    .isSeen.value = true;
                              }
                              final userId = controller.isHighlight.value
                                  ? controller
                                      .highLightedStoriesList[userIndex].id
                                  : controller.storiesList[userIndex].id;

                              if (userId != null) {
                                controller.saveLastStoryIndex(userId, index);
                              }
                            }
                            controller.currentStoryIndex.value = index;
                            controller.currentStoryIndex.refresh();
                          },
                          controller: controller.storyController,
                          progressPosition: ProgressPosition.top,
                          repeat: true,
                          indicatorOuterPadding: EdgeInsets.only(
                            top: MySize.getScaledSizeHeight(25),
                            left: MySize.getScaledSizeWidth(30),
                            right: MySize.getScaledSizeWidth(30),
                          ),
                          storyViewController: controller,
                        ),
                      ),
                      Positioned.fill(
                        child: ValueListenableBuilder(
                          valueListenable: controller.visibleOverlay,
                          builder: (context, value, child) {
                            return AnimatedSwitcher(
                              duration: Duration(milliseconds: 300),
                              switchInCurve: Curves.easeOut,
                              switchOutCurve: Curves.easeIn,
                              transitionBuilder:
                                  (Widget child, Animation<double> animation) {
                                return FadeTransition(
                                  opacity: animation,
                                  child: child,
                                );
                              },
                              child: controller.visibleOverlay.value
                                  ? Padding(
                                      key: const ValueKey('overlay'),
                                      padding: EdgeInsets.only(
                                        top: MySize.getScaledSizeHeight(50),
                                      ),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          GestureDetector(
                                            onTap: controller.isHighlight.value ? null : () {
                                              controller.storyController
                                                  .pause();
                                              controller.isPlaying
                                                  .value = false;
                                              if (controller
                                                  .isHighlight.value) {
                                                Get.toNamed(
                                                    Routes.profile)
                                                    ?.then((value) {
                                                  controller
                                                      .storyController
                                                      .play();
                                                  controller.isPlaying
                                                      .value = true;
                                                });
                                              } else {
                                                if (controller
                                                    .storiesList[
                                                userIndex]
                                                    .id ==
                                                    CurrentUser
                                                        .user.id) {
                                                  Get.toNamed(Routes
                                                      .profile)
                                                      ?.then((value) {
                                                    controller
                                                        .storyController
                                                        .play();
                                                    controller.isPlaying
                                                        .value = true;
                                                  });
                                                } else {
                                                  Get.toNamed(
                                                      Routes
                                                          .other_user_profile,
                                                      arguments: {
                                                        "UserId": controller
                                                            .storiesList[
                                                        userIndex]
                                                            .id
                                                      })?.then((value) {
                                                    controller
                                                        .storyController
                                                        .play();
                                                    controller.isPlaying
                                                        .value = true;
                                                  });
                                                }
                                              }
                                            },
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  margin: EdgeInsets.only(
                                                    right:
                                                        MySize.getScaledSizeWidth(
                                                            10),
                                                    left:
                                                        MySize.getScaledSizeWidth(
                                                            30),
                                                  ),
                                                  height:
                                                      MySize.getScaledSizeHeight(
                                                          42),
                                                  width:
                                                      MySize.getScaledSizeWidth(
                                                          42),
                                                  child: ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(50),
                                                    child: controller.isHighlight.value
                                                        ? profileImage(
                                                            userName: controller
                                                                    .highLightedStoriesList[
                                                                        userIndex]
                                                                    .title ??
                                                                "",
                                                            url: controller.highLightedStoriesList[userIndex].image ??
                                                                "",
                                                            width: MySize.getScaledSizeWidth(
                                                                42),
                                                            height:
                                                                MySize.getScaledSizeHeight(
                                                                    42),
                                                            borderColor: Colors
                                                                .transparent,
                                                            color: AppTheme
                                                                .darkGrey[100])
                                                        : profileImage(
                                                            userName: controller
                                                                    .storiesList[userIndex]
                                                                    .firstName ??
                                                                "",
                                                            url: controller.storiesList[userIndex].image ?? "",
                                                            width: MySize.getScaledSizeWidth(42),
                                                            height: MySize.getScaledSizeHeight(42),
                                                            borderColor: Colors.transparent,
                                                            color: AppTheme.darkGrey[100]),
                                                  ),
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    SizedBox(
                                                      width: MySize.getScaledSizeWidth(
                                                          250),
                                                      child: TypoGraphy(
                                                        maxLines: 1,
                                                        overflow: TextOverflow.ellipsis,
                                                        text: controller
                                                                .isHighlight.value
                                                            ? controller
                                                                    .highLightedStoriesList[
                                                                        userIndex]
                                                                    .title ??
                                                                ""
                                                            : "${controller.storiesList[userIndex].firstName ?? ""} ${controller.storiesList[userIndex].lastName ?? ""}",
                                                        color: AppTheme.white,
                                                        level: 4,
                                                      ),
                                                    ),
                                                    Space.height(3),
                                                    TypoGraphy(
                                                      text: () {
                                                        if (controller
                                                            .isHighlight.value) {
                                                          return controller.getTimeDifference(controller
                                                              .highLightedStoriesList[
                                                          userIndex]
                                                              .createdAt ??
                                                              DateTime.now());
                                                        } else {
                                                          DateTime dateTime = controller
                                                              .currentStoryIndex
                                                              .value <
                                                              (controller
                                                                  .storiesList[
                                                              userIndex]
                                                                  .stories ??
                                                                  [])
                                                                  .length
                                                              ? (controller
                                                              .storiesList[
                                                          userIndex]
                                                              .stories?[controller
                                                              .currentStoryIndex
                                                              .value]
                                                              .createdAt ??
                                                              DateTime.now())
                                                              : (controller
                                                              .storiesList[
                                                          userIndex]
                                                              .stories
                                                              ?.first
                                                              .createdAt ??
                                                              DateTime
                                                                  .now());
                                                       return controller.getTimeDifference(dateTime);
                                                        }
                                                      }(),
                                                      color: AppTheme.white,
                                                      level: 2,
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              HapticFeedback.lightImpact();
                                              Navigator.pop(context);
                                            },
                                            child: Container(
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      40),
                                              width:
                                                  MySize.getScaledSizeWidth(40),
                                              margin: EdgeInsets.only(
                                                right:
                                                    MySize.getScaledSizeWidth(
                                                        30),
                                              ),
                                              decoration: BoxDecoration(
                                                color: AppTheme.white
                                                    .withValues(alpha: 0.15),
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Icon(
                                                Icons.clear_rounded,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : SizedBox(key: const ValueKey('empty')),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
              slideTransform: CubeTransform(
                perspectiveScale: 0.001,
                rotationAngle: 90,
              ),
              itemCount: controller.isHighlight.value
                  ? controller.highLightedStoriesList.length
                  : controller.storiesList.length,
              initialPage: controller.currentUserIndex.value,
              enableAutoSlider: false,
              onSlideChanged: (index) {
                controller.hideTagContainer();
                controller.currentUserIndex.value = index;
                controller.storyController.pause();

                final userId = controller.isHighlight.value
                    ? controller.highLightedStoriesList[index].id
                    : controller.storiesList[index].id;

                final lastViewedIndex =
                    userId != null ? controller.getLastStoryIndex(userId) : 0;

                if (controller.isHighlight.value) {
                  if (lastViewedIndex >=
                      (controller
                              .highLightedStoriesList[index].stories?.length ??
                          0)) {
                    controller.currentStoryIndex.value = (controller
                                .highLightedStoriesList[index]
                                .stories
                                ?.length ??
                            1) -
                        1;
                    if (controller.highLightedStoriesList[index].stories !=
                            null &&
                        controller.highLightedStoriesList[index].stories!
                            .isNotEmpty) {
                      controller.highLightedStoriesList[index].stories!.last
                          .story?.isSeen.value = false;
                    }
                  } else {
                    controller.currentStoryIndex.value = lastViewedIndex;
                  }
                } else {
                  if (lastViewedIndex >=
                      (controller.storiesList[index].stories?.length ?? 0)) {
                    controller.currentStoryIndex.value =
                        (controller.storiesList[index].stories?.length ?? 1) -
                            1;
                    if (controller.storiesList[index].stories != null &&
                        controller.storiesList[index].stories!.isNotEmpty) {
                      controller.storiesList[index].stories!.last.isSeen.value =
                          false;
                    }
                  } else {
                    controller.currentStoryIndex.value = lastViewedIndex;
                  }
                }

                controller.storyController.play();
              },
              onSlideEnd: () {
                controller.isChangingSlide.value = false;
              },
              onSlideStart: () {
                controller.isChangingSlide.value = true;
              },
            ),
          ),
        );
      },
    );
  }
}

Widget buildStoryItemFromApi({
  required OneStory story,
  Key? key,
  required StoryViewController controller,
  bool showUploading = false,
  required BuildContext context,
  required CarouselSliderController sliderController,
}) {
  final isText = story.mediaType == 'text';
  final isVideo = story.mediaType == 'video';
  final hasProperties = (story.properties ?? []).isNotEmpty;

  bool isNetworkUrl(String? url) {
    return url != null &&
        (url.startsWith('http://') || url.startsWith('https://'));
  }

  return Container(
    key: key,
    decoration: BoxDecoration(
      color: Colors.black,
    ),
    clipBehavior: Clip.hardEdge,
    child: Stack(
      clipBehavior: Clip.hardEdge,
      alignment: Alignment.center,
      children: <Widget>[
        if (isVideo && (story.mediaLink ?? '').isNotEmpty)
          controller.isChangingSlide.value
              ? AspectRatio(
                  aspectRatio: 9 / 16,
                  child: CachedNetworkImage(
                    imageUrl: story.thumbnailPath ?? '',
                  ),
                )
              : StoryVideoPlayer(
                  isPlaying: controller.isPlaying,
                  videoUrl: story.mediaLink ?? '',
                  storyController: controller.storyController,
                  isM3U8: story.mediaLink?.contains('.m3u8') ?? false,
                  oneStory: story,
                ),
        if ((story.overlayImage ?? '').isNotEmpty)
          isNetworkUrl(story.overlayImage)
              ? CachedNetworkImage(
            imageUrl: (story.overlayImage ?? '') ,
            cacheManager: CachedNetworkImageProvider.defaultCacheManager,
            placeholder: (context, url) {
              controller.storyController.pause();
              return const Center(child: SizedBox(height:70 , width: 70,child: CircularProgressIndicator(color: Colors.white,strokeWidth: 1.5,)));
            },
            imageBuilder: (context, imageProvider) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                controller.storyController.play();
              });
              return Image(
                image: imageProvider,
                fit: BoxFit.cover,
              );
            },
            errorWidget: (context, url, error) {
              controller.storyController.play();
              return const Center(child: TypoGraphy(text: "Can't load the image",color: Colors.white,level: 4,));
            },
          ) : Image.file(File(story.overlayImage ?? '')),
        Align(
          alignment: Alignment.centerLeft,
          heightFactor: 1,
          child: SizedBox(
            width: 70,
            child: InkWell(
              onTap: () {
                controller.hideTagContainer();
                if (controller.currentStoryIndex.value == 0 &&
                    controller.currentUserIndex.value > 0) {
                  controller.storyController.pause();
                  final prevUserStoriesLength = controller.isHighlight.value
                      ? (controller
                              .highLightedStoriesList[
                                  controller.currentUserIndex.value - 1]
                              .stories
                              ?.length ??
                          0)
                      : (controller
                              .storiesList[
                                  controller.currentUserIndex.value - 1]
                              .stories
                              ?.length ??
                          0);

                  if (prevUserStoriesLength > 0) {
                    controller.currentStoryIndex.value = 0;
                    sliderController.previousPage();
                  }
                } else if (controller.currentStoryIndex.value > 0) {
                  controller.storyController.previous();
                }
              },
            ),
          ),
        ),
        if (hasProperties)
            Positioned.fill(
              child: Stack(
                clipBehavior: Clip.hardEdge,
                alignment: Alignment.center,
                children: (story.properties ?? []).map(
                  (e) {
                    return  buildPositionedItem(e, context: context, controller: controller);
                  },
                ).toList(),
              ),
            ),
        Obx(() => Positioned(
              left: ((controller.currentTagItem.value?.position.dx ?? 1) *
                      MySize.screenWidth) +
                  (MySize.screenWidth / MySize.getScaledSizeWidth(2.65)) -
                  30,
              top: (controller.currentTagItem.value?.position.dy ?? 0) > 0
                  ? (((controller.currentTagItem.value?.position.dy ?? 1) *
                          MySize.screenHeight) +
                      (MySize.screenHeight / MySize.getScaledSizeHeight(2.6)) -
                      60)
                  : (((controller.currentTagItem.value?.position.dy ?? 1) *
                          MySize.screenHeight) +
                      (MySize.screenHeight / MySize.getScaledSizeHeight(2.6)) +
                      60),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                switchInCurve: Curves.easeInOut,
                switchOutCurve: Curves.easeInOut,
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: child,
                  );
                },
                child: controller.isTagVisible.value &&
                        controller.currentTagItem.value != null
                    ? Center(
                        child: GestureDetector(
                          onTap: () {
                            if (controller.currentTagItem.value?.subProjectId !=
                                    null &&
                                controller.currentTagItem.value?.subProjectId !=
                                    0) {
                              Get.toNamed(Routes.subProject_detail, arguments: {
                                "projectId": controller
                                    .currentTagItem.value?.subProjectId,
                              })!
                                  .then(
                                (value) {
                                  controller.hideTagContainer();
                                  controller.storyController.play();
                                },
                              );
                            } else if (controller
                                        .currentTagItem.value?.projectId !=
                                    null &&
                                controller.currentTagItem.value?.projectId !=
                                    0) {
                              Get.toNamed(Routes.project_detail, arguments: {
                                "projectId":
                                    controller.currentTagItem.value?.projectId,
                              })!
                                  .then(
                                (value) {
                                  controller.hideTagContainer();
                                  controller.storyController.play();
                                },
                              );
                            }
                          },
                          child: tagContainer(
                              text: controller.currentTagItem.value?.text ?? '',
                              isTop: (controller
                                          .currentTagItem.value?.position.dy ??
                                      0) >
                                  0),
                        ),
                      )
                    : SizedBox.shrink(),
              ),
            )),
        Obx(() =>
        !controller.isHighlight.value && Get.find<ExploreController>().isUploading.value && showUploading
                ? Positioned(
                    left: 20,
                    bottom: 20,
                    child: Row(
                      children: [
                        SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: AppTheme.white,
                              strokeWidth: 3,
                            )),
                        Space.width(10),
                        TypoGraphy(
                          text: 'Uploading',
                          level: 4,
                          color: AppTheme.white,
                        ),
                      ],
                    ))
                : SizedBox()),
        if (story.userId == CurrentUser.user.id)
          Positioned(
            right: 20,
            bottom: 0,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                controller.isPlaying.value = false;
                controller.storyController.pause();
                !controller.isHighlight.value
                    ? ImagePickerBottomSheet.show(
                        context: context,
                        child: Column(
                          children: [
                            Space.height(40),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                showStoryDeleteDialogue(
                                    controller: controller,
                                    context: context,
                                    storyId: story.id ?? 0);
                              },
                              child: Row(
                                children: [
                                  Space.width(30),
                                  SvgPicture.asset(
                                    AppImage.trashIcon,
                                    height: MySize.size24,
                                    width: MySize.size24,
                                    color: AppTheme.red,
                                  ),
                                  Space.width(20),
                                  TypoGraphy(
                                    text: "Delete",
                                    level: 5,
                                    color: AppTheme.red,
                                  )
                                ],
                              ),
                            ),
                            Space.height(40),
                          ],
                        ))
                    : ImagePickerBottomSheet.show(
                        context: context,
                        child: Column(
                          children: [
                            Space.height(40),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                if ((controller
                                                .highLightedStoriesList[
                                                    controller
                                                        .currentUserIndex.value]
                                                .stories ??
                                            [])
                                        .length >
                                    1) {
                                  showStoryDeleteDialogue(
                                      controller: controller,
                                      context: context,
                                      storyId: story.id ?? 0,
                                      isHighlight: true);
                                } else {
                                  showHighlightDeleteDialogue(
                                      context: context,
                                      highlightId: controller
                                              .highLightedStoriesList[controller
                                                  .currentUserIndex.value]
                                              .id ??
                                          0,
                                      isInStory: true);
                                }
                              },
                              child: Row(
                                children: [
                                  Space.width(30),
                                  SvgPicture.asset(
                                    AppImage.trashIcon,
                                    height: MySize.size24,
                                    width: MySize.size24,
                                    color: AppTheme.red,
                                  ),
                                  Space.width(20),
                                  TypoGraphy(
                                    text: (controller
                                                        .highLightedStoriesList[
                                                            controller
                                                                .currentUserIndex
                                                                .value]
                                                        .stories ??
                                                    [])
                                                .length >
                                            1
                                        ? "Remove Story"
                                        : "Delete Highlight",
                                    level: 5,
                                    color: AppTheme.red,
                                  )
                                ],
                              ),
                            ),
                            Space.height(40),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                Get.toNamed(Routes.story_cover_selector,
                                        arguments: {
                                      "highlight": controller
                                              .highLightedStoriesList[
                                          controller.currentUserIndex.value],
                                      'isFromHighlight': true
                                    })!
                                    .then(
                                  (value) {
                                    controller.isPlaying.value = true;
                                    controller.storyController.play();
                                  },
                                );
                              },
                              child: Row(
                                children: [
                                  Space.width(30),
                                  SvgPicture.asset(
                                    AppImage.editIcon,
                                    height: MySize.size24,
                                    width: MySize.size24,
                                    color: AppTheme.whiteWithBase,
                                  ),
                                  Space.width(20),
                                  TypoGraphy(
                                    text: "Edit Highlight",
                                    level: 5,
                                  )
                                ],
                              ),
                            ),
                            Space.height(40),
                          ],
                        ));
              },
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  spacing: MySize.getScaledSizeHeight(5),
                  children: List.generate(
                    3,
                    (index) {
                      return Container(
                        height: 5,
                        width: 5,
                        decoration: BoxDecoration(
                            color: AppTheme.white, shape: BoxShape.circle),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
      ],
    ),
  );
}

Widget tagContainer({required String text, bool isTop = false}) {
  return Stack(
    clipBehavior: Clip.none,
    alignment: Alignment.center,
    children: [
      !isTop
          ? Positioned(
              top: -9,
              child: ClipPath(
                clipper: HalfTriangleClipper(),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 500, sigmaY: 500),
                  child: Container(
                    color: AppTheme.white.withValues(alpha: 0.1),
                    width: 20,
                    height: 10,
                    child: SizedBox(),
                  ),
                ),
              ),
            )
          : Positioned(
              bottom: -9,
              child: RotationTransition(
                turns: AlwaysStoppedAnimation(180 / 360),
                child: ClipPath(
                  clipper: HalfTriangleClipper(),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 500, sigmaY: 500),
                    child: Container(
                      color: AppTheme.white.withValues(alpha: 0.1),
                      width: 20,
                      height: 10,
                    ),
                  ),
                ),
              ),
            ),
      ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 500, sigmaY: 500),
          child: IntrinsicHeight(
            child: IntrinsicWidth(
              child: Container(
                alignment: Alignment.center,
                constraints: const BoxConstraints(minHeight: 40, minWidth: 120),
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: AppTheme.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(9),
                ),
                child: TypoGraphy(
                  text: text,
                  color: Colors.white,
                  level: 4,
                ),
              ),
            ),
          ),
        ),
      ),
    ],
  );
}

class HalfTriangleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.moveTo(0, size.height);
    path.lineTo(size.width / 2, 0);
    path.lineTo(size.width, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

class CustomStyleArrow extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1
      ..style = PaintingStyle.fill;

    final double triangleH = 15;
    final double triangleW = 20.0;
    final double width = size.width;
    final double height = size.height;

    final Path trianglePath = Path()
      ..moveTo(width / 2 - triangleW / 2, height)
      ..lineTo(width / 2, triangleH + height)
      ..lineTo(width / 2 + triangleW / 2, height)
      ..close();

    canvas.drawPath(trianglePath, paint);
    final BorderRadius borderRadius = BorderRadius.circular(15);
    final Rect rect = Rect.fromLTRB(0, 0, width, height);
    final RRect outer = borderRadius.toRRect(rect);
    canvas.drawRRect(outer, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

void showStoryDeleteDialogue(
    {required BuildContext context,
    required int storyId,
    bool isHighlight = false,
    required StoryViewController controller}) {
  showDialog(
      context: context,
      builder: (context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Dialog(
            insetPadding:
                EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(70)),
            backgroundColor: Color(0xff25282d),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Padding(
              padding: EdgeInsets.all(MySize.getScaledSizeHeight(20))
                  .copyWith(top: MySize.getScaledSizeHeight(30)),
              child: Column(
                spacing: MySize.getScaledSizeHeight(10),
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  TypoGraphy(
                    text: isHighlight
                        ? 'Remove this story?'
                        : 'Delete this story?',
                    level: 4,
                    fontWeight: FontWeight.w600,
                    color: Color(0xfff2f4f4),
                  ),
                  TypoGraphy(
                    textAlign: TextAlign.center,
                    text:
                        'Are you sure you want to ${isHighlight ? 'Remove' : 'Delete'} this story? Once ${isHighlight ? 'removed' : 'deleted'} , it cannot be recovered.',
                    level: 4,
                    fontWeight: FontWeight.w400,
                    color: Color(0xfff2f4f4),
                  ),
                  Space.height(15),
                  InkWell(
                    onTap: isHighlight
                        ? () {
                            HapticFeedback.lightImpact();
                            Get.back();
                            controller.callApiForRemoveStoryFromHighlight(
                                context: context);
                            controller.update();
                          }
                        : () {
                            HapticFeedback.lightImpact();
                            Get.back();
                            Get.back();
                            Get.find<ExploreController>()
                                .storyDataList
                                .removeWhere((e) => e.id == storyId);
                            controller.callApiForDeleteStory(
                                context: context, storyId: storyId);
                          },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            textAlign: TextAlign.center,
                            text: isHighlight ? 'Remove' : 'Delete',
                            level: 3,
                            fontWeight: FontWeight.w500,
                            color: Color(0xfff56475),
                          ),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Get.back();
                      Get.find<StoryViewController>(
                              tag: (Get.arguments['isHighlight'] ?? false)
                                  .toString())
                          .storyController
                          .play();
                      Get.find<StoryViewController>(
                              tag: (Get.arguments['isHighlight'] ?? false)
                                  .toString())
                          .isPlaying
                          .value = true;
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            textAlign: TextAlign.center,
                            text: 'Cancel',
                            level: 3,
                            fontWeight: FontWeight.w500,
                            color: Color(0xfff2f4f4),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      });
}

Widget buildPositionedItem(
  EditableItem item, {
  required BuildContext context,
  required StoryViewController controller,
}) {
  return DraggableWidget(
    upperBound: 1,
    lowerBound: 0.98,
    draggableWidget: item,
    onTap: () {
    if (controller.isTagVisible.value &&
        controller.currentTagItem.value == item) {
      controller.hideTagContainer();
      controller.storyController.play();
    } else {
      controller.storyController.pause();
      controller.showTagContainer(item);
    }
  }, fontList: [
    'Roboto',
    'Lato',
    'Open Sans',
    'Montserrat',
    'Poppins',
    'Raleway',
    'Ubuntu',
    'Playfair Display',
    'Quicksand',
    'Dancing Script',
    'Alegreya',
    'B612',
    'Varela',
    'Vollkorn',
    'Rakkas',
    'Neonderthaw',
    'Sacramento',
    'UnifrakturMaguntia'
  ],
  );
}

// Widget buildItemContent(EditableItem item) {
//   switch (item.type) {
//     case StoryType.text:
//       return buildTextWidget(item);
//     default:
//       return const SizedBox.shrink();
//   }
// }
//
// Widget buildTextWidget(EditableItem item) {
//   final fonts = [
//     'Roboto',
//     'Lato',
//     'Open Sans',
//     'Montserrat',
//     'Poppins',
//     'Raleway',
//     'Ubuntu',
//     'Playfair Display',
//     'Quicksand',
//     'Dancing Script',
//     'Alegreya',
//     'B612',
//     'Varela',
//     'Vollkorn',
//     'Rakkas',
//     'Neonderthaw',
//     'Sacramento',
//     'UnifrakturMaguntia'
//   ];
//
//   final double maxWidth = MySize.screenWidth * 0.9;
//
//   return ConstrainedBox(
//     constraints: BoxConstraints(maxWidth: maxWidth),
//     child: Stack(
//       children: [
//         Text(
//           item.text,
//           textAlign: item.textAlign,
//           softWrap: true,
//           overflow: TextOverflow.visible,
//           style: GoogleFonts.getFont(fonts[item.fontFamily % fonts.length],
//               fontWeight: FontWeight.w500,
//               fontSize: item.fontSize,
//               color: item.textColor,
//               background: Paint()
//                 ..strokeWidth = 20.0
//                 ..color = item.backGroundColor
//                 ..style = PaintingStyle.stroke
//                 ..strokeJoin = StrokeJoin.round
//                 ..filterQuality = FilterQuality.high
//                 ..strokeCap = StrokeCap.round
//                 ..maskFilter = const MaskFilter.blur(BlurStyle.solid, 1)),
//         ),
//         Text(
//           item.text,
//           textAlign: item.textAlign,
//           softWrap: true,
//           overflow: TextOverflow.visible,
//           style: GoogleFonts.getFont(fonts[item.fontFamily % fonts.length],
//               fontWeight: FontWeight.w500,
//               fontSize: item.fontSize,
//               color: item.textColor,
//               background: Paint()
//                 ..strokeWidth = 20.0
//                 ..color = item.backGroundColor
//                 ..style = PaintingStyle.fill
//                 ..strokeJoin = StrokeJoin.round
//                 ..filterQuality = FilterQuality.high
//                 ..strokeCap = StrokeCap.round
//                 ..maskFilter = const MaskFilter.blur(BlurStyle.solid, 1)),
//         ),
//       ],
//     ),
//   );
// }
