import 'dart:developer';
import 'package:better_player_plus/better_player_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../models/story_model.dart';
import '../../../../utillites/app_theme.dart';
import '../controller/story_utils.dart';

/*
class StoryVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final StoryController storyController;
  final bool isM3U8;
  final OneStory? oneStory;
  final RxBool isPlaying;
  final VideoPlayerController? cachedVideoController;

   const StoryVideoPlayer({
    super.key,
    required this.videoUrl,
    required this.storyController,
    this.isM3U8 = false,
    required this.isPlaying,
    this.oneStory,
    required this.cachedVideoController,
  });

  @override
  StoryVideoPlayerState createState() => StoryVideoPlayerState();
}

class StoryVideoPlayerState extends State<StoryVideoPlayer> {
  VideoPlayerController? _controller;
  bool _isDisposed = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();

    ever(widget.isPlaying, (bool isPlaying) {
      try {
        if (isPlaying) {
          _controller?.play();
        } else {
          _controller?.pause();
        }
      } catch (e) {
      }
    });
  }

  @override
  void didUpdateWidget(StoryVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.videoUrl != widget.videoUrl) {
      if (_controller != null && widget.isM3U8 == oldWidget.isM3U8) {
        _resetVideoController();
      } else {
        _disposeController();
        _initializeVideoPlayer();
      }
    }
  }

  void _resetVideoController() {
    widget.storyController.pause();
    try {

      if(widget.oneStory?.videoController != null){
        _controller = widget.oneStory?.videoController;
        setState(() {
          _isInitialized = true;
        });
        _controller?.seekTo(Duration.zero);
        _controller?.play();
        widget.storyController.play();
      }else if(widget.cachedVideoController != null){
        _controller = widget.cachedVideoController;
        setState(() {
          _isInitialized = true;
        });
        _controller?.seekTo(Duration.zero);
        _controller?.play();
        widget.storyController.play();
      }else {

        _controller =
        widget.isM3U8
            ? VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl),
          videoPlayerOptions: VideoPlayerOptions(
            mixWithOthers: true,
            allowBackgroundPlayback: false,
          ), formatHint: VideoFormat.hls,)
            : VideoPlayerController.file(File(widget.videoUrl),
            videoPlayerOptions: VideoPlayerOptions(
              mixWithOthers: true,
              allowBackgroundPlayback: false,
            ));



        _controller?.initialize().then((_) {
          if (!_isDisposed && mounted) {
            setState(() {
              _isInitialized = true;
            });
            _controller?.seekTo(Duration.zero);
            _controller?.play();
            widget.storyController.play();
          }
        }).catchError((error) {
          if (mounted) {
            widget.storyController.play();
          }
        });
      }
    } catch (e) {
      _disposeController();
      _initializeVideoPlayer();
    }
  }

  void _initializeVideoPlayer() {
    widget.storyController.pause();

    if (widget.oneStory?.videoController != null) {
      try {
        _controller = widget.oneStory?.videoController;

        if (_controller?.value.isInitialized ?? false) {
          _isInitialized = true;
          _controller?.play();
          widget.storyController.play();
          return;
        } else {
          _controller?.initialize().then((_) {
            if (!_isDisposed && mounted) {
              setState(() {
                _isInitialized = true;
              });
              _controller?.play();
              widget.storyController.play();
            }
          }).catchError((error) {
            if (mounted) {
              widget.storyController.play();
            }
          });
          return;
        }
      } catch (e) {
      }
    }else {
      try {
        _controller =
        widget.isM3U8
            ? VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl),
          videoPlayerOptions: VideoPlayerOptions(
            mixWithOthers: true,
            allowBackgroundPlayback: false,
          ), formatHint: VideoFormat.hls,)
            : VideoPlayerController.file(File(widget.videoUrl),
            videoPlayerOptions: VideoPlayerOptions(
              mixWithOthers: true,
              allowBackgroundPlayback: false,
            ));

        _controller?.initialize().then((_) {
          if (!_isDisposed && mounted) {
            setState(() {
              _isInitialized = true;
            });
            _controller?.play();
            widget.storyController.play();
          }
        }).catchError((error) {
          if (mounted) {
            widget.storyController.play();
          }
        });
      } catch (e) {
      }
    }
  }

  void _disposeController() {
    try {
      _controller?.pause();
      _controller?.dispose();
      _controller = null;
      _isInitialized = false;
      widget.isPlaying.value = true;
      widget.oneStory?.recreateVideoController();
    } catch (e) {
      log('Error disposing controller: $e');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _disposeController();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.black,
      body: Center(
        child:  _controller != null
            ? _isInitialized ?AspectRatio(
          aspectRatio: _controller!.value.aspectRatio,
          child: VideoPlayer(_controller!),
        )
            : widget.oneStory?.thumbnailPath != null
            ? CachedNetworkImage(
          imageUrl: widget.oneStory!.thumbnailPath!,
        )
            : SizedBox()
            : Center(
          child: SizedBox(
            width: 70,
            height: 70,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
          ),
        ),
      ),
    );
  }
}
*/


class StoryVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final StoryController storyController;
  final bool isM3U8;
  final OneStory? oneStory;
  final RxBool isPlaying;

  const StoryVideoPlayer({
    super.key,
    required this.videoUrl,
    required this.storyController,
    this.isM3U8 = false,
    required this.isPlaying,
    this.oneStory,
  });

  @override
  StoryVideoPlayerState createState() => StoryVideoPlayerState();
}

class StoryVideoPlayerState extends State<StoryVideoPlayer> {
  BetterPlayerController? _controller;
  bool _isDisposed = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
    ever(widget.isPlaying, (bool isPlaying) {
      try {
        if (isPlaying) {
          _controller?.play();
        } else {
          _controller?.pause();
        }
      } catch (e) {
        log('error ==> $e}');
      }
    });
  }

  @override
  void didUpdateWidget(StoryVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.videoUrl != widget.videoUrl) {
      _disposeController();
      _initializeVideoPlayer();
    }
  }

  void _initializeVideoPlayer() {
    widget.storyController.pause();
    try {
      final isNetwork = Uri.parse(widget.oneStory?.mediaLink ?? '').isAbsolute &&
          ((widget.oneStory?.mediaLink ?? '').startsWith('http://') || (widget.oneStory?.mediaLink ?? '').startsWith('https://'));
      final BetterPlayerDataSource dataSource = BetterPlayerDataSource(
        isNetwork ? BetterPlayerDataSourceType.network : BetterPlayerDataSourceType.file,
        widget.oneStory?.mediaLink ?? '',
        videoFormat: (widget.oneStory?.mediaLink ?? '').contains('.m3u8') ? BetterPlayerVideoFormat.hls : null,
        bufferingConfiguration: const BetterPlayerBufferingConfiguration(
          minBufferMs: 5000,
          maxBufferMs: 10000,
          bufferForPlaybackMs: 1000,
          bufferForPlaybackAfterRebufferMs: 2000,
        ),
        cacheConfiguration: isNetwork && !Get.find<ExploreController>().isUploading.value
            ?   BetterPlayerCacheConfiguration(

          useCache: true,
          preCacheSize: 10 * 1024 * 1024,
          maxCacheSize: 100 * 1024 * 1024,
          maxCacheFileSize: 50 * 1024 * 1024,
          key: "story_video_${widget.oneStory?.mediaLink.hashCode}",
        )
            : null,
      );

      final betterPlayerConfiguration = BetterPlayerConfiguration(
        autoPlay: true,
        looping: false,
        aspectRatio: 9 / 16,
        handleLifecycle: true,
        fit: BoxFit.contain,
        autoDetectFullscreenAspectRatio: true,
        expandToFill: true,
        autoDispose: true,
        controlsConfiguration: BetterPlayerControlsConfiguration(
          showControls: false,
        ),
      );

      _controller = BetterPlayerController(betterPlayerConfiguration);
      _controller?.setupDataSource(dataSource).then((_) {
        if (!_isDisposed && mounted) {
          _isInitialized = true;
          _controller?.play();
          widget.storyController.play();
          setState(() {

          });
        }
      }).catchError((error) {
        widget.storyController.pause();
        final isNetwork = Uri.parse(widget.oneStory?.mediaLink ?? '').isAbsolute &&
            ((widget.oneStory?.mediaLink ?? '').startsWith('http://') || (widget.oneStory?.mediaLink ?? '').startsWith('https://'));

        final BetterPlayerDataSource dataSource = BetterPlayerDataSource(
          isNetwork ? BetterPlayerDataSourceType.network : BetterPlayerDataSourceType.file,
          widget.oneStory?.mediaLink ?? '',
          videoFormat: (widget.oneStory?.mediaLink ?? '').contains('.m3u8') ? BetterPlayerVideoFormat.hls : null,
          bufferingConfiguration: const BetterPlayerBufferingConfiguration(
            minBufferMs: 5000,
            maxBufferMs: 10000,
            bufferForPlaybackMs: 1000,
            bufferForPlaybackAfterRebufferMs: 2000,
          ),
        );

        final betterPlayerConfiguration = BetterPlayerConfiguration(
          autoPlay: true,
          looping: false,
          aspectRatio: 9 / 16,
          handleLifecycle: true,
          fit: BoxFit.contain,
          autoDetectFullscreenAspectRatio: true,
          expandToFill: true,
          autoDispose: true,
          controlsConfiguration: BetterPlayerControlsConfiguration(
            showControls: false,
          ),
        );

        _controller = BetterPlayerController(betterPlayerConfiguration);

        _controller?.setupDataSource(dataSource).then((_) {
          if (!_isDisposed && mounted) {
            _isInitialized = true;
            _controller?.play();
            widget.storyController.play();
            setState(() {

            });
          }
        });
      });
    } catch (e) {
      log('error ===> $e');
    } finally {
      widget.storyController.play();
    }
  }

  void _disposeController() {
    try {
      _controller?.pause();
      _controller?.dispose();
      _controller = null;
      widget.isPlaying.value = true;
    } catch (e) {
      log('Error disposing controller: $e');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _disposeController();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.black,
      body: Center(
        child: _controller != null
            ? _isInitialized ? BetterPlayer(controller: _controller!,) : widget.oneStory?.thumbnailPath != null
            ? AspectRatio(
          aspectRatio: 9/16,
              child: CachedNetworkImage(
                        imageUrl: widget.oneStory!.thumbnailPath!,
                      ),
            ) : SizedBox()
            : Center(
          child: SizedBox(
            width: 70,
            height: 70,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
          ),
        ),
      ),
    );
  }
}