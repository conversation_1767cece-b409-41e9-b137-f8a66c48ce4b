import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/explore/controllers/explore_controller.dart';
import 'package:incenti_ai/app/modules/story/controller/story_utils.dart';
import 'package:video_player/video_player.dart';
import '../../../../constants/constant.dart';
import '../../../../models/get_all_highlights_model.dart';
import '../../../../models/story_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../profile_view/controllers/profile_view_controller.dart';
import '../../project_detail_view/controllers/project_detail_view_controller.dart';
import '../../sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';


class StoryViewController extends GetxController {
  var argument = Get.arguments;
  RxInt currentUserIndex = 0.obs;
  RxInt currentStoryIndex = 0.obs;
  late StoryController storyController;
  RxList<Story> storiesList = <Story>[].obs;
  RxList<Highlight> highLightedStoriesList = <Highlight>[].obs;
  RxBool isPlaying = true.obs;
  RxBool isLoading = true.obs;
  RxDouble progress = 0.0.obs;
  Rx<Duration> duration = Duration.zero.obs;
  Rx<Duration> position = Duration.zero.obs;
  VideoPlayerController? videoController;
  ValueNotifier<bool> visibleOverlay = ValueNotifier(true);
  RxBool isTagVisible = false.obs;
  Rx<EditableItem?> currentTagItem = Rx<EditableItem?>(null);
  ApiManager apiManager = ApiManager();
  RxMap<int,RxMap<int,VideoPlayerController>> cachedVideoController = <int,RxMap<int,VideoPlayerController>>{}.obs;
  RxBool isHighlight = false.obs;
  RxBool isChangingSlide = false.obs;
  RxInt subProjectId = 0.obs;
  RxInt projectId = 0.obs;

  @override
  void onInit() {
    super.onInit();
    projectId.value = argument['projectId'] ?? 0;
    subProjectId.value = argument['subProjectId'] ?? 0;
    isHighlight.value = argument['isHighlight'] ?? false;
    storiesList.value = argument['storiesList'] ?? [];
    highLightedStoriesList.value = argument['highLightedStoriesList'] ?? [];
    currentUserIndex.value = argument['index'] ?? 0;
    storyController = StoryController();
  }

  void saveLastStoryIndex(int userId, int storyIndex) {
    userLastStoryIndexMap[userId] = storyIndex;
  }

  int getLastStoryIndex(int userId) {
    return userLastStoryIndexMap[userId] ?? 0;
  }

  final Map<int, int> userLastStoryIndexMap = {};


  @override
  void onClose() {
    storyController.dispose();
    super.onClose();
  }

  @override
  void dispose() {
    storyController.dispose();
    super.dispose();
  }

  void showTagContainer(EditableItem item) {
    currentTagItem.value = item;
    isTagVisible.value = true;
    isPlaying.value = false;
  }

  void hideTagContainer() {
    isTagVisible.value = false;
    currentTagItem.value = null;
    isPlaying.value = true;
  }



  Future<void> callApiForDeleteStory(
      {required BuildContext context,
        required int storyId,
      }) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
    ApiModel("/stories/$storyId", APIType.DELETE);
    isLoading.value = false;
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isLoading.value = true;
            Get.find<ExploreController>().callApiForGetCurrentStory();
          }
        } catch (error) {
          isLoading.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = true;
      },
    );
  }

  Future<void> callApiForRemoveStoryFromHighlight({
    required BuildContext context,
  }) async {
    FocusScope.of(context).unfocus();
    try {
      final controller =   Get.find<StoryViewController>(
          tag: (Get.arguments['isHighlight'] ?? false)
              .toString());

      Map<String, dynamic> dict = {
        "title": highLightedStoriesList[currentUserIndex.value].title,
        "AddStoriesIds": [],
        "RemoveStoriesIds": [highLightedStoriesList[currentUserIndex.value].stories?[currentStoryIndex.value].story?.id],
        "image" : highLightedStoriesList[currentUserIndex.value].image,
      };


      controller.highLightedStoriesList[controller.currentUserIndex.value].stories?.removeAt(controller.currentStoryIndex.value);
      controller.update();

      if (subProjectId.value != 0) {
        dict["ProjectId"] = subProjectId.value;
      } else if (projectId.value != 0) {
        dict["ProjectId"] = projectId.value;
      }

      ApiManager().callApi(
        ApiModel("/highlights/${highLightedStoriesList[currentUserIndex.value].id}", APIType.PATCH),
        params: dict,
        successCallback: (response, message) async {
          if(Get.isRegistered<SubProjectDetailViewController>()){
            await Get.find<SubProjectDetailViewController>().callApiForGetHighlightsOfSubProject(context: context);
          }else if(Get.isRegistered<ProjectDetailViewController>(tag: Get.arguments['projectId'].toString())){
            await Get.find<ProjectDetailViewController>(tag: Get.arguments['projectId'].toString()).callApiForGetHighlightsOfProject(context: context);
          }else {
            await Get
                .find<ProfileViewController>()
                .callApiForGetHighlightsOfProfile(context: context);
          }       CommonFunction.showCustomSnackbar(message: response['message']);
        },
        failureCallback: (message, statusCode) {
          CommonFunction.showCustomSnackbar(message: statusCode,isError: true,backgroundColor: AppTheme.red);
        },
      );
    } catch (e) {
      log("Error creating highlight: $e");
    }
  }

  String getTimeDifference(DateTime dateTime) {
    DateTime now = DateTime.now().toLocal();
    Duration difference = now.difference(dateTime);

    if (difference.inMinutes > 0) {
      if (difference.inHours > 0) {
        if (difference.inDays > 0) {
          if (difference.inDays ~/ 30 > 0) {
            if (difference.inDays ~/ 365 > 0) {
              int years = difference.inDays ~/ 365;
              return "$years year${years > 1 ? 's' : ''} ago";
            } else {
              int months = difference.inDays ~/ 30;
              return "$months month${months > 1 ? 's' : ''} ago";
            }
          } else {
            int days = difference.inDays;
            return "$days day${days > 1 ? 's' : ''} ago";
          }
        } else {
          int hours = difference.inHours;
          return "$hours hr${hours > 1 ? 's' : ''} ago";
        }
      } else {
        int minutes = difference.inMinutes;
        return "$minutes min${minutes > 1 ? 's' : ''} ago";
      }
    }

    return "Just now";
  }


}
