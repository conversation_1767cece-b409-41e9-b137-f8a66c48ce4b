import 'package:rxdart/rxdart.dart';

enum LoadState { loading, success, failure }

enum Direction { up, down, left, right , previous}

enum ProgressPosition { top, bottom, none }

enum IndicatorHeight { small, medium, large }

class VerticalDragInfo {
  bool cancel = false;

  Direction? direction;

  void update(double primaryDelta) {
    Direction tmpDirection;

    if (primaryDelta > 0) {
      tmpDirection = Direction.down;
    } else {
      tmpDirection = Direction.up;
    }

    if (direction != null && tmpDirection != direction) {
      cancel = true;
    }

    direction = tmpDirection;
  }
}

enum PlaybackState { pause, play, next, previous , previousUser }

class StoryController {
  final playbackNotifier = BehaviorSubject<PlaybackState>();

  void pause() {
    playbackNotifier.add(PlaybackState.pause);
  }

  void play() {
    playbackNotifier.add(PlaybackState.play);
  }

  void next() {
    playbackNotifier.add(PlaybackState.next);
  }

  void previous() {
    playbackNotifier.add(PlaybackState.previous);
  }

  void dispose() {
    playbackNotifier.close();
  }

  void navigateToPreviousUser() {
    playbackNotifier.add(PlaybackState.previousUser);
  }

}
