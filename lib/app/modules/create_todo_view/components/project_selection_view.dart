
import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/typography.dart';
import '../controllers/create_todo_view_controller.dart';

Widget projectSelection({required BuildContext context,required CreateTodoViewController controller,required String? originalSelectedProject}) {
  return BackdropFilter(
    filter: ImageFilter.blur(
        sigmaX: 5, sigmaY: 5),
    child: FractionallySizedBox(
      heightFactor: 0.85,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
          color: AppTheme.subBottom,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(40),
            topRight: Radius.circular(40),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal:
            MySize.getScaledSizeWidth(30),
          ),
          child: Column(
            children: [
              Space.height(36),
              Row(
                children: [
                  InkWell(
                    child: SvgPicture.asset(
                        AppImage.backArrow,color: AppTheme.whiteWithNull,),
                    onTap: () {
                      controller
                          .selectedProjectId
                          .value =
                          originalSelectedProject;
                      Navigator.pop(context);
                    },
                  ),
                  Spacer(),
                  TypoGraphy(
                      text: "Projects",
                      level: 12),
                  Spacer(),
                  InkWell(
                    onTap: () {
                      final selectedProject =
                      controller
                          .createdProject
                          .firstWhereOrNull(
                            (project) =>
                        project.id
                            .toString() ==
                            controller
                                .selectedProjectId
                                .value,
                      );
                      if (selectedProject !=
                          null) {
                        controller
                            .taskProjectNameController
                            .value
                            .text =
                            selectedProject
                                .name;
                        controller
                            .selectedProject
                            .value =
                            selectedProject
                                .name;
                        controller
                            .taskSubProjectNameController
                            .value
                            .clear();
                      } else {
                        controller
                            .taskProjectNameController
                            .value
                            .clear();
                        controller
                            .taskSubProjectNameController
                            .value
                            .clear();
                        controller
                            .selectedProject
                            .value = "";
                        controller
                            .selectedSubProjectId
                            .value = "";
                      }
                      Navigator.pop(context);
                    },
                    child: TypoGraphy(
                      text: "Done",
                      level: 12,
                      color:
                      AppTheme.primaryIconDark,
                    ),
                  )
                ],
              ),
              Space.height(30),
              Obx(
                () => controller
                    .createdProject
                    .isNotEmpty ?
                  ValueListenableBuilder(
                  valueListenable: controller
                      .searchController.value,
                  builder:
                      (context, value, child) {
                    return AppTextField(
                      controller: controller
                          .searchController
                          .value,
                      focusNode: controller
                          .searchFocusNode,
                      padding: EdgeInsets.only(
                        top:
                        MySize.size12 ?? 12,
                        bottom:
                        MySize.size10 ?? 12,
                      ),
                      onChangedValue:
                          (p0) async {
                        controller.debounceTimer
                            ?.cancel();
                        controller
                            .debounceTimer =
                            Timer(
                                Duration(
                                    milliseconds:
                                    500),
                                    () {
                                  controller
                                      .page.value = 1;
                                  controller
                                      .createdProject
                                      .clear();
                                  controller
                                      .pullRefresh();
                                });
                      },
                      height: MySize
                          .getScaledSizeHeight(
                          50),
                      prefixIcon: Padding(
                        padding:
                        EdgeInsets.all(4),
                        child: SvgPicture.asset(
                          AppImage.searchIcon,
                          height: MySize.size24,
                          width: MySize.size24,
                          color: AppTheme.grey,
                        ),
                      ),
                      suffixIcon: (controller
                          .searchController
                          .value
                          .text
                          .isNotEmpty)
                          ? GestureDetector(
                        onTap: () {
                          controller
                              .searchController
                              .value
                              .clear();
                          controller.page
                              .value = 1;
                          controller
                              .createdProject
                              .clear();
                          controller
                              .pullRefresh();
                        },
                        child: Padding(
                          padding: EdgeInsets.only(
                              right: MySize
                                  .size15 ??
                                  20),
                          child:
                          SvgPicture
                              .asset(
                            AppImage
                                .textFieldClear,
                            // height: MySize.size20,
                            // width: MySize.size20,
                            color:
                            AppTheme
                                .grey,
                          ),
                        ),
                      )
                          : null,
                      hintText: "Search",
                      hintStyle: TextStyle(
                        color: AppTheme.grey,
                        fontSize: MySize.size16,
                        fontWeight:
                        FontWeight.w400,
                      ),
                    );
                  },
                ) : SizedBox.shrink(),
              ),
              Space.height(30),
              Expanded(
                child: Obx(
                      () => controller.isLoading
                      .value &&
                      controller
                          .createdProject
                          .isEmpty
                      ? Loader()
                      : controller
                      .searchController
                      .value
                      .text
                      .isNotEmpty &&
                      controller
                          .createdProject
                          .isEmpty
                      ? Padding(
                    padding:
                    EdgeInsets
                        .only(
                      top: MySize
                          .getScaledSizeHeight(
                          250),
                      left:
                      paddingHoriZontal,
                      right:
                      paddingHoriZontal,
                    ),
                    child: Column(
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture
                            .asset(
                          "assets/images/icon_search.svg",
                          height: MySize
                              .size50,
                          color: AppTheme.whiteWithNull,
                        ),
                        Empty(
                          title:
                          "Search Result Not Found !",
                        ),
                      ],
                    ),
                  )
                      : CustomScrollView(
                    keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior
                        .onDrag,
                    slivers: [
                      CustomSliverListView(
                        emptyWidget:
                        Padding(
                            padding:
                            EdgeInsets.only(
                              top: MySize.getScaledSizeHeight(250),
                              left: paddingHoriZontal,
                              right: paddingHoriZontal,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                TypoGraphy(
                                  text: "No Project",
                                  level: 5,
                                ),
                                Space.height(5),
                                TypoGraphy(
                                  text: "No project yet!",
                                  level: 3,
                                  color: AppTheme.grey,
                                )
                              ],
                            )),
                        maximumReachedWidget:
                        const SizedBox(),
                        itemBuilder:
                            (p0, p1,
                            index) {
                          return Obx(
                                () {
                              final userId =
                              controller.createdProject[index];
                              final filteredProjects = controller.createdProject;
                              final project = filteredProjects[index];
                              final isSelected =
                                  controller.selectedProjectId.value == userId.id.toString();
                              return Padding(
                                padding:
                                EdgeInsets.only(bottom: MySize.getScaledSizeHeight(32)),
                                child:
                                Row(
                                  children: [
                                    TypoGraphy(
                                      text: project.name,
                                      level: 5,
                                      color: isSelected ? AppTheme.primary1 : (AppTheme.whiteWithBase),
                                    ),
                                    Spacer(),
                                    GestureDetector(
                                      onTap: () {
                                        HapticFeedback.lightImpact();
                                        controller.selectedProjectId.value = isSelected ? null : userId.id.toString();
                                      },
                                      child: Container(
                                        height: MySize.getScaledSizeHeight(28),
                                        width: MySize.getScaledSizeWidth(28),
                                        decoration: BoxDecoration(
                                          color: isSelected ? AppTheme.primary1 : Colors.transparent,
                                          border: Border.all(
                                            color: isSelected ? AppTheme.primary1 : AppTheme.whiteWithBase,
                                          ),
                                          borderRadius: BorderRadius.circular(6.14),
                                        ),
                                        child: isSelected
                                            ? Padding(
                                          padding: EdgeInsets.all(4),
                                          child: SvgPicture.asset(
                                            AppImage.checkIcon,
                                            color: AppTheme.white,
                                          ),
                                        )
                                            : null,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                        items: controller.createdProject
                            .where((project) =>
                        project.projectMembers.isEmpty ||
                            project.projectMembers.any((member) =>
                            member.access != "read"))
                            .toList(),
                        isLoading: controller
                            .isLoading
                            .value,
                        hasMoreData:
                        controller
                            .hasMoreData
                            .value,
                        onLoadMore:
                            () {
                          return controller.callApiForProjectData(
                              context:
                              context);
                        },
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}