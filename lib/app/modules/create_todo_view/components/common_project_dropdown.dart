import 'package:flutter/material.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';

import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/common_function.dart';

/*class ProjectDropdownField extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final String labelText;
  final List<String> projectList;
  final Function(String) onProjectSelected;

  const ProjectDropdownField({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.labelText,
    required this.projectList,
    required this.onProjectSelected,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        FocusScope.of(context).unfocus(); // dismiss keyboard if open

        final selected = await showModalBottomSheet<String>(
          context: context,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          builder: (context) {
            return ListView(
              shrinkWrap: true,
              children: projectList
                  .map(
                    (project) => ListTile(
                  title: Text(project),
                  onTap: () => Navigator.pop(context, project),
                ),
              )
                  .toList(),
            );
          },
        );

        if (selected != null) {
          controller.text = selected;
          onProjectSelected(selected);
        }
      },
      child: AbsorbPointer(
        // prevents the keyboard from opening
        child: AppTextField(
          controller: controller,
          focusNode: focusNode,
          labelText: labelText,
          textCapitalization: TextCapitalization.words,
          suffixIcon: Padding(
            padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(10)),
            child: Icon(Icons.keyboard_arrow_down, color: AppTheme.baseBlack,size: MySize.getScaledSizeHeight(30),),
          ),
        ),
      ),
    );
  }
}*/
class ProjectDropdownField extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final String labelText;
  final List<dynamic>? projectList;
  final Function(String)? onProjectSelected;
  final Function()? onTap;
  final bool enabled;

  const ProjectDropdownField({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.labelText,
    this.projectList,
    this.onProjectSelected,
    this.enabled = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: enabled
          ? onTap ?? () async {
        FocusScope.of(context).unfocus();

        final selected = await showModalBottomSheet(
          context: context,
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
          ),
          builder: (context) {
            return ListView(
              shrinkWrap: true,
              children: (projectList ?? [])
                  .map(
                    (project) => ListTile(
                  title: Text(project.name),
                  onTap: () => Navigator.pop(context, project),
                ),
              )
                  .toList(),
            );
          },
        );

        if (selected != null) {
          controller.text = selected;
          onProjectSelected?.call(selected);
        }
      }
          : (){
        CommonFunction.showCustomSnackbar(
          message: "Please Select Project First",
          isError: true,
          backgroundColor: AppTheme.red);},
      child: AbsorbPointer(
        child: AppTextField(
          controller: controller,
          focusNode: focusNode,
          labelText: labelText,
          textCapitalization: TextCapitalization.words,
          enabled: enabled,
          labelStyle: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: MySize.size16,
            fontFamily: "Inter",
            color: enabled ? (AppTheme.whiteWithBase) : AppTheme.grey,
          ),
          suffixIcon: Padding(
            padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(10)),
            child: Icon(Icons.keyboard_arrow_down, color: enabled ? (AppTheme.whiteWithBase) : AppTheme.grey ,size: MySize.getScaledSizeHeight(30),),
          ),
        ),
      ),
    );
  }
}

