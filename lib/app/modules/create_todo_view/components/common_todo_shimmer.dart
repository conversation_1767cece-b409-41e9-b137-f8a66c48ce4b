import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';

class TodoShimmerView extends StatelessWidget {
  const TodoShimmerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: MySize.getScaledSizeWidth(30))
          .copyWith(
          bottom: MySize.getScaledSizeHeight(18)),
      child: Shimmer.fromColors(
          baseColor: AppTheme.baseShimmer,
          highlightColor: AppTheme.highlightShimmer,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: MySize.getScaledSizeHeight(30),
                width: MySize.getScaledSizeWidth(30),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.baseBlack,
                  ),
                ),
              ),
              Space.width(15),
              Expanded(
                child: Container(
                  height: MySize.getScaledSizeHeight(142),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[400],
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
              ),
            ],
          )
      ),
    );
  }
}
