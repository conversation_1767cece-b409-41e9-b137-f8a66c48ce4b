
import 'dart:async';
import 'dart:developer';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_user_data_model.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/typography.dart';
import '../controllers/create_todo_view_controller.dart';

Widget teamUserSelection({required BuildContext context,required CreateTodoViewController controller,required UserData? originalSelectedProject}) {
  return BackdropFilter(
    filter:
    ImageFilter.blur(sigmaX: 5, sigmaY: 5),
    child: FractionallySizedBox(
      heightFactor: 0.85,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
          color: AppTheme.subBottom,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(40),
            topRight: Radius.circular(40),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal:
            MySize.getScaledSizeWidth(30),
          ),
          child: Column(
            children: [
              Space.height(36),
              Row(
                children: [
                  InkWell(
                    child: SvgPicture.asset(
                        AppImage.backArrow,color: AppTheme.whiteWithNull,),
                    onTap: () {
                      controller.selectedUser
                          .value =
                          originalSelectedProject;
                      Navigator.pop(context);
                    },
                  ),
                  Spacer(),
                  TypoGraphy(
                      text: "Assign Friends",
                      level: 12),
                  Spacer(),
                  InkWell(
                    onTap: () {
                      log("object ==${controller.selectedUser.value}");
                      Navigator.pop(context);
                    },
                    child: TypoGraphy(
                      text: "Done",
                      level: 12,
                      color: AppTheme.primaryIconDark,
                    ),
                  )
                ],
              ),
              Space.height(30),
              Obx(
                () => controller
                    .projectUserList
                    .isNotEmpty ?
                ValueListenableBuilder(
                  valueListenable: controller
                      .searchController.value,
                  builder:
                      (context, value, child) {
                    return AppTextField(
                      controller: controller
                          .searchController.value,
                      focusNode: controller
                          .searchFocusNode,
                      padding: EdgeInsets.only(
                        top: MySize.size12 ?? 12,
                        bottom:
                        MySize.size10 ?? 12,
                      ),
                      onChangedValue: (p0) {
                        controller.debounceTimer
                            ?.cancel();
                        controller.debounceTimer =
                            Timer(
                                Duration(
                                    milliseconds:
                                    500), () {
                              controller.page.value =
                              1;
                              controller
                                  .projectUserList
                                  .clear();
                              controller
                                  .pullRefreshForUser();
                            });
                      },
                      height: MySize
                          .getScaledSizeHeight(
                          50),
                      prefixIcon: Padding(
                        padding:
                        EdgeInsets.all(4),
                        child: SvgPicture.asset(
                          AppImage.searchIcon,
                          height: MySize.size24,
                          width: MySize.size24,
                          color: AppTheme.grey,
                        ),
                      ),
                      suffixIcon: (controller
                          .searchController
                          .value
                          .text
                          .isNotEmpty)
                          ? GestureDetector(
                        onTap: () {
                          controller
                              .searchController
                              .value
                              .clear();
                          controller.page
                              .value = 1;
                          controller
                              .projectUserList
                              .clear();
                          controller
                              .pullRefreshForUser();
                        },
                        child: Padding(
                          padding: EdgeInsets.only(
                              right: MySize
                                  .size15 ??
                                  20),
                          child: SvgPicture
                              .asset(
                            AppImage
                                .textFieldClear,
                            color: AppTheme
                                .grey,
                          ),
                        ),
                      )
                          : null,
                      hintText: "Search",
                      hintStyle: TextStyle(
                        color: AppTheme.grey,
                        fontSize: MySize.size16,
                        fontWeight:
                        FontWeight.w400,
                      ),
                    );
                  },
                ) : SizedBox.shrink(),
              ),
              Expanded(
                child: Obx(
                      () {
                        final userList = controller.projectUserList;
                        return controller.isLoading
                      .value &&
                      controller
                          .projectUserList
                          .isEmpty
                      ? Loader()
                      : controller
                      .searchController
                      .value
                      .text
                      .isNotEmpty &&
                      controller
                          .projectUserList
                          .isEmpty
                      ? Padding(
                    padding:
                    EdgeInsets
                        .only(
                      top: MySize
                          .getScaledSizeHeight(
                          250),
                      left:
                      paddingHoriZontal,
                      right:
                      paddingHoriZontal,
                    ),
                    child: Column(
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture
                            .asset(
                          "assets/images/icon_search.svg",
                          height: MySize
                              .size50,
                          color: AppTheme.whiteWithNull,
                        ),
                        Empty(
                          title:
                          "Search Result Not Found !",
                        ),
                      ],
                    ),
                  )
                      : CustomScrollView(
                    keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior
                        .onDrag,
                    slivers: [
                      CustomSliverListView(
                        emptyWidget:
                        Padding(
                            padding: EdgeInsets
                                .only(
                              top:
                              MySize.getScaledSizeHeight(250),
                              left:
                              paddingHoriZontal,
                              right:
                              paddingHoriZontal,
                            ),
                            child:
                            Column(
                              crossAxisAlignment:
                              CrossAxisAlignment.center,
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                TypoGraphy(
                                  text: "No team member",
                                  level: 5,
                                ),
                                Space.height(5),
                                TypoGraphy(
                                  text: "No team members assigned yet to this project.",
                                  level: 3,
                                  color: AppTheme.grey,
                                )
                              ],
                            )),
                        maximumReachedWidget:
                        const SizedBox(),
                        itemBuilder:
                            (p0, p1,
                            index) {
                          return Obx(
                                () {
                              final userId =
                              userList[index];
                              // final isSelected =
                              //     controller.selectedUsersMap[userId] ??
                              //         false;
                              final isSelected = controller.selectedUser.value?.id == userId.user?.id;
                              return InkWell(
                                onTap:
                                    () {
                                  HapticFeedback.lightImpact();
                                  // controller.selectedUsersMap[userId] =
                                  //     !isSelected;
                                  // controller.selectedUser.value = userId;
                                  controller.selectedUser.value = isSelected ? null : userId.user;
                                    },
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: MySize.getScaledSizeHeight(8)),
                                  child: ListTile(
                                      contentPadding: EdgeInsets.zero,
                                      leading: profileImage(
                                        url: userList[index].user?.image ?? "",
                                        userName: userList[index].user?.firstName ?? "",
                                        iconHeight: MySize.getScaledSizeHeight(60),
                                        iconWidth: MySize.getScaledSizeWidth(60),
                                        height: MySize.getScaledSizeHeight(60),
                                        width: MySize.getScaledSizeWidth(60),
                                      ),
                                      title: TypoGraphy(
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        text: "${userList[index].user?.firstName} ${userList[index].user?.lastName}",
                                        level: 12,
                                        color: isSelected ? AppTheme.primary1 : (AppTheme.whiteWithBase),
                                      ),
                                      // subtitle: TypoGraphy(
                                      //   maxLines: 1,
                                      //   overflow: TextOverflow.ellipsis,
                                      //   text: userList[index].email,
                                      //   level: 2,
                                      //   fontWeight: FontWeight.w400,
                                      //   color: AppTheme.grey,
                                      // ),
                                      trailing: isSelected
                                          ? SvgPicture.asset(
                                        AppImage.checkIcon,
                                        color: AppTheme.primary1,
                                        height: MySize.getScaledSizeHeight(20),
                                      )
                                          : null),
                                ),
                              );
                            },
                          );
                        },
                        items: userList,
                        isLoading:
                        controller
                            .isLoading
                            .value,
                        hasMoreData:
                        controller
                            .hasMoreData
                            .value,
                        onLoadMore:
                            () {
                            return controller.callApiForGetProjectUser(
                                context:
                                context,
                                projectId: (controller.selectedSubProjectId.value.toString().isNotEmpty &&
                                    controller.selectedSubProjectId.value != null)
                                    ? controller.selectedSubProjectId.value.toString()
                                    : controller.selectedProjectId.value.toString());
                        },
                      )
                    ],
                  );
}
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

/*
                        Obx(
                          () => Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              children: List.generate(
                                controller.selectedUser.length,
                                (index) {
                                  final user = controller.selectedUsersMap.keys
                                      .toList()[index];
                                  return Padding(
                                    padding:
                                        EdgeInsets.only(right: MySize.getScaledSizeWidth(10), bottom: MySize.getScaledSizeHeight(10)),
                                    child: Container(
                                      height: MySize.getScaledSizeHeight(36),
                                      decoration: BoxDecoration(
                                          color: AppTheme.subPrimary,
                                          borderRadius: BorderRadius.circular(5)),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Space.width(5),
                                          profileImage(
                                              url: user.image ?? "",
                                              userName: user.firstName ?? "",
                                              iconHeight:
                                                  MySize.getScaledSizeHeight(27),
                                              iconWidth:
                                                  MySize.getScaledSizeHeight(27),
                                              width:
                                                  MySize.getScaledSizeHeight(27),
                                              height:
                                                  MySize.getScaledSizeHeight(27),
                                              textStyle: TextStyle(
                                                  fontSize: 12,
                                                  color: AppTheme.white)),
                                          Space.width(6),
                                          TypoGraphy(
                                            text:
                                                "${user.firstName} ${user.lastName}",
                                            level: 3,
                                            fontWeight: FontWeight.w400,
                                          ),
                                          Space.width(5),
                                          InkWell(
                                            onTap: () {
                                              controller.selectedUsersMap.remove(user);
                                            },
                                            child: SvgPicture.asset(
                                                AppImage.closeImage),
                                          ),
                                          Space.width(5),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
*/
