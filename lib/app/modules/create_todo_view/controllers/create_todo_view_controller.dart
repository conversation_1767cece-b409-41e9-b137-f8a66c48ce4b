import 'dart:async';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:intl/intl.dart';
import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../main.dart';
import '../../../../models/app_project_model.dart';
import '../../../../models/app_project_team_member_model.dart';
import '../../../../models/app_todo_model.dart';
import '../../../../models/app_user_data_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/current_user.dart';
import '../../bottom_bar/controllers/bottom_bar_controller.dart';

class CreateTodoViewController extends GetxController {
  Rx<TextEditingController> taskNameController = TextEditingController().obs;
  Rx<TextEditingController> taskProjectNameController =
      TextEditingController().obs;
  Rx<TextEditingController> taskSubProjectNameController =
      TextEditingController().obs;
  Rx<TextEditingController> taskTeamController = TextEditingController().obs;
  Rx<TextEditingController> taskDateController = TextEditingController().obs;
  Rx<TextEditingController> taskDescriptionController =
      TextEditingController().obs;
  Rx<TextEditingController> searchController = TextEditingController().obs;
  FocusNode taskNameFocusNode = FocusNode();
  FocusNode taskProjectNameFocusNode = FocusNode();
  FocusNode taskTeamFocusNode = FocusNode();
  FocusNode searchFocusNode = FocusNode();
  FocusNode taskSubProjectNameFocusNode = FocusNode();
  FocusNode taskDateFocusNode = FocusNode();
  Rx<FocusNode> taskDescriptionFocusNode = FocusNode().obs;
  RxInt charCount = 0.obs;
  RxInt maxChars = 250.obs;
  RxString selectedPriority = 'Low'.obs;
  DateTime? selectedDate;
  DateTime? pickDate;
  var args = Get.arguments;

  // DateTime initialDate = DateTime.now();
  final RxList<String> priorities = ['Low', 'Medium', 'High'].obs;
  RxString selectedProject = ''.obs;
  RxString selectedSubProject = ''.obs;

  // final RxList<PlatformFile> files = <PlatformFile>[].obs;
  final int maxFiles = 3;
  RxList<ProjectTeamMemberData> projectUserList = <ProjectTeamMemberData>[].obs;
  RxBool isLoading = false.obs;
  int limit = 10;
  RxInt page = 1.obs;
  RxBool hasMoreData = true.obs;
  RxBool isBottom = false.obs;
  RxBool isTodoLoading = false.obs;
  ApiManager apiManager = ApiManager();
  RxMap<UserData, bool> selectedUsersMap = <UserData, bool>{}.obs;
  Rxn<UserData> selectedUser = Rxn<UserData>();
  RxnString selectedProjectId = RxnString();
  RxnString selectedSubProjectId = RxnString();
  RxList<Project> createdProject = <Project>[].obs;
  Timer? debounceTimer;
  RxList<SubProjectData> subProjectList = <SubProjectData>[].obs;
  RxString todoId = "-1".obs;
  Rx<ToDo> getTodoDetailData = ToDo().obs;
  RxList imageList = [].obs;

  Color getColor(String priority) {
    if (selectedPriority.value == priority) {
      if (priority == 'Low') return AppTheme.success;
      if (priority == 'Medium') return AppTheme.yellow;
      if (priority == 'High') return AppTheme.red;
    }
    return Colors.transparent;
  }

  Color getTextColor(String priority) {
    return selectedPriority.value == priority
        ? AppTheme.white
        : AppTheme.whiteWithBase;
  }

  void pickFiles() async {
    if (imageList.length >= maxFiles) {
      CommonFunction.showCustomSnackbar(
          message: "You can only upload up to 3 files",
          backgroundColor: AppTheme.red,
          isError: true);
      return;
    }

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        withData: true,
        allowMultiple: false,
      );

      if (result != null) {
        final file = result.files.first;
        final fileExtension = file.extension?.toLowerCase();

        // Check if file extension is allowed
        if (fileExtension != 'pdf' &&
            fileExtension != 'doc' &&
            fileExtension != 'docx') {
          CommonFunction.showCustomSnackbar(
              message: "Only PDF and DOC/DOCX files are allowed",
              backgroundColor: AppTheme.red,
              isError: true);
          return;
        }
        await callApiForUserImage(
            context: Get.context!, imageFiles: file.path!);
        // files.add(file);
        CommonFunction.showCustomSnackbar(
            message: "File added successfully",
            backgroundColor: AppTheme.success);
      }
    } catch (e) {
      CommonFunction.showCustomSnackbar(
          message: "Error uploading file. Please try again.",
          backgroundColor: AppTheme.red,
          isError: true);
    }
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    log('arggs ===> ${args}');
    if (args != null && args["isBottom"] != null) {
      isBottom.value = args["isBottom"];
      log('jhdsj ===> ${isBottom.value}');
    }
    taskDescriptionFocusNode.value.addListener(() {
      update(); // Calls update() to refresh the UI
    });
    taskDescriptionController.value.addListener(
      () {
        updateCharCount();
        update();
      },
    );
    if (args != null && args["todoId"] != null) {
      todoId.value = args["todoId"];
    }
    if (args != null && args["ProjectId"] != null) {
      print("project id ==> ${args["ProjectId"]}");
      selectedProject.value = args["ProjectId"].toString();
      selectedProjectId.value = args["ProjectId"].toString();
    }
    if (todoId.value != "-1") {
      callApiForGetOneTodo(context: Get.context!);
    }
  }

  void updateCharCount() {
    charCount.value =
        taskDescriptionController.value.text.length.clamp(0, maxChars.value);
  }

  Future<void> callApiForGetOneTodo({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isTodoLoading.value = true;
    final ApiModel getPost = ApiModel("/todos/${todoId.value}", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            getTodoDetailData.value = ToDo.fromJson(response["data"]);
            log("getTodoDetailData === ${getTodoDetailData.value}");
            taskNameController.value.text = getTodoDetailData.value.name ?? "";
            taskDescriptionController.value.text =
                getTodoDetailData.value.description ?? "";
            selectedPriority.value = getTodoDetailData.value.priority == 1
                ? "Low"
                : getTodoDetailData.value.priority == 2
                    ? "Medium"
                    : "High";
            if (getTodoDetailData.value.dueDate != null) {
              selectedDate =
                  DateTime.parse(getTodoDetailData.value.dueDate.toString());
              pickDate =
                  DateTime.parse(getTodoDetailData.value.dueDate.toString());
              DateTime dateTime = DateTime.parse(
                  selectedDate.toString()); // Parses the ISO string
              String formattedDate =
                  DateFormat('MMMM dd, yyyy').format(dateTime);
              taskDateController.value.text = formattedDate;

              // taskDateController.value.text =
              //     "${selectedDate?.day}/${selectedDate?.month}/${selectedDate?.year}";
              log("value == ${taskDateController.value.text}");
            }
            if (getTodoDetailData.value.project != null) {
              if (getTodoDetailData.value.project?.parentProjectData != null) {
                selectedProjectId.value = getTodoDetailData
                    .value.project?.parentProjectData?.id
                    .toString();
                taskProjectNameController.value.text =
                    getTodoDetailData.value.project?.parentProjectData?.name ??
                        "";
                selectedProject.value =
                    getTodoDetailData.value.project?.parentProjectData?.name ??
                        "";
                selectedSubProjectId.value =
                    getTodoDetailData.value.project?.id.toString();
                taskSubProjectNameController.value.text =
                    getTodoDetailData.value.project?.name ?? "";
              } else {
                selectedProject.value =
                    getTodoDetailData.value.project?.name ?? "";
                selectedProjectId.value =
                    getTodoDetailData.value.project?.id.toString();
                taskProjectNameController.value.text =
                    getTodoDetailData.value.project?.name ?? "";
              }
            }
            if (getTodoDetailData.value.assignedUser != null) {
              selectedUser.value = UserData(
                id: getTodoDetailData.value.assignedUser?.id,
                firstName: getTodoDetailData.value.assignedUser?.firstName,
                lastName: getTodoDetailData.value.assignedUser?.lastName,
                image: getTodoDetailData.value.assignedUser?.image,
              );
            }
            if (getTodoDetailData.value.files.isNotEmpty) {
              for (var imageData in getTodoDetailData.value.files) {
                String? imageUrl = imageData.link;

                imageList.add({
                  "link": imageUrl,
                  "originalname": imageData.name,
                  "type": imageUrl?.split('.').last,
                });
              }
              // files.value = (getTodoDetailData.value.files ?? []).map((e) => PlatformFile(name: e.name ?? "", size: 10,path: e.link),).toList();
            }
            isTodoLoading.value = false;
          }
        } catch (error) {
          isTodoLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isTodoLoading.value = false;
      },
    );
  }

  callApiForUserImage(
      {required BuildContext context, required String imageFiles}) async {
    app.resolve<CustomDialogs>().showCircularDialog(context);
    try {
      FormData formData = FormData.fromMap({
        "file": await MultipartFile.fromFile(imageFiles,
            filename: imageFiles.split('/').last.trim()),
        // Sending single or multiple images in one request
      });

      await apiManager.callApi(
        APIS.generalImageUpload.imageUpload,
        params: formData,
        successCallback: (response, message) {
          imageList.add({
            "link": response['data'][0]['link'],
            "originalname": response['data'][0]['originalname'],
            "type": response['data'][0]['type'],
          });
          app.resolve<CustomDialogs>().hideCircularDialog(context);
        },
        failureCallback: (message, statusCode) {
          app.resolve<CustomDialogs>().hideCircularDialog(context);
          log("Error: $message");
        },
      );
    } catch (e) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log("Exception: $e");
    }
  }

  callApiForCreateToDo({required BuildContext context}) {
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "name": taskNameController.value.text.trim(),
    };
    if (taskDescriptionController.value.text.isNotEmpty) {
      dict["description"] = taskDescriptionController.value.text;
    } else {
      if (args != null && args["isEdit"] != null) {
        dict["description"] = "";
      }
    }
    if (imageList.isNotEmpty) {
      dict["attachments"] = imageList;
    } else {
      if (args != null && args["isEdit"] != null) {
        dict["attachments"] = null;
      }
    }
    if (selectedPriority.value.isNotEmpty) {
      if (selectedPriority.value == 'Low') {
        dict["priority"] = 1;
      } else if (selectedPriority.value == 'Medium') {
        dict["priority"] = 2;
      } else if (selectedPriority.value == 'High') {
        dict["priority"] = 3;
      }
    }
    if (selectedDate != null) {
      dict["dueDate"] = selectedDate?.toIso8601String();
      log("Date ====> ${selectedDate?.toIso8601String()}");
    }

    if (args != null && args["isProjectCreate"] != null) {
      dict["ProjectId"] = args["ProjectId"].toString();
    } else {
      if (selectedSubProjectId.value.toString().isNotEmpty &&
          selectedSubProjectId.value != null) {
        dict["ProjectId"] = selectedSubProjectId.value;
      } else {
        log("selected id == ${selectedProjectId.value}");
        if (selectedProjectId.value.toString().isNotEmpty &&
            selectedProjectId.value != null) {
          dict["ProjectId"] = selectedProjectId.value;
        }
      }
    }

    if (selectedUser.value != null) {
      dict["AssignedUserId"] = selectedUser.value?.id;
    } else {
      if (args != null && args["isEdit"] != null) {
        dict["AssignedUserId"] = null;
      }
    }
    final ApiModel editTodo = ApiModel("/todos/${todoId.value}", APIType.PATCH);
    log("dict === $dict");
    return apiManager.callApi(
      args != null && args["isEdit"] != null ? editTodo : APIS.todo.createTodo,
      params: dict,
      successCallback: (response, message) {
        isLoading.value = false;
        if (args != null && args["isBottom"] != null) {
          Get.find<BottomBarController>().currentIndex.value = 4;
        }
        isLoading.value = false;
        Navigator.pop(context);
        CommonFunction.showCustomSnackbar(message: response["message"]);
        print("response == $response");
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  pullRefreshForUserSubProject() {
    page.value = 1;
    hasMoreData.value = true;
    callApiForOneSubProject(
        context: Get.context!, projectId: selectedProjectId.value.toString());
  }

  Future<void> callApiForOneSubProject(
      {required BuildContext context, String? projectId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;

    return apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {
        "UserId": CurrentUser.user.id,
        "ParentId": projectId,
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            if (response["data"]["data"].length < limit) {
              hasMoreData.value = false;
            }
            subProjectList.value = List<SubProjectData>.from(response["data"]
                    ["data"]
                .map((x) => SubProjectData.fromJson(x)));
            subProjectList.value = subProjectList
                .where((project) =>
            project.projectMembers.isEmpty ||
                project.projectMembers.any((member) =>
                member.access != "read"))
                .toList();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  pullRefreshForUser() {
    page.value = 1;
    hasMoreData.value = true;
    if (selectedProject.isNotEmpty) {
      // controller.page.value = 1;
      callApiForGetProjectUser(
          context: Get.context!,
          projectId: (selectedSubProjectId.value.toString().isNotEmpty &&
                  selectedSubProjectId.value != null)
              ? selectedSubProjectId.value.toString()
              : selectedProjectId.value.toString());
    } /* else {
      // controller.page.value = 1;
      callApiForGetTeamUser(context: Get.context!);
    }*/
  }

  Future<void> callApiForGetProjectUser(
      {required BuildContext context, String? projectId}) {
    isLoading.value = true;
    final ApiModel getProjectMember =
        ApiModel("/project-members/projects/$projectId", APIType.GET);

    return apiManager.callApi(
      getProjectMember,
      params: {if (searchController.value.text.isNotEmpty)
        "searchQuery": searchController.value.text,},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // Parse the response and update the list
            ProjectTeamMemberResponse parsedResponse =
                ProjectTeamMemberResponse.fromJson(response);
            if (parsedResponse.data.length < limit) {
              hasMoreData.value = false;
            }
            projectUserList.value = parsedResponse.data;
            print("final parsedResponse data ==> ${projectUserList.value}");
            projectUserList.value = projectUserList
                .where((element) => element.status ?? false)
                .toList();
            print("final data ==> ${projectUserList.value}");
            isLoading.value = false;
          }
        } catch (error) {
          hasMoreData.value = false;
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        hasMoreData.value = false;
        isLoading.value = false;
      },
    );
  }

  pullRefresh() {
    page.value = 1;
    hasMoreData.value = true;
    callApiForProjectData(context: Get.context!);
  }

  Future<void> callApiForProjectData({required BuildContext context}) async {
    if (!hasMoreData.value) return;
    isLoading.value = true;
    final params = {
      "UserId": CurrentUser.user.id,
      'page': page.value,
      'limit': limit,
      if (searchController.value.text.isNotEmpty)
        "searchQuery": searchController.value.text,
    };

    apiManager.callApi(
      APIS.project.getAllMyProject,
      params: params,
      successCallback: (response, message) {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse =
                ProjectResponse.fromJson(response);
            final newData = projectResponse.data.data;
            final isLastPage = newData.length < limit;
            hasMoreData.value = !isLastPage;

            // if (page.value == 1) {
            //   createdProject.value = newData;
            // } else {
            createdProject.addAll(newData);
            createdProject.value = createdProject
                .where((project) =>
            project.projectMembers.isEmpty ||
                project.projectMembers.any((member) =>
                member.access != "read"))
                .toList();
            // }

            page.value++;
          }
        } catch (error) {
          log("Error processing response: $error");
          hasMoreData.value = false;
        } finally {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }
}
