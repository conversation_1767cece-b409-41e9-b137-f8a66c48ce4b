import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../controllers/splash_controller.dart';

RxBool isPopupOpen = false.obs;

class SplashView extends GetWidget<SplashController> {
  const SplashView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    log("HEIGHT : ${MySize.screenHeight}");
    log("WIDTH : ${MySize.screenWidth}");
    precacheImage(AssetImage(AppImage.tutorial1), context);
    precacheImage(AssetImage(AppImage.tutorial2), context);
    precacheImage(AssetImage(AppImage.tutorial3), context);
    precacheImage(AssetImage(AppImage.tutorial4), context);
    precacheImage(AssetImage(AppImage.tutorial5), context);
    precacheImage(AssetImage(AppImage.appLogo), context);
    return Scaffold(
      backgroundColor: AppTheme.primary1,
      body: Center(
        child: AnimatedBuilder(
          animation: controller.animationController,
          builder: (context, child) {
            return ShaderMask(
              shaderCallback: (Rect bounds) {
                return LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    AppTheme.white, // White at the top
                    AppTheme.white.withValues(alpha: 0.1),
                    AppTheme.white.withValues(alpha: 0.1),
                    AppTheme.white.withValues(alpha: 0.1),
                    AppTheme.white.withValues(alpha: 0.1),
                    AppTheme.white.withValues(alpha: 0.1),
                  ],
                  stops: [
                    controller.gradientAnimation.value,
                    0.1,
                    0.2,
                    0.3,
                    0.4,
                    2
                  ],
                ).createShader(bounds);
              },
              child: Image.asset(
                AppImage.appLogo,
                width: MySize.getScaledSizeWidth(250),
                height: MySize.getScaledSizeHeight(250),
              ),
            );
          },
        ),
      ),
    );
  }
}
