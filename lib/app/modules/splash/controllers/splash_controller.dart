import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../constants/api.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/current_user.dart';
import '../../../routes/app_pages.dart';
import '../../story_editor/controllers/story_editor_controller.dart';

bool isConnected = true;
String lastRoute = "";
var args = Get.arguments;

class SplashController extends GetxController with SingleGetTickerProviderMixin {
  RxInt count = 0.obs;
  ApiManager apiManager = ApiManager();
  bool isFirstTime = true;
  RxBool hasData = false.obs;
  bool? isVersionMatched;

  late AnimationController animationController;
  late Animation<Color?> colorAnimation;
  late Animation<Offset> positionAnimation;
  late Animation<double> gradientAnimation;

  @override
  void onInit() async {
    super.onInit();

    animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    gradientAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animationController, curve: Curves.linear),
    );

    // Start both animation and API call in parallel
    final animationFuture = animationController.forward().then((_) => null);
    final configFuture = callApiForAppConfig(context: Get.context!);

    final configStatus = await configFuture;
    await animationFuture;

    // Wait 1 second after both complete
    // await Future.delayed(const Duration(seconds: 1));

    // Navigate based on app config result
    switch (configStatus) {
      case "maintenance":
        Get.offAllNamed(Routes.APP_UNDER_MAINTENANCE);
        break;
      case "forceUpdate":
        Get.offAllNamed(Routes.APP_UPDATE_AVAILABLE);
        break;
      case "softUpdate":
        Get.offAllNamed(Routes.APP_UPDATE_AVAILABLE, arguments: {"softUpdate": true});
        break;
      case "normal":
        if (box.hasData('finalToken')) {
          CurrentUser.getMe(callback: () async {
            if (CurrentUser.user.customSetting?.homeSection == "profile") {
              Get.offAllNamed(Routes.Bottom_Bar);
              await Future.delayed(const Duration(milliseconds: 50), () {
                Get.toNamed(Routes.profile);
              });
            } else {
              Get.offAllNamed(Routes.Bottom_Bar);
            }
          });
        } else {
          Get.offNamed(Routes.ONBOARDING);
        }
        break;
      case "failure":
      default:
      // Optional: Show retry or toast
        break;
    }

    // Initialize background upload service and listen to connectivity
    await StoryBackgroundUploadService.initialize();
    await Future.delayed(const Duration(seconds: 1), () async {
      Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> result) {
        if (isFirstTime && !Platform.isAndroid) {
          isFirstTime = false;
        } else {
          if (result[0] == ConnectivityResult.none) {
            isConnected = false;
            if (Get.currentRoute != Routes.NO_INTERNET) {
              lastRoute = Get.currentRoute;
              args = Get.arguments;
              Get.toNamed(Routes.NO_INTERNET)?.then((value) {
                if (Get.currentRoute == Routes.SPLASH) {
                  restartAnimation();
                }
              });
            }
          } else {
            isConnected = true;
            if (Get.currentRoute == Routes.NO_INTERNET) {
              Get.offNamedUntil(
                lastRoute,
                    (route) => !(route.settings.name == Routes.NO_INTERNET || route.settings.name == lastRoute),
                arguments: args,
              );
              if (Get.currentRoute == Routes.SPLASH) {
                restartAnimation();
              }
            }
          }
        }
      });

      if ((await Connectivity().checkConnectivity())[0] == ConnectivityResult.none) {
        isConnected = false;
        Get.toNamed(Routes.NO_INTERNET)?.then((value) {
          restartAnimation();
        });
      }
    });
  }

  void restartAnimation() {
    animationController.reverse();
    animationController.forward();
  }

  Future<String> callApiForAppConfig({required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    final PackageInfo info = await PackageInfo.fromPlatform();
    String appVersion = info.version;

    final completer = Completer<String>();

    apiManager.callApi(
      APIS.appConfig.get,
      successCallback: (response, message) async {
        Map<String, dynamic> configData = response['appConfig'];

        bool underMaintenance = configData['appInMaintenance'] == true;
        bool forceUpdate = configData['forceUpdate'] == true;
        bool softUpdate = configData['softUpdate'] == true;
        String androidVersion = configData['androidVersionCode'] ?? "2.0.0";
        String iosVersion = configData['iosVersionCode'] ?? "2.0.0";

        if (underMaintenance) {
          completer.complete("maintenance");
        } else if (forceUpdate &&
            (Platform.isAndroid ? appVersion != androidVersion : appVersion != iosVersion)) {
          completer.complete("forceUpdate");
        } else if (softUpdate &&
            (Platform.isAndroid ? appVersion != androidVersion : appVersion != iosVersion)) {
          completer.complete("softUpdate");
        } else {
          completer.complete("normal");
        }

        log("::::::::::::::::::::APP CONFIG $response");
      },
      failureCallback: (status, message) {
        completer.complete("failure");
      },
    );

    return completer.future;
  }

  @override
  void onClose() {
    animationController.dispose();
    super.onClose();
  }
}
