import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/typography.dart';

class TermsServiceView extends StatelessWidget {
  const TermsServiceView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(30)),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Space.height(50),
              Align(
                alignment: Alignment.centerLeft,
                child: Ink<PERSON><PERSON>(
                  onTap: () {
                    Get.back();
                  },
                  child: SvgPicture.asset(
                    AppImage.backArrow,
                    height: MySize.getScaledSizeHeight(28),
                    width: MySize.getScaledSizeHeight(28),
                    color: AppTheme.whiteWithNull,
                  ),
                ),
              ),
              Space.height(20),
              TypoGraphy(
                text: "Floment (parent company: Incentivize app, Inc), Inc.",
                level: 6,
                fontWeight: FontWeight.w600,
              ),
              Space.height(20),
              TypoGraphy(
                text: "General Terms of Service",
                level: 5,
                fontWeight: FontWeight.w600,
              ),
              Space.height(10),
              TypoGraphy(
                text: "Last Updated: Nov 11, 2023",
                level: 3,
                fontWeight: FontWeight.w600,
                color: AppTheme.grey,
              ),
              Space.height(20),
              TypoGraphy(
                text: "These General Terms of Service (these “Terms of Service”) are between Floment (parent company: Incentivize app, Inc),"
                    " Inc. (“Floment (parent company: Incentivize app, Inc)”, “we”, “us” and “our”) and anyone, whether on behalf of themselves or"
                    " another entity (“you” or “your”) and govern your registration and/or use of our application made available on our website "
                    "www.incenti.ai or any other domains or services owned by Floment (parent company: Incentivize app, Inc) (collectively, the “Services”)"
                    ". By using the website, you agree to follow and be bound by these Terms of Service, "
                    "as well as the Privacy Policy and any other policies referenced herein",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Color(0xFFfef9c3),
                  border: Border.all(color: Color(0xFFfacc15), width: 1.5)
                ),
                padding: EdgeInsets.all(15),
                child: TypoGraphy(
                  text: "ARBITRATION NOTICE: YOU AGREE THAT DISPUTES BETWEEN YOU AND Floment (parent company: Incentivize app, Inc) WILL BE RESOLVED "
                      "BY BINDING, INDIVIDUAL ARBITRATION AND YOU WAIVE YOUR RIGHT TO PARTICIPATE"
                      " IN A CLASS ACTION LAWSUIT OR CLASS-WIDE ARBITRATION. WE EXPLAIN SOME EXCEPTIONS AND HOW YOU CAN OPT OUT OF ARBITRATION BELOW.",
                  level: 3,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFFa16207),
                ),
              ),
              Space.height(20),
              TypoGraphy(
                text: "Use License",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "a. Permission is granted to temporarily download one copy of the materials (information or software) on Floment "
                    "(parent company: Incentivize app, Inc), Inc's website for personal, non-commercial transitory viewing only. "
                    "This is the grant of a license, not a transfer of title, and under this license you may not:modify or copy the material;\n"
                    "use the materials for any commercial purpose, or for any public display (commercial or non-commercial);\n\n"
                    "attempt to decompile or reverse engineer any software contained on Floment (parent company: Incentivize app, Inc), Inc's website;\n\n"
                    "remove any copyright or other proprietary notations from the materials; orremove any copyright or other proprietary notations from the materials; or"
                    "transfer the materials to another person or “mirror” the materials on any other server. Therefore, ALL OTHER "
                    "AGREEMENTS ENTERED INTO SEPRATELY BETWEEN YOU AND Floment (parent company: Incentivize app, Inc) ARE DEEMED SUPPLMENTARY "
                    "TERMS THAT ARE AN INTEGRAL PART OF THE TERMS OF SERVICE AND SHALL HAVE THE SAME LEGAL EFFECT. YOUR USE OF THE SERVICES IS "
                    "DEEMED YOUR ACCEPTANCE OF THE ABOVE SUPPLEMENTARY TERMS.\n\n"
                    "b. This license shall automatically terminate if you violate any of these restrictions and may be terminated by Floment "
                    "(parent company: Incentivize app, Inc), Inc at any time. Upon terminating your viewing of these materials or upon"
                    " the termination of this license, you must destroy any downloaded materials in your possession whether in electronic or printed format.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Disclaimer",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "a. The materials on Floment (parent company: Incentivize app, Inc), Inc's website are provided on an 'as is'"
                    " basis. Floment (parent company: Incentivize app, Inc), Inc makes no warranties, expressed or implied, and hereby disclaims "
                    "and negates all other warranties including, without limitation, implied warranties or conditions "
                    "of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.\n\n"
                    "b. Further, Floment (parent company: Incentivize app, Inc), Inc does not warrant or make any representations concerning "
                    "the accuracy, likely results, or reliability of "
                    "the use of the materials on its website or otherwise relating to such materials or on any sites linked to this site.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Limitations",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "In no event shall Floment (parent company: Incentivize app, Inc), Inc or its suppliers be "
                    "liable for any damages (including, without limitation, damages for loss of data or profit, or due to business"
                    " interruption) arising out of the use or inability to use the materials on Floment (parent company: Incentivize app, Inc),"
                    " Inc's website, even if Floment (parent company: Incentivize app, Inc), Inc or a Floment (parent company: Incentivize app, Inc),"
                    " Inc authorized representative has been notified orally or in writing of the possibility of such damage. Because some jurisdictions"
                    " do not allow limitations on implied warranties, or"
                    " limitations of liability for consequential or incidental damages, these limitations may not apply to you.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Accuracy of materials",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "The materials appearing on Floment (parent company: Incentivize app, Inc), Inc's website could include technical,"
                    " typographical, or photographic errors. Incentivize App, Inc does not warrant that any of the materials on its website "
                    "are accurate, complete or current. Floment (parent company: Incentivize app, Inc), Inc may make changes to the materials "
                    "contained on its website at any time without notice. However, "
                    "Floment (parent company: Incentivize app, Inc), Inc does not make any commitment to update the materials.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Links",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Floment (parent company: Incentivize app, Inc), Inc has not reviewed all of the sites linked to its website"
                    " and is not responsible for the contents of any such linked site. The inclusion of any link does not imply endorsement"
                    " by Floment (parent company: Incentivize app, Inc), Inc of the site. "
                    "Use of any such linked website is at the user's own risk.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Adult Content",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Floment (parent company: Incentivize app, Inc), Inc does not allow pornography of any kind. If you share "
                    "or upload pornographic material, your content may be modified or removed. This may also result in removal or restriction "
                    "of your account by the sole judgement of Floment (parent company: Incentivize app, Inc), Inc. Floment (parent company:"
                    " Incentivize app, Inc) is a community of free speech, "
                    "but we cannot and will not host pornography for ethical and legal reasons.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Modifications",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Floment (parent company: Incentivize app, Inc), Inc may revise these terms of service for its website "
                    "at any time without notice. By using this website you are agreeing"
                    " to be bound by the then current version of these terms of service.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(20),
              TypoGraphy(
                text: "Governing Law",
                level: 5,
                fontWeight: FontWeight.w700,
              ),
              Space.height(20),
              TypoGraphy(
                text: "These terms and conditions are governed by and construed in accordance with the laws of Delaware and "
                    "you irrevocably submit to the exclusive jurisdiction of the courts in that State or location.",
                level: 4,
                fontWeight: FontWeight.w400,
              ),
              Space.height(30),
            ],
          ),
        ),
      ),
    );
  }
}
