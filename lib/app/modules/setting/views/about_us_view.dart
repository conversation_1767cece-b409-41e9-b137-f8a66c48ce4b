import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';

class AboutUsView extends StatelessWidget {
  const AboutUsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(30)),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Space.height(50),
            Align(
              alignment: Alignment.centerLeft,
              child: InkWell(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  padding: EdgeInsets.all(10),
                  child: SvgPicture.asset(
                    AppImage.backArrow,
                    height: MySize.getScaledSizeHeight(28),
                    width: MySize.getScaledSizeHeight(28),
                    color: AppTheme.whiteWithNull,
                  ),
                ),
              ),
            ),
            Image.asset(
              AppImage.appLogo,
              color: AppTheme.primaryIconDark,
              width: MySize.getScaledSizeWidth(100),
              height: MySize.size39,
            ),
            Space.height(40),
            TypoGraphy(
              text: "About Floment",
              level: 6,
              fontWeight: FontWeight.w600,
            ),
            Space.height(10),
            TypoGraphy(
              text: "Welcome to Floment, your ultimate productivity companion. Our platform is designed to help you achieve your goals and maximize your potential. ",
              level: 4,
              fontWeight: FontWeight.w400,
              textAlign: TextAlign.center,
            ),
            Space.height(50),
            TypoGraphy(
              text: "Vision",
              level: 6,
              fontWeight: FontWeight.w600,
              textAlign: TextAlign.center,
            ),
            Space.height(10),
            TypoGraphy(
              text: "Built exclusively for you and your goals\nTailored tools and features to help you achieve your unique goals.",
              level: 3,
              fontWeight: FontWeight.w400,
              textAlign: TextAlign.center,
            ),
            Space.height(50),
            TypoGraphy(
              text: "Mission",
              level: 6,
              fontWeight: FontWeight.w600,
              textAlign: TextAlign.center,
            ),
            Space.height(10),
            TypoGraphy(
              text: "Strategize and set clear, actionable goals for your projects. Use our robust tools to outline your objectives and create a roadmap for success.",
              level: 3,
              fontWeight: FontWeight.w400,
              textAlign: TextAlign.center,
            ),
            Space.height(50),
            TypoGraphy(
              text: "Who are we?",
              level: 6,
              fontWeight: FontWeight.w600,
              textAlign: TextAlign.center,
            ),
            Space.height(10),
            TypoGraphy(
              text: "Floment began in co-founder Raj Patel living room in 2002 and was officially launched on May 5, 2003.",
              level: 3,
              fontWeight: FontWeight.w400,
              textAlign: TextAlign.center,
            ),
            Space.height(20),
            TypoGraphy(
              text: "Floment is a platform that connects you with the right people, resources, and opportunities to help you achieve your goals. We are committed to providing you with the tools and support you need to succeed.",
              level: 3,
              fontWeight: FontWeight.w400,
              textAlign: TextAlign.center,
            )
          ],
        ),
      ),
    );
  }
}
