import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/setting/component/change_password_view.dart';
import 'package:incenti_ai/app/modules/setting/views/about_us_view.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/current_user.dart';

import '../../../../constants/api.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';
import '../../../routes/app_pages.dart';
import '../component/custom_navigation_view.dart';
import '../views/privacy_policy_view.dart';
import '../views/terms_service_view.dart';


class SettingController extends GetxController {

  RxString selectedOption = 'Home'.obs;
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController oldPasswordController = TextEditingController();
  RxBool isNewPasswordVisible = true.obs;
  RxBool isConfirmPasswordVisible = true.obs;
  RxBool isOldPasswordVisible = true.obs;
  RxBool isError = false.obs;
  RxBool isLoading = false.obs;
  ApiManager apiManager = ApiManager();
  RxBool switchValue = false.obs;

  RxList validPasswordFormate =
      ["1 uppercase", "8 characters", "1 number", "1 Special characters"].obs;

  RxList<bool> passwordConditionsMet =
  RxList.generate(4, (index) => false);

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    switchValue.value = box.read('isDarkMode');
    if(CurrentUser.user.customSetting != null) {
      selectedOption.value = CurrentUser.user.customSetting?.homeSection?.capitalize ?? "explore";
    }
  }

  final List<Map<String, dynamic>> options = [
    {'title': 'Explore', 'icon': AppImage.navExplore},
    {'title': 'Projects', 'icon': AppImage.navProjects},
    {'title': 'Communities', 'icon': AppImage.navCommunication},
    {'title': 'To Dos', 'icon': AppImage.navTodos},
    {'title': 'Profile', 'icon': AppImage.userCircle},
  ];

  callApiForChangeNavigation({required BuildContext context,bool isFromSignUp = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "customSetting" : {
        "homeSection": selectedOption.value.toLowerCase()
      }
    };

    log("BODY : $dict");

    return apiManager.callApi(
      APIS.user.updateMe,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            if(isFromSignUp) {
              Get.toNamed(Routes.create_project,
                  arguments: {"isFromSignUp": true});
            } else {
              Get.back();
            }
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            if (CurrentUser.user.customSetting != null) {
              CurrentUser.user.customSetting?.homeSection = selectedOption.value.toLowerCase();
            }
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForChangePassword({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "currentPassword": oldPasswordController.value.text,
      "newPassword": newPasswordController.value.text,
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.changePassword,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            CommonFunction.showCustomSnackbar(
                message: response['message'],
            );
            Get.offAllNamed(Routes.ONBOARDING);
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          isError: true,
          backgroundColor: AppTheme.red
        );
      },
    );
  }

  List settingOption = [
    {
      "image": AppImage.nightMode,
      "name": "Theme",
      "onTap": () {
        // Get.find<ThemeController>().changeTheme();
      }
    },
    if (CurrentUser.user.isPasswordSet == true) // Add this condition
      {
        "image": AppImage.changePassword,
        "name": "Change Password",
        "onTap": () {
          Navigator.push(Get.context!, MaterialPageRoute(builder: (context) => ChangePasswordView()));
        }
      },
    {
      "image": AppImage.onboardingLogo,
      "name": "Onboarding",
      "onTap": () {
        Get.toNamed(Routes.tutorial,arguments: {"isSetting": true});
        // Navigator.push(Get.context!, MaterialPageRoute(builder: (context) => CustomNavigationView(),));
      }
    },
    {
      "image": AppImage.customNav,
      "name": "Customise Navigation",
      "onTap": () {
        Navigator.push(Get.context!, MaterialPageRoute(builder: (context) => CustomNavigationView(),));
      }
    },
    {
      "image": AppImage.aboutUs,
      "name": "About Us",
      "onTap": () {
        Navigator.push(Get.context!, MaterialPageRoute(builder: (context) => AboutUsView()));
      }
    },
    {
      "image": AppImage.terms,
      "name": "Terms of Service",
      "onTap": () async {
        // Uri url = Uri.parse("https://www.incenti.ai/terms");
        // if (!await launchUrl(url)) {
        //   throw Exception('Could not launch $url');
        // }
        Navigator.push(Get.context!, MaterialPageRoute(builder: (context) => TermsServiceView()));

      }
    },
    {
      "image": AppImage.privacy,
      "name": "Privacy Policy",
      "onTap": () async {
        Navigator.push(Get.context!, MaterialPageRoute(builder: (context) => PrivacyPolicyView()));
        // Uri url = Uri.parse("https://www.incenti.ai/privacy-policy");
        // if (!await launchUrl(url)) {
        //   throw Exception('Could not launch $url');
        // }
      }
    },
  ].obs;

}
