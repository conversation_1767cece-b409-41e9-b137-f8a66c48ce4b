import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/setting/controllers/setting_controller.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/typography.dart';

class ChangePasswordView extends StatelessWidget {
  const ChangePasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingController>();
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Space.height(30),
                Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: SvgPicture.asset(
                      AppImage.backArrow,
                      height: MySize.getScaledSizeHeight(28),
                      width: MySize.getScaledSizeHeight(28),
                      color: AppTheme.whiteWithNull,
                    ),
                  ),
                ),
                Space.height(10),
                TypoGraphy(
                  text: "Change Password",
                  level: 8,
                  fontWeight: FontWeight.w700,
                ),
                Space.height(30),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 52),
                  child: Obx(
                        () => AppTextField(
                      controller: controller.oldPasswordController,
                      labelText: "Old Password",
                      suffixIconAsset: AppImage.showPassword,
                      obscureText: controller.isOldPasswordVisible.value,
                      suffixIcon: Padding(
                        padding: EdgeInsets.only(right: MySize.size20 ?? 20),
                        child: InkWell(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            controller.isOldPasswordVisible.value =
                            !controller.isOldPasswordVisible.value;
                          },
                          child: controller.isOldPasswordVisible.value
                              ? SvgPicture.asset(
                            AppImage.showPassword,
                            color: AppTheme.whiteWithNull,
                          )
                              : Icon(
                            Icons.visibility_outlined,
                            color: AppTheme.whiteWithBase,
                            size: MySize.size20,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Space.height(30),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 52),
                  child: Obx(
                        () => AppTextField(
                          errorMessage: controller.isError.value,
                      controller: controller.newPasswordController,
                      labelText: "New Password",
                      suffixIconAsset: AppImage.showPassword,
                      obscureText: controller.isNewPasswordVisible.value,
                      suffixIcon: Padding(
                        padding: EdgeInsets.only(right: MySize.size20 ?? 20),
                        child: InkWell(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            controller.isNewPasswordVisible.value =
                            !controller.isNewPasswordVisible.value;
                          },
                          child: controller.isNewPasswordVisible.value
                              ? SvgPicture.asset(
                            AppImage.showPassword,
                            color: AppTheme.whiteWithNull,
                          )
                              : Icon(
                            Icons.visibility_outlined,
                            color: AppTheme.whiteWithBase,
                            size: MySize.size20,
                          ),
                        ),
                      ),
                      onChangedValue: (value) {
                        CommonFunction.checkPasswordFormat(
                            passwordValue: controller.newPasswordController.text,
                            passwordConditionsMet: controller.passwordConditionsMet);
                        if(value == controller.confirmPasswordController.value.text &&
                            controller
                                .confirmPasswordController.value.text.isNotEmpty) {
                          controller.isError.value = false;
                        } else if (controller
                            .newPasswordController.value.text.isEmpty ||
                            value!.isEmpty) {
                          controller.isError.value = false;
                        }
                      },
                      // onChangedValue: (p0) => CommonFunction.checkPasswordFormat(
                      //     passwordValue: controller.newPasswordController.text,
                      //     passwordConditionsMet: controller.passwordConditionsMet),
                    ),
                  ),
                ),
                Space.height(MySize.size8 ?? 8),
                Obx(
                      () => Padding(
                    padding: EdgeInsets.only(left: MySize.size20 ?? 52),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Wrap(
                        spacing: MySize.size4 ?? 4,
                        runSpacing: MySize.size4 ?? 4,
                        children: List.generate(
                          controller.validPasswordFormate.length,
                              (index) {
                            return Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: MySize.size8 ?? 8,
                                  vertical: MySize.size4 ?? 4),
                              decoration: BoxDecoration(
                                  color: controller.passwordConditionsMet[index]
                                      ? AppTheme.success.withValues(alpha: 0.1)
                                      : AppTheme.grey.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(5)),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (controller.passwordConditionsMet[index]) ...[
                                    SvgPicture.asset(AppImage.checkIcon),
                                    Space.width(4),
                                  ],
                                  TypoGraphy(
                                    text: controller.validPasswordFormate[index],
                                    level: 2,
                                    color: controller.passwordConditionsMet[index]
                                        ? AppTheme.success
                                        : AppTheme.grey,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
                Space.height(30),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 52),
                  child: Obx(
                        () => AppTextField(
                      onChangedValue: (value) {
                        if (value == controller.newPasswordController.value.text &&
                            value!.isNotEmpty &&
                            controller
                                .newPasswordController.value.text.isNotEmpty) {
                          controller.isError.value = false;
                        } else if (controller
                            .newPasswordController.value.text.isEmpty ||
                            value!.isEmpty) {
                          controller.isError.value = false;
                        } else {
                          controller.isError.value = true;
                        }
                      },
                      errorMessage: controller.isError.value,
                      controller: controller.confirmPasswordController,
                      labelText: "Confirm New Password",
                      suffixIconAsset: AppImage.showPassword,
                      obscureText: controller.isConfirmPasswordVisible.value,
                      suffixIcon: Padding(
                        padding: EdgeInsets.only(right: MySize.size20 ?? 20),
                        child: InkWell(
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            controller.isConfirmPasswordVisible.value =
                            !controller.isConfirmPasswordVisible.value;
                          },
                          child: controller.isConfirmPasswordVisible.value
                              ? SvgPicture.asset(
                            AppImage.showPassword,
                            color: AppTheme.whiteWithNull,
                          )
                              : Icon(
                            Icons.visibility_outlined,
                            color: AppTheme.whiteWithBase,
                            size: MySize.size20,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Space.height(30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Obx(
                      () => Buttons(
                        isLoading: controller.isLoading.value,
                        onTap: () {
                          // Get.back();
                          if (controller.newPasswordController.text.isNotEmpty &&
                              controller
                                  .confirmPasswordController.text.isNotEmpty &&
                              controller.newPasswordController.text ==
                                  controller.confirmPasswordController.text &&
                              controller.passwordConditionsMet
                                  .every((condition) => condition == true)) {
                            controller.callApiForChangePassword(context: context);
                          } else {
                            if (controller.newPasswordController.text !=
                                controller.confirmPasswordController.text) {
                              controller.isError.value = true;
                            } else if(controller.passwordConditionsMet
                                .every((condition) => condition == false)){
                              controller.isError.value = true;
                            }
                          }
                        },
                        buttonText: "Update",
                        buttonTextLevel: 4,
                        width: MySize.getScaledSizeWidth(189),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
