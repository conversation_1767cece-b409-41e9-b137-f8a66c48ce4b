import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../controllers/app_under_maintenance_controller.dart';

class AppUnderMaintenanceView extends GetView<AppUnderMaintenanceController> {
  const AppUnderMaintenanceView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    MySize().init(context);
    return Scaffold(
        // backgroundColor: AppTheme.white,
        body: SafeArea(child: LayoutBuilder(
          builder: (context, constraints) {
            double screenWidth = constraints.maxWidth;
            double screenHeight = constraints.maxHeight;
            double horizontalPadding = screenWidth * 0.05; // 5% of screen width
            double verticalSpacing = screenHeight * 0.05; // 5% of screen height
            return Container(
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(AppImage.splashBackground),
                  fit: BoxFit.cover,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                child: Column(
                  children: [
                    SizedBox(height: verticalSpacing),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: screenWidth * 0.025),
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: Image.asset(AppImage.appLogo,
                            width: MySize.getScaledSizeHeight(150),
                            color: AppTheme.whiteWithBase,
                            fit: BoxFit.contain),
                      ),
                    ),
                    SizedBox(height: verticalSpacing * 1.6),
                    Image.asset(
                      AppImage.appUnderMaintenance,
                      height: screenHeight * 0.5,
                      // width: screenWidth * 0.6,
                    ),
                    SizedBox(height: verticalSpacing * 0.5),
                    TypoGraphy(
                      text: "The app is under development.",
                      color: box.read('isDarkMode') ? AppTheme.white : AppTheme.darkGrey,
                      level: 5,
                    ),
                    const Space.height(20),
                    Center(
                      child: SizedBox(
                        width: MySize.getScaledSizeWidth(281),
                        child: TypoGraphy(
                          textAlign: TextAlign.center,
                          text:
                              "Our team is working on the app, the app will\nbe launched soon.",
                          color: box.read('isDarkMode') ? AppTheme.white : AppTheme.darkGrey,
                          level: 3,
                        ),
                      ),
                    ),
                    const Space.height(75),
                    Buttons(
                        height: MySize.getScaledSizeHeight(40),
                        buttonTextLevel: 4,
                        borderRadius: BorderRadius.circular(9),
                        buttonText: "Retry",
                        onTap: () {
                          Get.toNamed(Routes.SPLASH);
                        }),
                  ],
                ),
              ),
            );
          },
        )));
  }
}
