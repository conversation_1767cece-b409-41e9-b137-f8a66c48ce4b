import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import '../../../../constants/app_argument_key.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../controllers/verify_otp_view_controller.dart';

class VerifyOtpView extends GetWidget<VerifyOtpViewController> {
  const VerifyOtpView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      // backgroundColor: AppTheme.white,
      body: SingleChildScrollView(
        child: Obx(
          () => Column(
            children: [
              Space.height(50),
              Padding(
                padding: EdgeInsets.only(left: MySize.size30 ?? 30),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      Get.back();
                    },
                    child: SvgPicture.asset(
                      AppImage.backArrow,
                      height: MySize.size28,
                      width: MySize.size28,
                      color: AppTheme.whiteWithNull,
                    ),
                  ),
                ),
              ),
              Space.height(30.37),
              Image.asset(
                AppImage.appLogo,
                color: AppTheme.whiteWithBase,
                width: MySize.getScaledSizeWidth(110),
                // height: MySize.size39,
              ),
              Space.height(22.34),
              TypoGraphy(
                text: "Verify OTP",
                level: 8,
                fontWeight: FontWeight.w700,
                // color: AppTheme.baseBlack,
              ),
              Space.height(10),
              TypoGraphy(
                text: "Enter the 6-digit OTP we sent to the email",
                level: 3,
                fontWeight: FontWeight.w400,
                color: AppTheme.grey,
              ),
              Space.height(5),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TypoGraphy(
                    text: "address",
                    level: 3,
                    fontWeight: FontWeight.w400,
                    color: AppTheme.grey,
                  ),
                  TypoGraphy(
                    text: " ‘${controller.args[AppArguments.email]}’",
                    level: 3,
                    fontWeight: FontWeight.w600,
                    // color: AppTheme.baseBlack,
                  ),
                  Space.width(3),
                  InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      Get.back();
                    },
                    child: SvgPicture.asset(
                      AppImage.editIcon,
                      height: MySize.size15,
                      width: MySize.size15,
                    ),
                  ),
                ],
              ),
              Space.height(28),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size40 ?? 52),
                child: Pinput(
                  length: 6,
                  focusNode: controller.otpFocusNode,
                  controller: controller.otpController,
                  focusedPinTheme: controller.buildPinTheme(AppTheme.primary1),
                  defaultPinTheme: controller.buildPinTheme(AppTheme.grey),
                  errorPinTheme: controller.buildPinTheme(AppTheme.red),
                  forceErrorState:
                      controller.errorMessage.value.isNotEmpty ? true : false,
                  onSubmitted: (String pin) {
                    controller.otpController.text = pin;
                    if (controller.args != null &&
                        controller.args[AppArguments.isForgotPassword] !=
                            null) {
                      controller.callApiForVerifyOtpForgotPassword(
                          context: context);
                    } else {
                      if (controller.otpController.text.isNotEmpty) {
                        controller.callApiForVerifyOtp(context: context);
                      } else {
                        controller.errorMessage.value = "";
                      }
                      // Get.toNamed(Routes.User_Detail);
                    }
                  },
                ),
              ),
              if (controller.errorMessage.value.isNotEmpty) ...[
                Space.height(6),
                Padding(
                  padding: EdgeInsets.only(right: MySize.size40 ?? 52),
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: TypoGraphy(
                      text: "Invalid OTP. Please try again.",
                      level: 3,
                      fontWeight: FontWeight.w400,
                      color: AppTheme.red,
                    ),
                  ),
                ),
              ],
              controller.errorMessage.value.isNotEmpty
                  ? Space.height(8)
                  : Space.height(30),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size40 ?? 52),
                child: Buttons(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    if (controller.args != null &&
                        controller.args[AppArguments.isForgotPassword] !=
                            null) {
                      controller.callApiForVerifyOtpForgotPassword(
                          context: context);
                    } else {
                      if (controller.otpController.text.isNotEmpty) {
                        controller.callApiForVerifyOtp(context: context);
                      } else {
                        controller.errorMessage.value = "";
                      }
                      // Get.toNamed(Routes.User_Detail);
                    }
                  },
                  buttonText: 'Verify  OTP',
                  buttonTextLevel: 4,
                  width: MySize.size188,
                  height: MySize.size70 ?? 70,
                  isLoading: controller.isLoading.value,
                ),
              ),
              Space.height(30),
              TypoGraphy(
                text: "Didn’t receive the code?",
                level: 3,
                fontWeight: FontWeight.w400,
                color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey[50] : AppTheme.baseBlack,
              ),
              Space.height(3),
              Obx(
                () => InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    if (controller.timerText.value == "Resend OTP") {
                      controller
                          .startTimer(); // Restart the timer when "Resend OTP" is tapped
                      if (controller.args != null &&
                          controller.args[AppArguments.isForgotPassword] !=
                              null) {
                        controller.callApiForResendForgotPassword(
                            context: context);
                      } else {
                        controller.callApiForReSendOtp(context: context);
                      }
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TypoGraphy(
                        text: controller.timerText.value == "Resend OTP"
                            ? ""
                            : "Resend in ",
                        level: 3,
                        fontWeight: FontWeight.w400,
                        // color: AppTheme.baseBlack,
                      ),
                      TypoGraphy(
                        text: controller.timerText.value,
                        level: 3,
                        fontWeight: FontWeight.w700,
                        color: controller.timerText.value == "Resend OTP"
                            ? AppTheme.primaryIconDark
                            : AppTheme.whiteWithBase,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
