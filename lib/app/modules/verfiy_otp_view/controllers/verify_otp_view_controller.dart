import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:pinput/pinput.dart';
import '../../../../constants/api.dart';
import '../../../../constants/app_argument_key.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';
import '../../../routes/app_pages.dart';

class VerifyOtpViewController extends GetxController {
  TextEditingController otpController = TextEditingController();
  FocusNode otpFocusNode = FocusNode();
  RxString timerText = "00:30".obs;
  RxString errorMessage = "".obs;
  Timer? timer;
  RxInt start = 30.obs;
  RxBool isReset = false.obs;
  var args = Get.arguments;
  RxBool isLoading = false.obs;
  ApiManager apiManager = ApiManager();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    otpFocusNode.requestFocus();
    startTimer();

    Future.delayed(
      Duration(seconds: 1),
      () {
        CommonFunction.showCustomSnackbar(
          message: "OTP has been sent. Check your inbox.",
        );
      },
    );
  }

  void startTimer() {
    start.value = 30; // Reset timer
    timerText.value = "00:30"; // Reset timer display
    timer?.cancel(); // Cancel any existing timer
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (start.value == 0) {
        timer.cancel(); // Stop timer
        timerText.value = "Resend OTP"; // Change text to "Resend OTP"
      } else {
        start--;
        timerText.value =
            "00:${start.value.toString().padLeft(2, '0')}"; // Update timer display
      }
    });
  }

  @override
  void onClose() {
    timer?.cancel(); // Cancel the timer when the controller is disposed
    super.onClose();
  }

  callApiForVerifyOtp({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "OTP": otpController.text,
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.verifySignUpOTP,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String? token = response['token'];
            box.write("token", token);
            isLoading.value = false;
            if(response['isNewUser'] == true) {
              Get.toNamed(Routes.User_Detail);
            } else {
              CurrentUser.getMe(callback: () async {
                Get.offAllNamed(Routes.Bottom_Bar); // Replace with your desired route
              });
            }
          } else {
            errorMessage.value = "Invalid OTP. Please try again.";
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        errorMessage.value = "Invalid OTP. Please try again.";
        isLoading.value = false;
      },
    );
  }

  callApiForReSendOtp({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "email": args[AppArguments.email],
      "password": args[AppArguments.password],
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.signUp,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String? token = response['token'];
            box.remove("token");
            box.write("token", token);
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  PinTheme buildPinTheme(Color borderColor) {
    return PinTheme(
      height: MySize.size60,
      width: MySize.size50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: box.read('isDarkMode') ? AppTheme.bottomBar  :AppTheme.white,
        border: Border.all(color: borderColor),
      ),
      textStyle: TextStyle(fontSize: MySize.size20, color: AppTheme.whiteWithBase, fontWeight: FontWeight.w600),
    );
  }

  callApiForVerifyOtpForgotPassword({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "OTP": otpController.text,
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.verifyForgotPassword,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String? token = response['token'];
            box.remove("token");
            box.write("token", token);
            isLoading.value = false;
            Get.offAndToNamed(Routes.Reset_Password);
          } else {
            errorMessage.value = "Invalid OTP. Please try again.";
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        errorMessage.value = "Invalid OTP. Please try again.";
        isLoading.value = false;
      },
    );
  }

  callApiForResendForgotPassword({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "email": args[AppArguments.email],
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.forgotPassword,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String? token = response['token'];
            box.write("token", token);
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }


}
