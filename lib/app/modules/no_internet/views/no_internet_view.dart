import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../controllers/no_internet_controller.dart';

class NoInternetView extends GetWidget<NoInternetController> {
  const NoInternetView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return PopScope(
      canPop: true,
      child: Scaffold(
        backgroundColor: AppTheme.primary1,
        body: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              double screenHeight = constraints.maxHeight;
              double verticalSpacing =
                  screenHeight * 0.05; // 5% of screen height
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: verticalSpacing * 2),
                  Image.asset(
                    AppImage.appLogo,
                    width: MySize.size160,
                    height: MySize.size70,
                  ),
                  SizedBox(height: verticalSpacing * 2),
                  Image.asset(
                    AppImage.noInternet,
                    height: MySize.size220,
                    color: AppTheme.white,
                  ),
                  Space.height(20),
                  TypoGraphy(
                    text: "No internet connection",
                    color: AppTheme.white,
                    level: 5,
                  ),
                  Space.height(20),
                  TypoGraphy(
                    text:
                        "Please, check the internet connection.\n                    Turn it on if off.",
                    color: AppTheme.white,
                    level: 3,
                  ),
                  // SizedBox(height: verticalSpacing * 1.9),
                  Spacer(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Buttons(
                        buttonText: "Retry",
                        buttonTextLevel: 4,
                        width: MySize.size188,
                        height: MySize.size70 ?? 70,
                        color: AppTheme.white,
                        textColor: AppTheme.primary1,
                        onTap: () async {
                          HapticFeedback.lightImpact();
                          var connectivityResult =
                              await Connectivity().checkConnectivity();
                          if (connectivityResult[0] !=
                              ConnectivityResult.none) {
                            Get.back();
                          }
                        },
                      ),
                    ],
                  ),
                  SizedBox(height: verticalSpacing * 1.9),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
