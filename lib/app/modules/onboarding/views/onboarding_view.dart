
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/typography.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/buttons.dart';
import '../../../routes/app_pages.dart';
import '../controllers/onboarding_controller.dart';

class OnboardingView extends GetWidget<OnboardingController> {
  const OnboardingView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      backgroundColor: const Color(0xFF121722),
      body: Stack(
        children: [
          // Repeating animated background
          AnimatedBuilder(
            animation: controller.animationController,
            builder: (context, child) {
              return Stack(
                children: [
                  // First image
                  Transform.translate(
                    offset: Offset(controller.animation.value, 0),
                    child: Image.asset(
                      AppImage.onboardingAnimation,
                    ),
                  ),
                  // Second image
                  Transform.translate(
                    offset: Offset(
                        controller.animation.value + MySize.screenWidth, 0),
                    child: Image.asset(
                      AppImage.onboardingAnimation,
                    ),
                  ),
                  // Third image
                  Transform.translate(
                    offset: Offset(controller.animation.value + MySize.screenWidth * 2, 0),
                    child: Image.asset(
                      AppImage.onboardingAnimation,
                    ),
                  ),
                ],
              );
            },
          ),

          // Static Background Image
          Image.asset(
            AppImage.onboardingBack,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
          ),
          Positioned(
            bottom: MySize.getScaledSizeHeight(60),
            left: 0,
            right: 0,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: MySize.size35 ?? 44),
                  child: Image.asset(
                    AppImage.appLogo,
                    width: MySize.getScaledSizeWidth(150),
                  ),
                ),
                Space.height(30.95),
                Padding(
                  padding: EdgeInsets.only(left: MySize.size35 ?? 44),
                  child: TypoGraphy(
                    text: "Organize your\nsocial media &",
                    level: 10,
                    color: AppTheme.white,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: MySize.size35 ?? 44),
                  child: Row(
                    children: [
                      TypoGraphy(
                        text: "Find",
                        level: 10,
                        color: AppTheme.white,
                      ),
                      Space.width(8),
                      Expanded(
                        child: SizedBox(
                          height: MySize.size50,
                          child: ListView.builder(
                            padding: EdgeInsets.zero,
                            controller: controller.scrollController,
                            itemCount: controller.organizeList.length,
                            shrinkWrap: true,
                            // physics: NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              return SizedBox(
                                height: MySize.size50,
                                child: TypoGraphy(
                                  text: controller.organizeList[index],
                                  level: 10,
                                  color: AppTheme.primary1,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Space.height(18),
                Padding(
                  padding: EdgeInsets.only(left: MySize.size35 ?? 44),
                  child: TypoGraphy(
                    text:
                        "Inspire Others by Unleashing your Own\nGrowth Potential",
                    level: 4,
                    fontWeight: FontWeight.w400,
                    color: AppTheme.white,
                  ),
                ),
                Space.height(66),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size35 ?? 44),
                  child: Buttons(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Get.toNamed(Routes.Sign_Up);
                    },
                    buttonText: 'Sign Up',
                    buttonTextLevel: 4,
                    width: MySize.size158,
                    height: MySize.size70 ?? 70,
                  ),
                ),
                Space.height(30),
                Align(
                  alignment: Alignment.center,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Get.toNamed(Routes.Sign_In);
                    },
                    child: TypoGraphy(
                      text: "Sign In",
                      level: 4,
                      color: AppTheme.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


/*class AutoScrollingText extends StatefulWidget {
  @override
  _AutoScrollingTextState createState() => _AutoScrollingTextState();
}

class _AutoScrollingTextState extends State<AutoScrollingText> {
  final List<String> _words = ['boy', 'girl', 'other'];
  late ScrollController _scrollController;
  late Timer _timer;
  int _currentIndex = 0;
  bool _isReversing = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _timer.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _timer =
        Timer.periodic(const Duration(seconds: 1, milliseconds: 500), (timer) {
      setState(() {
        if (_isReversing) {
          _currentIndex--;
          if (_currentIndex == 0) {
            _isReversing = false;
          }
        } else {
          _currentIndex++;
          if (_currentIndex == _words.length - 1) {
            _isReversing = true;
          }
        }
        _scrollToIndex(_currentIndex);
      });
    });
  }

  void _scrollToIndex(int index) {
    _scrollController.animateTo(
      index * 50.0, // 50.0 is the height of one item
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      Text(
        'testing',
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      SizedBox(width: 5),
      Expanded(
          child: Container(
        height: 50,
        child: ListView.builder(
          padding: EdgeInsets.zero,
          controller: _scrollController,
          itemCount: _words.length,
          itemBuilder: (context, index) {
            return SizedBox(
              height: 50,
              child: Center(
                child: Text(
                  _words[index],
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
            );
          },
        ),
      )),
    ]);
  }
}*/
