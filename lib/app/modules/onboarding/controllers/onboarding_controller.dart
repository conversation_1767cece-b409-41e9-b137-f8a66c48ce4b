import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';

class OnboardingController extends GetxController
    with SingleGetTickerProviderMixin {
  late AnimationController animationController;
  late Animation<double> animation;

  RxList organizeList = [
    "creators",
    "communities",
    "projects"
  ].obs;

  late ScrollController scrollController;
  late Timer timer;
  RxInt currentIndex = 0.obs;
  RxBool isReversing = false.obs;

  @override
  void onInit() {
    super.onInit();
    scrollController = ScrollController();
    startAutoScroll();
    // Initialize AnimationController
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 15), // Animation duration
    );

    // Define animation (left to right repetitive scrolling)
    animation = Tween<double>(
      begin: 0.0, // Start at position 0
      end: -Get.width * 2, // Move to the left by the screen width
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.linear, // Linear scrolling effect
    ));
    // Add a listener to reverse the animation when it reaches the end or beginning
    animationController.addStatusListener((status) async {
      if (status == AnimationStatus.completed) {
        await Future.delayed(const Duration(seconds: 2));
        animationController.reverse(); // Reverse animation
      } else if (status == AnimationStatus.dismissed) {
        await Future.delayed(const Duration(seconds: 2));
        animationController.forward(); // Play animation again
      }
    });

    // Start the animation
    animationController.forward();

  }

  void startAutoScroll() {
    timer = Timer.periodic(const Duration(seconds: 1, milliseconds: 500), (timer) {
        if (isReversing.value) {
          currentIndex.value--;
          if (currentIndex.value == 0) {
            isReversing.value = false;
          }
        } else {
          currentIndex.value++;
          if (currentIndex.value == organizeList.length - 1) {
            isReversing.value = true;
          }
        }
        scrollToIndex(currentIndex.value);
    });
  }
  void scrollToIndex(int index) {
    scrollController.animateTo(
      index * (MySize.size50 ?? 50),
      duration: const Duration(milliseconds: 500),
      curve: Curves.linear,
    );
  }

  @override
  void onClose() {
    // Dispose of the animation controller
    animationController.dispose();
    timer.cancel();
    scrollController.dispose();
    super.onClose();
  }
}
