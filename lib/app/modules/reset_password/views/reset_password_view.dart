import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../controllers/reset_password_controller.dart';

class ResetPasswordView extends GetWidget<ResetPasswordController> {
  const ResetPasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return WillPopScope(
      onWillPop: () {
        Get.offNamedUntil(Routes.Sign_In, (route) => route.settings.name == Routes.ONBOARDING);
        return Future.value(false);
      },
      child: Scaffold(
        // backgroundColor: AppTheme.white,
        body: SingleChildScrollView(
          physics: ClampingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Space.height(90),
              Image.asset(
                AppImage.appLogo,
                color: AppTheme.whiteWithBase,
                width: MySize.getScaledSizeWidth(120),
                // height: MySize.size39,
              ),
              Space.height(25),
              TypoGraphy(
                text: "Reset Password",
                level: 8,
                fontWeight: FontWeight.w700,
                // color: AppTheme.baseBlack,
              ),
              Space.height(24),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 52),
                child: Obx(
                  () => AppTextField(
                    controller: controller.newPasswordController,
                    labelText: "New Password",
                    suffixIconAsset: AppImage.showPassword,
                    obscureText: controller.isNewPasswordVisible.value,
                    suffixIcon: Padding(
                      padding: EdgeInsets.only(right: MySize.size20 ?? 20),
                      child: InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          controller.isNewPasswordVisible.value =
                              !controller.isNewPasswordVisible.value;
                        },
                        child: controller.isNewPasswordVisible.value
                            ? SvgPicture.asset(
                                AppImage.showPassword,
                          color:  AppTheme.whiteWithNull,
                              )
                            : Icon(
                                Icons.visibility_outlined,
                          color:  AppTheme.whiteWithBase,
                                size: MySize.size20,
                              ),
                      ),
                    ),
                    onChangedValue: (p0) => CommonFunction.checkPasswordFormat(
                        passwordValue: controller.newPasswordController.text,
                        passwordConditionsMet: controller.passwordConditionsMet),
                  ),
                ),
              ),
              Space.height(MySize.size8 ?? 8),
              Obx(
                () => Padding(
                  padding: EdgeInsets.only(left: MySize.size45 ?? 52),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Wrap(
                      spacing: MySize.size4 ?? 4,
                      runSpacing: MySize.size4 ?? 4,
                      children: List.generate(
                        controller.validPasswordFormate.length,
                        (index) {
                          return Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.size8 ?? 8,
                                vertical: MySize.size4 ?? 4),
                            decoration: BoxDecoration(
                                color: controller.passwordConditionsMet[index]
                                    ? AppTheme.success.withValues(alpha: 0.1)
                                    : AppTheme.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(5)),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (controller.passwordConditionsMet[index]) ...[
                                  SvgPicture.asset(AppImage.checkIcon),
                                  Space.width(4),
                                ],
                                TypoGraphy(
                                  text: controller.validPasswordFormate[index],
                                  level: 2,
                                  color: controller.passwordConditionsMet[index]
                                      ? AppTheme.success
                                      : AppTheme.grey,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
              Space.height(30),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 52),
                child: Obx(
                  () => AppTextField(
                    onChangedValue: (value) {
                      if (value == controller.newPasswordController.value.text &&
                          value!.isNotEmpty &&
                          controller
                              .newPasswordController.value.text.isNotEmpty) {
                        controller.isError.value = false;
                      } else if (controller
                              .newPasswordController.value.text.isEmpty ||
                          value!.isEmpty) {
                        controller.isError.value = false;
                      } else {
                        controller.isError.value = true;
                      }
                    },
                    errorMessage: controller.isError.value,
                    controller: controller.confirmPasswordController,
                    labelText: "Confirm New Password",
                    suffixIconAsset: AppImage.showPassword,
                    obscureText: controller.isConfirmPasswordVisible.value,
                    suffixIcon: Padding(
                      padding: EdgeInsets.only(right: MySize.size20 ?? 20),
                      child: InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          controller.isConfirmPasswordVisible.value =
                              !controller.isConfirmPasswordVisible.value;
                        },
                        child: controller.isConfirmPasswordVisible.value
                            ? SvgPicture.asset(
                                AppImage.showPassword,
                          color:  AppTheme.whiteWithNull,
                              )
                            : Icon(
                                Icons.visibility_outlined,
                          color:  AppTheme.whiteWithBase,
                                size: MySize.size20,
                              ),
                      ),
                    ),
                  ),
                ),
              ),
              Space.height(30),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 52),
                child: Buttons(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    if (controller.newPasswordController.text.isNotEmpty &&
                        controller
                            .confirmPasswordController.text.isNotEmpty &&
                        controller.newPasswordController.text ==
                            controller.confirmPasswordController.text &&
                        controller.passwordConditionsMet
                            .every((condition) => condition == true)) {
                      controller.callApiForResetPassword(context: context);
                    } else {
                      if (controller.newPasswordController.text !=
                          controller.confirmPasswordController.text) {
                        controller.isError.value = true;
                      }
                    }
                  },
                  buttonText: 'Reset Password',
                  buttonTextLevel: 4,
                  width: MySize.size188,
                  height: MySize.size70 ?? 70,
                ),
              ),
              Space.height(30),
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  Get.offNamedUntil(Routes.Sign_In, (route) => route.settings.name == Routes.ONBOARDING);
                },
                child: TypoGraphy(
                  text: "Cancel",
                  level: 3,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Space.height(15),
            ],
          ),
        ),
      ),
    );
  }
}
