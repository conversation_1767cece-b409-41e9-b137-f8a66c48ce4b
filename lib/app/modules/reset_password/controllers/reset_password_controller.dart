
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/common_function.dart';

import '../../../../constants/api.dart';
import '../../../../services/api_manager.dart';
import '../../../routes/app_pages.dart';

class ResetPasswordController extends GetxController {
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  RxBool isNewPasswordVisible = true.obs;
  RxBool isConfirmPasswordVisible = true.obs;
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;
  ApiManager apiManager = ApiManager();

  RxList validPasswordFormate =
      ["1 uppercase", "8 characters", "1 number", "1 Special characters"].obs;

  RxList<bool> passwordConditionsMet =
      RxList.generate(4, (index) => false);

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  callApiForResetPassword({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "newPassword": newPasswordController.value.text,
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.resetPassword,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            Get.offNamedUntil(Routes.Sign_In, (route) => route.settings.name == Routes.ONBOARDING);
            CommonFunction.showCustomSnackbar(message: "Password reset successfully.");
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

}
