
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;

import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_post_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/image_slider_show.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../post/components/html_common_rel.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import 'common_widget_view.dart';
import 'custom_icon_picker.dart';
import 'custom_story_loader.dart';

String truncateHtmlWithLineLimit(String htmlContent,
    {int maxLines = 2, required int maxChars}) {
  final document = html_parser.parse(htmlContent);
  final buffer = StringBuffer();
  int textCharCount = 0; // Only count visible text characters
  int lineCount = 1;
  bool reachedLimit = false;
  void processNode(dom.Node node) {
    if (reachedLimit) return;
    if (node is dom.Text) {
      final text = node.text;
      // Skip empty or whitespace-only text that doesn't add meaningful content
      if (text.trim().isEmpty) {
        // Still add single spaces for text flow
        if (text == ' ') {
          buffer.write(' ');
        }
        return;
      }
      final remaining = maxChars - textCharCount;
      if (remaining <= 0) {
        reachedLimit = true;
        return;
      }
      // Truncate text if it exceeds remaining character limit
      final toWrite = (text.length <= remaining) ? text : text.substring(0, remaining);
      buffer.write(toWrite);
      textCharCount += toWrite.length;
      if (textCharCount >= maxChars) {
        // Add "..." immediately after reaching character limit
        final readMoreColor = Theme.of(Get.context!).brightness == Brightness.dark
            ? '#FFFFFF'
            : '#000000';
        buffer.write("<a href='readMore' style='all: unset; font-size: 20px; font-weight: 600; color: $readMoreColor; text-decoration: none; font-family: sans-serif; display: inline;'>...</a>");
        reachedLimit = true;
      }
    } else if (node is dom.Element) {
      // Skip img tags completely - don't display them at all
      if (node.localName == 'img') {
        return;
      }
      // Skip br tags - but add a space instead for proper text flow
      if (node.localName == 'br') {
        buffer.write(' ');
        return;
      }
      // For other HTML tags, preserve the structure but don't count tags in character limit
      // Write opening tag
      buffer.write('<${node.localName}');
      node.attributes.forEach((k, v) => buffer.write(' $k="$v"'));
      buffer.write('>');
      // Process children
      for (var child in node.nodes) {
        processNode(child);
        if (reachedLimit) break;
      }
      // Write closing tag
      buffer.write('</${node.localName}>');
    }
  }
  // Process all nodes in the document body
  if (document.body != null) {
    for (var node in document.body!.nodes) {
      processNode(node);
      if (reachedLimit) break;
    }
  }
  // Return the result without adding "See more" again since it's already added inline
  return buffer.toString();
}

Padding commonFeedView(Post res, int index, BuildContext context,
    {bool isUser = false,
    required VoidCallback onLike,
    required VoidCallback onLikeLongPress,
    required VoidCallback onComment,
    required VoidCallback onBookmark,
    void Function()? onShare,
    required bool isLiked,
    required int likesCount,
    required bool isBookmarked,
    required int commentsCount,
      bool? isProfile,
    void Function()? onTap,
    void Function()? onFeedHeadTap,
    void Function()? onRepostTap,
    Widget? headView,
    bool isExplore = false,
    void Function()? blockCallBack,
    bool isPostEditUser = false}) {
  List<Media> sortedMedia = List.from(res.media);
  sortedMedia.sort((a, b) =>
      (b.isCoverImage == true ? 1 : 0) - (a.isCoverImage == true ? 1 : 0));
  double maxHeight = sortedMedia.isNotEmpty
      ? sortedMedia
          .map((e) => e.height)
          .toList()
          .reduce((a, b) => a > b ? a : b)
      : (res.properties?.height ?? 0);
  if (maxHeight == 0) maxHeight = (res.properties?.height ?? 0);
  return Padding(
    padding: EdgeInsets.only(
      left: MySize.getScaledSizeWidth(20),
      right: MySize.getScaledSizeWidth(20),
      top: MySize.getScaledSizeHeight(15),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        headView ??
            feedHeadView(res, context,
                isUser: isUser,
                isPostEditUser: isPostEditUser,
                onFeedTap: onFeedHeadTap,
                onRepost: onRepostTap,
                isExplore: isExplore,
                blockCallBack: blockCallBack,isProfile: isProfile ?? false),
        InkWell(
          onTap: onTap ??
              () {
                Get.toNamed(Routes.post_detail,
                    arguments: {"postID": res.id, "index": index});
              },
          child: SizedBox(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height:
                      res.media.isEmpty ? 0 : MySize.getScaledSizeHeight(15),
                ),
                res.media.isEmpty
                    ? const SizedBox()
                    : Stack(
                        children: [
                          ImageSlideshow(
                            width: double.infinity,
                            height: MySize.getScaledSizeHeight(maxHeight > 520 ? 520 : maxHeight),
                            initialPage: 0,
                            indicatorColor: sortedMedia.length == 1
                                ? Colors.transparent
                                : AppTheme.white,
                            indicatorBackgroundColor: sortedMedia.length == 1
                                ? Colors.transparent
                                : AppTheme.white.withOpacity(0.50),
                            indicatorRadius: MySize.getScaledSizeWidth(3.5),
                            isLoop: false,
                            autoPlayInterval: 0,
                            children: [
                              for (int i = 0; i < sortedMedia.length; i++) ...[
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(25),
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(right: 0.5),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(26),
                                          color: sortedMedia.length == 1
                                              ? null
                                              : Colors.black,
                                        ),
                                        height: maxHeight == 0
                                            ? 0
                                            : MySize.getScaledSizeHeight(
                                                    maxHeight) -
                                                1,
                                        width: double.infinity,
                                      ),
                                      SizedBox(
                                        width: double.infinity,
                                        child: NetworkImageComponent(
                                          imageUrl: sortedMedia[i].link,
                                          height: MySize.getScaledSizeHeight(
                                              maxHeight),
                                          simmerHeight:
                                              MySize.getScaledSizeHeight(
                                                  maxHeight),
                                          // width: MySize.getScaledSizeWidth(90),
                                          boxFit:
                                              maxHeight > sortedMedia[i].height
                                                  ? BoxFit.fitWidth
                                                  : BoxFit.cover,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                res.title == ""
                    ? const SizedBox()
                    : SizedBox(
                        height: MySize.getScaledSizeHeight(10),
                      ),
                res.title == ""
                    ? const SizedBox()
                    : TypoGraphy(
                        text: res.title,
                        level: 4,
                      ),
                res.description == "" || res.description == "<br> "
                    ? const SizedBox()
                    : SizedBox(
                        height: MySize.getScaledSizeHeight(8),
                      ),
                res.description != "" || res.description != "<br> "
                    ? HtmlWidget(
                        truncateHtmlWithLineLimit(
                            cleanHtmlColorStyles( res.description.toString().replaceAll("<a ",
                                "<a style='font-weight: 600; color: #6D11D2; text-decoration: none;' "),),
                          maxChars: 70,
                        ),
                        textStyle: TextStyle(
                          overflow: TextOverflow.ellipsis,
                        ),
                        onTapUrl: (url) async {
                          if (url.startsWith('/user/')) {
                            final slug = url.replaceFirst('/user/', '');
                            int userId = slug.split('-').last.isNotEmpty
                                ? int.parse(slug.split('-').last)
                                : 0;
                            if (userId == 0) {
                              return false;
                            } else if (userId == CurrentUser.user.id) {
                              Get.toNamed(Routes.profile);
                              return true;
                            } else {
                              Get.toNamed(Routes.other_user_profile,
                                  arguments: {"UserId": userId});
                              return true;
                            }
                          } else if (url.startsWith('/projects/')) {
                            final slug = url.replaceFirst('/projects/', '');
                            Get.toNamed(Routes.project_detail,
                                arguments: {"projectSlug": slug});
                            return true;
                          }
                          if (url == 'readMore') {
                            Get.toNamed(Routes.post_detail,
                                arguments: {"postID": res.id, "index": index});
                          } else {
                            // throw Exception('Could not launch $url');
                            await openLink(url);
                          }
                          return true;
                        },
                        customWidgetBuilder: (element) {
                          if (element.attributes["src"] != null) {
                            return SizedBox();
                          }
                          return null;
                        },
                      )
                    : const SizedBox(),
                res.description == "" || res.description == "<br> "
                    ? const SizedBox()
                    : SizedBox(
                        height: MySize.getScaledSizeHeight(5),
                      ),
                Padding(
                  padding: EdgeInsets.only(
                    top: MySize.getScaledSizeHeight(
                        res.description == "" || res.description == "<br> "
                            ? 10
                            : 18),
                  ),
                  child: reactionView(
                    onLikeLongPress: onLikeLongPress ?? () {},
                    onLike: onLike,
                    onComment: onComment,
                    onBookmark: onBookmark,
                    isLiked: isLiked,
                    likesCount: likesCount,
                    isBookmarked: isBookmarked,
                    commentsCount: commentsCount,
                    onShare: onShare,
                    UserId: res.user?.id ?? 0, postId: res.id,
                  ),
                ),
              ],
            ),
          ),
        ),
        Space.height(19),
        Divider(
          thickness: 1,
          color: Theme.of(context).brightness == Brightness.dark
              ? Color(0xFF262b36)
              : AppTheme.grey[50],
          height: 1,
        ),
      ],
    ),
  );
}

String cleanHtmlColorStyles(String html) {
  final darkColors = [
    'black',
    '#000000',
    '#000',
    '#000000ff',
    'rgb(0, 0, 0)',
    'rgba(0, 0, 0, 1)',
    'rgba(0, 0, 0, 1.0)',
    '#111111',
    '#1a1a1a',
    '#222222',
    '#2a2a2a',
    '#333333',
    '#444444',
  ];

  final lightColors = [
    'white',
    '#ffffff',
    '#fff',
    '#ffffffff',
    'rgb(255, 255, 255)',
    'rgba(255, 255, 255, 1)',
    'rgba(255, 255, 255, 1.0)'
  ];

  final colorPattern = RegExp(r'color\s*:\s*([^;]+)', caseSensitive: false);
  final styleRegExp = RegExp(r'style\s*=\s*"([^"]*)"', caseSensitive: false);

  return html.replaceAllMapped(styleRegExp, (match) {
    final styleContent = match.group(1)!;
    final styles = styleContent.split(';').map((s) => s.trim()).toList();

    final filtered = styles.where((style) {
      final lower = style.toLowerCase();

      // Always remove background-color
      if (lower.startsWith('background-color')) return false;

      // Conditionally remove color
      if (lower.startsWith('color')) {
        final match = colorPattern.firstMatch(lower);
        if (match != null) {
          final value = match.group(1)!.trim().toLowerCase();

          if (darkColors.contains(value) || lightColors.contains(value)) return false;

          // Handle rgba or rgb
          if (value.startsWith('rgb')) {
            final rgbMatch = RegExp(r'rgba?\((\d+),\s*(\d+),\s*(\d+)', caseSensitive: false).firstMatch(value);
            if (rgbMatch != null) {
              final r = int.parse(rgbMatch.group(1)!);
              final g = int.parse(rgbMatch.group(2)!);
              final b = int.parse(rgbMatch.group(3)!);
              final brightness = (r + g + b) / 3;

              // Remove only if it's a dark grayscale tone
              if ((r - g).abs() < 15 && (r - b).abs() < 15 && (g - b).abs() < 15 && brightness < 60) {
                return false;
              }
            }
          }

          // Handle hex values
          if (value.startsWith('#')) {
            final hex = value.replaceAll('#', '').toLowerCase();
            int? r, g, b;

            if (hex.length == 3) {
              r = int.parse(hex[0] * 2, radix: 16);
              g = int.parse(hex[1] * 2, radix: 16);
              b = int.parse(hex[2] * 2, radix: 16);
            } else if (hex.length >= 6) {
              r = int.parse(hex.substring(0, 2), radix: 16);
              g = int.parse(hex.substring(2, 4), radix: 16);
              b = int.parse(hex.substring(4, 6), radix: 16);
            }

            if (r != null && g != null && b != null) {
              final brightness = (r + g + b) / 3;

              // Remove only if it's a dark grayscale tone
              if ((r - g).abs() < 15 && (r - b).abs() < 15 && (g - b).abs() < 15 && brightness < 60) {
                return false;
              }
            }
          }
        }
      }

      return true;
    }).toList();

    if (filtered.isEmpty) return '';
    return 'style="${filtered.join('; ')}"';
  });
}

String cleanHtmlStyles(String html) {
  final color = [
    'black',
    '#000000',
    '#000',
    '#000000ff',
    'rgb(0, 0, 0)',
    'rgba(0, 0, 0, 1)',
    'rgba(0, 0, 0, 1.0)',
    'white',
    '#ffffff',
    '#fff',
    '#ffffffff',
    'rgb(255, 255, 255)',
    'rgba(255, 255, 255, 1)',
    'rgba(255, 255, 255, 1.0)'
  ];
  final styleRegExp = RegExp(r'style\s*=\s*"([^"]*)"', caseSensitive: false);

  return html.replaceAllMapped(styleRegExp, (match) {
    final styleContent = match.group(1)!;
    final styles = styleContent.split(';').map((s) => s.trim()).toList();

    final filtered = styles.where((style) {
      final lower = style.toLowerCase();

      // Remove background-color always
      if (lower.startsWith('background-color')) return false;

      // Remove color only if black or white
      if (lower.startsWith('color')) {
        final value = lower.split(':').last.trim();
        return !color.contains(value);
      }

      return true;
    }).toList();

    if (filtered.isEmpty) return '';
    return 'style="${filtered.join('; ')}"';
  });
}

Widget storyNotAddedView(
    {bool isUploading = false, required BuildContext context}) {
  return Padding(
    padding: EdgeInsets.only(
      right: MySize.getScaledSizeWidth(12),
    ),
    child: GestureDetector(
      onTap: isUploading
          ? () {}
          : () {
              HapticFeedback.lightImpact();
              ImagePickerBottomSheet.show(
                context: context,
                child: bottomSheet(context: context),
              );
            },
      child: Column(
        children: [
          Container(
            height: MySize.getScaledSizeHeight(70),
            width: MySize.getScaledSizeWidth(70),
            margin: EdgeInsets.symmetric(
              vertical: MySize.getScaledSizeHeight(11),
              horizontal: MySize.getScaledSizeWidth(8),
            ).copyWith(bottom: MySize.getScaledSizeHeight(10)),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: isUploading
                ? Stack(
                    alignment: Alignment.center,
                    children: [
                      CustomStoryLoader(
                        size: MySize.getScaledSizeWidth(70),
                        color: AppTheme.primary1,
                        secondCircleColor: AppTheme.primary1,
                        thirdCircleColor: AppTheme.primary1,
                      ),
                      Padding(
                        padding: EdgeInsets.all(4),
                        child: profileImage(
                          textStyle: TextStyle(
                              fontSize: MySize.size25,
                              color: AppTheme.white,
                              fontWeight: FontWeight.w600),
                          url: CurrentUser.user.image ?? "",
                          borderColor: Colors.transparent,
                          color: AppTheme.darkGrey[100],
                          userName: CurrentUser.user.firstName ?? "",
                          height: MySize.getScaledSizeHeight(70),
                          width: MySize.getScaledSizeWidth(70),
                          iconWidth: MySize.getScaledSizeHeight(70),
                          iconHeight: MySize.getScaledSizeWidth(70),
                        ),
                      ),
                    ],
                  )
                : Stack(
                    clipBehavior: Clip.none,
                    alignment: Alignment.center,
                    children: [
                      profileImage(
                        textStyle: TextStyle(
                            fontSize: MySize.size25,
                            color: AppTheme.white,
                            fontWeight: FontWeight.w600),
                        url: CurrentUser.user.image ?? "",
                        borderColor: Colors.transparent,
                        color: AppTheme.darkGrey[100],
                        userName: CurrentUser.user.firstName ?? "",
                        height: MySize.getScaledSizeHeight(70),
                        width: MySize.getScaledSizeWidth(70),
                        iconWidth: MySize.getScaledSizeHeight(70),
                        iconHeight: MySize.getScaledSizeWidth(70),
                      ),
                      Positioned(
                        right: -5,
                        bottom: -2,
                        child: Container(
                          height: MySize.getScaledSizeHeight(28),
                          width: MySize.getScaledSizeHeight(28),
                          decoration: BoxDecoration(
                              color: AppTheme.subPrimary,
                              shape: BoxShape.circle,
                              border: Border.all(
                                  color: AppTheme.white, width: 1.5)),
                          child: Center(
                              child: Icon(
                            Icons.add,
                            size: MySize.size20 ?? 18,
                            color: AppTheme.primary1,
                          )),
                        ),
                      )
                    ],
                  ),
          ),
          TypoGraphy(
            text: 'Your Story',
            level: 3,
          )
        ],
      ),
    ),
  );
}

Widget userStoryView(
    {void Function()? onTap,
    required String thumbnail,
    String? overlayImage,
    bool isStorySeen = false,
    required String profileImageUrl,
    required String userName,
    required RxBool isUploading,
    required String mentioned}) {
  return Padding(
    padding: EdgeInsets.only(
      right: MySize.getScaledSizeWidth(12),
    ),
    child: GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Space.height(10),
          Stack(
            children: [
              Obx(
                () => isUploading.value
                    ? Stack(
                        alignment: Alignment.center,
                        children: [
                          CustomStoryLoader(
                            size: MySize.getScaledSizeWidth(70),
                            color: AppTheme.primary1,
                            secondCircleColor: AppTheme.primary1,
                            thirdCircleColor: AppTheme.primary1,
                          ),
                          Padding(
                            padding: EdgeInsets.all(4),
                            child: profileImage(
                              textStyle: TextStyle(
                                  fontSize: MySize.size25,
                                  color: AppTheme.white,
                                  fontWeight: FontWeight.w600),
                              url: CurrentUser.user.image ?? "",
                              width: MySize.getScaledSizeWidth(60),
                              height: MySize.getScaledSizeWidth(60),
                              iconHeight: MySize.getScaledSizeWidth(60),
                              iconWidth: MySize.getScaledSizeWidth(60),
                              borderColor: Colors.transparent,
                              color: AppTheme.darkGrey[100],
                              userName: userName,
                            ),
                          ),
                        ],
                      )
                    : Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isStorySeen
                                ? AppTheme.primary1
                                : AppTheme.disable,
                            width: MySize.getScaledSizeWidth(
                              3.5,
                            ),
                          ),
                        ),
                        child: Container(
                          width: MySize.getScaledSizeWidth(60),
                          height: MySize.getScaledSizeWidth(60),
                          margin: EdgeInsets.symmetric(
                            vertical: MySize.getScaledSizeHeight(3),
                            horizontal: MySize.getScaledSizeWidth(3),
                          ),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                          child: profileImage(
                              textStyle: TextStyle(
                                  fontSize: MySize.size25,
                                  color: AppTheme.white,
                                  fontWeight: FontWeight.w600),
                              url: profileImageUrl,
                              width: MySize.getScaledSizeWidth(60),
                              height: MySize.getScaledSizeWidth(60),
                              iconHeight: MySize.getScaledSizeWidth(60),
                              iconWidth: MySize.getScaledSizeWidth(60),
                              borderColor: Colors.transparent,
                              color: AppTheme.darkGrey[100],
                              userName: userName),
                        ),
                      ),
              ),
            ],
          ),
          Space.height(8),
          TypoGraphy(
            text: userName,
            level: 3,
          )
        ],
      ),
    ),
  );
}
