import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_argument_key.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../projects/controllers/projects_controller.dart';
import '../../sign_in/components/icon_with_label_view.dart';
import '../../todo_view/controllers/todo_view_controller.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import 'common_widget_view.dart';

class CustomIconPicker extends StatelessWidget {
  final List iconData;
  final String? title;
  final String? subtitle1;
  final String? subtitle2;
  final bool isProfile;

  const CustomIconPicker({
    super.key,
    required this.iconData,
    this.title,
    this.subtitle1,
    this.subtitle2, this.isProfile = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Space.height(19),
        TypoGraphy(
          text: title,
          level: 8,
          fontWeight: FontWeight.w300,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TypoGraphy(
              text: subtitle1,
              level: 8,
              fontWeight: FontWeight.w300,
              // color: AppTheme.grey,
            ),
            TypoGraphy(
              text: subtitle2,
              level: 8,
              fontWeight: FontWeight.w700,
            ),
          ],
        ),
        Space.height(20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: MySize.getScaledSizeWidth(20),
          children: [
            IconWithLabel(
              iconPath: iconData[0]["iconPath"]!,
              label: iconData[0]["label"]!,
              level: 1,
              fontWeight: FontWeight.w500,
              space: MySize.size10,
              onTap: () {
                HapticFeedback.lightImpact();
                Navigator.pop(context);
                Get.toNamed(Routes.post, arguments: {if(isProfile) "isProfile": isProfile});
              },
            ),
            // Space.width(100),
            IconWithLabel(
              iconPath: iconData[1]["iconPath"]!,
              label: iconData[1]["label"]!,
              level: 1,
              fontWeight: FontWeight.w500,
              space: MySize.size10,
              onTap: () {
                Get.back();
                HapticFeedback.lightImpact();
                ImagePickerBottomSheet.show(
                  context: context,
                  child: bottomSheet(context: context),
                );
              },
            ),
            // Space.width(100),
            IconWithLabel(
              iconPath: iconData[2]["iconPath"]!,
              label: iconData[2]["label"]!,
              level: 1,
              fontWeight: FontWeight.w500,
              space: MySize.size10,
              onTap: () {
                Get.back();
                HapticFeedback.lightImpact();
                Get.put(TodoViewController());
                Get.toNamed(Routes.create_todo, arguments: {"isBottom": true})
                    ?.then(
                  (value) {
                    Get.find<TodoViewController>().page.value = 1;
                    Get.find<TodoViewController>().hasMoreData.value = true;
                    Get.find<TodoViewController>().todoList.clear();
                    Get.find<TodoViewController>()
                        .callApiForToDo(context: Get.context!);
                  },
                );
              },
            ),
            // Space.width(100),
            IconWithLabel(
              iconPath: iconData[3]["iconPath"]!,
              label: iconData[3]["label"]!,
              level: 1,
              fontWeight: FontWeight.w500,
              space: MySize.size10,
              onTap: () {
                Navigator.pop(context);
                HapticFeedback.lightImpact();
                Get.put(ProjectsController());
                Get.toNamed(Routes.create_project,
                    arguments: {"isBottom": true});
              },
            )
          ],
        ),
        /*Space.height(35),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconWithLabel(
              iconPath: iconData[2]["iconPath"]!,
              label: iconData[2]["label"]!,
              level: 1,
              fontWeight: FontWeight.w500,
              space: MySize.size10,
              onTap: () {
                HapticFeedback.lightImpact();
              },
            ),
            Space.width(100),
            IconWithLabel(
              iconPath: iconData[3]["iconPath"]!,
              label: iconData[3]["label"]!,
              level: 1,
              fontWeight: FontWeight.w500,
              space: MySize.size10,
              onTap: () {
                HapticFeedback.lightImpact();
                Get.toNamed(Routes.create_project,arguments: {"isBottom": true});
              },
            )
          ],
        ),*/
        Space.height(40),
      ],
    );
  }
}

Widget bottomSheet({required BuildContext context}) {
  return Padding(
    padding: EdgeInsets.all(MySize.size30 ?? 30),
    child: Column(
      children: [
        TypoGraphy(
          text: 'Create Story',
          level: 12,
        ),
        Space.height(30),
        Row(
          spacing: MySize.size15 ?? 15,
          children: [
            storyContainer(
                imageUrl: AppImage.storyCameraIcon,
                name: 'Camera',
                onTap: () async {
                  CommonFunction.requestPermissions(
                    context: Get.context!,
                    onPermissionsGranted: () async {
                      File? pickedImage =
                      await CommonFunction.pickImageFromCamera(
                          isAspectRatio: false,
                          isNeedAspectRatio: false,
                          hideBottomControl: false,
                          isNeedCropping: false);
                      if (pickedImage != null) {
                        Get.toNamed(Routes.story_editor,
                            arguments: {AppArguments.file: pickedImage});
                      } else {
                        Get.back();
                      }
                    },
                    title: "Please Allow Camera",
                    message: "Please allow Camera to select a profile image.",
                    permissions: [Permission.camera],
                  );
                  // await CommonFunction.pickImageFromCamera(
                  //         isAspectRatio: false,
                  //         isNeedAspectRatio: false,
                  //         hideBottomControl: false,
                  //         isNeedCropping: false)
                  //     .then((value) {
                  //   if (value != null) {
                  //     Get.toNamed(Routes.story_editor,
                  //         arguments: {AppArguments.file: value});
                  //   }
                  // });
                }),
            storyContainer(
                imageUrl: AppImage.galleryIcon,
                name: 'Gallery',
                onTap: () async {
                  await CommonFunction.pickImageFromGallery(
                          isAspectRatio: false,
                          isNeedAspectRatio: false,
                          hideBottomControl: false,
                          isNeedCropping: false)
                      .then((value) {
                    if (value != null) {
                      Get.toNamed(Routes.story_editor,
                          arguments: {AppArguments.file: value});
                    }
                  });
                }),
            storyContainer(
                imageUrl: AppImage.videoIcon,
                name: 'Video',
                onTap: () async {
                  await CommonFunction.pickVideo().then((value) {
                    if (value != null) {
                      Get.toNamed(Routes.video_trim,
                          arguments: {AppArguments.file: value});
                    }
                  });
                }),
            storyContainer(
                imageUrl: AppImage.textIcon,
                name: 'Text',
                onTap: () {
                  Get.toNamed(Routes.story_editor, arguments: {'isText': true});
                }),
          ],
        ),
        Space.height(40),
        /* Row(
          children: [
            TypoGraphy(
              text: 'Archive Stories',
              level: 4,
              fontWeight: FontWeight.w400,
            ),
            Space.width(15),
            CupertinoSwitch(value: false, onChanged: (value){}),
          ],
        )*/
      ],
    ),
  );
}
