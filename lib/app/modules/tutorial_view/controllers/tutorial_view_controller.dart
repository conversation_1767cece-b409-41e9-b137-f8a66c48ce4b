import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/common_function.dart';

import '../../../../utillites/common_function.dart';
import '../../setting/component/custom_navigation_view.dart';
import '../../setting/controllers/setting_controller.dart';
import '../views/tutorial_view.dart';

class TutorialViewController extends GetxController {
  final PageController pageController = PageController();
  RxInt currentIndex = 0.obs;
  var args = Get.arguments;
  final RxInt totalPages = 5.obs;
  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
    CommonFunction.getFcmToken();
  }

  void nextPage() {
    if (currentIndex.value < totalPages.value - 1) {
      pageController.nextPage(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Get.offAllNamed(Routes.ONBOARDING);
      if(args != null && args['isSetting'] == true) {
        Get.back();
      } else {
        Get.put(SettingController());
        Navigator.pushReplacement(Get.context!, MaterialPageRoute(
          builder: (context) => CustomNavigationView(isFromSignUp: true,),));
      }
    }
  }


  final List<OnboardingPageData> pages = [
    OnboardingPageData(
      title: 'Follow Projects in\nReal-Time',
      description:
      'Get inspired by following real-time\njourneys of creators working on projects\nthat matter to you. Stay updated with their\nprogress, ideas, and behind-the-scenes\n moments all in one place.',
      image: AppImage.tutorial1,
    ),
    OnboardingPageData(
      title: 'Start Building\nCommunities',
      description:
      "Network and connect with like-minded\nbuilders in dedicated spaces. Ask\nquestions, share insights, and connect with\nothers on similar journeys.",
      image: AppImage.tutorial3,
    ),
    OnboardingPageData(
      title: 'Integrated Task\nManagement',
      description:
      'Seamlessly capture ideas and mental\nnotes with built-in to-do lists designed\nto keep your projects moving forward.\nTurn observations into action without\nswitching apps.',
      image: AppImage.tutorial4,
    ),
    OnboardingPageData(
      title: 'AI Genie\nCompanion',
      description:
      'Your personal project co-pilot that helps\nideate, refine, and build. Bounce ideas off\nGenie to unlock creative solutions and\naccelerate your projectslike having a\nstrategic sparring partner available 24/7.',
      image: AppImage.tutorial5,
    ),
    OnboardingPageData(
      title: 'Project Management\nEcosystem',
      description:
      ' Start documenting and showcasing your\npersonal projects, from concept to completion,\nwith engaging content. Choose to share publicly\nor keep it private—your journey, your rules.',
      image: AppImage.tutorial2,
    ),
  ];

  void skipToEnd() {
    pageController.jumpToPage(totalPages.value - 1);
  }

  void onPageChanged(int index) {
      currentIndex.value = index;
  }

}
