import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/buttons.dart';
import 'package:incenti_ai/utillites/typography.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../setting/component/custom_navigation_view.dart';
import '../../setting/controllers/setting_controller.dart';
import '../controllers/tutorial_view_controller.dart';

class TutorialView extends GetWidget<TutorialViewController> {
  const TutorialView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Obx(
                () => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Buttons(
                  buttonText: !(controller.currentIndex.value ==
                      controller.pages.length - 1)
                      ? "Next"
                      : (controller.args != null && controller.args['isSetting'] == true) ? "Done" : "Get Started",
                  buttonTextLevel: 4,
                  width: MySize.getScaledSizeWidth(199),
                  height: MySize.getScaledSizeHeight(70),
                  onTap: controller.nextPage,
                ),
              ],
            ),
          ),
          Space.height(30),
          Obx(
            () => !(controller.currentIndex.value ==
                controller.pages.length - 1) ?
            InkWell(
              onTap: () {
                if (controller.currentIndex.value < controller.totalPages.value - 1) {
                  controller.skipToEnd();
                } else {
                  // Get.offAllNamed(Routes.ONBOARDING);
                  if(controller.args != null && controller.args['isSetting'] == true) {
                    Get.back();
                  } else {
                    Get.put(SettingController());
                    Navigator.pushReplacement(Get.context!, MaterialPageRoute(
                      builder: (context) =>
                          CustomNavigationView(isFromSignUp: true,),));
                  }
                }
              },
              child: TypoGraphy(
                text: "Skip",
                level: 4,
                fontWeight: FontWeight.w600,
              ),
            ) : SizedBox(),
          ),
          Space.height(Platform.isAndroid ? 40 : 10),
        ],
      ),
      body: PageView.builder(
        controller: controller.pageController,
        onPageChanged: controller.onPageChanged,
        itemCount: controller.pages.length,
        // physics: NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final data = controller.pages[index];
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Space.height(60),
              if(index != 0 || (controller.args != null && controller.args!['isSetting'] == true))
                Align(
                  alignment: Alignment.topLeft,
                  child: InkWell(
                    onTap: () {
                      if(index == 0 && (controller.args != null && controller.args!['isSetting'] == true)) {
                        Get.back();
                      } else {
                        controller.pageController.previousPage(
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(30)),
                      child: SvgPicture.asset(
                        AppImage.backArrow,
                        height: MySize.size28,
                        width: MySize.size28,
                        color: AppTheme.whiteWithNull,
                      ),
                    ),
                  ),
                ),
              Image.asset(data.image,width: double.infinity,height: MySize.getScaledSizeHeight(350),fit: BoxFit.contain,),
              Space.height(35),
              Obx(
                () => Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    controller.pages.length,
                    (index) => Container(
                      margin: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(4)),
                      width: MySize.getScaledSizeWidth(8),
                      height: MySize.getScaledSizeHeight(8),
                      decoration: BoxDecoration(
                        color: controller.currentIndex.value == index
                            ? Colors.deepPurple
                            : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ),
              Space.height(20),
              TypoGraphy(
                text: data.title,
                textAlign: TextAlign.center,
                textStyle: TextStyle(
                  fontSize: MySize.getScaledSizeHeight(26),
                  fontWeight: FontWeight.w700,
                ),
              ),
              Space.height(15),
              TypoGraphy(
                text: data.description,
                level: 4,
                fontWeight: FontWeight.w300,
                textAlign: TextAlign.center,
              ),
            ],
          );
        },
      ),
    );
  }
}

class OnboardingPageData {
  final String title;
  final String description;
  final String image;

  OnboardingPageData({
    required this.title,
    required this.description,
    required this.image,
  });
}
