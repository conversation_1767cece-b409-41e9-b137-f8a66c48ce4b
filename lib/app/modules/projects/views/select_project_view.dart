import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/post/controllers/post_controller.dart';
import 'package:incenti_ai/app/modules/projects/controllers/projects_controller.dart';
import 'package:incenti_ai/utillites/app_text_field.dart';
import 'package:incenti_ai/utillites/app_theme.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_grid_view.dart';
import '../../../../utillites/common_shimmer_grid_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../components/project_card.dart';
import '../components/show_sub_project_bottomsheet.dart';

class SelectProjectView extends StatelessWidget {
  const SelectProjectView({super.key});

  @override
  Widget build(
    BuildContext context,
  ) {
    return GetBuilder<ProjectsController>(
      init: ProjectsController(),
      builder: (projectController) {
        Get.put(ProjectsController());
        return Scaffold(
          // backgroundColor: Colors.white,
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: box.read('isDarkMode') ? AppTheme.darkBackground : AppTheme.white,
            elevation: 0,
            leading: Padding(
              padding: EdgeInsets.only(left: MySize.size25 ?? 20),
              child: InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  Get.back();
                },
                child: SvgPicture.asset(
                  AppImage.backArrow,
                  height: MySize.size28,
                  width: MySize.size28,
                  color: AppTheme.whiteWithNull,
                ),
              ),
            ),
            title: TypoGraphy(
              text: "Select Project",
              level: 12,
            ),
           /* actions: [
              Obx(
                () => projectController.createdProject.isNotEmpty
                    ? Center(
                        child: InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () {
                            Get.toNamed(Routes.create_project)?.then(
                              (value) {
                                Get.find<ProjectsController>().page.value = 1;
                                Get.find<ProjectsController>()
                                    .hasMoreData
                                    .value = true;
                                Get.find<ProjectsController>()
                                    .createdProject
                                    .clear();
                                projectController.callApiForProjectData(
                                    context: context, isSelected: true);
                              },
                            );
                          },
                          child: TypoGraphy(
                            text: "+ Create",
                            level: 12,
                            color: AppTheme.primary1,
                          ),
                        ),
                      )
                    : SizedBox.shrink(),
              ),
              Space.width(25),
            ],*/
          ),
          body: SafeArea(
              child: Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size25 ?? 20),
            child: Column(
              children: [
                ValueListenableBuilder(
                  valueListenable: projectController.searchController.value,
                  builder: (context, value, child) {
                    return AppTextField(
                      controller: projectController.searchController.value,
                      padding: EdgeInsets.only(
                        top: MySize.size12 ?? 12,
                        bottom: MySize.size10 ?? 12,
                      ),
                      onChangedValue: (text) {
                        if ((text?.trim() ?? "").isNotEmpty) {
                          Get.find<ProjectsController>().page.value = 1;
                          Get.find<ProjectsController>().hasMoreData.value =
                              true;
                          projectController.callApiForProjectData(
                              context: context, isSelected: true);
                        }
                      },
                      height: MySize.getScaledSizeHeight(50),
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(4),
                        child: SvgPicture.asset(
                          AppImage.searchIcon,
                          height: MySize.size24,
                          width: MySize.size24,
                          color: AppTheme.grey,
                        ),
                      ),
                      suffixIcon: (projectController
                              .searchController.value.text.isNotEmpty)
                          ? GestureDetector(
                              onTap: () {
                                projectController.searchController.value
                                    .clear();
                                Get.find<ProjectsController>().page.value = 1;
                                Get.find<ProjectsController>()
                                    .hasMoreData
                                    .value = true;
                                Get.find<ProjectsController>()
                                    .createdProject
                                    .clear();
                                projectController.callApiForProjectData(
                                    context: context, isSelected: true);
                              },
                              child: Padding(
                                padding:
                                    EdgeInsets.only(right: MySize.size15 ?? 20),
                                child: SvgPicture.asset(
                                  AppImage.textFieldClear,
                                  // height: MySize.size20,
                                  // width: MySize.size20,
                                  color: AppTheme.grey,
                                ),
                              ),
                            )
                          : null,
                      hintText: "Search",
                      hintStyle: TextStyle(
                        color: AppTheme.grey,
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w400,
                      ),
                    );
                  },
                ),
                Space.height(30),
                Obx(
                  () => projectController.isLoadingCreated.value
                      ? Expanded(
                          child: ShimmerGridView(),
                        )
                      : projectController.createdProject.isNotEmpty
                          ? Expanded(
                              child: GridView.builder(
                                keyboardDismissBehavior:
                                    ScrollViewKeyboardDismissBehavior.onDrag,
                                gridDelegate: FixedHeightGridDelegate(
                                  itemHeight: MySize.size220 ?? 200,
                                  crossAxisCount: 2,
                                  crossAxisSpacing: MySize.size20 ?? 20,
                                  mainAxisSpacing: MySize.size10 ?? 30,
                                ),
                                shrinkWrap: true,
                                controller: ScrollController(),
                                itemCount:
                                    projectController.createdProject.length,
                                itemBuilder: (context, index) {
                                  var project =
                                      projectController.createdProject[index];
                                  bool isSelected =
                                      projectController.selectedIndex.value ==
                                          index;
                                  return GestureDetector(
                                    onTap: () {
                                      projectController
                                          .updateSelectedIndex(index);
                                      projectController.isChecked.value = true;
                                      projectController
                                          .selectedSubProjectIndex.value = -1;
                                      projectController.callApiForOneSubProject(
                                          context: context,
                                          projectId: project.id.toString());
                                      showMoveToProjectBottomSheet(
                                        context: context,
                                        project: project,
                                        isChecked: projectController.isChecked,
                                        isSubLoading:
                                            projectController.isSubLoading,
                                        selectedSubProjectIndex:
                                            projectController
                                                .selectedSubProjectIndex,
                                        subProjectList:
                                            projectController.subProjectList,
                                        callBack: () {
                                          projectController
                                              .updateSelectedIndex(index);
                                          projectController.isChecked.value =
                                              true;
                                          projectController
                                              .selectedSubProjectIndex
                                              .value = -1;
                                          projectController
                                              .callApiForOneSubProject(
                                                  context: context,
                                                  projectId:
                                                      project.id.toString());
                                        },
                                        projectCallBack: () {
                                          Get.find<ProjectsController>()
                                              .page
                                              .value = 1;
                                          Get.find<ProjectsController>()
                                              .hasMoreData
                                              .value = true;
                                          Get.find<ProjectsController>()
                                              .createdProject
                                              .clear();
                                          projectController
                                              .callApiForProjectData(
                                                  context: context,
                                                  isSelected: true);
                                        },
                                      );
                                    },
                                    child: ProjectCard(
                                      project: project,
                                      isSelected: isSelected,
                                    ),
                                  );
                                },
                              ),
                            )
                          : projectController
                                      .searchController.value.text.isNotEmpty &&
                                  projectController.createdProject.isEmpty
                              ? Padding(
                                  padding: EdgeInsets.only(
                                      top: MySize.getScaledSizeHeight(250)),
                                  child: Empty(
                                    title: "No Project available!",
                                  ),
                                )
                              : Center(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Space.height(50),
                                      SvgPicture.asset(AppImage.emptyProject),
                                      Space.height(30),
                                      TypoGraphy(
                                        text:
                                            "Let’s get started, create your project today!",
                                        level: 12,
                                        textAlign: TextAlign.center,
                                      ),
                                      Space.height(50),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Buttons(
                                            buttonText: "+ Create ",
                                            buttonTextLevel: 4,
                                            height: MySize.size70 ?? 70,
                                            width: MySize.size198,
                                            onTap: () {
                                              HapticFeedback.lightImpact();
                                              Get.toNamed(Routes.create_project)
                                                  ?.then(
                                                (value) {
                                                  Get.find<ProjectsController>()
                                                      .page
                                                      .value = 1;
                                                  Get.find<ProjectsController>()
                                                      .hasMoreData
                                                      .value = true;
                                                  Get.find<ProjectsController>()
                                                      .createdProject
                                                      .clear();
                                                  projectController
                                                      .callApiForProjectData(
                                                          context: context,
                                                          isSelected: true);
                                                },
                                              );
                                            },
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                ),
              ],
            ),
          )),
          resizeToAvoidBottomInset: false,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: Obx(
            () => projectController.selectedIndex.value != -1 &&
                    projectController.createdProject.isNotEmpty
                ? Container(
                    padding: EdgeInsets.only(
                        bottom: MySize.size0 ?? 40, top: MySize.size10 ?? 10),
                    // decoration: BoxDecoration(
                    //   gradient: LinearGradient(
                    //     colors: [
                    //       box.read('isDarkMode') ? AppTheme.baseBlack.withAlpha(10) : AppTheme.white.withAlpha(10),
                    //       box.read('isDarkMode') ? AppTheme.baseBlack : AppTheme.white,
                    //     ],
                    //     begin: Alignment.topCenter,
                    //     end: Alignment.bottomCenter,
                    //   ),
                    // ),
                    child: Padding(
                      padding: EdgeInsets.only(bottom: MySize.size30 ?? 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Buttons(
                            buttonText: "Save",
                            buttonTextLevel: 4,
                            height: MySize.size65 ?? 70,
                            width: MySize.size198 ?? 70,
                            onTap: () {
                              Get.find<PostController>().project?.value =
                                  projectController.createdProject[
                                      projectController.selectedIndex.value];
                              Get.offNamedUntil(
                                Routes.post_publish,
                                (route) => route.settings.name == Routes.post,
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  )
                : SizedBox(),
          ),
        );
      },
    );
  }
}
