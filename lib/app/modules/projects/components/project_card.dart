
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_project_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';

class ProjectCard extends StatelessWidget {
  final Project project;
  final bool isSelected;
  final bool isSubProject;

  const ProjectCard(
      {super.key,
      required this.project,
      this.isSelected = false,
      this.isSubProject = false});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            Container(
              height: MySize.size142,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected! ? AppTheme.success : Colors.transparent,
                  width: 2,
                ),
              ),
              child:  project.image.isNotEmpty ? ClipRRect(
                borderRadius: BorderRadius.circular(23),
                child: NetworkImageComponent(
                  imageUrl: project.image,
                  simmerHeight: MySize.size142,
                  width: double.infinity,
                ),
              ) : Image.asset(
                AppImage.defaultImage,
                width: double.infinity,
                height: MySize.size142,
              ),
            ),
            if (isSelected)
              Positioned(
                right: MySize.getScaledSizeWidth(12.11),
                top: MySize.getScaledSizeHeight(12),
                child: Container(
                  height: MySize.size29,
                  width: MySize.size29,
                  decoration: const BoxDecoration(
                    color: AppTheme.success,
                    shape: BoxShape.circle,
                  ),
                  padding: const EdgeInsets.all(5),
                  child: SvgPicture.asset(
                    AppImage.checkIcon,
                    color: AppTheme.white,
                    height: MySize.size8,
                    width: MySize.size12,
                  ),
                ),
              ),
          ],
        ),
        Space.height(6),
        // TypoGraphy(
        //   text: '${project.subProjectsCount} Sub-projects',
        //   level: 2,
        //   fontWeight: FontWeight.w400,
        //   color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey : null,
        //
        // ),
        Space.height(4),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (project.isPrivate) ...[
              Image.asset(
                AppImage.lockProject,
                height: MySize.getScaledSizeHeight(32),
                width: MySize.getScaledSizeWidth(32),
              ),
              Space.width(8),
            ],
            Expanded(
              child: TypoGraphy(
                text: project.name,
                textStyle: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Inter',
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
