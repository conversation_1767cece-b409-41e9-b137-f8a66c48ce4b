import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/move_project/controllers/move_project_controller.dart';
import 'package:incenti_ai/utillites/loader.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../models/app_project_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_grid_view.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../create_project/views/create_sub_project.dart';
import '../../post/controllers/post_controller.dart';

Future<void> showMoveToProjectBottomSheet({
  required BuildContext context,
  required Project project,
  required RxBool isChecked,
  required RxBool isSubLoading,
  required List<SubProjectData> subProjectList,
  required RxInt selectedSubProjectIndex,
  bool isMoveProject = false,
  void Function()? callBack,
  void Function()? projectCallBack,
  bool isShowParentProject = true,
  bool isSubProject = false,
  // required ProjectsController projectController,
}) async {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
    ),
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return FractionallySizedBox(
            heightFactor: 0.85,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.subBottom,
                border: BorderDirectional(
                    top: BorderSide(color: AppTheme.borderColor),
                    start: BorderSide(color: AppTheme.borderColor),
                    end: BorderSide(color: AppTheme.borderColor)),
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(40),
                    topLeft: Radius.circular(40)),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    Space.height(8),
                    Container(
                      height: MySize.size5,
                      width: MySize.size34,
                      decoration: BoxDecoration(
                        color: box.read('isDarkMode')
                            ? AppTheme.darkBackground
                            : AppTheme.black.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(100),
                      ),
                    ),
                    Space.height(20),
                    TypoGraphy(
                        text: isMoveProject
                            ? "Move to Project"
                            : "Select Sub Project",
                        level: 12),
                    Space.height(30),
                    Row(
                      children: [
                        project.image.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(25),
                                child: NetworkImageComponent(
                                  imageUrl: project.image,
                                  height: MySize.getScaledSizeHeight(65),
                                  width: MySize.getScaledSizeWidth(76.97),
                                  simmerHeight: MySize.getScaledSizeHeight(65),
                                ),
                              )
                            : Image.asset(
                                AppImage.defaultImage,
                                height: MySize.getScaledSizeHeight(65),
                                width: MySize.getScaledSizeWidth(76.97),
                              ),
                        Space.width(20),
                        Expanded(
                          child: TypoGraphy(text: project.name, level: 12),
                        ),
                        Space.width(30),
                        if (isShowParentProject)
                          Obx(
                            () => InkWell(
                              onTap: () {
                                isChecked.value = !isChecked.value;
                                if (isMoveProject && isChecked.value) {
                                  selectedSubProjectIndex.value = -1;
                                }
                              },
                              child: Container(
                                height: MySize.size25,
                                width: MySize.size25,
                                decoration: BoxDecoration(
                                  border: isChecked.value
                                      ? null
                                      : Border.all(
                                          width: 1.43,
                                          color: AppTheme.baseBlack),
                                  shape: BoxShape.circle,
                                  color: isChecked.value
                                      ? AppTheme.success
                                      : Colors.transparent,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(4),
                                  child: SvgPicture.asset(
                                    AppImage.checkIcon,
                                    color: AppTheme.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (isMoveProject) ...[
                      Space.height(30),
                      TypoGraphy(text: "or move to Sub-project", level: 12),
                    ],
                    Space.height(20),
                    Expanded(
                      child: Obx(() {
                        if (isSubLoading.value) {
                          return Loader();
                        }
                        if (subProjectList.isEmpty) {
                          return Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TypoGraphy(
                                text: "No sub-projects found",
                                level: 4,
                              ),
                              Space.height(20),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Buttons(
                                    buttonText: "+ Create",
                                    buttonTextLevel: 4,
                                    height: MySize.getScaledSizeHeight(50),
                                    width: MySize.getScaledSizeHeight(189),
                                    onTap: () {
                                      showModalBottomSheet(
                                        context: context,
                                        isScrollControlled: true,
                                        backgroundColor: Colors.transparent,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(20)),
                                        ),
                                        builder: (context) =>
                                            subProjectVisibility(context, false,
                                                isSubCreate: true,
                                                projectId:
                                                    project.id.toString()),
                                      ).then(
                                        (value) {
                                          callBack!();
                                        },
                                      );
                                    },
                                  ),
                                ],
                              )
                            ],
                          );
                        }
                        return GridView.builder(
                          keyboardDismissBehavior:
                              ScrollViewKeyboardDismissBehavior.onDrag,
                          gridDelegate: FixedHeightGridDelegate(
                            itemHeight: MySize.getScaledSizeHeight(205) ?? 200,
                            crossAxisCount: 2,
                            crossAxisSpacing: MySize.size20 ?? 20,
                            mainAxisSpacing: MySize.size10 ?? 30,
                          ),
                          shrinkWrap: true,
                          itemCount: subProjectList.length,
                          controller: ScrollController(),
                          itemBuilder: (context, index) {
                            print("project id: ${project.id}");
                            print(
                                "sub project id: ${subProjectList[index].id}");
                            final subProject = subProjectList[index];
                            return Obx(() {
                              bool isSelected =
                                  selectedSubProjectIndex.value == index ||
                                      project.id == subProjectList[index].id;
                              return InkWell(
                                onTap: () {
                                  if (selectedSubProjectIndex.value == index) {
                                    selectedSubProjectIndex.value = -1;
                                  } else {
                                    selectedSubProjectIndex.value = index;
                                  }
                                  if (isMoveProject &&
                                      selectedSubProjectIndex.value != -1) {
                                    isChecked.value = false;
                                  }
                                },
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Stack(
                                      children: [
                                        Container(
                                          height: MySize.size142,
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(25),
                                          ),
                                          child: subProject.image != null
                                              ? ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(23),
                                                  child: NetworkImageComponent(
                                                    imageUrl: subProject.image,
                                                    simmerHeight:
                                                        MySize.size142,
                                                    width: double.infinity,
                                                  ),
                                                )
                                              : Image.asset(
                                                  AppImage.defaultImage,
                                                  width: double.infinity,
                                                  height: MySize.size142,
                                                ),
                                        ),
                                        if ((project.id ==
                                                subProjectList[index].id ||
                                            !isSubProject))
                                          Positioned(
                                            right: MySize.getScaledSizeWidth(
                                                12.11),
                                            top: MySize.getScaledSizeHeight(12),
                                            child: Container(
                                              height: MySize.size25,
                                              width: MySize.size25,
                                              decoration: BoxDecoration(
                                                border: isSelected
                                                    ? null
                                                    : Border.all(
                                                        width: 1.43,
                                                        color: AppTheme.white),
                                                shape: BoxShape.circle,
                                                color: isSelected
                                                    ? AppTheme.success
                                                    : Colors.transparent,
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(4),
                                                child: isSelected
                                                    ? SvgPicture.asset(
                                                        AppImage.checkIcon,
                                                        color: AppTheme.white,
                                                      )
                                                    : SizedBox(),
                                              ),
                                            ),
                                          )
                                      ],
                                    ),
                                    Space.height(6),
                                    if (subProject.postsCount != "0") ...[
                                      TypoGraphy(
                                        text: '${subProject.postsCount} Posts',
                                        level: 2,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ],
                                    Space.height(4),
                                    Row(
                                      children: [
                                        if (subProject.isPrivate) ...[
                                          Image.asset(
                                            AppImage.lockProject,
                                            height:
                                                MySize.getScaledSizeHeight(32),
                                            width:
                                                MySize.getScaledSizeWidth(32),
                                          ),
                                          Space.width(8),
                                        ],
                                        Expanded(
                                          child: TypoGraphy(
                                            text: subProject.name,
                                            textStyle: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w700,
                                              fontFamily: 'Inter',
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 2,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              );
                            });
                          },
                        );
                      }),
                    ),
                    Space.height(20),
                    if (!isSubProject)
                      Obx(() {
                        bool showButton = isChecked.value ||
                            selectedSubProjectIndex.value != -1;

                        return showButton
                            ? Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Buttons(
                                        buttonText: isMoveProject
                                            ? "Move here"
                                            : "Save",
                                        buttonTextLevel: 4,
                                        width: MySize.size198,
                                        onTap: () {
                                          int? selectedProjectId;
                                          if (selectedSubProjectIndex.value !=
                                              -1) {
                                            selectedProjectId = subProjectList[
                                                    selectedSubProjectIndex
                                                        .value]
                                                .id;
                                          } else if (isChecked.value) {
                                            selectedProjectId = project.id;
                                          }
                                          if (isMoveProject &&
                                              selectedProjectId != null) {
                                            Get.find<MoveProjectController>()
                                                .callApiForUpdatePost(
                                                    context: context,
                                                    projectId:
                                                        selectedProjectId);
                                          } else {
                                            if (selectedSubProjectIndex.value !=
                                                -1) {
                                              var selectedSubProject =
                                                  subProjectList[
                                                      selectedSubProjectIndex
                                                          .value];
                                              Get.find<PostController>()
                                                  .subProject
                                                  .value = selectedSubProject;
                                            }
                                            Get.back();
                                          }
                                          // if (selectedSubProjectIndex.value != -1) {
                                          //   var selectedSubProject = subProjectList[
                                          //       selectedSubProjectIndex.value];
                                          //
                                          //   // Store it in PostController
                                          //   Get.find<PostController>()
                                          //       .subProject
                                          //       .value = selectedSubProject;
                                          // }
                                          // Get.back();
                                        },
                                      ),
                                    ],
                                  ),
                                  Space.height(20),
                                ],
                              )
                            : SizedBox();
                      }),
                  ],
                ),
              ),
            ),
          );
        },
      );
    },
  ).then(
    (value) {
      if (projectCallBack != null) {
        projectCallBack();
      }
    },
  );
}
