import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/current_user.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_project_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';

class ProjectsController extends GetxController with WidgetsBindingObserver {
  DateTime? _backgroundTime;
  RxInt selectedIndex = (-1).obs;
  RxInt selectedSubProjectIndex = (-1).obs;
  RxBool isLoading = false.obs;
  RxBool isSubLoading = false.obs;
  RxBool isLoadingCreated = false.obs;
  RxBool isLoadingFollowed = false.obs;
  RxBool isLoadingExplore = false.obs;
  ApiManager apiManager = ApiManager();
  Rx<TextEditingController> searchController = TextEditingController().obs;
  FocusNode searchFocusNode = FocusNode();
  RxList<Project> followedProject = <Project>[].obs;
  RxList<Project> exploreProject = <Project>[].obs;
  RxList<Project> createdProject = <Project>[].obs;
  RxList<SubProjectData> subProjectList = <SubProjectData>[].obs;
  RxInt page = 1.obs;
  RxInt selectedTabIndex = 0.obs;
  RxInt previousTabIndex = 0.obs;
  int limit = 40;
  RxBool hasMoreData = true.obs;
  RxList tabList = ["Created", "Followed", "Explore"].obs;
  var args = Get.arguments;
  RxBool isChecked = false.obs;
  Timer? debounceTimer;
  ScrollController scrollController = ScrollController();
  RxString selectedReason = "".obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;
  void updateSelectedIndex(int index) {
    selectedIndex.value = index;
    update(); // Notify UI to rebuild
  }

  void onScrollListener(void Function() onScroll, ScrollController controller) {
    if (!hasMoreData.value) return;

    // Use a threshold for safer scroll detection
    if (controller.position.pixels >=
        controller.position.maxScrollExtent - 200) {
      onScroll();
    }
  }

  @override
  void onInit() {
    WidgetsBinding.instance.addObserver(this);
    callApiForProjectData(
        context: Get.context!,
        isSelected: (selectedTabIndex.value == 0 || args != null && args['isSelected'] != null) ? true : false);
    scrollController.addListener(
          () => onScrollListener(() {
        page.value++;
        callApiForProjectData(
            context: Get.context!,
            isSelected:
            ( selectedTabIndex.value == 0 || args != null && args['isSelected'] != null) ? true : false);
      }, scrollController),
    );
    super.onInit();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  RxList<Project> get selectedProjectList {
    switch (selectedTabIndex.value) {
      case 0:
        return createdProject;
      case 1:
        return followedProject;
      default:
        return exploreProject;
    }
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      _backgroundTime = DateTime.now();
    } else if (state == AppLifecycleState.resumed) {
      if (_backgroundTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_backgroundTime!);
        if (difference.inMinutes >= 3) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            scrollController.animateTo(0, duration: Duration(milliseconds: 700), curve: Curves.easeInOut).then((value) =>  pullRefresh(),);
          });
        }
      }
    }
  }

  callApiForReportProject(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-projects/project/$projectId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response == > $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            page.value = 1;
            hasMoreData.value = true;
            exploreProject.clear();
            callApiForProjectData(
              context: Get.context!,
              isSelected: selectedTabIndex.value == 0,
              isFollowed: selectedTabIndex.value == 1,
            );
          }
        } catch (error) {
          log("error: $message");
        }
      },
      failureCallback: (message, statusCode) {
        log("error: $message");
      },
    );
  }

  void pullRefresh() {
    page.value = 1;
    hasMoreData.value = true;
    _clearAllProjects();

    callApiForProjectData(
      context: Get.context!,
      isSelected: selectedTabIndex.value == 0,
      isFollowed: selectedTabIndex.value == 1,
    );
  }

  void _clearAllProjects() {
    exploreProject.clear();
    followedProject.clear();
    createdProject.clear();
  }


  void callApiForProjectData({
    required BuildContext context,
    bool isSelected = false,
    bool isFollowed = false,
  }) {
    final isSearching = searchController.value.text.isNotEmpty;

    // Only debounce for the first page of search
    if (isSearching && page.value == 1) {
      debounceTimer?.cancel();
      debounceTimer = Timer(Duration(milliseconds: 500), () {
        _clearAllProjects();
        fetchProjectData(context, isSelected, isFollowed);
      });
    } else {
      fetchProjectData(context, isSelected, isFollowed);
    }
  }

  void fetchProjectData(BuildContext context, bool isSelected, bool isFollowed) {
    int currentTab = selectedTabIndex.value;
    if (!hasMoreData.value) return;

    _setLoadingState(currentTab, true);

    final params = {
      if (isSelected) "UserId": CurrentUser.user.id,
      'page': page.value,
      'limit': limit,
      if (searchController.value.text.isNotEmpty)
        "searchQuery": searchController.value.text,
      if (isFollowed) "followed": 1
    };

    apiManager.callApi(
      APIS.project.getAllMyProject,
      params: params,
      successCallback: (response, message) {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse = ProjectResponse.fromJson(response);

            if (projectResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }

            if (selectedTabIndex.value != currentTab) return;

            _updateProjectList(currentTab, projectResponse.data.data);
          }
        } catch (error) {
          log("Error processing response: $error");
          hasMoreData.value = false;
        } finally {
          _setLoadingState(currentTab, false);
        }
      },
      failureCallback: (message, statusCode) {
        log("Error: $message");
        _setLoadingState(currentTab, false);
        hasMoreData.value = false;
      },
    );
  }

  void _setLoadingState(int tab, bool isLoading) {
    if (tab == 0) {
      isLoadingCreated.value = isLoading;
    } else if (tab == 1) {
      isLoadingFollowed.value = isLoading;
    } else {
      isLoadingExplore.value = isLoading;
    }
  }

  void _updateProjectList(int tab, List<Project> newData) {
    if (newData.isNotEmpty) {
      if (tab == 0) {
        final existingIds = createdProject.map((e) => e.id).toSet();

        final newItems = newData.where((item) => !existingIds.contains(item.id));
        // createdProject.clear();
        createdProject.addAll(newItems);
      } else if (tab == 1) {
        final existingIds = followedProject.map((e) => e.id).toSet();

        final newItems = newData.where((item) => !existingIds.contains(item.id));
        // followedProject.clear();
        followedProject.addAll(newItems);
      } else {
        final existingIds = exploreProject.map((e) => e.id).toSet();

        final newItems = newData.where((item) => !existingIds.contains(item.id));
        // exploreProject.clear();
        exploreProject.addAll(newItems);
      }
      // page.value++;
    }
  }


  Future<void> callApiForOneSubProject(
      {required BuildContext context, String? projectId}) {
    FocusScope.of(context).unfocus();
    isSubLoading.value = true;

    return apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {"UserId": CurrentUser.user.id, "ParentId": projectId},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            subProjectList.value = List<SubProjectData>.from(response["data"]
                    ["data"]
                .map((x) => SubProjectData.fromJson(x)));
            subProjectList.value = subProjectList.where(
                    (element) =>
                (CurrentUser.user.id == element.userId || (element.projectMembers.isNotEmpty && element.projectMembers[0].access != "read"))).toList();

            isSubLoading.value = false;
          }
        } catch (error) {
          isSubLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isSubLoading.value = false;
      },
    );
  }

  Future<void> callApiForDeleteOneProject(
      {required BuildContext context, String? projectId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(
              message: "Delete Project Successfully",
            );
            callApiForProjectData(context: Get.context!, isSelected: true);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForHideProject(
      {required BuildContext context, String? projectId,required bool isHide,required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      params: {"hide": isHide},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            createdProject[index].hide =
            !(createdProject[index].hide ?? false);
            Get.back();
            if(createdProject[index]
                .hide ==
                true) {
              CommonFunction.showCustomSnackbar(message: "Other users won't be able to view this Private Project Title and Cover Image");
            } else {
              CommonFunction.showCustomSnackbar(message: "Other Users are now able to view your Project Title and Cover Image");
            }

            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }
}
