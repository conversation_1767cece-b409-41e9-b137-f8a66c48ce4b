import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/explore/components/common_post_widget.dart';
import 'package:incenti_ai/app/modules/post_detail_view/controllers/post_detail_view_controller.dart';
import 'package:incenti_ai/main.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:intl/intl.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_post_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../communities/controllers/communities_controller.dart';
import '../../explore/components/common_widget_view.dart';
import '../../post/components/html_common_rel.dart';

Row postHeadView(
  Post res,
  BuildContext context,
) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      InkWell(
        onTap: () {
          if (res.user?.id == CurrentUser.user.id) {
            Get.toNamed(Routes.profile);
          } else {
            Get.toNamed(Routes.other_user_profile,
                arguments: {"UserId": res.user?.id});
          }
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(50),
          child: profileImage(
              userName: res.user?.firstName ?? "",
              url: res.user?.image ?? "",
              width: MySize.getScaledSizeWidth(42),
              height: MySize.getScaledSizeHeight(42),
              borderColor: Colors.transparent,
              color: AppTheme.darkGrey[100]),
        ),
      ),
      SizedBox(width: MySize.getScaledSizeWidth(12)),
      Expanded(
        child: InkWell(
          onTap: () {
            if (res.user?.id == CurrentUser.user.id) {
              Get.toNamed(Routes.profile);
            } else {
              Get.toNamed(Routes.other_user_profile,
                  arguments: {"UserId": res.user?.id});
            }
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TypoGraphy(
                text: "${res.user?.firstName} ${res.user?.lastName}",
                level: 4,
              ),
              Space.height(2.5),
              TypoGraphy(
                text: res.createdAt != null
                    ? DateFormat('MMM d, y')
                        .format(DateTime.parse(res.createdAt.toString()))
                    : "",
                level: 2,
                fontWeight: FontWeight.w400,
                color: AppTheme.grey,
              )
            ],
          ),
        ),
      ),
      if (CurrentUser.user.id != res.user?.id) ...[
        if (!Get.put(PostDetailViewController(), tag: res.projectId.toString())
            .isFollowing
            .value) ...[
          Buttons(
            buttonText: "Follow",
            height: MySize.size40 ?? 40,
            width: MySize.getScaledSizeWidth(84),
            buttonTextLevel: 4,
            onTap: () {
              HapticFeedback.lightImpact();
              Get.put(PostDetailViewController(), tag: res.projectId.toString())
                  .callApiForFollowUser(context: context, userId: res.user?.id);
            },
          )
        ],
        if (Get.put(PostDetailViewController(), tag: res.projectId.toString())
            .isFollowing
            .value) ...[
          InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              Get.put(PostDetailViewController(), tag: res.projectId.toString())
                  .callApiForUnFollowUser(
                      context: context, userId: res.user?.id);
              // controller.callApiForUnFollowUser(
              //     context: context);
              // controller.callApiForProjectUnFollow(
              //     context: context,
              //     projectId: controller
              //         .getProjectDetailData
              //         .value
              //         .id
              //         .toString());
            },
            child: Container(
              width: MySize.getScaledSizeWidth(125),
              height: MySize.size39,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  border: Border.all(width: 1, color: AppTheme.grey)),
              alignment: Alignment.center,
              child: TypoGraphy(
                text: "Following",
                textStyle: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: "Inter",
                    color: AppTheme.grey),
              ),
            ),
          )
        ],
      ]
    ],
  );
}

Row postSubProject(Post res) {
  return Row(
    children: [
      if (res.project != null) ...[
        Padding(
          padding: EdgeInsets.only(
              top: MySize.getScaledSizeHeight(22),
              left: MySize.getScaledSizeWidth(5)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () {
                  // if(res.project?.isPrivate == true && res.user?.id != CurrentUser.user.id) {
                  //   return;
                  // } else {
                  //   Get.toNamed(Routes.project_detail, arguments: {
                  //     "projectId": res.project?.parentProjectData != null ? res
                  //         .project?.parentProjectData?.id : res.project?.id
                  //   });
                  // }
                  if ((res.project?.parentProjectData != null &&
                          res.project?.parentProjectData?.isPrivate == true) ||
                      (res.project?.parentProjectData == null &&
                          res.project?.isPrivate == true)) {
                    if (res.user?.id != CurrentUser.user.id) {
                      return;
                    }
                  }

                  Get.toNamed(Routes.project_detail, arguments: {
                    "projectId": res.project?.parentProjectData != null
                        ? res.project?.parentProjectData?.id
                        : res.project?.id,
                    "fromPost": true
                  });
                },
                child: Row(
                  children: [
                    Container(
                      height: MySize.getScaledSizeHeight(26),
                      width: MySize.getScaledSizeWidth(26),
                      margin: EdgeInsets.only(
                        right: MySize.getScaledSizeWidth(5),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(50),
                        child: res.project?.parentProjectData != null
                            ? (res.project?.parentProjectData?.image ?? "")
                                    .isNotEmpty
                                ? NetworkImageComponent(
                                    imageUrl:
                                        res.project?.parentProjectData?.image,
                                    simmerHeight:
                                        MySize.getScaledSizeHeight(76),
                                    width: MySize.getScaledSizeWidth(90),
                                  )
                                : Image.asset(AppImage.defaultImage)
                            : (res.project?.image ?? "").isNotEmpty
                                ? NetworkImageComponent(
                                    imageUrl: res.project?.image,
                                    simmerHeight:
                                        MySize.getScaledSizeHeight(76),
                                    width: MySize.getScaledSizeWidth(90),
                                  )
                                : Image.asset(AppImage.defaultImage),
                      ),
                    ),
                    Space.width(10),
                    TypoGraphy(
                      text: res.project?.parentProjectData?.name ??
                          res.project?.name,
                      level: 3,
                      fontWeight: FontWeight.w600,
                    ),
                    Space.width(10),
                    SvgPicture.asset(
                      AppImage.back,
                      color: AppTheme.white,
                    ),
                  ],
                ),
              ),
              if (res.project?.parentProjectData != null) ...[
                Space.height(5),
                InkWell(
                  onTap: () {
                    if (res.project?.isPrivate == true &&
                        res.user?.id != CurrentUser.user.id) {
                      return;
                    } else {
                      Get.toNamed(Routes.subProject_detail, arguments: {
                        "projectId": res.project?.id,
                        "fromPost": true
                      });
                    }
                  },
                  child: Row(
                    children: [
                      Space.width(40),
                      SvgPicture.asset(
                        AppImage.hierarchySubproject,
                        height: MySize.size18,
                        width: MySize.size18,
                        color: AppTheme.whiteWithNull,
                      ),
                      Space.width(5),
                      TypoGraphy(
                        text: res.project?.name,
                        level: 3,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                ),
              ]
            ],
          ),
        ),
      ],
      if (res.community != null) ...[
        Padding(
          padding: EdgeInsets.only(
              top: MySize.getScaledSizeHeight(22),
              left: MySize.getScaledSizeWidth(5)),
          child: InkWell(
            onTap: () {
              Get.put(CommunitiesController());
              Get.toNamed(Routes.community_detail,
                  arguments: {'communityId': res.community?.id});
            },
            child: Row(
              children: [
                Container(
                  height: MySize.getScaledSizeHeight(26),
                  width: MySize.getScaledSizeWidth(26),
                  margin: EdgeInsets.only(
                    right: MySize.getScaledSizeWidth(5),
                  ),
                  child: ClipRRect(
                      borderRadius: BorderRadius.circular(50),
                      child: (res.community?.image ?? "").isNotEmpty
                          ? NetworkImageComponent(
                              imageUrl: res.community?.image,
                              simmerHeight: MySize.getScaledSizeHeight(76),
                              width: MySize.getScaledSizeWidth(90),
                            )
                          : Image.asset(AppImage.defaultCommunity)),
                ),
                Space.width(10),
                TypoGraphy(
                  text: res.community?.name ?? '',
                  level: 3,
                  fontWeight: FontWeight.w600,
                ),
                Space.width(10),
                SvgPicture.asset(
                  AppImage.back,
                  color: AppTheme.white,
                ),
              ],
            ),
          ),
        ),
      ]
    ],
  );
}

Padding postDetail(Post res, BuildContext context) {
  List<Media> sortedMedia = List.from(res.media);
  sortedMedia.sort((a, b) =>
      (b.isCoverImage == true ? 1 : 0) - (a.isCoverImage == true ? 1 : 0));
  List<Media> filteredMedia = res.media
      .where((m) => m.isCustom == true && m.isCoverImage == true)
      .toList();
  String processedHtml = res.description;
  List<String> youTubeVideoIds = [];

  // Regex to find YouTube iframes
  final iframeRegex = RegExp(
    r'<iframe[^>]*src="https://(?:www\.)?(?:youtube\.com/embed/|youtu\.be/)([a-zA-Z0-9_-]+)(?:\?[^"]*)?[^>]*></iframe>',
    caseSensitive: false,
  );

  final matches = iframeRegex.allMatches(processedHtml);

  for (int i = 0; i < matches.length; i++) {
    final match = matches.elementAt(i);
    final videoId = match.group(1);
    if (videoId != null) {
      youTubeVideoIds.add(videoId);
      // Replace iframe with a placeholder div
      processedHtml = processedHtml.replaceFirst(
        match.group(0)!,
        '<div class="youtube-placeholder" data-video-id="$videoId" data-index="$i">YouTube Video Player</div>',
      );
    }
  }
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: MySize.size10 ?? 10),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        res.title == ""
            ? const SizedBox()
            : SizedBox(
                height: MySize.getScaledSizeHeight(20),
              ),
        res.title == ""
            ? const SizedBox()
            : TypoGraphy(
                text: res.title,
                // level: 5,
                textStyle: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: MySize.getScaledSizeHeight(26),
                  color: AppTheme.whiteWithBase,
                  fontFamily: "Inter",
                ),
                // fontWeight: FontWeight.w700,
              ),
        filteredMedia.isEmpty ? const SizedBox() : Space.height(10),
        filteredMedia.isEmpty
            ? const SizedBox()
            : SizedBox(
                width: double.infinity,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(25),
                  child: NetworkImageComponent(
                    imageUrl: filteredMedia[0].link,
                    height: filteredMedia[0].height != 0
                        ? MySize.getScaledSizeHeight(filteredMedia[0].height)
                        : MySize.getScaledSizeHeight(520),
                    simmerHeight: filteredMedia[0].height != 0
                        ? MySize.getScaledSizeHeight(filteredMedia[0].height)
                        : MySize.getScaledSizeHeight(520),
                    boxFit: BoxFit.cover,
                  ),
                ),
              ),
        res.description == "" || res.description == "<br> "
            ? const SizedBox()
            : SizedBox(
                height: MySize.getScaledSizeHeight(12),
              ),
        res.description != "" || res.description != "<br> "
            ? HtmlWidget(
          cleanHtmlColorStyles(processedHtml
                    .toString()
                    .replaceAll("#FF", "#")
                    .replaceAllMapped(
                  RegExp(r'<a\s+([^>]*href="([^"]+)")[^>]*>'),
                  (match) {
                    final attributes = match.group(1)!;
                    final href = match.group(2)!;

                    String style = href
                            .startsWith('unsafe:spell-error://spell-error')
                        ? box.read('isDarkMode')
                            ? "color: #FFFFFF; text-decoration: none;"
                            : "color: #2D394A; text-decoration: none;"
                        : "font-weight: 600; color: #6D11D2; text-decoration: none;";

                    final styledAttributes = attributes.contains('style=')
                        ? attributes.replaceFirst(
                            RegExp(r'style="[^"]*"'), 'style="$style"')
                        : '$attributes style="$style"';

                    return '<a $styledAttributes>';
                  },
                ).replaceAllMapped(
                  RegExp(r"(<img[^>]*>)<br>"),
                  (match) => "${match[1]}\n",
                )),
                textStyle: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  fontSize: MySize.getScaledSizeHeight(19),
                  color: AppTheme.whiteWithBase,
                  fontFamily: "Inter",
                ),
                onTapUrl: (url) async {
                  if (url.startsWith('/user/')) {
                    final slug = url.replaceFirst('/user/', '');
                    int userId = slug.split('-').last.isNotEmpty
                        ? int.parse(slug.split('-').last)
                        : 0;
                    if (userId == 0) {
                      return false;
                    } else if (userId == CurrentUser.user.id) {
                      Get.toNamed(Routes.profile);
                      return true;
                    } else {
                      Get.toNamed(Routes.other_user_profile,
                          arguments: {"UserId": userId});
                      return true;
                    }
                  } else if (url.startsWith('/projects/')) {
                    final slug = url.replaceFirst('/projects/', '');
                    Get.toNamed(Routes.project_detail,
                        arguments: {"projectSlug": slug});
                    return true;
                  } else {
                    // await launchUrl(Uri.parse(url));
                    await openLink(url);
                    return true;
                  }
                },
                onLoadingBuilder: (context, element, loadingProgress) {
                  return Loader();
                },
                customWidgetBuilder: (element) {
                  if (element.localName == 'img') {
                    final imageUrl = element.attributes['src'] ?? '';
                    return GestureDetector(
                      child: Container(
                        margin: const EdgeInsets.only(top: 15),
                        child: SizedBox(
                          width: double.infinity,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(25),
                            child: NetworkImageComponent(imageUrl: imageUrl),
                          ),
                        ),
                      ),
                    );
                  }
                  if (element.localName == 'div' &&
                      element.classes.contains('youtube-placeholder')) {
                    final videoId = element.attributes['data-video-id'];
                    final index =
                        int.tryParse(element.attributes['data-index'] ?? '0') ??
                            0;

                    if (videoId != null && index < youTubeVideoIds.length) {
                      return Container(
                        margin: const EdgeInsets.symmetric(vertical: 10),
                        child: YouTubePlayerWidget(videoId: videoId),
                      );
                    }
                  }
                  return null;
                },
              )
            : const SizedBox(),
      ],
    ),
  );
}

class YouTubePlayerWidget extends StatefulWidget {
  final String videoId;

  const YouTubePlayerWidget({super.key, required this.videoId});

  @override
  State<YouTubePlayerWidget> createState() => _YouTubePlayerWidgetState();
}

class _YouTubePlayerWidgetState extends State<YouTubePlayerWidget> {
  late YoutubePlayerController _controller;
  bool _isPlayerReady = false;

  @override
  void initState() {
    super.initState();
    _controller = YoutubePlayerController(
      initialVideoId: widget.videoId,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
        enableCaption: true,
        loop: false,
        forceHD: false,
      ),
    )..addListener(() {
        if (_isPlayerReady && mounted) {
          // You can listen for updates here
        }
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerBuilder(
      onExitFullScreen: () {
        // Restore orientation if needed
        SystemChrome.setPreferredOrientations(DeviceOrientation.values);
      },
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Colors.blueAccent,
        onReady: () {
          _isPlayerReady = true;
        },
        bottomActions: const [
          CurrentPosition(),
          ProgressBar(isExpanded: true),
          RemainingDuration(),
        ],
      ),
      builder: (context, player) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: player,
        );
      },
    );
  }
}

Column imageViewer(Post res, BuildContext context, String image) {
  return Column(
    children: [
      Space.height(60),
      Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
        child: feedHeadView(res, context, isImagePreview: true),
      ),
      Space.height(23),
      Expanded(
        child: NetworkImageComponent(imageUrl: image),
      ),
      Space.height(36),
    ],
  );
}
