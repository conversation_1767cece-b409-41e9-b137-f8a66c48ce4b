import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utillites/app_theme.dart';
import '../../../../utillites/typography.dart';

class TermsAndPrivacy extends StatelessWidget {
  final String text1;
  final String text2;
  final Color textColor;
  final double? underlineWidth;
  final double? underlineHeight;

  const TermsAndPrivacy({
    super.key,
    required this.text1,
    required this.text2,
    this.textColor = Colors.grey,
    this.underlineWidth,
    this.underlineHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () async {
            Uri url = Uri.parse("https://www.incenti.ai/terms");
            if (await launchUrl(url)) {
            throw Exception('Could not launch $url');
            }
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              TypoGraphy(
                text: text1,
                level: 2,
                fontWeight: FontWeight.w400,
                color: AppTheme.grey,
                textAlign: TextAlign.center,
              ),
              Container(
                width: underlineWidth,
                height: underlineHeight,
                color: textColor,
              )
            ],
          ),
        ),
        TypoGraphy(
          text: " and our ",
          level: 2,
          fontWeight: FontWeight.w400,
          color: AppTheme.grey,
          textAlign: TextAlign.center,
        ),
        InkWell(
          onTap: () async {
            Uri url = Uri.parse("https://www.incenti.ai/privacy-policy");
            if (await launchUrl(url)) {
            throw Exception('Could not launch $url');
            }
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              TypoGraphy(
                text: text2,
                level: 2,
                fontWeight: FontWeight.w400,
                color: AppTheme.grey,
                textAlign: TextAlign.center,
              ),
              Container(
                width: underlineWidth,
                height: underlineHeight,
                color: textColor,
              )
            ],
          ),
        ),
      ],
    );
  }
}
