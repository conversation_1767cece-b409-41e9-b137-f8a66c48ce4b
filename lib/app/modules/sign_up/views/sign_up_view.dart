import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/app_text_field.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/common_validation.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../sign_in/components/divider_with_text_view.dart';
import '../../sign_in/components/icon_with_label_view.dart';
import '../components/terms_privacy.dart';
import '../controllers/sign_up_controller.dart';

class SignUpView extends GetWidget<SignUpController> {
  const SignUpView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return WillPopScope(
      onWillPop: () {
        return Future.value(true);
      },
      child: Scaffold(
        // backgroundColor: AppTheme.white,
        body: NotificationListener<ScrollNotification>(
          onNotification: (scrollNotification) {
            if (scrollNotification is ScrollStartNotification) {
              FocusScope.of(context).unfocus();
            }
            return false;
          },
          child: SingleChildScrollView(
            child: Form(
              key: controller.signUpKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Space.height(108.07),
                  Image.asset(
                    AppImage.appLogo,
                    color: AppTheme.whiteWithBase,
                    width: MySize.getScaledSizeWidth(120),
                    // height: MySize.size39,
                  ),
                  Space.height(46.34),
                  TypoGraphy(
                    text: "Create an account",
                    level: 8,
                    // color: AppTheme.baseBlack,
                    fontWeight: FontWeight.w700,
                  ),
                  Space.height(26),
                  Obx(
                    () => Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(45) ?? 52),
                      child: AppTextField(
                        controller: controller.emailController,
                        labelText: "Email*",
                        errorMessage: controller.isError.value,
                        onChangedValue: (p0) {
                          if (CommonValidation.isEmail(
                                  value: controller.emailController.text,
                                  validationMessage: "") !=
                              null) {
                            controller.isError.value = true;
                          } else {
                            controller.isError.value = false;
                          }
                        },
                      ),
                    ),
                  ),
                  Space.height(20),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: MySize.size45 ?? 52),
                    child: Obx(
                      () => AppTextField(
                        controller: controller.passwordController,
                        labelText: "Create Password*",
                        errorMessage: controller.isPasswordError.value,
                        suffixIconAsset: AppImage.showPassword,
                        obscureText: controller.isPasswordVisible.value,
                        suffixIcon: Padding(
                          padding: EdgeInsets.only(right: MySize.size20 ?? 20),
                          child: InkWell(
                            highlightColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            onTap: () {
                              controller.isPasswordVisible.value =
                                  !controller.isPasswordVisible.value;
                            },
                            child: controller.isPasswordVisible.value
                                ? SvgPicture.asset(
                                    AppImage.showPassword,
                                    color:  AppTheme.whiteWithNull,
                                  )
                                : Icon(
                                    Icons.visibility_outlined,
                                    color: AppTheme.whiteWithBase,
                                    size: MySize.size20,
                                  ),
                          ),
                        ),
                        onChangedValue: (p0) =>
                            CommonFunction.checkPasswordFormat(
                                passwordValue:
                                    controller.passwordController.text,
                                passwordConditionsMet:
                                    controller.passwordConditionsMet),
                      ),
                    ),
                  ),
                  Space.height(8),
                  Obx(
                    () => Padding(
                      padding: EdgeInsets.only(left: MySize.size45 ?? 52),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Wrap(
                          spacing: MySize.size4 ?? 4,
                          runSpacing: MySize.size4 ?? 4,
                          children: List.generate(
                            controller.validPasswordFormate.length,
                            (index) {
                              return Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size8 ?? 8,
                                    vertical: MySize.size4 ?? 4),
                                decoration: BoxDecoration(
                                    color: controller
                                            .passwordConditionsMet[index]
                                        ? AppTheme.success
                                            .withValues(alpha: 0.1)
                                        : AppTheme.grey.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(5)),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    if (controller
                                        .passwordConditionsMet[index]) ...[
                                      SvgPicture.asset(AppImage.checkIcon),
                                      Space.width(4),
                                    ],
                                    TypoGraphy(
                                      text: controller
                                          .validPasswordFormate[index],
                                      level: 2,
                                      color: controller
                                              .passwordConditionsMet[index]
                                          ? AppTheme.success
                                          : AppTheme.grey,
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                  Space.height(28),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 52),
                    child: Obx(
                      () => Buttons(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          if (controller.signUpKey.currentState?.validate() ==
                              false) {
                            return;
                          }
                          if (CommonValidation.isEmail(
                                      value: controller.emailController.text,
                                      validationMessage: "") ==
                                  null &&
                              controller.emailController.text.isNotEmpty &&
                              controller.passwordController.text.isNotEmpty &&
                              controller.passwordConditionsMet
                                  .every((condition) => condition == true)) {
                            controller.callApiForSignUp(context: context);
                          } else {
                            if (CommonValidation.isEmail(
                                    value: controller.emailController.text,
                                    validationMessage: "") !=
                                null) {
                              controller.isError.value = true;
                            } else {
                              controller.isError.value = false;
                              controller.isPasswordError.value = false;
                            }
                          }
                        },
                        buttonText: 'Verify Your Email',
                        buttonTextLevel: 4,
                        width: MySize.getScaledSizeWidth(258),
                        height: MySize.size70 ?? 70,
                        isLoading: controller.isLoading.value,
                      ),
                    ),
                  ),
                  Space.height(30),
                  DividerWithText(
                    text: "or continue with",
                    dividerWidth: MySize.size54,
                    dividerColor: AppTheme.grey[50],
                  ),
                  Space.height(25),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconWithLabel(
                        iconPath: AppImage.googleLogo,
                        label: "Google",
                        onTap: () {
                          HapticFeedback.lightImpact();
                          controller.signInWithGoogle(context: context);
                        },
                      ),
                      Space.width(22),
                      IconWithLabel(
                        iconPath: AppImage.linkdinLogo,
                        label: "LinkedIn",
                        onTap: () {
                          HapticFeedback.lightImpact();
                          controller.signInWithLinkedin(context: context);
                        },
                      ),
                      if (Platform.isIOS) ...[
                        Space.width(22),
                        IconWithLabel(
                          iconPath: AppImage.appleLogo,
                          label: "Apple",
                          onTap: () {
                            HapticFeedback.lightImpact();
                            controller.signInWithApple(context: context);
                          },
                        ),
                      ],
                    ],
                  ),
                  Space.height(40),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TypoGraphy(
                        text: "Already have an account?",
                        level: 4,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).brightness == Brightness.dark ? AppTheme.mutedTextColor : null,
                      ),
                      InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          HapticFeedback.lightImpact();
                          Get.offAndToNamed(Routes.Sign_In);
                        },
                        child: TypoGraphy(
                          text: " Sign In",
                          level: 4,
                          fontWeight: Theme.of(context).brightness == Brightness.dark ? FontWeight.w500 : FontWeight.w700,
                          color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey[50] : AppTheme.primary1,
                        ),
                      )
                    ],
                  ),
                  Space.height(60),
                  TypoGraphy(
                    text: "By continuing you’re indicating that you accept our",
                    level: 2,
                    fontWeight: FontWeight.w400,
                    color: AppTheme.grey,
                    textAlign: TextAlign.center,
                  ),
                  TermsAndPrivacy(
                    text1: "Terms of Use",
                    text2: "Privacy Policy",
                    textColor: AppTheme.grey,
                    underlineWidth: MySize.getScaledSizeWidth(75),
                    underlineHeight: MySize.size1 ?? 1,
                  ),
                  Space.height(30),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
