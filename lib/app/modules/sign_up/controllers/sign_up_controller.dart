import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_argument_key.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:signin_with_linkedin/signin_with_linkedin.dart';
import '../../../../constants/api.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/app_common_social_auth.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';
import '../../../routes/app_pages.dart';

class SignUpController extends GetxController {
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  RxBool isLoading = false.obs;
  FocusNode emailFocusNode = FocusNode();
  ApiManager apiManager = ApiManager();
  final GlobalKey<FormState> signUpKey = GlobalKey<FormState>();
  RxBool isError = false.obs;
  RxBool isPasswordError = false.obs;
  RxString accessToken = "".obs;

  LinkedInUser? linkedInUser;
  RxBool isPasswordVisible = true.obs;
  RxList validPasswordFormate =
      ["1 uppercase", "8 characters", "1 number", "1 Special characters"].obs;

  RxList<bool> passwordConditionsMet = RxList.generate(4, (index) => false);


  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    emailController.dispose();
    passwordController.dispose();
    emailFocusNode.dispose();
  }

  callApiForSignUp({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "email": emailController.value.text,
      "password": passwordController.value.text,
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.signUp,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String? token = response['token'];
            box.write("token", token);
            if(box.read('firstName') != null || box.read('lastName') != null || box.read('image') != null || box.read('uid') != null) {
              box.remove('firstName');
              box.remove('lastName');
              box.remove('image');
              box.remove('uid');
            }
            Get.toNamed(Routes.Verify_Otp, arguments: {
              AppArguments.email: emailController.text,
              AppArguments.password: passwordController.text
            });
            isLoading.value = false;
          } else {
            app
                .resolve<CustomDialogs>()
                .getDialog(title: "Oops!", desc: response["message"]);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        app
            .resolve<CustomDialogs>()
            .getDialog(title: "Oops!", desc: statusCode);
        isLoading.value = false;
      },
    );
  }

  signInWithGoogle({required BuildContext context}) {
    isLoading.value = true;
    app.resolve<CustomDialogs>().showCircularDialog(context);
    CommonAPIs.signInWithGoogle(
      context: context,
      successCallback: (response, message) {
        try {
          String token = response['token'];
          box.write('token', token);
          if (response['data']['isNewUser'] == true) {
            box.write('firstName', response['data']['firstName']);
            box.write('lastName', response['data']['lastName']);
            box.write('image', response['data']['image']);
            box.write('uid', response['data']['uid']);
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
            Get.toNamed(Routes.User_Detail);
            // app.resolve<CustomDialogs>().hideCircularDialog(context);
            // isLoading.value = false;
          } else {
            box.write("finalToken", token);
            CurrentUser.getMe(callback: () async {
              Get.offAllNamed(
                  Routes.Bottom_Bar); // Replace with your desired route
            });
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
          }
        } catch (error) {
          app.resolve<CustomDialogs>().hideCircularDialog(context);
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        isLoading.value = false;
      },
    );
    isLoading.value = false;
  }

  signInWithApple({required BuildContext context}) {
    isLoading.value = true;
    app.resolve<CustomDialogs>().showCircularDialog(context);
    CommonAPIs.signInWithApple(
      context: context,
      successCallback: (response, message) {
        try {
          String token = response['token'];
          box.write('token', token);
          log("response ==> $response");
          print("response ==> $response");
          if (response['data']['isNewUser'] == true) {
            // box.write('firstName', response['data']['firstName']);
            // box.write('lastName', response['data']['lastName']);
            // box.write('image', response['data']['image']);
            box.write('uid', response['data']['uid']);
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
            Get.toNamed(Routes.User_Detail,arguments: {"isFromApple": true});
            // isLoading.value = false;
            // app.resolve<CustomDialogs>().hideCircularDialog(context);
          } else {
            box.write("finalToken", token);
            CurrentUser.getMe(callback: () async {
              Get.offAllNamed(
                  Routes.Bottom_Bar); // Replace with your desired route
            });
            isLoading.value = false;
            app.resolve<CustomDialogs>().hideCircularDialog(context);
          }
        } catch (error) {
          app.resolve<CustomDialogs>().hideCircularDialog(context);
          CommonFunction.showCustomSnackbar(
            message: error.toString(),
            backgroundColor: AppTheme.red,
            isError: true,
          );
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          backgroundColor: AppTheme.red,
          isError: true,
        );
        isLoading.value = false;
      },
    );
    isLoading.value = false;
  }

  signInWithLinkedin({required BuildContext context}) {
    isLoading.value = true;

    CommonAPIs.signInWithLinkedIn(
      context: context,
      successCallback: (response, message) {
        try {
          String token = response['token'];
          box.write('token', token);
          if (response['data']['isNewUser'] == true) {
            box.write('firstName', response['data']['firstName']);
            box.write('lastName', response['data']['lastName']);
            box.write('image', response['data']['image']);
            box.write('email', response['data']['email']);
            box.write('uid', response['data']['uid']);
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
            Get.toNamed(Routes.User_Detail,arguments: {"isFromLinkedIn": true});
          } else {
            box.write("finalToken", token);
            CurrentUser.getMe(callback: () async {
              Get.offAllNamed(Routes.Bottom_Bar);
            });
          }
        } catch (error) {
          log('Error processing LinkedIn response: $error');
        }
      },
      failureCallback: (message, statusCode) {
        log("LinkedIn Auth Failed: $message");
        isLoading.value = false;
      },
    );
    isLoading.value = false;
  }


}
