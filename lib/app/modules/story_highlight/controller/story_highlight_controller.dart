import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/models/story_model.dart';
import 'package:incenti_ai/services/api_manager.dart';
import 'package:intl/intl.dart';

import '../../../../constants/constant.dart';
import '../../../../utillites/current_user.dart';

class StoryItem {
  final String id;
  final String imageUrl;
  final DateTime date;
  final RxBool isSelected;

  StoryItem({
    required this.id,
    required this.imageUrl,
    required this.date,
    bool selected = false,
  }) : isSelected = selected.obs;
}

class StoryHighlightController extends GetxController {
  var args = Get.arguments;
  final RxString selectedMonth = ''.obs;
  final ScrollController scrollController = ScrollController();
  final RxMap<String, int> monthIndices = <String, int>{}.obs;
  final RxBool isLoading = true.obs;
  RxList<OneStory> userStories = <OneStory>[].obs;
  RxInt projectId = 0.obs;
  RxInt subProjectId = 0.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    projectId.value = args['projectId'] ?? 0;
    subProjectId.value = args['subProjectId'] ?? 0;
    await callApiForGetCurrentUserStory();
    scrollController.addListener(_updateSelectedMonth);
    isLoading.value = false;
    _scrollToBottom();
  }

  @override
  void onClose() {
    scrollController.removeListener(_updateSelectedMonth);
    scrollController.dispose();
    super.onClose();
  }

  void _buildMonthIndices() {
    final Map<String, int> indices = {};
    final DateFormat formatter = DateFormat('MMMM yyyy');

    for (int i = 0; i < userStories.length; i++) {
      final story = userStories[i];
      final monthYearKey = formatter.format((story.createdAt ?? DateTime.now()).toLocal());

      if (!indices.containsKey(monthYearKey)) {
        indices[monthYearKey] = i;
      }
    }

    monthIndices.value = indices;
    if (indices.isNotEmpty) {
      selectedMonth.value = indices.keys.first;
    }
  }

  void _updateSelectedMonth() {
    if (scrollController.hasClients && monthIndices.isNotEmpty) {
      final currentPosition = scrollController.offset;
      final viewportHeight = scrollController.position.viewportDimension;

      String closestMonth = monthIndices.keys.first;
      int minDistance = userStories.length;

      for (final entry in monthIndices.entries) {
        final monthKey = entry.key;
        final storyIndex = entry.value;

        final approximateItemHeight = 300.0;
        final itemsPerRow = 3;
        final rowPosition = storyIndex ~/ itemsPerRow * approximateItemHeight;

        final distance = (rowPosition - currentPosition).abs();
        if (distance < minDistance) {
          minDistance = distance.toInt();
          closestMonth = monthKey;
        }
      }

      if (selectedMonth.value != closestMonth) {
        selectedMonth.value = closestMonth;
      }
    }
  }

  void scrollToMonth(String month) {
    if (monthIndices.containsKey(month)) {
      final storyIndex = monthIndices[month]!;

      final approximateItemHeight = 300.0;
      final itemsPerRow = 3;
      final rowPosition = storyIndex ~/ itemsPerRow * approximateItemHeight;

      scrollController.animateTo(
        rowPosition,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

    Future<void> callApiForGetCurrentUserStory(){
    final ApiModel getStories =
    ApiModel("/stories/users/${CurrentUser.user.id}", APIType.GET);
    return ApiManager().callApi(
      getStories,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            userStories.value = List<OneStory>.from(response['data']['data']!.map((x) => OneStory.fromJson(x)));
            userStories.value = userStories.reversed.toList();
            _buildMonthIndices();
            log('sdsds ===> ${userStories.toJson()}');
          }
        } catch (error) {
           log('error === $error');
        }
      },
      failureCallback: (message, statusCode) {
        log('error === $message');
      },
    );
  }

  void _scrollToBottom() {
    Future.delayed(Duration(milliseconds: 100), () {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 10),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void toggleSelection(OneStory story) {
    story.isSelected.value = !story.isSelected.value;
  }

  List<OneStory> getSelectedStories() {
    return userStories.where((story) => story.isSelected.value).toList();
  }


  void createHighlight() {
    final selectedStories = getSelectedStories();
    if (selectedStories.isEmpty) {
      Get.snackbar('Error', 'Please select at least one story');
      return;
    }

    // Implement creation of highlight
    // For now, just show a snackbar
    Get.snackbar(
      'Success',
      'Created highlight with ${selectedStories.length} stories',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }
}