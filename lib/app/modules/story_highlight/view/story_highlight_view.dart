import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/story_editor/common_component/animated_onTap_button.dart';
import 'package:incenti_ai/app/routes/app_pages.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/models/story_model.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:intl/intl.dart';
import 'package:incenti_ai/app/modules/story_highlight/controller/story_highlight_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../utillites/empty.dart';


class StoryHighlightView extends GetWidget<StoryHighlightController> {
  const StoryHighlightView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.black,
      elevation: 0,
      leadingWidth: 50,
      leading: InkWell(
          onTap: () => Get.back(),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: SvgPicture.asset(
              AppImage.backArrow,
              color:  AppTheme.white ,
            ),
          )),
      title: Row(
        children: [
          Obx(() => controller.getSelectedStories().isNotEmpty
              ? TypoGraphy(
                  text: '${controller.getSelectedStories().length} selected',
                  level: 5,
                  color: AppTheme.white,
                  fontWeight: FontWeight.w600,
                )
              : TypoGraphy(
                  text: 'New highlight',
                  level: 5,
                  color: AppTheme.white,
                  fontWeight: FontWeight.w600,
                )),
        ],
      ),
      centerTitle: true,
      actions: [
        Obx(() => TextButton(
              onPressed: controller.getSelectedStories().isNotEmpty
                  ? () => Get.toNamed(Routes.story_cover_selector,arguments: {'selectedStories' : controller.getSelectedStories(),'projectId': controller.projectId.value,"subProjectId": controller.subProjectId.value})
                  : () {},
              child: TypoGraphy(
                text: 'Next',
                level: 1,
                color: controller.getSelectedStories().isNotEmpty
                    ?  AppTheme.primary1
                    : const Color(0xff99a1ab),
                fontWeight: FontWeight.w600,
              ),
            )),
      ],
    );
  }

  Widget _buildBody() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(color: Colors.white),
        );
      }

      if(controller.userStories.isEmpty){
        return Center(child: Empty(
          textColor: AppTheme.white,
          message: "No Stories available!",
        ),);
      }

      return Column(
        children: [
          Expanded(
            child: Theme(
              data: ThemeData(
                highlightColor: Colors.white,
              ),
              child: RawScrollbar(
                thickness: 4,
                thumbColor: AppTheme.white,
                controller: controller.scrollController,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: _buildStoriesGrid(),
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildStoriesGrid() {
    return GridView.builder(
      controller: controller.scrollController,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 9 / 16,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: controller.userStories.length,
      itemBuilder: (context, index) {
        final story = controller.userStories[index];

        bool isFirstOfMonth = false;
        final DateFormat monthYearFormat = DateFormat('MMMM yyyy');

        if (index == 0) {
          isFirstOfMonth = true;
        } else {
          final currentMonthYear = monthYearFormat
              .format((story.createdAt ?? DateTime.now()).toLocal());
          final prevMonthYear = monthYearFormat.format(
              (controller.userStories[index - 1].createdAt ?? DateTime.now())
                  .toLocal());
          isFirstOfMonth = currentMonthYear != prevMonthYear;
        }

        if (isFirstOfMonth) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildStoryItem(story),
              ),
            ],
          );
        }

        return _buildStoryItem(story);
      },
    );
  }

  Widget _buildStoryItem(OneStory story) {
    return AnimatedOnTapButton(
      lowerBound: 0.98,
      upperBound: 1,
      onTap: () => controller.toggleSelection(story),
      child: Stack(
        fit: StackFit.expand,
        children: [
          ClipRRect(
            child: CachedNetworkImage(
              imageUrl: (story.mediaType == StoryType.image.name ||
                      story.mediaType == StoryType.text.name)
                  ? story.overlayImage ?? ''
                  : story.thumbnailPath ?? '',
              fit: BoxFit.cover,
              errorWidget: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
          ),
          Positioned(
            top: 8,
            left: 8,
            child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Column(
                  children: [
                    TypoGraphy(
                      text: DateFormat('d').format(
                          (story.createdAt ?? DateTime.now()).toLocal()),
                      color: Colors.white,
                      level: 4,
                    ),
                    TypoGraphy(
                      text: DateFormat('MMM').format(
                          (story.createdAt ?? DateTime.now()).toLocal()),
                      color: Colors.white,
                      level: 2,
                    )
                  ],
                )),
          ),
          Obx(() => story.isSelected.value
              ? Container(
                  decoration: BoxDecoration(
                      color: AppTheme.white.withValues(alpha: 0.6)),
                )
              : SizedBox()),
          Positioned(
            right: 8,
            bottom: 8,
            child: Obx(() => InkWell(
                  onTap: () => controller.toggleSelection(story),
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      color: story.isSelected.value
                          ? AppTheme.whiteF4F4F4
                          : Colors.transparent,
                      border: Border.all(
                        color: story.isSelected.value
                            ? AppTheme.whiteF4F4F4
                            : Color(0xff99a1ab),
                        width: 2,
                      ),
                    ),
                    child: story.isSelected.value
                        ? const Icon(
                            Icons.check,
                            color: Colors.black,
                            size: 20,
                          )
                        : null,
                  ),
                )),
          ),
        ],
      ),
    );
  }

  Widget _buildScrollIndicator() {
    return SizedBox(
      width: 40,
      child: Column(
        children: [
          const SizedBox(height: 10),
          Expanded(
            child: Container(
              width: 22,
              decoration: BoxDecoration(
                color: Colors.grey[850],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Obx(() => ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    itemCount: controller.monthIndices.length,
                    itemBuilder: (context, index) {
                      final month =
                          controller.monthIndices.keys.elementAt(index);
                      final isSelected =
                          controller.selectedMonth.value == month;

                      return GestureDetector(
                        onTap: () => controller.scrollToMonth(month),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Colors.grey[700]
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Center(
                            child: Text(
                              month.substring(0, 3),
                              style: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : Colors.grey[400],
                                fontSize: 10,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  )),
            ),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
