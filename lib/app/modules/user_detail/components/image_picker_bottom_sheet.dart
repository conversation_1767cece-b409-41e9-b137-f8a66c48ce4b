import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import '../../../../constants/app_image.dart';
import '../../../../main.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/typography.dart';

class ImagePickerBottomSheet {
  static void show({
    required BuildContext context,
    Function(File pickedImage)? onImagePicked,
    VoidCallback? onRemoveImage,
    double? cornerRadius,
    bool isScrollControlled = false,
    double? bottomSheetHeight,
    Color backgroundColor = Colors.transparent,
    Widget? child,
    bool isAspectRatio = false,
    bool isImageAvailable = false,
    double? ratioX,
    double? ratioY,
    bool isBackCoverPhoto = false,
  }) {
    showModalBottomSheet(
      backgroundColor: backgroundColor,
      context: context,
      isScrollControlled: isScrollControlled,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(cornerRadius ?? (MySize.size30 ?? 30)),
        ),
      ),
      // isScrollControlled: true,
      isDismissible: true,
      builder: (context) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Stack(
            children: [
              InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () => Get.back(),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                  child: Container(
                      height: double.infinity, width: double.infinity),
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  width: double.infinity,
                  height: bottomSheetHeight,
                  decoration: BoxDecoration(
                    color: AppTheme.appBottomSheet,
                    border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(40),
                        topRight: Radius.circular(40)),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Space.height(8),
                      Container(
                        height: MySize.size5,
                        width: MySize.size34,
                        decoration: BoxDecoration(
                          color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                      if (child == null) ...[
                        Space.height(40),
                        buildOption(
                          iconPath: AppImage.photoIcon,
                          text: isBackCoverPhoto
                              ? "Upload a Cover Photo"
                              : "Upload a Photo",
                          textColor: AppTheme.whiteWithBase,
                          onTap: () async {
                            File? pick =
                            await CommonFunction.pickImageFromGallery(
                                isAspectRatio: true,ratioX: ratioX,ratioY: ratioY);
                            if (pick != null) {
                              onImagePicked!(pick);
                              Get.back();
                            } else {
                              Get.back();
                            }
                            // CommonFunction.requestPermissions(
                            //   context: context,
                            //   onPermissionsGranted: () async {
                            //     File? pick =
                            //         await CommonFunction.pickImageFromGallery(
                            //             isAspectRatio: true,ratioX: ratioX,ratioY: ratioY);
                            //     if (pick != null) {
                            //       onImagePicked!(pick);
                            //       Get.back();
                            //     } else {
                            //       Get.back();
                            //     }
                            //   },
                            //   title: "Please Allow Gallery",
                            //   message:
                            //       "Please allow gallery to select a profile image.",
                            //   permissions: [Permission.photos],
                            // );
                          },
                        ),
                        Space.height(40),
                        buildOption(
                          iconPath: AppImage.cameraIcon,
                          text: "Take a Photo",
                          textColor: AppTheme.whiteWithBase,
                          onTap: () async {
                            CommonFunction.requestPermissions(
                              context: context,
                              onPermissionsGranted: () async {
                                File? pickedImage =
                                await CommonFunction.pickImageFromCamera(
                                    isAspectRatio: isAspectRatio,ratioX: ratioX,ratioY: ratioY);
                                if (pickedImage != null) {
                                  onImagePicked!(pickedImage);
                                  Get.back();
                                } else {
                                  Get.back();
                                }
                              },
                              title: "Please Allow Camera",
                              message:
                              "Please allow Camera to select a profile image.",
                              permissions: [Permission.camera],
                            );
                          },
                        ),
                        if(isImageAvailable) ...[
                          Space.height(40),
                          buildOption(
                            iconPath: AppImage.trashIcon,
                            text: "Remove",
                            isRemove: true,
                            textColor: AppTheme.red,
                            onTap: () {
                              onRemoveImage!();
                              Get.back();
                            },
                          ),
                        ],

                        Space.height(50),
                      ],
                      if (child != null) ...[child]
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Widget buildOption({
    required String iconPath,
    required String text,
    required Color textColor,
    required VoidCallback onTap,
    bool isRemove = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: MySize.size30 ?? 30),
      child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: onTap,
        child: Row(
          children: [
            SvgPicture.asset(
              iconPath,
              height: MySize.size24,
              width: MySize.size24,
              color:  isRemove ? AppTheme.red : AppTheme.whiteWithNull,
            ),
            SizedBox(width: MySize.size20),
            TypoGraphy(
              text: text,
              level: 5,
              color: textColor,
            ),
          ],
        ),
      ),
    );
  }
}
