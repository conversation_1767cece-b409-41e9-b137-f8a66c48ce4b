
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui' as ui;

class ProfileImageWithEditIcon extends StatelessWidget {
  final Widget? imagePath;
  final Widget? clipOval;
  final String? editIconPath;
  final double imageSize;
  final double iconSize;
  final double blurSigma;
  final double? left;
  final double? right;
  final double? top;
  final double? padding;
  final double? bottom;
  final VoidCallback? onEditIconTap;
  final Color? color;

  const ProfileImageWithEditIcon({
    super.key,
    this.imagePath,
    this.clipOval,
    this.editIconPath,
    this.left,
    this.right,
    this.top,
    this.padding,
    this.bottom,
    this.imageSize = 120.0,
    this.iconSize = 34.0,
    this.blurSigma = 30.0,
    this.onEditIconTap,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        clipOval ?? ClipOval(
          child: imagePath
        ),
        Positioned(
          right: right ?? 0,
          bottom: bottom,
          left: left,
          top: top,
          child: InkWell(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            onTap: onEditIconTap,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(iconSize / 2),
              child: BackdropFilter(
                filter: ui.ImageFilter.blur(
                  sigmaX: blurSigma,
                  sigmaY: blurSigma,
                ),
                child: SizedBox(
                  height: iconSize,
                  width: iconSize,
                  child: Padding(
                    padding: EdgeInsets.all(padding ?? iconSize * 0.2),
                    child: SvgPicture.asset(
                      editIconPath!,
                      height: iconSize * 0.5,
                      width: iconSize * 0.5,
                      color: color ?? Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
