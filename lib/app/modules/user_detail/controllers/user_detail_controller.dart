import 'dart:developer';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:incenti_ai/utillites/current_user.dart';
import '../../../../constants/api.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/common_function.dart';
import '../../../routes/app_pages.dart';

class UserDetailController extends GetxController {
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController aboutController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  Rx<TextEditingController> locationController = TextEditingController().obs;
  final GlobalKey userLocationFieldKey = GlobalKey();
  Rxn<String> location = Rxn(null);
  Rx<File?> selectedImage = Rx<File?>(null);
  FocusNode aboutFocusNode = FocusNode();
  RxBool isLoading = false.obs;
  RxBool isFirstNameError = false.obs;
  RxBool isLastNameError = false.obs;
  RxBool isAboutNameError = false.obs;
  RxBool isFromLinkedin = false.obs;
  RxBool isFromApple = false.obs;
  ApiManager apiManager = ApiManager();
  RxInt charCount = 0.obs; // To keep track of character count
  RxInt maxChars = 250.obs; // Maximum allowed characters
  RxString oldImageUrl = "".obs;
  RxString uuid = "".obs;
  RxString userImage = "".obs;
  var args = Get.arguments;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    aboutController.addListener(updateCharCount);
    if(args != null && args["isFromLinkedIn"] != null) {
      isFromLinkedin.value = args["isFromLinkedIn"];
    }
    if(args != null && args["isFromApple"] != null) {
      isFromApple.value = args["isFromApple"];
    }
    if(args != null && args["isEditUser"] != null) {
      firstNameController.text = CurrentUser.user.firstName ?? "";
      lastNameController.text = CurrentUser.user.lastName ?? "";
      aboutController.text = CurrentUser.user.about ?? "";
      userImage.value = CurrentUser.user.image ?? "";
      emailController.text = CurrentUser.user.email ?? "";
      locationController.value.text = CurrentUser.user.location ?? "";
      location.value = CurrentUser.user.location ?? "";
    } else {
      Future.delayed(
        Duration(seconds: 1),
            () {
          CommonFunction.showCustomSnackbar(
            message: "Your email is verified. Please complete your registration.",
          );
        },
      );
      if (box.read("firstName") != null ||
          box.read("lastName") != null) {
        firstNameController.text = box.read("firstName") ?? "";
        lastNameController.text = box.read("lastName") ?? "";
      }
      if(box.read("image") != null) {
        userImage.value = box.read("image");
      }
      if(box.read("uid") != null) {
        uuid.value = box.read("uid");
      }
      if(isFromLinkedin.value && box.read("email") != null) {
        emailController.text = box.read("email") ?? "";
      }
    }

  }

  void scrollToLocationField(BuildContext context) {
    Future.delayed(Duration(milliseconds: 700),(){
      Scrollable.ensureVisible(
        userLocationFieldKey.currentContext!,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  void updateCharCount() {
    charCount.value = aboutController.text.length.clamp(0, maxChars.value);
  }

  @override
  void dispose() {
    aboutController.removeListener(updateCharCount);
    aboutController.dispose();
    super.dispose();
  }

  // callApiForUserProfile({required BuildContext context}) async {
  //   Map<String, dynamic> dict = {};
  //   app.resolve<CustomDialogs>().showCircularDialog(context);
  //   if (selectedImage.value != null || oldImageUrl.value.isNotEmpty) {
  //     if(selectedImage.value != null) {
  //       dict["image"] = await MultipartFile.fromFile(selectedImage.value?.path ?? "",
  //           filename: selectedImage.value!
  //               .path
  //               .split('/')
  //               .last
  //               .trim());
  //     } else {
  //       dict["imageLink"] = oldImageUrl.value;
  //     }
  //   }
  //   FormData formData = FormData.fromMap(dict);
  //   return apiManager.callApi(
  //     APIS.user.uploadProfilePic,
  //     params: formData,
  //     successCallback: (response, message) {
  //       if (response['status'] == 'success') {
  //         log(response.toString());
  //         userImage.value = response["data"]["image"];
  //         app.resolve<CustomDialogs>().hideCircularDialog(context);
  //       }
  //     },
  //     failureCallback: (message, statusCode) {
  //       log("Error : $message");
  //       app.resolve<CustomDialogs>().hideCircularDialog(context);
  //     },
  //   );
  // }

  callApiForUserImage(
      {required BuildContext context}) async {
    try {
      Map<String, dynamic> dict = {
      };

        if (selectedImage.value != null) {
          if(selectedImage.value != null) {
            dict["file"] = await MultipartFile.fromFile(selectedImage.value?.path ?? "",
                filename: selectedImage.value!
                    .path
                    .split('/')
                    .last
                    .trim());
          }
        }

      FormData formData = FormData.fromMap(dict);
      app.resolve<CustomDialogs>().showCircularDialog(context);

      apiManager.callApi(
        APIS.generalImageUpload.imageUpload,
        params: formData,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            String imageUrl =
            response['data'][0]['link']; // Extract URL from response
            userImage.value = imageUrl;
            app.resolve<CustomDialogs>().hideCircularDialog(context);
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          app.resolve<CustomDialogs>().hideCircularDialog(context);
        },
      );
    } catch (e) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log("Exception: $e");
    }
  }

  callApiForUserData({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "firstName": firstNameController.value.text.trim(),
      "lastName": lastNameController.value.text.trim(),
    };
    if(aboutController.value.text.isNotEmpty){
      dict['about'] = aboutController.value.text;
    } else {
      dict['about'] = null;
    }
    if(location.value != null && (location.value ?? "").isNotEmpty){
      dict['location'] = location.value;
    } else {
      dict['location'] = null;
    }
    // Check conditions for image
    if (userImage.value.isEmpty) {
      dict["image"] = null;
    } else {
      dict["image"] = userImage.value;
    }
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.user.updateMe,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CurrentUser.getMe(
              callback: () async {
                if (args != null &&
                    args["isEditUser"] != null) {
                  Get.back();
                  CommonFunction.showCustomSnackbar(
                    message: response['message'],
                  );
                } else {
                  Get.offAllNamed(Routes.Bottom_Bar,
                      arguments: {"isFirstTime": true});
                }
              },
            );
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForCreateUserData({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "firstName": firstNameController.value.text.trim(),
      "lastName": lastNameController.value.text.trim(),
    };
    if(aboutController.value.text.isNotEmpty){
      dict['about'] = aboutController.value.text;
    } else {
      dict['about'] = null;
    }
    if(location.value != null && (location.value ?? "").isNotEmpty){
      dict['location'] = location.value;
    }
    if(isFromLinkedin.value) {
      dict['email'] = box.read("email")  ?? emailController.value.text.trim();
    }

    if(location.value != null && (location.value ?? "").isNotEmpty){
      dict['location'] = location.value;
    }
    if(uuid.value.isNotEmpty){
      dict['uid'] = uuid.value;
    }

    if(isFromLinkedin.value) {
      dict['email'] = box.read("email") ?? emailController.value.text.trim();
    }

    if(oldImageUrl.value.isNotEmpty) {
      dict['image'] = oldImageUrl.value;
    } else {
      if (userImage.value.isEmpty) {
        dict["image"] = null; // Pass null if no image is selected or stored
      } else {
        dict["image"] = userImage.value; // Pass existing image URL
      }
    }
    // Check conditions for image
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.user.createUser,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String token = response['token'];
            box.write('themeReminder', DateTime.now().toString());
            box.write("token", token);
            box.write("finalToken", token);
            print("themeReminder === ${box.read('themeReminder')}");
            CurrentUser.getMe(
              callback: () async {
                // Get.offAllNamed(Routes.Bottom_Bar,
                //     arguments: {"isFirstTime": true});
                Get.offAllNamed(Routes.tutorial);
                // Get.put(SettingController());
                // Navigator.pushReplacement(Get.context!, MaterialPageRoute(builder: (context) => CustomNavigationView(isFromSignUp: true,),));
              },
            );
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }


}
