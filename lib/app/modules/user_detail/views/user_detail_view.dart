import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/network_image.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/locaion_input_field.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../components/image_picker_bottom_sheet.dart';
import '../components/profile_image_view.dart';
import '../controllers/user_detail_controller.dart';

class UserDetailView extends GetWidget<UserDetailController> {
  const UserDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        // backgroundColor: AppTheme.white,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            controller.args != null && controller.args["isEditUser"] != null
                ? Space.height(60)
                : Space.height(50),
            Padding(
              padding: EdgeInsets.only(left: MySize.size30 ?? 30),
              child: Align(
                alignment: Alignment.centerLeft,
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    if (controller.args != null &&
                        controller.args["isEditUser"] != null) {
                      Get.back();
                    } else {
                      Get.offAllNamed(Routes.Sign_Up);
                    }
                  },
                  child: controller.args != null &&
                          controller.args["isEditUser"] != null
                      ? SvgPicture.asset(
                          AppImage.backArrow,
                          height: MySize.size28,
                    width: MySize.size28,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? AppTheme.white
                              : null,
                        )
                      : SvgPicture.asset(
                          AppImage.backArrow,
                          height: MySize.size28,
                          width: MySize.size28,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? AppTheme.white
                              : null,
                        ),
                ),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                physics: ClampingScrollPhysics(),
                child: Column(
                  children: [
                    controller.args != null &&
                            controller.args["isEditUser"] != null
                        ? Space.height(13.83)
                        : Space.height(41),
                    TypoGraphy(
                      text: controller.args != null &&
                              controller.args["isEditUser"] != null
                          ? "Edit Profile"
                          : "Fill out your\npersonal details to\ncontinue",
                      level: 8,
                      fontWeight: FontWeight.w700,
                      // color: AppTheme.baseBlack,
                      textAlign: TextAlign.center,
                    ),
                    controller.args != null &&
                            controller.args["isEditUser"] != null
                        ? Space.height(50)
                        : Space.height(21),
                    InkWell(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () {
                        if (controller.userImage.value.isEmpty) {
                          ImagePickerBottomSheet.show(
                            context: context,
                            ratioY: 120,
                            ratioX: 120,
                            isImageAvailable: controller.userImage.value !=
                                        "" ||
                                    controller.selectedImage.value != null ||
                                    controller.oldImageUrl.value != ""
                                ? true
                                : false,
                            onImagePicked: (pickedImage) {
                              controller.selectedImage.value = pickedImage;
                              controller.callApiForUserImage(context: context);
                            },
                            onRemoveImage: () {
                              controller.userImage.value = "";
                              controller.selectedImage.value = null;
                              controller.oldImageUrl.value = "";
                            },
                          );
                        }
                      },
                      child: Obx(
                        () => Container(
                          height: MySize.getScaledSizeHeight(120),
                          width: MySize.getScaledSizeWidth(120),
                          decoration: BoxDecoration(
                              color: box.read('isDarkMode') ? AppTheme.darkBackground : AppTheme.subPrimary,
                              shape: BoxShape.circle),
                          child: controller.userImage.value != "" ||
                                  controller.oldImageUrl.value.isNotEmpty
                              ? ProfileImageWithEditIcon(
                                  bottom: 3.0,
                                  imagePath: NetworkImageComponent(
                                    imageUrl:
                                        controller.oldImageUrl.value.isNotEmpty
                                            ? controller.oldImageUrl.value
                                            : controller.userImage.value,
                                    width: MySize.getScaledSizeWidth(120),
                                    height: MySize.getScaledSizeHeight(120),
                                  ),
                                  editIconPath: AppImage.editIcon,
                                  imageSize: MySize.getScaledSizeWidth(120),
                                  iconSize: MySize.getScaledSizeWidth(34),
                                  blurSigma: MySize.getScaledSizeWidth(35),
                                  onEditIconTap: () {
                                    ImagePickerBottomSheet.show(
                                      context: context,
                                      ratioY: 120,
                                      ratioX: 120,
                                      isImageAvailable: controller
                                                      .userImage.value !=
                                                  "" ||
                                              controller.selectedImage.value !=
                                                  null ||
                                              controller.oldImageUrl.value != ""
                                          ? true
                                          : false,
                                      onImagePicked: (pickedImage) {
                                        controller.selectedImage.value =
                                            pickedImage;
                                        controller.callApiForUserImage(
                                            context: context);
                                      },
                                      onRemoveImage: () {
                                        controller.userImage.value = "";
                                        controller.selectedImage.value = null;
                                        controller.oldImageUrl.value = "";
                                      },
                                    );
                                  },
                                )
                              : DottedBorder(
                                  strokeWidth:
                                      MySize.getScaledSizeWidth(1) ?? 4,
                                  borderType: BorderType.Circle,
                                  dashPattern: const [8, 6],
                                  color: AppTheme.primary1,
                                  borderPadding: EdgeInsets.all(
                                      MySize.getScaledSizeWidth(1) ?? 1),
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      spacing: MySize.size5 ?? 5,
                                      children: [
                                        SvgPicture.asset(
                                          AppImage.userIcon,
                                          height:
                                              MySize.getScaledSizeHeight(32),
                                          width: MySize.getScaledSizeWidth(32),
                                          color: AppTheme.whiteWithNull,
                                        ),
                                        TypoGraphy(
                                          text: "Upload",
                                          level: 2,
                                          fontWeight: FontWeight.w400,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                        ),
                      ),
                    ),
                    Space.height(28),
                    Obx(
                      () => Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(45) ?? 52),
                        child: AppTextField(
                          controller: controller.firstNameController,
                          labelText: "First Name*",
                          readOnly: (controller.isFromApple.value && box.read("firstName") != null)
                              ? true
                              : false,
                          textStyle: TextStyle(
                              color: (controller.isFromApple.value && box.read("lastName") != null)
                                  ? AppTheme.grey
                                  : null,
                              fontSize: MySize.getScaledSizeHeight(16),
                              fontWeight: FontWeight.w600),
                          errorMessage: controller.isFirstNameError.value,
                          textCapitalization: TextCapitalization.words,
                          // maxLength: 20,
                          onChangedValue: (value) {
                            controller.isFirstNameError.value =
                                value!.trim().isEmpty;
                          },
                        ),
                      ),
                    ),
                    Space.height(20),
                    Obx(
                      () => Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(45) ?? 52),
                        child: AppTextField(
                          controller: controller.lastNameController,
                          labelText: "Last Name*",
                          readOnly: (controller.isFromApple.value && box.read("lastName") != null)
                              ? true
                              : false,
                          errorMessage: controller.isLastNameError.value,
                          textCapitalization: TextCapitalization.words,
                          maxLength: 20,
                          textStyle: TextStyle(
                              color: (controller.isFromApple.value && box.read("lastName") != null)
                                  ? AppTheme.grey
                                  : null,
                              fontSize: MySize.getScaledSizeHeight(16),
                              fontWeight: FontWeight.w600),
                          onChangedValue: (value) {
                            controller.isLastNameError.value =
                                value!.trim().isEmpty;
                          },
                        ),
                      ),
                    ),
                    if (controller.args != null &&
                            controller.args["isEditUser"] != null ||
                        controller.isFromLinkedin.value) ...[
                      Space.height(20),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(45) ?? 52),
                        child: AppTextField(
                          controller: controller.emailController,
                          labelText: "Email",
                          textCapitalization: TextCapitalization.words,
                          readOnly: (controller.isFromLinkedin.value &&
                                      box.read("email") != null ||
                                  controller.args != null &&
                                      controller.args["isEditUser"] != null)
                              ? true
                              : false,
                          textStyle: TextStyle(
                              color: (controller.isFromLinkedin.value &&
                                          box.read("email") != null ||
                                      controller.args != null &&
                                          controller.args["isEditUser"] != null)
                                  ? AppTheme.grey
                                  : AppTheme.baseBlack,
                              fontSize: MySize.getScaledSizeHeight(16),
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                    Space.height(20),
                    ExpansionTile(
                      childrenPadding:
                          EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                      iconColor: AppTheme.whiteWithBase,
                      collapsedIconColor: AppTheme.whiteWithBase,
                      tilePadding:
                          EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                      shape: const RoundedRectangleBorder(
                        side: BorderSide.none,
                      ),
                      title: TypoGraphy(
                        text: "Add More (Optional)",
                        level: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      children: [
                        LocationInputField(
                          controller: controller.locationController.value,
                          key: controller.userLocationFieldKey,
                          selectedSuggestion: controller.location,
                          onFocused: () {
                            controller.scrollToLocationField(context);
                          },
                        ),
                        Space.height(20),
                        Obx(
                          () => AppTextField(
                            alignLabelWithHint: true,
                            textInputAction: TextInputAction.newline,
                            textInputType: TextInputType.multiline,
                            textCapitalization: TextCapitalization.sentences,
                            focusNode: controller.aboutFocusNode,
                            height: MySize.size140,
                            controller: controller.aboutController,
                            errorMessage: controller.isAboutNameError.value,
                            labelText: "About",
                            maxLines: 5,
                            maxLength: 250,
                          ),
                        ),
                        Space.height(6),
                        Obx(
                          () => Align(
                            alignment: Alignment.centerRight,
                            child: TypoGraphy(
                              text:
                                  "${controller.charCount}/${controller.maxChars}",
                              level: 2,
                              fontWeight: FontWeight.w400,
                              color: AppTheme.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Space.height(30),
                    Obx(
                      () => Padding(
                        padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(45)),
                        child: Buttons(
                          onTap: () {
                            HapticFeedback.lightImpact();
                            // Check if first letter is a number
                            bool isFirstNameInvalid = controller
                                    .firstNameController.text.isNotEmpty &&
                                RegExp(r'^[^a-zA-Z]+$').hasMatch(
                                    controller.firstNameController.text);
                        
                            bool isLastNameInvalid = controller
                                    .lastNameController.text.isNotEmpty &&
                                RegExp(r'^[^a-zA-Z]+$').hasMatch(
                                    controller.lastNameController.text);
                            bool isLocationFilled = controller.locationController.value.text.trim().isNotEmpty;
                            if (isFirstNameInvalid) {
                              CommonFunction.showCustomSnackbar(
                                  message:
                                      "Your First Name cannot contain only number and special character.",
                                  isError: true,
                                  backgroundColor: AppTheme.red);
                              return;
                            }
                            if (isLastNameInvalid) {
                              CommonFunction.showCustomSnackbar(
                                  message:
                                      "Your Last Name cannot contain only number and special character.",
                                  isError: true,
                                  backgroundColor: AppTheme.red);
                              return;
                            }
                        
                            if ((controller.firstNameController.text
                                    .trim()
                                    .isNotEmpty &&
                                controller.lastNameController.text
                                    .trim()
                                    .isNotEmpty)) {
                              print("value == > ${controller.locationController.value.text}");
                              print("controller.location.value == > ${controller.location.value}");
                        
                              if (!isLocationFilled || controller.location.value == controller.locationController.value.text) {
                                if (controller.args != null &&
                                    controller.args["isEditUser"] != null) {
                                  controller.callApiForUserData(
                                      context: context);
                                } else {
                                  controller.callApiForCreateUserData(
                                      context: context);
                                }
                              } else {
                                CommonFunction.showCustomSnackbar(
                                    message: "Please Choose Correct Location",
                                    isError: true,
                                    backgroundColor: AppTheme.red);
                                return;
                              }
                            } else {
                              if (controller.firstNameController.value.text
                                  .trim()
                                  .isEmpty) {
                                controller.isFirstNameError.value = true;
                              } else {
                                controller.isFirstNameError.value = false;
                              }
                              if (controller.lastNameController.value.text
                                  .trim()
                                  .isEmpty) {
                                controller.isLastNameError.value = true;
                              } else {
                                controller.isLastNameError.value = false;
                              }
                            }
                          },
                          buttonText: controller.args != null &&
                                  controller.args["isEditUser"] != null
                              ? 'Update'
                              : 'Continue',
                          buttonTextLevel: 4,
                          width: MySize.getScaledSizeWidth(188),
                          height: MySize.size70 ?? 70,
                          isLoading: controller.isLoading.value,
                        ),
                      ),
                    ),
                    Space.height(30),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
