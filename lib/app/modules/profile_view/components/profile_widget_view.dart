import 'dart:ui';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/profile_view/components/follower_sheet.dart';
import 'package:incenti_ai/app/modules/story/controller/story_controller.dart';
import 'package:incenti_ai/app/modules/story_editor/common_component/animated_onTap_button.dart';
import 'package:incenti_ai/models/get_all_highlights_model.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../controllers/profile_view_controller.dart';
import '../views/read_more_view.dart';
import 'following_sheet.dart';

Widget buildIcon(String asset, VoidCallback onTap, {double? padding,Color? color}) {
  return InkWell(
    onTap: onTap,
    child: SizedBox(
      height: MySize.size42,
      width: MySize.size42,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(50),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 80, sigmaY: 80),
          child: Padding(
            padding: EdgeInsets.all(padding ?? 11),
            child: SvgPicture.asset(
              asset,
              color: color ?? AppTheme.white,
            ),
          ),
        ),
      ),
    ),
  );
}

Widget buildProfileSection() {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(40)),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Space.height(10),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TypoGraphy(
                text: CurrentUser.user.fullName,
                level: 6,
                fontWeight: FontWeight.w600,
              ),
            ),
            Space.width(30),
            // Spacer(),
            buildEditProfileButton(),
          ],
        ),
        Space.height(8),
        if ((CurrentUser.user.location ?? "").isNotEmpty &&
            CurrentUser.user.location != null) ...[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: MySize.size2 ?? 10),
                child: Image.asset(
                  AppImage.location,
                  height: MySize.getScaledSizeHeight(15),
                  width: MySize.getScaledSizeWidth(15),
                ),
              ),
              Space.width(4),
              Expanded(
                child: TypoGraphy(
                  text: CurrentUser.user.location,
                  level: 3,
                  color: AppTheme.grey,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          Space.height(8),
        ],
        // Space.height(5),
        // TypoGraphy(
        //   text: CurrentUser.user.email,
        //   level: 3,
        //   fontWeight: FontWeight.w400,
        //   color: AppTheme.grey,
        // ),
        Space.height(10),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () {
                Get.find<ProfileViewController>()
                    .searchController
                    .value
                    .clear();
                Get.find<ProfileViewController>()
                    .callApiForFollowersData(context: Get.context!);
                showModalBottomSheet(
                  context: Get.context!,
                  isScrollControlled: true,
                  // Ensures the bottom sheet resizes with the keyboard
                  backgroundColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(40)),
                  ),
                  builder: (context) {
                    return StatefulBuilder(
                      builder: (context, setState) {
                        return FollowerSheet(
                          index: 0,
                          forFollowersCount:
                              "${CurrentUser.user.followersCount}",
                          searchController: Get.find<ProfileViewController>()
                              .searchController
                              .value,
                          isLoading:
                              Get.find<ProfileViewController>().isLoading,
                          followersList:
                              Get.find<ProfileViewController>().followersList,
                          onClearSearch: () {
                            Get.find<ProfileViewController>()
                                .searchController
                                .value
                                .clear();
                            Get.find<ProfileViewController>()
                                .callApiForFollowersData(context: Get.context!);
                          },
                          onSearchChanged: (text) {
                            if (text.trim().isNotEmpty) {
                              Get.find<ProfileViewController>()
                                  .callApiForFollowersData(
                                      context: Get.context!);
                            }
                          },
                          onFollowToggle: (user, index) {
                            user.isFollowing?.value = true;
                            Get.find<ProfileViewController>()
                                .callApiForFollowUser(
                              context: Get.context!,
                              userId: user.id,
                            );
                          },
                          onUnfollowConfirm: (user, index) {
                            Get.find<ProfileViewController>()
                                .callApiForUnFollowUser(
                              context: Get.context!,
                              userId: user.id,
                              isUnFollowed: false,
                              index: index,
                            );
                          },
                        );
                      },
                    );
                  },
                ).then(
                  (value) {
                    CurrentUser.getMe();
                  },
                );
              },
              child: TypoGraphy(
                  text: "${CurrentUser.user.followersCount} followers",
                  level: 4,
                  fontWeight: FontWeight.w700),
            ),
            Space.width(7),
            TypoGraphy(text: "•", level: 4, fontWeight: FontWeight.w700),
            Space.width(7),
            GestureDetector(
              onTap: () {
                Get.find<ProfileViewController>()
                    .searchController
                    .value
                    .clear();
                Get.find<ProfileViewController>()
                    .callApiForFollowingData(context: Get.context!);
                showModalBottomSheet(
                  context: Get.context!,
                  isScrollControlled: true,
                  // Ensures the bottom sheet resizes with the keyboard
                  backgroundColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(40)),
                  ),
                  builder: (context) {
                    return StatefulBuilder(
                      builder: (context, setState) {
                        return FollowingSheet(
                          index: 0,
                          forFollowersCount:
                              "${CurrentUser.user.followingsCount}",
                          searchController: Get.find<ProfileViewController>()
                              .searchController
                              .value,
                          isLoading:
                              Get.find<ProfileViewController>().isLoading,
                          followersList:
                              Get.find<ProfileViewController>().followersList,
                          onClearSearch: () {
                            print("onclick");
                            Get.find<ProfileViewController>()
                                .searchController
                                .value
                                .clear();
                            Get.find<ProfileViewController>()
                                .callApiForFollowingData(context: Get.context!);
                          },
                          onSearchChanged: (text) {
                            if (text.trim().isNotEmpty) {
                              Get.find<ProfileViewController>()
                                  .callApiForFollowingData(
                                      context: Get.context!);
                            }
                          },
                          onFollowToggle: (user, index) {
                            user?.isFollowing?.value = true;
                            Get.find<ProfileViewController>()
                                .callApiForFollowUser(
                                    context: Get.context!,
                                    userId: Get.find<ProfileViewController>()
                                        .followersList[index]
                                        .followingUser
                                        ?.id);
                          },
                          onUnfollowConfirm: (user, index) {
                            Get.find<ProfileViewController>()
                                .callApiForUnFollowUser(
                                    context: Get.context!,
                                    userId: Get.find<ProfileViewController>()
                                        .followersList[index]
                                        .followingUser
                                        ?.id,
                                    isUnFollowed: true,
                                    index: index);
                          },
                        );
                      },
                    );
                  },
                ).then(
                  (value) {
                    CurrentUser.getMe();
                  },
                );
              },
              child: TypoGraphy(
                  text: "${CurrentUser.user.followingsCount} following",
                  level: 4,
                  fontWeight: FontWeight.w700),
            ),
          ],
        ),
        Space.height(10),
        seeMore(
          text: CurrentUser.user.about,
          textAlign: TextAlign.start,
          maxLines: 2,
          fontSize: MySize.getScaledSizeWidth(15),
          fontWeight: FontWeight.w400,
        ),
        // Space.height(18),
      ],
    ),
  );
}

Widget buildEditProfileButton() {
  return InkWell(
    onTap: () {
      Get.toNamed(Routes.User_Detail, arguments: {"isEditUser": true});
    },
    child: Container(
      height: MySize.getScaledSizeHeight(34),
      width: MySize.getScaledSizeWidth(109),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        border: Border.all(width: 1, color: AppTheme.grey),
      ),
      alignment: Alignment.center,
      child: TypoGraphy(
        text: "Edit Profile",
        textStyle: TextStyle(
            fontSize: MySize.getScaledSizeWidth(15),
            fontWeight: FontWeight.w600,
            fontFamily: "Inter"),
      ),
    ),
  );
}

Widget buildStoryHighlightSection({
  required List<Highlight> highlightList,
  required double fontSize,
  double paddingLeft = 30,
  double? spacing,
  required BuildContext context,
  int? projectId,
  int? subProjectId,
  int? userId,
  bool isLoading = true,
}) {
  return isLoading
      ? Container(
          height: MySize.getScaledSizeHeight(100),
          alignment: Alignment.centerLeft,
          child: ListView.builder(
            padding: EdgeInsets.only(
              left: MySize.getScaledSizeWidth(30),
            ),
            shrinkWrap: true,
            itemCount: 5,
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            itemBuilder: (context, index) {
              return Shimmer.fromColors(
                baseColor: AppTheme.baseShimmer,
                highlightColor: AppTheme.highlightShimmer,
                child: Column(
                  children: [
                    Shimmer.fromColors(
                      baseColor: AppTheme.baseShimmer,
                      highlightColor: AppTheme.highlightShimmer,
                      child: Container(
                        margin: EdgeInsets.only(
                          right: MySize.getScaledSizeWidth(12),
                        ),
                        height: MySize.getScaledSizeHeight(70),
                        width: MySize.getScaledSizeHeight(70),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey[400],
                        ),
                      ),
                    ),
                    Space.height(10),
                    Shimmer.fromColors(
                      baseColor: AppTheme.baseShimmer,
                      highlightColor: AppTheme.highlightShimmer,
                      child: Container(
                        margin: EdgeInsets.only(
                          right: MySize.getScaledSizeWidth(12),
                        ),
                        height: MySize.getScaledSizeHeight(15),
                        width: MySize.getScaledSizeHeight(60),
                        decoration: BoxDecoration(
                            color: Colors.grey[400],
                            borderRadius: BorderRadius.circular(5)),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        )
      : SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Padding(
            padding: EdgeInsets.only(left: paddingLeft),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (userId == CurrentUser.user.id)
                  noHighlightView(
                      projectId: projectId, subProjectId: subProjectId),
                ...List.generate(highlightList.length, (index) {
                  Highlight highlight = highlightList[index];
                  return AnimatedOnTapButton(
                    onTap: () {
                      Get.toNamed(Routes.all_stories_view, arguments: {
                        'highLightedStoriesList': highlightList,
                        'index': index,
                        'isHighlight': true,
                        "projectId": projectId,
                        "subProjectId": subProjectId
                      });
                    },
                    onLongPress: userId == CurrentUser.user.id
                        ? () {
                            HapticFeedback.lightImpact();
                            ImagePickerBottomSheet.show(
                                context: context,
                                child: Column(
                                  children: [
                                    Space.height(40),
                                    InkWell(
                                      onTap: () {
                                        Navigator.pop(context);
                                        showHighlightDeleteDialogue(
                                            context: context,
                                            highlightId: highlight.id ?? 0);
                                      },
                                      child: Row(
                                        children: [
                                          Space.width(30),
                                          SvgPicture.asset(
                                            AppImage.trashIcon,
                                            height: MySize.size24,
                                            width: MySize.size24,
                                            color: AppTheme.red,
                                          ),
                                          Space.width(20),
                                          TypoGraphy(
                                            text: "Delete Highlight",
                                            level: 5,
                                            color: AppTheme.red,
                                          )
                                        ],
                                      ),
                                    ),
                                    Space.height(40),
                                    InkWell(
                                      onTap: () {
                                        Get.toNamed(Routes.story_cover_selector,
                                            arguments: {
                                              "highlight": highlight,
                                              'isFromHighlight': true,
                                              "projectId": projectId,
                                              "subProjectId": subProjectId
                                            });
                                      },
                                      child: Row(
                                        children: [
                                          Space.width(30),
                                          SvgPicture.asset(
                                            AppImage.editIcon,
                                            height: MySize.size24,
                                            width: MySize.size24,
                                            color: AppTheme.whiteWithBase,
                                          ),
                                          Space.width(20),
                                          TypoGraphy(
                                            text: "Edit Highlight",
                                            level: 5,
                                          )
                                        ],
                                      ),
                                    ),
                                    Space.height(40),
                                  ],
                                ));
                          }
                        : () {},
                    child: Padding(
                      padding: EdgeInsets.only(right: 10),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(
                                MySize.getScaledSizeWidth(100)),
                            child: Container(
                              height: MySize.getScaledSizeHeight(70),
                              width: MySize.getScaledSizeWidth(70),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                  image: NetworkImage(highlight.image ?? ''),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                          Space.height(10),
                          SizedBox(
                            width: MySize.getScaledSizeWidth(70),
                            child: Center(
                              child: TypoGraphy(
                                text: highlight.title ?? '',
                                textStyle: TextStyle(
                                  fontFamily: "Inter",
                                  fontWeight: FontWeight.w500,
                                  fontSize: fontSize,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                })
              ],
            ),
          ),
        );
}

void showHighlightDeleteDialogue(
    {required BuildContext context,
    required int highlightId,
    isInStory = false}) {
  showDialog(
      context: context,
      builder: (context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Dialog(
            insetPadding:
                EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(70)),
            backgroundColor: Color(0xff25282d),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Padding(
              padding: EdgeInsets.all(MySize.getScaledSizeHeight(20))
                  .copyWith(top: MySize.getScaledSizeHeight(30)),
              child: Column(
                spacing: MySize.getScaledSizeHeight(10),
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  TypoGraphy(
                    text: 'Delete this Highlight?',
                    level: 4,
                    fontWeight: FontWeight.w600,
                    color: Color(0xfff2f4f4),
                  ),
                  TypoGraphy(
                    textAlign: TextAlign.center,
                    text:
                        'Are you sure you want to delete this Highlight? Once deleted, it cannot be recovered.',
                    level: 4,
                    fontWeight: FontWeight.w400,
                    color: Color(0xfff2f4f4),
                  ),
                  Space.height(15),
                  InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.pop(context);
                      if (isInStory) {
                        Get.find<StoryViewController>(
                                tag: (Get.arguments['isHighlight'] ?? false)
                                    .toString())
                            .highLightedStoriesList
                            .removeAt(Get.find<StoryViewController>(
                                    tag: (Get.arguments['isHighlight'] ?? false)
                                        .toString())
                                .currentUserIndex
                                .value);
                        Get.find<StoryViewController>(
                                tag: (Get.arguments['isHighlight'] ?? false)
                                    .toString())
                            .update();
                        Navigator.pop(context);
                      }
                      Get.isRegistered<ProfileViewController>()
                          ? Get.find<ProfileViewController>()
                              .callApiForDeleteHighlight(
                                  context: context, highlightId: highlightId)
                          : Get.put<ProfileViewController>(
                                  ProfileViewController())
                              .callApiForDeleteHighlight(
                                  context: context, highlightId: highlightId);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            textAlign: TextAlign.center,
                            text: 'Delete',
                            level: 3,
                            fontWeight: FontWeight.w500,
                            color: Color(0xfff56475),
                          ),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.pop(context);
                      if (isInStory) {
                        Get.find<StoryViewController>(
                                tag: (Get.arguments['isHighlight'] ?? false)
                                    .toString())
                            .isPlaying
                            .value = true;
                        Get.find<StoryViewController>(
                                tag: (Get.arguments['isHighlight'] ?? false)
                                    .toString())
                            .storyController
                            .play();
                        Get.find<StoryViewController>(
                                tag: (Get.arguments['isHighlight'] ?? false)
                                    .toString())
                            .update();
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy(
                            textAlign: TextAlign.center,
                            text: 'Cancel',
                            level: 3,
                            fontWeight: FontWeight.w500,
                            color: Color(0xfff2f4f4),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      });
}

Widget noHighlightView({int? projectId, int? subProjectId}) {
  return GestureDetector(
    onTap: () {
      HapticFeedback.lightImpact();
      Get.toNamed(Routes.story_highlight_selection,
          arguments: {"projectId": projectId, "subProjectId": subProjectId});
    },
    child: Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: MySize.getScaledSizeHeight(70),
            width: MySize.getScaledSizeWidth(70),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: box.read('isDarkMode')
                  ? AppTheme.darkBackground
                  : AppTheme.subPrimary,
            ),
            child: DottedBorder(
              borderType: BorderType.Circle,
              dashPattern: const [6, 4],
              color: AppTheme.primary1,
              borderPadding: EdgeInsets.all(MySize.size1 ?? 1),
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                              color: AppTheme.whiteWithBase,
                              width: MySize.getScaledSizeHeight(1.5))),
                      child: Icon(
                        Icons.add,
                        color: AppTheme.whiteWithBase,
                        size: MySize.getScaledSizeHeight(20),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Space.height(10),
          TypoGraphy(
            text: "Create New",
            textStyle: TextStyle(
              fontFamily: "Inter",
              fontWeight: FontWeight.w500,
              fontSize: MySize.size13 ?? 13,
            ),
            fontWeight: FontWeight.w500,
          )
        ],
      ),
    ),
  );
}

Widget buildPostCategorySection({
  required List userPostList,
  required int currentSelectedIndex,
  required Function(int) onCategorySelected,
  double paddingLeft = 30,
  bool isGlobalSearch = false,
  bool isProjectMembers = false,
  double spacing = 10,
  int totalCount = 0,
}) {
  return Center(
    child: SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: EdgeInsets.only(
            left: paddingLeft, bottom: MySize.getScaledSizeHeight(20)),
        child: Row(
          children: List.generate(userPostList.length, (index) {
            final String category = userPostList[index];

            // Hide "To-dos" and "Files" if projectMembers is not empty
            if ((category == "To Do") && isProjectMembers) {
              return const SizedBox.shrink(); // hidden but index stays
            }
            return Padding(
              padding: EdgeInsets.only(right: spacing),
              child: InkWell(
                onTap: () => onCategorySelected(index),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.getScaledSizeWidth(16) ?? 16,
                    vertical: MySize.getScaledSizeHeight(12.5) ?? 12,
                  ),
                  decoration: BoxDecoration(
                    color: currentSelectedIndex == index
                        ? AppTheme.primary1
                        : box.read('isDarkMode')
                            ? Color(0xFF2F3136)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: currentSelectedIndex == index
                          ? AppTheme.primary1
                          : AppTheme.grey.withOpacity(0.2),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Row(
                    children: [
                      TypoGraphy(
                        text: userPostList[index],
                        level: 4,
                        fontWeight: FontWeight.w500,
                        color: currentSelectedIndex == index
                            ? AppTheme.white
                            : AppTheme.grey,
                      ),
                      if (currentSelectedIndex == index &&
                          isGlobalSearch &&
                          totalCount != 0)
                        TypoGraphy(
                          text: " ($totalCount)",
                          level: 4,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.white,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    ),
  );
}
