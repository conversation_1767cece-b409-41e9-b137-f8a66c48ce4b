import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/projects/controllers/projects_controller.dart';
import 'package:incenti_ai/utillites/loader.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_project_model.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/custom_grid_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';

Widget CommonProjectView({
  required ProjectsController controller,
  required Function(int index) onTap,
  Function()? callBack,
  required RxList<Project> listData,
  bool isIllustratorVisible = false,
  bool isSearchWidget = false,
  bool isFollowedVisible = false,
  bool isUser = false,
}) {
  return Obx(
    () => listData.isNotEmpty
            ? CustomScrollView(
                controller: controller.scrollController,
                physics: AlwaysScrollableScrollPhysics(),
                keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                slivers: [
                  CustomProjectGrid(
                    isUser: isUser,
                    projects: listData,
                    onTap: (index) {
                      onTap(index);
                    },
                    callBack: callBack ?? (){},
                  ),

                  // Fix Overflow: Loader inside a SizedBox
                  if (controller.hasMoreData.value)
                    SliverToBoxAdapter(
                      child: SizedBox(
                        height: 60, // Ensures enough space for the loader
                        child: Center(
                          child: Loader(),
                        ),
                      ),
                    ),
                ],
              )
            : isIllustratorVisible && isSearchWidget
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        "assets/images/icon_search.svg",
                        height: MySize.size50,
                        color: AppTheme.whiteWithNull,
                      ),
                      Empty(
                        title: "Search Result Not Found !",
                      ),
                    ],
                  )
                : isIllustratorVisible
                    ? Padding(
                        padding: EdgeInsets.only(top: 30),
                        child: Empty(
                          title: "No Project available!",
                        ),
                      )
                    : Center(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(AppImage.emptyProject),
                            Space.height(30),
                            TypoGraphy(
                              text: isFollowedVisible ? "No project followed yet!" : "Let’s get started, create your project today!",
                              level: 12,
                              textAlign: TextAlign.center,
                            ),
                            Space.height(70),
                          ],
                        ),
                      ),
  );
}
