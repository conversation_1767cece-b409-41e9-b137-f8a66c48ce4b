import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../models/section_base.dart';
import '../../../../../utillites/common_shimmer_effect.dart';
import '../../controllers/profile_view_controller.dart';
import '../custom_user_category_view.dart';

class ProfileBookmarkSection extends SectionBase<ProfileViewController> {
  ProfileBookmarkSection({required super.controller});

  @override
  String get title => 'Bookmarks';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return SizedBox();
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      Obx(() {
        return controller.apiManager.isLoading &&
            controller.BookMarkList.isEmpty
            ? SliverToBoxAdapter(child: ShimmerPostCard())
            : bookMarkPostDataView(context, controller);
      })
    ];
  }

  @override
  void onCategorySelected() {
    controller.BookMarkList.clear();
    controller.callApiForGetBookMarkProject(
        context: Get.context!);
  }
}
