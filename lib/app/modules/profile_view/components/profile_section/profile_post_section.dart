import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../models/section_base.dart';
import '../../../../../utillites/common_shimmer_effect.dart';
import '../../controllers/profile_view_controller.dart';
import '../custom_user_category_view.dart';

class ProfilePostSection extends SectionBase<ProfileViewController> {
  ProfilePostSection({required super.controller});

  @override
  String get title => 'Posts';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return SizedBox();
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      Obx(() {
        return controller.isGetPostLoading.value &&
            controller.postDataList.isEmpty
            ? SliverToBoxAdapter(child: ShimmerPostCard())
            : postDataView(context, controller);
      })
    ];
  }

  @override
  Future<void> onCategorySelected() async {
    controller.page.value = 1;
    controller.hasMoreData.value = true;
    controller.postDataList.clear();
    await controller.callApiForUserGetPost(
      context: Get.context!,
    );
  }
}
