import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../models/section_base.dart';
import '../../../../../utillites/common_shimmer_effect.dart';
import '../../controllers/profile_view_controller.dart';
import '../custom_user_category_view.dart';

class ProfileDraftSection extends SectionBase<ProfileViewController> {
  ProfileDraftSection({required super.controller});

  @override
  String get title => 'Drafts';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return SizedBox();
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      Obx(() {
        return controller.apiManager.isLoading &&
            controller.draftDataList.isEmpty
            ? SliverToBoxAdapter(child: ShimmerPostCard())
            : draftPostDataView(context, controller);
      })
    ];
  }

  @override
  Future<void> onCategorySelected() async {
    controller.page.value = 1;
    controller.hasMoreData.value = true;
    controller.draftDataList.clear();
    await controller.callApiForUserGetPost(
      context: Get.context!,
      isPublished: controller.currentSelectedIndex.value == 2,
    );
  }
}
