import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/profile_view/components/remover_follower.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../models/app_followers_model.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/loader.dart';
import '../../../routes/app_pages.dart';

class FollowingSheet extends StatelessWidget {
  final int index;
  final String forFollowersCount;

  // final bool isForFollowers;
  final TextEditingController searchController;
  final RxList<FollowersData> followersList;
  final VoidCallback onClearSearch;
  final Function(String) onSearchChanged;
  final Function(FollowersUser? user, int index) onFollowToggle;
  final Function(FollowersUser? user, int index) onUnfollowConfirm;
  final RxBool isLoading;

  // final ProfileViewController controller;

  const FollowingSheet(
      {super.key,
      // required this.controller,
      required this.index,
      // required this.isForFollowers,
      required this.forFollowersCount,
      required this.searchController,
      required this.followersList,
      required this.onClearSearch,
      required this.onSearchChanged,
      required this.onFollowToggle,
      required this.onUnfollowConfirm,
      required this.isLoading});

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: FractionallySizedBox(
        heightFactor: 0.85, // Adjust height as needed
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Color(0xFF181a20)
                : AppTheme.white,
            border: Border.all(color: AppTheme.borderColor),
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40), topRight: Radius.circular(40)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                Space.height(8),
                Container(
                  height: MySize.size5,
                  width: MySize.size34,
                  decoration: BoxDecoration(
                    color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
                Space.height(20),
                TypoGraphy(
                  text: "$forFollowersCount Following",
                  level: 11,
                ),
                Space.height(20),
                if (forFollowersCount != "0")
                  ValueListenableBuilder(
                    valueListenable: searchController,
                    builder: (context, value, child) {
                      return AppTextField(
                        controller: searchController,
                        padding: EdgeInsets.only(
                          top: MySize.size12 ?? 12,
                          bottom: MySize.size10 ?? 12,
                        ),
                        // onChangedValue: (text) {
                        //   if ((text?.trim() ?? "").isNotEmpty) {
                        //     controller.callApiForFollowingData(
                        //         context: Get.context!);
                        //   }
                        // },
                        onChangedValue: (text) => onSearchChanged(text ?? ""),
                        height: MySize.getScaledSizeHeight(50),
                        prefixIcon: Padding(
                          padding: EdgeInsets.all(4),
                          child: SvgPicture.asset(
                            AppImage.searchIcon,
                            height: MySize.size24,
                            width: MySize.size24,
                            color: AppTheme.grey,
                          ),
                        ),
                        suffixIcon: (searchController.text.isNotEmpty)
                            ? GestureDetector(
                                // onTap: () {
                                //   controller.searchController.value.clear();
                                //   controller.callApiForFollowingData(
                                //       context: Get.context!);
                                // },
                                onTap: () => onClearSearch(),
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      right: MySize.size15 ?? 20),
                                  child: SvgPicture.asset(
                                    AppImage.textFieldClear,
                                    // height: MySize.size20,
                                    // width: MySize.size20,
                                    color: AppTheme.grey,
                                  ),
                                ),
                              )
                            : null,
                        hintText: "Search",
                        hintStyle: TextStyle(
                          color: AppTheme.grey,
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w400,
                        ),
                      );
                    },
                  ),
                if (forFollowersCount != "0") Space.height(20),
                Expanded(
                    child: Obx(
                  () => isLoading.value
                      ? Loader()
                      : searchController.text.isNotEmpty &&
                              followersList.isEmpty
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                TypoGraphy(
                                  text: "No user found",
                                  level: 5,
                                ),
                                Space.height(5),
                                TypoGraphy(
                                  text: "Search Result Not Found !",
                                  level: 3,
                                  color: AppTheme.grey,
                                )
                              ],
                            )
                          : followersList.isNotEmpty
                              ? ListView.separated(
                                  separatorBuilder: (context, index) {
                                    return Space.height(10);
                                  },
                                  itemCount: followersList.length,
                                  itemBuilder: (context, index) {
                                    final FollowersUser? followersListData =
                                        followersList[index].followingUser;
                                    return InkWell(
                                      onTap: () {
                                        if(followersListData?.id == CurrentUser.user.id) {
                                          Get.toNamed(Routes.profile);
                                        } else {
                                          Get.offAndToNamed(
                                              Routes.other_user_profile,
                                              arguments: {
                                                "UserId": followersListData?.id
                                              });
                                        }
                                      },
                                      child: ListTile(
                                        contentPadding: EdgeInsets.zero,
                                        leading: profileImage(
                                          url: followersListData?.image ?? "",
                                          userName:
                                              followersListData?.firstName ?? "",
                                          iconHeight:
                                              MySize.getScaledSizeHeight(60),
                                          iconWidth:
                                              MySize.getScaledSizeWidth(60),
                                          height: MySize.getScaledSizeHeight(60),
                                          width: MySize.getScaledSizeWidth(60),
                                        ),
                                        title: TypoGraphy(
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          text:
                                              "${followersListData?.firstName} ${followersListData?.lastName} ${followersListData?.id != CurrentUser.user.id ? "" : "( You )"}",
                                          level: 12,
                                          // color: AppTheme.baseBlack,
                                        ),
                                        // subtitle: TypoGraphy(
                                        //   maxLines: 1,
                                        //   overflow: TextOverflow.ellipsis,
                                        //   text: "${followersListData?.email}",
                                        //   level: 2,
                                        //   color: AppTheme.grey,
                                        // ),
                                        trailing: followersListData?.id != CurrentUser.user.id ? Obx(
                                          () => InkWell(
                                            onTap: () {
                                              if (followersListData
                                                      ?.isFollowing?.value ??
                                                  false) {
                                                showModalBottomSheet(
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  context: context,
                                                  builder: (context) {
                                                    return RemoveFollower(
                                                      userName:
                                                          "${followersListData?.firstName} ${followersListData?.lastName}",
                                                      image: followersListData
                                                              ?.image ??
                                                          "",
                                                      onRemove: () =>
                                                          onUnfollowConfirm(
                                                              followersListData,
                                                              index),
                                                      // onRemove: () {
                                                      //   controller
                                                      //       .callApiForUnFollowUser(
                                                      //           context:
                                                      //               Get.context!,
                                                      //           userId: controller
                                                      //               .followersList[
                                                      //                   index]
                                                      //               .followingUser
                                                      //               ?.id,
                                                      //           isUnFollowed:
                                                      //               true,
                                                      //           index: index);
                                                      // },
                                                      isForUnfollow: true,
                                                    );
                                                  },
                                                );
                                              } else {
                                                onFollowToggle(
                                                    followersListData, index);
                                                // followersListData
                                                //     ?.isFollowing?.value = false;
                                                // controller.callApiForFollowUser(
                                                //     context: Get.context!,
                                                //     userId: controller
                                                //         .followersList[index]
                                                //         .followingUser
                                                //         ?.id);
                                              }
                                            },
                                            child: Container(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(50),
                                                color: (Theme.of(context)
                                                    .brightness ==
                                                    Brightness
                                                        .dark &&
                                                    !(followersListData
                                                        ?.isFollowing
                                                        ?.value ??
                                                        false))
                                                    ? AppTheme.bottomBar
                                                    : Colors.transparent,
                                                border: Border.all(
                                                  color: (followersListData
                                                              ?.isFollowing
                                                              ?.value ?? false)
                                                      ? AppTheme
                                                          .grey // Border grey when text is "Unfollow"
                                                      : Theme.of(context)
                                                      .brightness ==
                                                      Brightness
                                                          .dark
                                                      ? AppTheme.grey
                                                      : AppTheme
                                                      .primary1, // Otherwise, primary color
                                                ),
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal:
                                                        MySize.getScaledSizeWidth(
                                                            17),
                                                    vertical: MySize
                                                        .getScaledSizeHeight(11)),
                                                child: TypoGraphy(
                                                  text: followersListData
                                                              ?.isFollowing
                                                              ?.value ??
                                                          false
                                                      ? "Unfollow"
                                                      : "Follow",
                                                  textStyle: TextStyle(
                                                      fontFamily: "Inter",
                                                      color: (followersListData
                                                                  ?.isFollowing
                                                                  ?.value ??
                                                              false)
                                                          ? AppTheme.grey
                                                          : AppTheme.primaryIconDark,
                                                      fontSize: MySize
                                                          .getScaledSizeHeight(
                                                              15),
                                                      fontWeight:
                                                          FontWeight.w600),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ) : SizedBox(),
                                      ),
                                    );
                                  },
                                )
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    TypoGraphy(
                                      text: "No Following",
                                      level: 5,
                                    ),
                                    Space.height(5),
                                    TypoGraphy(
                                      text: "No following yet!",
                                      level: 3,
                                      color: AppTheme.grey,
                                    )
                                  ],
                                ),
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
