import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/typography.dart';

class RemoveFollower extends StatelessWidget {
  final String userName;
  final String image;
  final bool isForUnfollow;
  final void Function() onRemove;

  const RemoveFollower(
      {super.key,
        required this.userName,
        required this.image,
        required this.onRemove,
        required this.isForUnfollow});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.size12 ?? 12),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
                color: AppTheme.baseBlack,
                borderRadius: BorderRadius.circular(20)),
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: MySize.size20 ?? 20,
                  vertical: MySize.size20 ?? 20),
              child: Column(
                children: [
                  profileImage(
                    strokeWidth: 0,
                    borderColor: Colors.transparent,
                    url: image,
                    userName: userName,
                    iconHeight: MySize.getScaledSizeHeight(60),
                    iconWidth: MySize.getScaledSizeWidth(60),
                    height: MySize.getScaledSizeHeight(60),
                    width: MySize.getScaledSizeWidth(60),
                  ),
                  Space.height(10),
                  if (!isForUnfollow)
                    TypoGraphy(
                      text: "Remove follower?",
                      level: 4,
                      color: Colors.white,
                    ),
                  if (!isForUnfollow) Space.height(7),
                  SizedBox(
                    width: MySize.getScaledSizeWidth(320),
                    child: TypoGraphy(
                      maxLines: 2,
                      textAlign: TextAlign.center,
                      text: isForUnfollow
                          ? "if you change your mind, you'll have to request to follow $userName again!"
                          : "We won't tell $userName that they were removed from your followers",
                      level: 3,
                      color: isForUnfollow ? AppTheme.white :AppTheme.grey,
                    ),
                  ),
                  Space.height(35),
                  InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      onRemove();
                    },
                    child: TypoGraphy(
                      text: isForUnfollow ? "Unfollow" : "Remove",
                      color: AppTheme.red,
                      level: 4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Space.height(10),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.size12 ?? 12),
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  color: AppTheme.baseBlack,
                  borderRadius: BorderRadius.circular(20)),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: MySize.size20 ?? 20),
                child: Center(
                  child: TypoGraphy(
                    text: "Cancel",
                    level: 4,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
        Space.height(10),
      ],
    );
  }
}