import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:flutter/material.dart';
import 'package:incenti_ai/models/get_all_highlights_model.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:dio/dio.dart';
import '../../../../constants/api.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/constant.dart';
import '../../../../main.dart';
import '../../../../models/app_bookmark_model.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_followers_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/app_project_model.dart';
import '../../../../models/post_like_model.dart';
import '../../../../models/section_base.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/common_function.dart';
import '../../project_detail_view/controllers/project_detail_view_controller.dart';
import '../../projects/controllers/projects_controller.dart';
import '../../sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';
import '../components/profile_section/profile_bookmark_section.dart';
import '../components/profile_section/profile_draft_section.dart';
import '../components/profile_section/profile_post_section.dart';
import '../components/profile_section/profile_project_section.dart';

class ProfileViewController extends GetxController with WidgetsBindingObserver {
  DateTime? _backgroundTime;
  RxInt currentSelectedIndex = 0.obs;
  RxBool isLoading = false.obs;
  RxBool isGetPostLoading = false.obs;
  RxBool isHighlightLoading = false.obs;
  RxList<Post> postDataList = <Post>[].obs;
  RxList<Post> draftDataList = <Post>[].obs;
  RxList<Post> BookMarkList = <Post>[].obs;
  List<SectionBase> sections = [];
  int limit = 20;
  RxBool isProjectLoading = false.obs;
  RxInt page = 1.obs;
  RxBool hasMoreData = true.obs;
  ApiManager apiManager = ApiManager();
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;
  RxList<Comment> commentDataList = <Comment>[].obs;
  TextEditingController commentController = TextEditingController();
  FocusNode commentFocusNode = FocusNode();
  Rx<File?> selectedImage = Rx<File?>(null);
  RxString oldImageUrl = "".obs;
  Timer? debounceTimer;
  Rx<TextEditingController> searchController = TextEditingController().obs;
  RxList<FollowersData> followersList = <FollowersData>[].obs;
  RxInt currentTabIndex = 0.obs;
  RxBool isFollowing = false.obs;
  RxBool isButtonVisible = true.obs;
  RxList<Highlight> highlights = <Highlight>[].obs;
  RxString selectedReason = "".obs;
  var args = Get.arguments;
  RxList<Project> createdProject = <Project>[].obs;
  final ScrollController scrollController = ScrollController();

  void onScrollListener(void Function() onScroll, ScrollController controller) {
    if (!hasMoreData.value) return; // Stop calling API if no more data

    final maxScroll = controller.position.maxScrollExtent;
    if (maxScroll == controller.offset) onScroll();
  }

  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    callApiForGetHighlightsOfProfile(context: Get.context!);
    if (Get.isRegistered<ProjectsController>()) {
      Get.find<ProjectsController>()
          .callApiForProjectData(context: Get.context!);
    }
    callApiForUserGetPost(context: Get.context!);
    scrollController.addListener(
          () => onScrollListener(() {
        page.value++;
        fetchProjectData(
          context: Get.context!,);
      }, scrollController),
    );
    sections = [
      ProfilePostSection(controller: this),
      ProfileProjectSection(controller: this),
      ProfileDraftSection(controller: this),
      ProfileBookmarkSection(controller: this),
    ];
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      _backgroundTime = DateTime.now();
    } else if (state == AppLifecycleState.resumed) {
      if (_backgroundTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_backgroundTime!);
        if (difference.inMinutes >= 3) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            scrollController.animateTo(0, duration: Duration(seconds: 1), curve: Curves.easeInOut).then((value) =>  pullRefresh(),);
          });
        }
      }
    }
  }


  callApiForReportProject(
      {required BuildContext context, required String projectId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-projects/project/$projectId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response == > $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            page.value = 1;
            hasMoreData.value = true;
            createdProject.clear();
            fetchProjectData(
              context: Get.context!,
            );
          }
        } catch (error) {
          log("error: $message");
        }
      },
      failureCallback: (message, statusCode) {
        log("error: $message");
      },
    );
  }

  RxList iconData = [
    {"iconPath": AppImage.postIcon, "label": "Write"},
    {"iconPath": AppImage.storyIcon, "label": "Story"},
    {"iconPath": AppImage.todosIcon, "label": "To Do"},
    {"iconPath": AppImage.projectIcon, "label": "Project"},
  ].obs;

  Future<void> callApiForDeleteOneProject(
      {required BuildContext context, String? projectId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(
              message: "Delete Project Successfully",
            );
            fetchProjectData(context: Get.context!);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForHideProject(
      {required BuildContext context, String? projectId,required bool isHide,required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/projects/$projectId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      params: {"hide": isHide},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            createdProject[index].hide =
            !(createdProject[index].hide ?? false);
            Get.back();
            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  fetchProjectData({required BuildContext context}) {
    if (!hasMoreData.value) return;

    isProjectLoading.value = true;

    apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {
        "UserId": CurrentUser.user.id,
        'page': page.value,
        'limit': limit,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            ProjectResponse projectResponse =
            ProjectResponse.fromJson(response);

            if (projectResponse.data.data.length < limit) {
              hasMoreData.value = false; // No more pages left
            }
            createdProject.addAll(projectResponse.data.data);
            isProjectLoading.value = false;
          }
        } catch (error) {
          isProjectLoading.value = false;
          hasMoreData.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isProjectLoading.value = false;

        hasMoreData.value = false;
      },
    );
  }

  callApiForFollowersData({required BuildContext context}) {
    if (searchController.value.text.isNotEmpty) {
      // Cancel previous timer if exists
      debounceTimer?.cancel();

      // Start new debounce timer (500ms delay) for searching
      debounceTimer = Timer(Duration(milliseconds: 500), () {
        fetchFollowersData(context);
      });
    } else {
      fetchFollowersData(context);
    }
  }

  pullRefresh() async {
    callApiForGetHighlightsOfProfile(context: Get.context!);
    if (currentSelectedIndex.value == 1) {
      if (!Get.isRegistered<ProjectsController>()) {
        Get.put(ProjectsController());
      }
      Get.find<ProjectsController>()
          .selectedTabIndex
          .value = 0;
      Get.find<ProjectsController>().page.value = 1;
      Get.find<ProjectsController>().hasMoreData.value =
      true;
      Get.find<ProjectsController>().createdProject.clear();
      Get.find<ProjectsController>().callApiForProjectData(
        context: Get.context!,
        isSelected: true,
      );
    } else if (currentSelectedIndex.value == 3) {
      BookMarkList.clear();
      callApiForGetBookMarkProject(
          context: Get.context!);
    } else if (currentSelectedIndex.value == 2) {
      page.value = 1;
      hasMoreData.value = true;
      draftDataList.clear();
      await callApiForUserGetPost(
        context: Get.context!,
        isPublished: currentSelectedIndex.value == 2,
      );
    } else if (currentSelectedIndex.value == 0) {
      page.value = 1;
      hasMoreData.value = true;
      postDataList.clear();
      await callApiForUserGetPost(
        context: Get.context!,
      );
    }
  }

  callApiForReportPost(
      {required BuildContext context, required String postId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-posts/post/$postId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            BookMarkList.clear();
            callApiForGetBookMarkProject(context: context);
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
      },
    );
  }

  void fetchFollowersData(BuildContext context) {
    isLoading.value = true;

    apiManager.callApi(
      APIS.followers.getFollowers,
      params: {
        "UserId": CurrentUser.user.id,
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            FollowersResponse followersResponse =
                FollowersResponse.fromJson(response, isFollower: true);
            followersList.assignAll(followersResponse.data?.data ?? []);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForFollowingData({required BuildContext context}) {
    if (searchController.value.text.isNotEmpty) {
      // Cancel previous timer if exists
      debounceTimer?.cancel();

      // Start new debounce timer (500ms delay) for searching
      debounceTimer = Timer(Duration(milliseconds: 500), () {
        fetchFollowingData(context);
      });
    } else {
      fetchFollowingData(context);
    }
  }

  void fetchFollowingData(BuildContext context) {
    isLoading.value = true;

    apiManager.callApi(
      APIS.followers.getFollowings,
      params: {
        "UserId": CurrentUser.user.id,
        if (searchController.value.text.isNotEmpty)
          "searchQuery": searchController.value.text,
      },
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            FollowersResponse followingResponse =
                FollowersResponse.fromJson(response, isFollower: false);
            followersList.assignAll(followingResponse.data?.data ?? []);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForUserData({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    Map<String, dynamic> dict = {};

    // Check conditions for image
    if (oldImageUrl.value.isEmpty) {
      dict["backgroundImage"] =
          null; // Pass null if no image is selected or stored
    } else {
      dict["backgroundImage"] = oldImageUrl.value; // Pass existing image URL
    }
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.user.updateMe,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CurrentUser.getMe(
              callback: () async {
                // if (args != null &&
                //     args["isEditUser"] != null) {
                //   Get.back();
                // } else {
                //   Get.offAllNamed(Routes.Bottom_Bar,
                //       arguments: {"isFirstTime": true});
                // }
              },
            );
            isLoading.value = false;
          } else {
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForFollowUser(
      {required BuildContext context, required int? userId}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/friends/follow/user/$userId", APIType.POST);
    isFollowing.value = true;
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = true;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowing.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = false;
      },
    );
  }

  Future<void> callApiForUnFollowUser(
      {required BuildContext context,
      required int? userId,
      required bool isUnFollowed,
      required int index}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
        ApiModel("/friends/user/$userId", APIType.DELETE);
    isFollowing.value = false;
    return apiManager.callApi(
      updatePost,
      params: {if (isUnFollowed) "unfollow": 1},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            if (!isUnFollowed) {
              followersList.removeAt(index);
              Navigator.pop(context);
            } else {
              followersList[index].followingUser?.isFollowing?.value = false;
              Navigator.pop(context);
            }
          }
        } catch (error) {
          isFollowing.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = true;
      },
    );
  }

  Future<void> callApiForDeleteOnePost(
      {required BuildContext context, int? postId, bool isDraft = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/posts/$postId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            postDataList.clear();
            update();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForUserGetPost(
      {required BuildContext context, bool isPublished = false}) {
    FocusScope.of(context).unfocus();
    isGetPostLoading.value = true;
    // String? lastCreatedAt =
    //     (isPublished ? draftDataList : postDataList).isNotEmpty
    //         ? (isPublished ? draftDataList : postDataList)
    //             .last
    //             .createdAt
    //             ?.toIso8601String() // Convert DateTime to String
    //         : null;
    return apiManager.callApi(
      APIS.post.getAllUsePost,
      params: {
        "limit": limit,
        "page": page.value,
        "UserId": CurrentUser.user.id,
        if (isPublished) "isPublished": 0
      },
      successCallback: (response, message) async {
        // try {
          if (response['status'] == 'success') {

            PostResponse postResponse = PostResponse.fromJson(response);

            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (postResponse.data.data.isNotEmpty) {
              if (isPublished) {
                final existingIds = draftDataList.map((e) => e.id).toSet();

                final newItems = postResponse.data.data.where((item) => !existingIds.contains(item.id));
                draftDataList.addAll(newItems);
              } else {
                final existingIds = postDataList.map((e) => e.id).toSet();

                final newItems = postResponse.data.data.where((item) => !existingIds.contains(item.id));
                postDataList.addAll(newItems);
              }
              isGetPostLoading.value = false;
              page.value++;
            } else {
              isGetPostLoading.value = false;
            }
            log("response === $response");
          }
        // }
        // catch (error) {
        //   isGetPostLoading.value = false;
        //   hasMoreData.value = false;
        //   log("error ==> $error");
        // }
      },
      failureCallback: (message, statusCode) {
        isGetPostLoading.value = false;
        hasMoreData.value = false;
      },
    );
  }

  Future<void> callApiForPinPost({required BuildContext context, int? postId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/posts/pin/$postId", APIType.PATCH);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForBookMarkProject(
      {required BuildContext context,
      String? postId,
      required int index,
      bool isBookmark = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    if (isBookmark) {
      BookMarkList[index].isBookMarked?.value =
          !(BookMarkList[index].isBookMarked?.value ?? false);
    } else {
      postDataList[index].isBookMarked?.value =
          !(postDataList[index].isBookMarked?.value ?? false);
    }
    final ApiModel getPost = ApiModel("/bookmarks/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isBookMarked = response["post"]["isBookMarked"] == "0" ? false : true;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForLikeProject(
      {required BuildContext context,
      String? postId,
      required int index,
      isBookmark = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    if (isBookmark) {
      BookMarkList[index].isLiked?.value =
          !(BookMarkList[index].isLiked?.value ?? false);
      if (BookMarkList[index].isLiked?.value == true) {
        BookMarkList[index].likesCount?.value++;
      } else {
        BookMarkList[index].likesCount?.value--;
      }
    } else {
      postDataList[index].isLiked?.value =
          !(postDataList[index].isLiked?.value ?? false);
      if (postDataList[index].isLiked?.value == true) {
        postDataList[index].likesCount?.value++;
      } else {
        postDataList[index].likesCount?.value--;
      }
    }

    final ApiModel getPost = ApiModel("/post-likes/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isLiked = (response["post"]["isLiked"] == "0" ? false : true).obs;
            // postDataList[index].likesCount = int.parse(response["post"]["likesCount"] ?? "0");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForCommentProject(
      {required BuildContext context,
      String? postId,
      required int index,
      bool isBookMark = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");

            isLoading.value = false;

            Comment newComment;

            if (response['data'] != null) {
              newComment = Comment.fromJson(response['data']);
            } else {
              newComment = Comment(
                id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
                comment: commentController.value.text.trim(),
                createdAt: DateTime.now(),
                user: CurrentUser.user,
              );
            }

            List<Comment> updatedComments = [newComment, ...commentDataList];
            commentDataList.value = updatedComments;

            if (isBookMark) {
              BookMarkList[index].commentsCount?.value = commentDataList.length;
            } else {
              postDataList[index].commentsCount?.value = commentDataList.length;
            }
          }
        } catch (error) {
          log("Error in callApiForCommentProject: $error");
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetCommentProject(
      {required BuildContext context,
      String? postId,
      required int index,
      bool isBookMark = false}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
        ApiModel("/post-comments/post/$postId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse =
                CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            if (isBookMark) {
              BookMarkList[index].commentsCount?.value = commentDataList.length;
            } else {
              postDataList[index].commentsCount?.value = commentDataList.length;
            }
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetBookMarkProject({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;

    return apiManager.callApi(
      APIS.bookMark.get,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            BookMarkResponse postResponse = BookMarkResponse.fromJson(response);
            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (postResponse.data.data.isNotEmpty) {
              final existingIds = BookMarkList.map((e) => e.id).toSet();

              final newItems = postResponse.data.data.where((item) => !existingIds.contains(item.id));
              BookMarkList.addAll(newItems);
              isLoading.value = false;
            } else {
              return;
            }
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForUserImage(
      {required BuildContext context, required String imageFile}) async {
    try {
      Map<String, dynamic> dict = {
        "file": await MultipartFile.fromFile(imageFile,
            filename: imageFile.split('/').last.trim())
      };

      FormData formData = FormData.fromMap(dict);
      app.resolve<CustomDialogs>().showCircularDialog(context);

      apiManager.callApi(
        APIS.generalImageUpload.imageUpload,
        params: formData,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            String imageUrl =
                response['data'][0]['link']; // Extract URL from response
            oldImageUrl.value = imageUrl;
            callApiForUserData(context: Get.context!).then((value) {
              CommonFunction.showCustomSnackbar(
                  message: "Cover Image Updated Successfully");
            },);
            // CurrentUser.user.backgroundImage = oldImageUrl.value;
            app.resolve<CustomDialogs>().hideCircularDialog(context);
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          app.resolve<CustomDialogs>().hideCircularDialog(context);
        },
      );
    } catch (e) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log("Exception: $e");
    }
  }

  Future<void> callApiForGetHighlightsOfProfile(
      {required BuildContext context}) async {
    isHighlightLoading.value = true;

    try {
      await apiManager.callApi(
        CommonFunction.prepareApi(APIS.story.getHighlights, {'id': ''}),
        successCallback: (response, message) async {
          try {
            AllHighLightsModel allHighLightsModel =
                AllHighLightsModel.fromJson(response);
            highlights.value  =
                allHighLightsModel.data?.highlights ?? [];
            // List<Future> highlightDetailsFutures = [];
            //
            // for (Highlight highlight in highlightsList) {
            //   if (highlight.id != null) {
            //     highlightDetailsFutures.add(_getHighlightDetails(highlight.id!)
            //         .then((detailedHighlight) {
            //       if (detailedHighlight != null) {
            //         int index = highlightsList
            //             .indexWhere((h) => h.id == detailedHighlight.id);
            //         if (index != -1) {
            //           highlightsList[index] = detailedHighlight;
            //         }
            //       }
            //     }));
            //   }
            // }
            //
            // await Future.wait(highlightDetailsFutures);

            // highlights.value = highlightsList;
            isHighlightLoading.value = false;
          } catch (error) {
            isHighlightLoading.value = false;
          }
        },
        failureCallback: (message, statusCode) {
          isHighlightLoading.value = false;
        },
      );
    } catch (e) {
      isHighlightLoading.value = false;
    }
  }

  // Future<Highlight?> _getHighlightDetails(int highlightId) async {
  //   Completer<Highlight?> completer = Completer();
  //
  //   apiManager.callApi(
  //     CommonFunction.prepareApi(
  //         APIS.story.getHighlights, {'id': highlightId.toString()}),
  //     successCallback: (response, message) {
  //       try {
  //         if (response != null &&
  //             response['status'] == 'success' &&
  //             response['data'] != null) {
  //           Highlight detailedHighlight = Highlight.fromJson(response['data']);
  //
  //           completer.complete(detailedHighlight);
  //         } else {
  //           completer.complete(null);
  //         }
  //       } catch (error) {
  //         completer.complete(null);
  //       }
  //     },
  //     failureCallback: (message, statusCode) {
  //       completer.complete(null);
  //     },
  //   );
  //
  //   return completer.future;
  // }

  Future<void> callApiForDeleteHighlight({
    required BuildContext context,
    required int highlightId,
  }) {
    return apiManager.callApi(
      CommonFunction.prepareApi(
          APIS.story.deleteHighlight, {'id': highlightId.toString()}),
      successCallback: (response, message) async {
        try {
          if(Get.isRegistered<SubProjectDetailViewController>()){
            await Get.find<SubProjectDetailViewController>().callApiForGetHighlightsOfSubProject(context: Get.context!);
            Get.find<SubProjectDetailViewController>().update();
          }else if(Get.isRegistered<ProjectDetailViewController>(tag: Get.arguments['projectId'].toString())){
            await Get.find<ProjectDetailViewController>(tag: Get.arguments['projectId'].toString()).callApiForGetHighlightsOfProject(context: Get.context!);
            Get.find<ProjectDetailViewController>(tag: Get.arguments['projectId'].toString()).update();
          }else {
            await Get
                .find<ProfileViewController>()
                .callApiForGetHighlightsOfProfile(context: context);
          }

          CommonFunction.showCustomSnackbar(message: response['message']);
        } catch (error) {
          log('error ==> $error');
        }
      },
      failureCallback: (message, statusCode) {
        log('error in delete highlight ==> $statusCode');
      },
    );
  }
  callApiForGetPostLike({required BuildContext context,required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {"limit": 20, if (lastCreatedAt != null) "createdAt": lastCreatedAt},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;

        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;

    }
  }
}
