
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/network_image.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/custom_scroll.dart';
import '../../../routes/app_pages.dart';
import '../../bottom_bar/views/bottom_bar_view.dart';
import '../../setting/controllers/setting_controller.dart';
import '../../setting/views/setting_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../components/custom_user_category_view.dart';
import '../components/profile_widget_view.dart';
import '../controllers/profile_view_controller.dart';

class ProfileView extends GetWidget<ProfileViewController> {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: customBottomNavigation(),
      body: RefreshIndicator(
        onRefresh: () async {
          controller.pullRefresh();
        },
        child: Column(
          children: [
            Stack(
              children: [
                Obx(
                  () => CurrentUser.user.backgroundImage == ""
                      ? Image.asset(
                          "assets/images/banner.png",
                          width: double.infinity,
                          height: MySize.getScaledSizeHeight(182),
                          fit: BoxFit.fill,
                        )
                      : SizedBox(),
                ),
                Obx(
                  () => CurrentUser.user.backgroundImage != ""
                      ? NetworkImageComponent(
                          imageUrl: CurrentUser.user.backgroundImage,
                          height: MySize.getScaledSizeHeight(182),
                          width: double.infinity,
                          simmerHeight: MySize.getScaledSizeHeight(182),
                        )
                      : SizedBox(),
                ),
                Container(
                  width: double.infinity,
                  height: MySize.getScaledSizeHeight(182),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.black.withOpacity(0.8),
                        AppTheme.black.withOpacity(0),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Space.height(60),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
                      child: Row(
                        children: [
                          InkWell(
                            child: buildIcon(AppImage.backArrow, () {
                              if (CurrentUser.user.customSetting?.homeSection ==
                                  "profile") {
                                Get.offAllNamed(Routes.Bottom_Bar);
                              } else {
                                Get.back();
                              }
                            }, padding: 7),
                          ),
                          Spacer(),
                          buildIcon(AppImage.settingIcon, () {
                            // Get.toNamed(Routes.setting);
                            Get.put(SettingController());
                            Get.bottomSheet(
                              const SettingView(),
                              isScrollControlled: true,
                              // Allows full-screen height if needed
                              backgroundColor: Colors.white,
                              // Ensures background consistency
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(20)),
                              ),
                            );
                          }, padding: 9),
                          Space.width(15),
                          buildIcon(AppImage.moreVertIcon, () {
                            HapticFeedback.lightImpact();
                            ImagePickerBottomSheet.show(
                              isImageAvailable: CurrentUser.user.backgroundImage != "" ||
                                  controller.selectedImage.value != null ||
                                  controller.oldImageUrl.value != ""
                                  ? true
                                  : false,
                              isAspectRatio: true,
                              context: context,
                              isBackCoverPhoto: true,
                              onImagePicked: (pickedImage) {
                                controller.selectedImage.value = pickedImage;
                                controller.callApiForUserImage(
                                    context: context,
                                    imageFile: pickedImage.path);
                              },
                              onRemoveImage: () {
                                controller.selectedImage.value = null;
                                controller.oldImageUrl.value = "";
                                controller.callApiForUserData(context: context).then((value) {
                                  CommonFunction.showCustomSnackbar(message: "Remove Cover Image Successfully");
                                },);
                              },
                            );
                          }, padding: 9),
                        ],
                      ),
                    ),
                    Space.height(4),
                    Padding(
                      padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(30)),
                      child: Obx(
                        () => profileImage(
                          url: CurrentUser.user.image ?? "",
                          userName: CurrentUser.user.firstName ?? "",
                          iconHeight: MySize.size117,
                          iconWidth: MySize.size117,
                          width: MySize.size117 ?? 105,
                          height: MySize.size117 ?? 105,
                          strokeWidth: MySize.size3 ?? 1.0,
                          borderColor: AppTheme.white,
                          backgroundColor: Colors.transparent,
                          textStyle: TextStyle(
                              fontSize: MySize.size65, color: AppTheme.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Expanded(
              child: Obx(
                () => CustomScrollView(
                  controller: controller.currentSelectedIndex.value == 1
                      ? controller.scrollController
                      : null,
                  physics: AlwaysScrollableScrollPhysics(),
                  slivers: [
                    SliverToBoxAdapter(child: buildProfileSection()),
                    SliverToBoxAdapter(child: Space.height(25)),
                    SliverToBoxAdapter(
                      child: buildStoryHighlightSection(
                        context: context,
                        highlightList: controller.highlights,
                        fontSize: MySize.size13 ?? 13, userId: CurrentUser.user.id,
                        isLoading: controller.isHighlightLoading.value,
                      ),
                    ),
                    SliverToBoxAdapter(child: Space.height(35)),
                    SliverToBoxAdapter(
                      child: CustomHorizontalScrollbar(
                        child: buildPostCategorySection(
                          userPostList: controller.sections
                              .map((e) => e.title)
                              .toList(),
                          currentSelectedIndex:
                              controller.currentSelectedIndex.value,
                          onCategorySelected: (index) {
                            controller.currentSelectedIndex.value = index;
                            controller
                                .sections[index].onCategorySelected();
                          },
                        ),
                      ),
                    ),
                    ...controller
                        .sections[controller.currentSelectedIndex.value]
                        .viewBuilder(context)
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Obx(
        () => controller.currentSelectedIndex.value == 0 ||
                controller.currentSelectedIndex.value == 1
            ? floatingAction(context, controller)
            : SizedBox(),
      ),
    );
  }
}
