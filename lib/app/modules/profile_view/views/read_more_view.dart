

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import '../../../../constants/app_size_constant.dart';

class seeMore extends StatefulWidget {
  final String? text;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextStyle? textStyle;
  final FontStyle? fontStyle;
  final TextDecoration? textDecoration;
  final TextAlign? textAlign;
  final Color? color;
  final int? maxLines;
  final TextOverflow? overflow;

  const seeMore({
    super.key,
    this.text,
    this.fontWeight,
    this.fontSize,
    this.textStyle,
    this.fontStyle,
    this.textDecoration,
    this.color,
    this.textAlign,
    this.overflow,
    this.maxLines,
  });

  @override
  _seeMoreState createState() => _seeMoreState();
}

class _seeMoreState extends State<seeMore> {
  bool _isExpanded = false;

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    final String fullText = widget.text ?? "";
    final textStyle = widget.textStyle ??
        TextStyle(
          fontSize: widget.fontSize ?? MySize.getScaledSizeWidth(16),
          fontWeight: widget.fontWeight ?? FontWeight.normal,
          color: widget.color ?? (AppTheme.whiteWithBase),
          fontFamily: "Inter",
        );

    return LayoutBuilder(
      builder: (context, constraints) {
        // Create a TextPainter to calculate if text overflows
        final textPainter = TextPainter(
          text: TextSpan(text: fullText, style: textStyle),
          maxLines: widget.maxLines ?? 2,
          textDirection: TextDirection.ltr,
        );

        textPainter.layout(maxWidth: constraints.maxWidth);
        final bool isOverflowing = textPainter.didExceedMaxLines;

        String visibleText = fullText;
        if (!_isExpanded && isOverflowing) {
          // Approximate the cutoff point
          int cutoffIndex = (fullText.length * 0.5).toInt();
          visibleText = fullText.substring(0, cutoffIndex);
        }

        return RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: (_isExpanded || !isOverflowing) ? fullText : "$visibleText...",
                style: textStyle,
              ),
              if (isOverflowing) ...[
                TextSpan(
                  text: _isExpanded ? "...see less" : " see more",
                  style: TextStyle(
                    fontSize: widget.fontSize ?? MySize.getScaledSizeWidth(16),
                    fontWeight: FontWeight.w700,
                    color: AppTheme.primaryIconDark,
                    fontFamily: "Inter",
                  ),
                  recognizer: TapGestureRecognizer()..onTap = _toggleExpanded,
                ),
              ],
            ],
          ),
          textAlign: widget.textAlign ?? TextAlign.center,
        );
      },
    );
  }

}
