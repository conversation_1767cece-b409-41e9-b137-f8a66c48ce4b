import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../models/app_community_model.dart';
import '../../../../models/section_base.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../communities/components/common_community_caard.dart';
import '../../communities/components/community_caard_shimmer_view.dart';
import '../../communities/controllers/communities_controller.dart';
import '../controllers/global_search_view_controller.dart';

class GlobalSearchCommunitySection extends SectionBase<GlobalSearchViewController> {
  GlobalSearchCommunitySection({required super.controller});

  @override
  String get title => 'Communities';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return SizedBox();
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(
        child: Space.height(20),
      ),
      Obx(
            () {
          return controller.isCommunityLoading.value &&
              controller.communityCreatedList.isEmpty
              ? SliverToBoxAdapter(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              // Disable scrolling inside CustomScrollView
              shrinkWrap: true,
              itemCount: 5,
              itemBuilder: (context, index) =>
                  ShimmerGroupCard(),
            ),
          )
              : controller.searchController.value.text
              .isNotEmpty &&
              controller.communityCreatedList.isEmpty
              ? SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.only(
                  top: MySize.getScaledSizeHeight(250)),
              child: Column(
                mainAxisAlignment:
                MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    "assets/images/icon_search.svg",
                    height: MySize.size50,
                    color: AppTheme.whiteWithNull,
                  ),
                  Empty(
                    title: "Search Result Not Found !",
                  ),
                ],
              ),
            ),
          )
              : CustomSliverListView(
            emptyWidget: Padding(
                padding: EdgeInsets.only(
                  top: MySize.getScaledSizeHeight(100),
                  left: MySize.getScaledSizeHeight(48),
                  right: MySize.getScaledSizeHeight(48),
                ),
                child: Column(
                  children: [
                    SvgPicture.asset(
                        AppImage.noCommunity),
                    Space.height(30),
                    TypoGraphy(
                      text:
                      "No communities created yet.",
                      level: 12,
                    )
                  ],
                )),
            maximumReachedWidget: const SizedBox(),
            itemBuilder: (context,
                CommunityData communityData, index) {
              final selectedCommunityList =
                  controller.communityCreatedList;
              CommunityData res =
              selectedCommunityList[index];
              return Obx(
                    () => InkWell(
                  onTap: () {
                    Get.put(CommunitiesController());
                    Get.find<CommunitiesController>().selectedTabIndex.value = 2;
                    Get.toNamed(Routes.community_detail,
                        arguments: {
                          "communityId": res.id,
                          "index": index
                        })?.then(
                          (value) {
                        controller.page.value = 1;
                        controller.hasMoreData.value =
                        true;
                        controller.isCommunityLoading
                            .value = true;
                        controller.communityCreatedList
                            .clear();
                        controller.fetchCommunityData(
                            context: context);
                      },
                    );
                  },
                  child: CommonGroupCard(
                    isJoined:
                    res.isJoined?.value ?? false,
                    title: res.name ?? "",
                    imageUrl: res.image,
                    membersCount: res.members ?? "0",
                    profileImages: res.communityMembers,
                    onJoinPressed: () {
                      HapticFeedback.lightImpact();
                      controller
                          .callApiForJoinCommunity(
                          context: context,
                          communityId:
                          res.id.toString(),
                          index: index);
                    },
                    selectedTabIndex: 2,
                  ),
                ),
              );
            },
            isLoading:
            controller.isCommunityLoading.value,
            items: controller.communityCreatedList,
            hasMoreData: controller.hasMoreData.value,
            onLoadMore: () {
              return controller.fetchCommunityData(
                context: context,
              );
            },
          );
        },
      ),
    ];
  }

  @override
  void onCategorySelected() {
    controller.communityCreatedList.clear();
    controller.page.value = 1;
    controller.hasMoreData.value = true;
    controller.fetchCommunityData(context: Get.context!);
  }
}
