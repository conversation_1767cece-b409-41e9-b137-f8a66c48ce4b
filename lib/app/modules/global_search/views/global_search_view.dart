import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import '../../../../constants/app_image.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/custom_scroll.dart';
import '../../profile_view/components/profile_widget_view.dart';
import '../controllers/global_search_view_controller.dart';

class GlobalSearchView extends StatefulWidget {
  const GlobalSearchView({super.key});

  @override
  State<GlobalSearchView> createState() => _GlobalSearchViewState();
}

class _GlobalSearchViewState extends State<GlobalSearchView> {
  final GlobalSearchViewController controller =
      Get.put(GlobalSearchViewController());

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      // backgroundColor: AppTheme.white,
      body: Column(
        children: [
          Space.height(60),
          Row(
            children: [
              Space.width(30),
              InkWell(
                onTap: () {
                  Get.back();
                },
                child: SvgPicture.asset(AppImage.backArrow,color: AppTheme.whiteWithNull,),
              ),
              Space.width(15),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(right: MySize.size30 ?? 30),
                  child: ValueListenableBuilder(
                    valueListenable: controller.searchController.value,
                    builder: (context, value, child) {
                      return AppTextField(
                        controller: controller.searchController.value,
                        focusNode: controller.searchFocusNode,
                        padding: EdgeInsets.only(
                          top: MySize.size12 ?? 12,
                          bottom: MySize.size10 ?? 12,
                        ),
                        onChangedValue: (text) {
                          if((text?.trim() ?? "").isNotEmpty) {
                            controller.debounceTimer?.cancel();
                            controller.debounceTimer =
                                Timer(Duration(milliseconds: 500), () {
                                  if (controller.currentSelectedIndex.value == 0) {
                                    controller.page.value = 1;
                                    controller.hasMoreData.value = true;
                                    controller.postDataList.clear();
                                    controller.callApiForExplorePost(
                                        context: context);
                                  } else if (controller.currentSelectedIndex.value ==
                                      2) {
                                    controller.page.value = 1;
                                    controller.projectUserList.clear();
                                    controller.hasMoreData.value = true;
                                    controller.callApiForGetTeamUser(
                                        context: context);
                                  } else if (controller.currentSelectedIndex.value ==
                                      3) {
                                    controller.page.value = 1;
                                    controller.communityCreatedList.clear();
                                    controller.hasMoreData.value = true;
                                    controller.fetchCommunityData(context: context);
                                  } else if (controller.currentSelectedIndex.value ==
                                      1) {
                                    controller.projectPage.value = 1;
                                    controller.createdProject.clear();
                                    controller.hasMoreData.value = true;
                                    controller.fetchProjectData(context: context);
                                  }
                                });
                          }
                        },
                        height: MySize.getScaledSizeHeight(50),
                        prefixIcon: Padding(
                          padding: EdgeInsets.all(4),
                          child: SvgPicture.asset(
                            AppImage.searchIcon,
                            height: MySize.size24,
                            width: MySize.size24,
                            color: AppTheme.grey,
                          ),
                        ),
                        suffixIcon: (controller
                                .searchController.value.text.isNotEmpty)
                            ? GestureDetector(
                                onTap: () {
                                  controller.searchController.value.clear();
                                  Future.delayed(
                                    Duration(milliseconds: 500),
                                    () {
                                      if (controller
                                              .currentSelectedIndex.value ==
                                          0) {
                                        controller.page.value = 1;
                                        controller.postDataList.clear();
                                        controller.hasMoreData.value = true;
                                        controller.callApiForExplorePost(
                                            context: context);
                                      } else if (controller
                                              .currentSelectedIndex.value ==
                                          2) {
                                        controller.page.value = 1;
                                        controller.projectUserList.clear();
                                        // controller.hasMoreData.value = true;
                                        // controller.callApiForGetTeamUser(
                                        //     context: context);
                                      } else if (controller
                                              .currentSelectedIndex.value ==
                                          3) {
                                        controller.page.value = 1;
                                        controller.communityCreatedList.clear();
                                        controller.hasMoreData.value = true;
                                        controller.fetchCommunityData(
                                            context: context);
                                      } else if (controller
                                              .currentSelectedIndex.value ==
                                          1) {
                                        controller.projectPage.value = 1;
                                        controller.createdProject.clear();
                                        controller.hasMoreData.value = true;
                                        controller.fetchProjectData(
                                            context: context);
                                      }
                                    },
                                  );
                                },
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      right: MySize.size15 ?? 20),
                                  child: SvgPicture.asset(
                                    AppImage.textFieldClear,
                                    // height: MySize.size20,
                                    // width: MySize.size20,
                                    color: AppTheme.grey,
                                  ),
                                ),
                              )
                            : null,
                        hintText: "Search posts, projects, user...",
                        hintStyle: TextStyle(
                          color: AppTheme.grey,
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w400,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          Space.height(18),
          Obx(
            () => CustomHorizontalScrollbar(
              child: buildPostCategorySection(
                isGlobalSearch: true,
                totalCount: controller.searchController.value.text.isNotEmpty ? controller.totalCount.value : 0,
                onCategorySelected: (index) {
                  controller.currentSelectedIndex.value = index;
                  controller.totalCount.value = 0;
                  controller
                      .sections[index].onCategorySelected();
                },
                userPostList: controller.sections
                    .map((e) => e.title)
                    .toList(),
                currentSelectedIndex: controller.currentSelectedIndex.value,
              ),
            ),
          ),
          Space.height(10),
          Obx(
            () => Expanded(
              child: CustomScrollView(
                controller: controller.currentSelectedIndex.value == 1
                    ? controller.scrollController
                    : null,
                physics: AlwaysScrollableScrollPhysics(),
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                slivers: [
                  ...controller
                      .sections[controller.currentSelectedIndex.value]
                      .viewBuilder(context)
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
