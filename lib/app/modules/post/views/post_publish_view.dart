import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../components/image_select_dialog.dart';
import '../components/post_widget.dart';
import '../controllers/post_controller.dart';

class PostPublishView extends GetWidget<PostController> {
  const PostPublishView({super.key});

  @override
  Widget build(
    BuildContext context,
  ) {
    return Scaffold(
      // backgroundColor: Colors.white,
      body: SafeArea(
        child: CustomScrollView(
          physics: ClampingScrollPhysics(),
          slivers: [
            SliverAppBar(
              pinned: false,
              backgroundColor: Theme.of(context).brightness == Brightness.dark ? AppTheme.darkBackground : AppTheme.white,
              automaticallyImplyLeading: false,
              scrolledUnderElevation: 5,
              floating: true,
              centerTitle: true,
              leading: Padding(
                padding: EdgeInsets.only(left: MySize.size25 ?? 20),
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    Get.back();
                  },
                  child: SvgPicture.asset(
                    AppImage.backArrow,
                    height: MySize.size28,
                    width: MySize.size28,
                    color: AppTheme.whiteWithNull,
                  ),
                ),
              ),
              title: TypoGraphy(
                text: /*controller.args != null &&
                        controller.args["isDraft"] != null
                        ? "Edit Draft Post"
                        : controller.args != null &&
                        controller.args["isPostEdit"] != null
                        ? "Edit Post"
                        : */
                    controller.args != null && (controller.args["isCommunity"] != null && controller.args["isCommunityEdit"] != null)
                        ? "Edit post for community"
                        : controller.args != null && (controller.args["isCommunity"] != null)
                            ? "Create post for community"
                            : "Publish",
                level: 12,
              ),
              toolbarHeight: MySize.size62 ?? 60,
            ),
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.size40 ?? 20),
                    child: TypoGraphy(
                      text: "Select a cover photo from the\npost images.",
                      level: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Space.height(15),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Padding(
                      padding: EdgeInsets.only(left: MySize.size40 ?? 20),
                      child: Row(
                        children: [
                          Obx(
                            () => controller.isPhotoButtonVisible.value
                                ? Padding(
                                    padding: EdgeInsets.only(top: MySize.getScaledSizeWidth(5)),
                                    child: Obx(
                                      () => GestureDetector(
                                        onTap: controller.isPhotoButtonVisible.value
                                            ? () {
                                                // Ensure max 10 photos selection
                                                // if (controller.imageList.length >= 10) {
                                                //   CommonFunction.showCustomSnackbar(
                                                //       message: "You can select up to 10 photos only.",
                                                //       isError: true,
                                                //       backgroundColor: AppTheme.red
                                                //   );
                                                //   return;
                                                // }
                                                // Get.dialog(
                                                ImageTypeSelectPopup(
                                                  onCamera: () async {
                                                    Navigator.pop(context);
                                                    CommonFunction.requestPermissions(
                                                      context: context,
                                                      onPermissionsGranted: () async {
                                                        XFile? pickedFile;
                                                        pickedFile = await ImagePicker().pickImage(source: ImageSource.camera);

                                                        final dir = await getTemporaryDirectory();
                                                        final targetPath = '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                                                        if (pickedFile == null) return;
                                                        final XFile? compressedFile = await FlutterImageCompress.compressAndGetFile(
                                                          pickedFile.path ?? '',
                                                          targetPath,
                                                          quality: 70,
                                                          keepExif: true,
                                                        );

                                                        if (compressedFile != null) {
                                                          await controller.callApiForPostView(context: context, imageFiles: compressedFile.path);
                                                        }
                                                      },
                                                      title: "Please Allow Camera",
                                                      message: "Please allow Camera to select a image.",
                                                      permissions: [Permission.camera],
                                                    );
                                                  },
                                                  onGallery: () async {
                                                    Navigator.pop(context);
                                                    XFile? pickedFiles = await ImagePicker().pickImage(source: ImageSource.gallery);

                                                    final dir = await getTemporaryDirectory();
                                                    final targetPath = '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                                                    if (pickedFiles == null) return;

                                                    final originalFile = File(pickedFiles.path);
                                                    final originalSize = originalFile.lengthSync() / (1024 * 1024);
                                                    print('📷 Original image size: ${originalSize.toStringAsFixed(2)} MB');

                                                    final XFile? compressedFile = await FlutterImageCompress.compressAndGetFile(
                                                      pickedFiles.path ?? '',
                                                      targetPath,
                                                      quality: 50,
                                                      keepExif: true,
                                                    );

                                                    if (compressedFile != null) {
                                                      final originalFile = File(compressedFile.path);
                                                      final originalSize = originalFile.lengthSync() / (1024 * 1024);
                                                      print('📷 Compress image size: ${originalSize.toStringAsFixed(2)} MB');

                                                      await controller.callApiForPostView(
                                                        context: context,
                                                        imageFiles: compressedFile.path,
                                                      );
                                                    }
                                                  },
                                                );
                                                // );
                                              }
                                            : () {
                                                CommonFunction.showCustomSnackbar(message: "You can only add one cover photo per post.", isError: true, backgroundColor: Color(0xFFEF3B41));
                                              },
                                        child: Image.asset(
                                          Theme.of(context).brightness == Brightness.dark ? AppImage.selectedDarkImage : AppImage.selectedImage,
                                          height: MySize.getScaledSizeHeight(67),
                                          width: MySize.getScaledSizeWidth(87),
                                        ),
                                      ),
                                    ),
                                  )
                                : SizedBox(),
                          ),
                          Obx(() => controller.isPhotoButtonVisible.value ? Space.width(10) : SizedBox()),
                          Obx(
                            () => SizedBox(
                              height: MySize.getScaledSizeHeight(67),
                              child: ListView.builder(
                                itemCount: controller.imageList.length,
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (context, index) {
                                  print("image list ==> ${controller.imageList}");
                                  return Obx(
                                    () => Padding(
                                      padding: EdgeInsets.only(
                                        right: MySize.getScaledSizeWidth(10),
                                        top: MySize.getScaledSizeWidth(5),
                                      ),
                                      child: GestureDetector(
                                        onTap: () {
                                          for (var element in controller.imageList) {
                                            element["isCover"] = false;
                                          }
                                          controller.coverImageIndex.value = index;
                                          controller.coverPhoto.value = controller.imageList[index]["path"].toString();
                                          controller.imageList[index]["isCover"] = true;
                                          controller.update();
                                        },
                                        child: Stack(
                                          clipBehavior: Clip.none,
                                          children: [
                                            Container(
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                  color: controller.coverImageIndex.value == index ? AppTheme.primary1 : Colors.transparent,
                                                  width: MySize.getScaledSizeWidth(2),
                                                ),
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                              height: MySize.getScaledSizeHeight(66.22),
                                              width: MySize.getScaledSizeWidth(87),
                                              child: ClipRRect(
                                                  borderRadius: BorderRadius.circular(10),
                                                  child: NetworkImageComponent(
                                                    imageUrl: controller.imageList[index]["path"].toString(),
                                                    simmerHeight: MySize.getScaledSizeHeight(66.22),
                                                    width: MySize.getScaledSizeWidth(87),
                                                  )),
                                            ),
                                            controller.coverImageIndex.value == index
                                                ? Positioned(
                                                    right: -5,
                                                    top: -5,
                                                    child: InkWell(
                                                      highlightColor: Colors.transparent,
                                                      splashColor: Colors.transparent,
                                                      onTap: () {
                                                        controller.imageList.removeAt(index);
                                                        // If the removed image was the first one
                                                        if (index == 0) {
                                                          controller.isPhotoButtonVisible.value = true; // Show the button
                                                        }
                                                        if (controller.coverImageIndex.value == index) {
                                                          controller.coverImageIndex.value = -1;
                                                          controller.coverPhoto.value = "";
                                                        } else if (controller.coverImageIndex.value > index) {
                                                          controller.coverImageIndex.value -= 1;
                                                        }
                                                        controller.update();
                                                      },
                                                      child: ClipRRect(
                                                        borderRadius: BorderRadius.circular(50),
                                                        child: BackdropFilter(
                                                          filter: ImageFilter.blur(
                                                            sigmaX: 10,
                                                            sigmaY: 10,
                                                          ),
                                                          child: SizedBox(
                                                            height: MySize.size20,
                                                            width: MySize.size20,
                                                            child: Padding(
                                                              padding: EdgeInsets.all(5),
                                                              child: SvgPicture.asset(
                                                                AppImage.closeImage,
                                                                color: AppTheme.whiteWithNull,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  )
                                                : SizedBox(),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Obx(
                    () => controller.coverPhoto.value == ""
                        ? const SizedBox()
                        : SizedBox(
                            height: MySize.getScaledSizeHeight(20.78),
                          ),
                  ),
                  Center(
                    child: Obx(
                      () => controller.coverPhoto.value == ""
                          ? const SizedBox()
                          : Stack(
                              alignment: Alignment.bottomCenter,
                              children: [
                                ClipRRect(
                                    borderRadius: BorderRadius.circular(25),
                                    child: NetworkImageComponent(
                                      imageUrl: controller.coverPhoto.value.toString(),
                                      simmerHeight: MySize.getScaledSizeHeight(123),
                                      height: MySize.getScaledSizeHeight(123),
                                      width: MySize.getScaledSizeWidth(172),
                                    )
                                    // Image.network(
                                    //   postController.coverPhoto.value
                                    //       .toString(),
                                    //   height: MySize.getScaledSizeHeight(123),
                                    //   width: MySize.getScaledSizeWidth(172),
                                    //   fit: BoxFit.cover,
                                    // ),
                                    ),
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(25),
                                  child: Image.asset(
                                    AppImage.coverBack,
                                    height: MySize.getScaledSizeHeight(123),
                                    width: MySize.getScaledSizeWidth(172),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                    bottom: MySize.getScaledSizeHeight(10),
                                  ),
                                  child: TypoGraphy(
                                    text: "Cover Photo",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.white,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                  SizedBox(
                    height: MySize.getScaledSizeHeight(31),
                  ),
                  controller.args != null && controller.args["isCommunity"] != null
                      ? SizedBox()
                      : Padding(
                          padding: EdgeInsets.only(left: MySize.size40 ?? 20),
                          child: projectAndVisibility(controller, context),
                        ),
                ],
              ),
            )
          ],
        ),
      ),
      bottomNavigationBar: postBottomView(controller, context),
    );
  }
}
