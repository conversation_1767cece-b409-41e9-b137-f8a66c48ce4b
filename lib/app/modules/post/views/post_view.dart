import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:get/get.dart' hide Node;
import 'package:incenti_ai/utillites/common_profile_widget.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../explore/components/common_widget_view.dart';
import '../components/common_editor_toolbar.dart';
import '../components/genie_widget.dart';
import '../controllers/post_controller.dart';
import 'package:flutter_quill_extensions/flutter_quill_extensions.dart';

class PostView extends GetWidget<PostController> {
  const PostView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return PopScope(
      canPop: false,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: Stack(
          children: [
            SafeArea(
              // bottom: false,
              child: Column(
                children: [
                  Space.height(10),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20),
                    child: Row(
                      children: [
                        Obx(
                          () => InkWell(
                            highlightColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            onTap: controller.isButtonVisible.value
                                ? () async {
                                    controller.isButtonVisible.value = false;
                                    if (controller.args != null && (controller.args["isCommunity"] != null)) {
                                      Get.back();
                                    } else {
                                      if (controller.args != null && (controller.args["isDraft"] != null ||
                                              controller.args["isPostEdit"] != null ||
                                              controller.args["isCreate"] != null || controller.args["isCreateProject"] != null)) {
                                        if (controller.args != null && controller.args["isDraft"] != null) {
                                          List deltaJson = controller.descriptionController.document.toDelta().toJson();
                                          QuillDeltaToHtmlConverter converter =
                                              QuillDeltaToHtmlConverter(
                                            List.castFrom(deltaJson),
                                            ConverterOptions(
                                              converterOptions:
                                                  OpConverterOptions(
                                                inlineStylesFlag: true,
                                                inlineStyles: InlineStyles({
                                                  'color': InlineStyleType(
                                                    fn: (value, _) =>
                                                        'color: $value;',
                                                  ),
                                                  'background': InlineStyleType(
                                                    fn: (value, _) =>
                                                        'background-color: $value;',
                                                  ),
                                                }),
                                              ),
                                              sanitizerOptions: OpAttributeSanitizerOptions(allow8DigitHexColors: true,),
                                            ),
                                          );

                                          controller.htmlContent.value = converter.convert();

                                          String? temp;

                                          temp = controller.htmlContent.value;
                                          double maxHeight = await controller.getMaxImageHeight(
                                                  controller.imageList,
                                                  MySize.screenWidth - MySize.getScaledSizeWidth(40));
                                          controller.callApiForUpdatePost(context: context, height: maxHeight);
                                        } else {
                                          if (controller.args != null &&
                                              controller.args["isPostEdit"] != null) {
                                            List deltaJson = controller.descriptionController.document.toDelta().toJson();
                                            QuillDeltaToHtmlConverter
                                                converter =
                                                QuillDeltaToHtmlConverter(
                                              List.castFrom(deltaJson),
                                              ConverterOptions(
                                                converterOptions:
                                                    OpConverterOptions(
                                                  inlineStylesFlag: true,
                                                  inlineStyles: InlineStyles({
                                                    'color': InlineStyleType(
                                                      fn: (value, _) =>
                                                          'color: $value;',
                                                    ),
                                                    'background':
                                                        InlineStyleType(
                                                      fn: (value, _) =>
                                                          'background-color: $value;',
                                                    ),
                                                  }),
                                                ),
                                                sanitizerOptions: OpAttributeSanitizerOptions(allow8DigitHexColors: true,),
                                              ),
                                            );

                                            controller.htmlContent.value = converter.convert();
                                            String? temp;

                                            temp = controller.htmlContent.value;
                                            if (controller.getPostDetailData.value.title ==
                                                    controller.titleController.value.text &&
                                                controller.normalizeHtml(controller.getPostDetailData.value.description) ==
                                                    controller.normalizeHtml(controller.htmlContent.value)) {
                                              Get.back();
                                            } else {
                                              showBackDialogue(
                                                  context: context);
                                            }
                                            controller.isButtonVisible.value = true;
                                          } else {
                                            if (controller.args != null &&
                                                controller.args["isCreateProject"] != null) {
                                              List deltaJson = controller.descriptionController.document.toDelta().toJson();
                                              QuillDeltaToHtmlConverter
                                                  converter =
                                                  QuillDeltaToHtmlConverter(
                                                List.castFrom(deltaJson),
                                                ConverterOptions(
                                                  converterOptions: OpConverterOptions(
                                                    inlineStylesFlag: true,
                                                    inlineStyles: InlineStyles({
                                                      'color': InlineStyleType(
                                                        fn: (value, _) =>
                                                            'color: $value;',
                                                      ),
                                                      'background':
                                                          InlineStyleType(
                                                        fn: (value, _) =>
                                                            'background-color: $value;',
                                                      ),
                                                    }),
                                                  ),
                                                  sanitizerOptions: OpAttributeSanitizerOptions(allow8DigitHexColors: true),
                                                ),
                                              );

                                              controller.htmlContent.value = converter.convert();
                                              String? temp;
                                              if (controller.titleController.value.text.isNotEmpty) {
                                                double maxHeight = await controller.getMaxImageHeight(
                                                        controller.imageList,
                                                        MySize.screenWidth - MySize.getScaledSizeWidth(40));
                                                controller.isCrossButton.value = true;
                                                controller.callApiForCreatePost(context: context, height: maxHeight);
                                              } else {
                                                Get.back();
                                              }
                                            } else {
                                              Get.back();
                                            }
                                          }
                                        }
                                      } else {
                                        List deltaJson = controller.descriptionController.document.toDelta().toJson();
                                        QuillDeltaToHtmlConverter converter =
                                            QuillDeltaToHtmlConverter(
                                          List.castFrom(deltaJson),
                                          ConverterOptions(
                                            converterOptions:
                                                OpConverterOptions(
                                              inlineStylesFlag: true,
                                              inlineStyles: InlineStyles({
                                                'color': InlineStyleType(
                                                  fn: (value, _) =>
                                                      'color: $value;',
                                                ),
                                                'background': InlineStyleType(
                                                  fn: (value, _) =>
                                                      'background-color: $value;',
                                                ),
                                              }),
                                            ),
                                            sanitizerOptions: OpAttributeSanitizerOptions(allow8DigitHexColors: true,),
                                          ),
                                        );

                                        controller.htmlContent.value =
                                            converter.convert();
                                        String? temp;
                                        temp = controller.htmlContent.value;
                                        if (controller.titleController.value.text.isNotEmpty) {
                                          controller.isCrossButton.value = true;
                                          double maxHeight = await controller.getMaxImageHeight(
                                                  controller.imageList,
                                                  MySize.screenWidth - MySize.getScaledSizeWidth(40));
                                          controller.callApiForCreatePost(context: context, height: maxHeight);
                                        } else {
                                          Get.close(1);
                                        }
                                      }
                                    }
                                  }
                                : () {},
                            child: Icon(
                              Icons.clear_rounded,
                              size: MySize.size28,
                              // color: AppTheme.baseBlack,
                            ),
                          ),
                        ),
                        Spacer(),
                        // TypoGraphy(
                        //   text: /*controller.args != null &&
                        //           controller.args["isDraft"] != null
                        //       ? "Edit Draft Post"
                        //       : controller.args != null &&
                        //               controller.args["isPostEdit"] != null
                        //           ? "Edit Post"
                        //           :*/
                        //   controller.args != null &&
                        //       (controller.args["isCommunity"] != null && controller.args["isCommunityEdit"] != null)
                        //       ? "Edit post for community" : controller.args != null &&
                        //       (controller.args["isCommunity"] != null) ? "Create post for community"
                        //       : "Write",
                        //   level: 12,
                        // ),
                        // Spacer(),
                        InkWell(
                          onTap: () async {
                            List deltaJson = controller.descriptionController.document.toDelta().toJson();
                            controller.titleFocusNode.unfocus();
                            controller.editorFocusNode.unfocus();
                            QuillDeltaToHtmlConverter converter =
                                QuillDeltaToHtmlConverter(
                              List.castFrom(deltaJson),
                              ConverterOptions(
                                converterOptions: OpConverterOptions(
                                  inlineStylesFlag: true,
                                  inlineStyles: InlineStyles({
                                    'color': InlineStyleType(
                                      fn: (value, _) => 'color: $value;',
                                    ),
                                    'background': InlineStyleType(
                                      fn: (value, _) =>
                                          'background-color: $value;',
                                    ),
                                  }),
                                ),
                                sanitizerOptions: OpAttributeSanitizerOptions(
                                  allow8DigitHexColors: true,
                                ),
                              ),
                            );
                            controller.htmlContent.value = converter.convert();
                            String temp = controller.htmlContent.value;
                            controller.htmlContent.value = temp.replaceAllMapped(
                                RegExp(r'(?<!<br>)\s*<img '),
                                (match) => '<br>' + match.group(0)!);
                            controller.updateImageList(temp);
                            if (controller.args != null && (controller.args["isCommunity"] != null)) {
                              if (controller.titleController.value.text.isNotEmpty) {
                                Get.toNamed(Routes.post_publish);
                              } else {
                                CommonFunction.showCustomSnackbar(
                                  message: "Title is required!",
                                  isError: true,
                                  backgroundColor: Color(0xFFEF3B41),
                                );
                              }
                            } else {
                              if (controller.titleController.value.text.isNotEmpty) {
                                Get.toNamed(Routes.post_publish);
                              } else {
                                CommonFunction.showCustomSnackbar(
                                  message: "Title is required!",
                                  isError: true,
                                  backgroundColor: Color(0xFFEF3B41),
                                );
                              }
                            }
                          },
                          child: TypoGraphy(
                            text: "Next",
                            level: 12,
                            color: AppTheme.primaryIconDark,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Space.height(12),
                  Expanded(
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          controller: controller.editorScrollController,
                          child: Column(
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20),
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minHeight: MySize.getScaledSizeHeight(55),
                                    maxHeight: MySize.getScaledSizeHeight(450), // Max height to avoid excessive growth
                                  ),
                                  child: IntrinsicHeight(
                                    child: TextField(
                                      autofocus: true,
                                      inputFormatters: [
                                        LengthLimitingTextInputFormatter(200)
                                      ],
                                      textInputAction: TextInputAction.done,
                                      controller: controller.titleController,
                                      focusNode: controller.titleFocusNode,
                                      decoration: InputDecoration(
                                        hintText: "Write your post",
                                        border: InputBorder.none,
                                        hintStyle: TextStyle(
                                          fontWeight: FontWeight.w700,
                                          fontSize: MySize.size26,
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? null
                                              : AppTheme.grey[50],
                                          fontFamily: "Inter",
                                        ),
                                      ),
                                      keyboardType: TextInputType.multiline,
                                      textCapitalization: TextCapitalization.sentences,
                                      maxLines: null,
                                      // Allows expansion
                                      style: TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontSize: MySize.size26,
                                        // color: AppTheme.baseBlack,
                                        fontFamily: "Inter",
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTapDown: (details) {
                                  final position = controller.descriptionController.selection.start;
                                  if (position >= 0) {
                                    final text = controller.descriptionController.document.toPlainText();
                                    final wordInfo = _getWordAtPosition(text, position);
                                    if (wordInfo != null) {
                                      final word = wordInfo.$1;

                                      if (controller.suggestions.containsKey(word)) {
                                        controller.showSuggestions(
                                            word,
                                            controller.suggestions[word]!,
                                            details.globalPosition,
                                            context);
                                      }
                                    }
                                  }
                                },
                                child: InkWell(
                                  onTap: Platform.isIOS
                                      ? () {
                                          controller.editorFocusNode.requestFocus();
                                        }
                                      : null,
                                  child: QuillEditor(
                                    focusNode: controller.editorFocusNode,
                                    scrollController: controller.editorScrollController,
                                    controller: controller.descriptionController,
                                    config: QuillEditorConfig(
                                      scrollable: false,
                                      showCursor: true,
                                      placeholder: "Description",
                                      // expands: true,
                                      padding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20),
                                      linkActionPickerDelegate:
                                          (BuildContext context, String link,
                                              Node node) async {
                                        if (link.startsWith('spell-error://')) {
                                          return LinkMenuAction.none;
                                        } else {
                                          return defaultLinkActionPickerDelegate(
                                              context, link, node);
                                        }
                                      },
                                      customStyleBuilder:
                                          (Attribute attribute) {
                                        if (attribute.key ==
                                            Attribute.link.key) {
                                          final linkValue =
                                              attribute.value.toString();
                                          if (linkValue.startsWith('spell-error://')) {
                                            return TextStyle(
                                              decorationColor: Colors.red,
                                              decoration:
                                                  TextDecoration.underline,
                                            );
                                          } else if (linkValue.startsWith('https://') || linkValue.startsWith('http://')) {
                                            return TextStyle(
                                                color: AppTheme.primary1,
                                                fontWeight: FontWeight.w600);
                                          } else {
                                            return TextStyle(
                                                color: AppTheme.primary1,
                                                fontWeight: FontWeight.w600);
                                          }
                                        } else {
                                          return TextStyle();
                                        }
                                      },
                                      customStyles: DefaultStyles(
                                        link: TextStyle(),
                                        paragraph: DefaultTextBlockStyle(
                                          TextStyle(
                                            color: AppTheme.whiteWithBase,
                                            fontSize: MySize.getScaledSizeHeight(19),
                                            fontFamily: "Inter",
                                          ),
                                          HorizontalSpacing(0, 0),
                                          VerticalSpacing(0, 0),
                                          VerticalSpacing.zero,
                                          BoxDecoration(
                                            border: Border.all(
                                              color: Colors.red,
                                              width: 50,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                        ),
                                      ),
                                      embedBuilders: [
                                        ...FlutterQuillEmbeds.editorBuilders(
                                          imageEmbedConfig:
                                              QuillEditorImageEmbedConfig(
                                            imageProviderBuilder:
                                                (context, imageUrl) {
                                              if (imageUrl.startsWith('assets/')) {
                                                return AssetImage(imageUrl);
                                              }
                                              return null;
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          left: 0,
                          child: Obx(() {
                            final isVisible =
                                controller.showMentionDropdown.value;
                            return IgnorePointer(
                              ignoring: !isVisible,
                              child: ClipRRect(
                                borderRadius: BorderRadius.only(topLeft: Radius.circular(40), topRight: Radius.circular(40)),
                                child: AnimatedContainer(
                                  duration: Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                  height: isVisible
                                      ? MySize.getScaledSizeHeight(280)
                                      : 0,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? AppTheme.baseBlack
                                        : AppTheme.white,
                                    border: BorderDirectional(
                                      top: BorderSide(color: AppTheme.borderColor),
                                      start: BorderSide(color: AppTheme.borderColor),
                                      end: BorderSide(color: AppTheme.borderColor),
                                    ),
                                    borderRadius: BorderRadius.only(topLeft: Radius.circular(40), topRight: Radius.circular(40)),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 10,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: isVisible
                                      ? (controller.isMentionLoading.value
                                          ? Center(child: Loader())
                                          : ListView.separated(
                                              padding: EdgeInsets.symmetric(vertical: 10),
                                              separatorBuilder: (context, index) => Divider(color: AppTheme.grey,),
                                              shrinkWrap: true,
                                              itemCount: controller.mentionDropdownList.length,
                                              itemBuilder: (context, index) {
                                                final item = controller.mentionDropdownList[index];
                                                return InkWell(
                                                  onTap: () {
                                                    controller.insertMentionInHtml(
                                                      name: item.name,
                                                      slug: item.slug,
                                                      type: item.type,
                                                    );
                                                    controller.mentionList.add(item);
                                                  },
                                                  child: Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20, vertical: MySize.size5 ?? 5),
                                                    child: Row(
                                                      children: [
                                                        item.type == 'user'
                                                            ? profileImage(
                                                                url: item.image ?? "",
                                                                userName: item.name,
                                                                height: MySize.getScaledSizeHeight(40),
                                                                width: MySize.getScaledSizeWidth(40),
                                                                iconHeight: MySize.getScaledSizeHeight(40),
                                                                iconWidth: MySize.getScaledSizeHeight(40),
                                                                borderColor: Colors.transparent,
                                                                color: AppTheme.darkGrey[100],
                                                                textStyle: TextStyle(
                                                                    fontSize: MySize.getScaledSizeHeight(16),
                                                                    color: AppTheme.white))
                                                            : ClipRRect(
                                                                borderRadius: BorderRadius.circular(8),
                                                                child: item.image != null
                                                                        ? Center(
                                                                            child: CachedNetworkImage(
                                                                              height: MySize.getScaledSizeHeight(40),
                                                                              width: MySize.getScaledSizeWidth(40),
                                                                              fit: BoxFit.cover,
                                                                              imageUrl: item.image ?? "",
                                                                              placeholder: (context, url) => Loader(),
                                                                              errorWidget: (context, url, error) => Center(
                                                                                child: Container(
                                                                                  color: AppTheme.primary1,
                                                                                  height: MySize.getScaledSizeHeight(40),
                                                                                  width: MySize.getScaledSizeWidth(40),
                                                                                  child: Icon(
                                                                                    Icons.folder,
                                                                                    color: Colors.white,
                                                                                    size: MySize.getScaledSizeHeight(20),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          )
                                                                        : Center(
                                                                            child:
                                                                                Container(
                                                                              color: AppTheme.primary1,
                                                                              height: MySize.getScaledSizeHeight(40),
                                                                              width: MySize.getScaledSizeWidth(40),
                                                                              child: Icon(
                                                                                Icons.folder,
                                                                                color: Colors.white,
                                                                                size: MySize.getScaledSizeHeight(20),
                                                                              ),
                                                                            ),
                                                                          ),
                                                              ),
                                                        SizedBox(width: MySize.size10 ?? 10),
                                                        Expanded(
                                                          child: Text(
                                                            item.name,
                                                            style: TextStyle(
                                                              fontWeight: FontWeight.w500,
                                                              color: box.read('isDarkMode')
                                                                  ? AppTheme.white
                                                                  : AppTheme.black,
                                                              fontSize: MySize.getScaledSizeHeight(16),
                                                            ),
                                                            maxLines: 1,
                                                            overflow: TextOverflow.ellipsis,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            ))
                                      : null,
                                ),
                              ),
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                  // Spacer(),
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 10,
                    ),
                    child: Obx(() {
                      final showToolbar = !controller.showMentionDropdown.value;
                      return AnimatedOpacity(
                        opacity: showToolbar ? 1.0 : 0.0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        child: showToolbar
                            ? CommonEditorToolbar(
                                descriptionController:
                                    controller.descriptionController,
                                editorFocusNode: controller.editorFocusNode,
                                theme: Theme.of(context),
                                onBotTap: () => showGenieBottomSheet(
                                  controller: controller,
                                  context: context,
                                ),
                              )
                            : SizedBox.shrink(),
                      );
                    }),
                  ),
                ],
              ),
            ),
            Obx(
              () => controller.isGetPostLoading.value
                  ? Container(
                      color: AppTheme.black.withValues(alpha: 0.5),
                      child: Loader(),
                    )
                  : SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  (String, int, int)? _getWordAtPosition(String text, int position) {
    if (position < 0 || position >= text.length) return null;

    // Find the start of the word
    int start = position;
    while (start > 0 && _isWordChar(text[start - 1])) {
      start--;
    }

    // Find the end of the word
    int end = position;
    while (end < text.length && _isWordChar(text[end])) {
      end++;
    }

    if (start == end) return null;

    return (text.substring(start, end), start, end);
  }

  bool _isWordChar(String char) {
    return RegExp(r'[A-Za-z0-9_\-]').hasMatch(char);
  }
}
