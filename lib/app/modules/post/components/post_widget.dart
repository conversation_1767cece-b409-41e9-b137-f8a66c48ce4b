import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/projects/controllers/projects_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/buttons.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../main.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/network_image.dart';
import '../../../routes/app_pages.dart';
import '../../projects/components/show_sub_project_bottomsheet.dart';
import '../controllers/post_controller.dart';

Container postBottomView(PostController postController, BuildContext context) {
  return Container(
    padding: EdgeInsets.only(
      top: MySize.getScaledSizeHeight(8),
      bottom: MySize.getScaledSizeHeight(30),
    ),
    // color: AppTheme.white,
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(
              () => Buttons(
                buttonTextLevel: 4,
                width: MySize.size198,
                height: MySize.size70 ?? 70,
                buttonText: "Publish",
                isLoading: postController.isLoading.value,
                onTap: () async {
                  if (postController.visibilityName.isNotEmpty) {
                    if (postController.args != null &&
                        postController.args["isCommunity"] != null) {
                      if (postController.args != null &&
                          postController.args["PostId"] != null) {
                        double maxHeight =
                            await postController.getMaxImageHeight(
                                postController.imageList,
                                MySize.screenWidth -
                                    MySize.getScaledSizeWidth(40));
                        postController.callApiForUpdateCommunityPost(
                            context: context, height: maxHeight);
                      } else {
                        double maxHeight =
                            await postController.getMaxImageHeight(
                                postController.imageList,
                                MySize.screenWidth -
                                    MySize.getScaledSizeWidth(40));
                        postController.callApiForCreateCommunityPost(
                            context: context, height: maxHeight);
                      }
                    } else {
                      double maxHeight = await postController.getMaxImageHeight(
                          postController.imageList,
                          MySize.screenWidth - MySize.getScaledSizeWidth(40));
                      if (postController.args != null &&
                              postController.args["isPostEdit"] != null ||
                          postController.args != null &&
                              postController.args["isDraft"] != null) {
                        postController.callApiForUpdatePost(
                            context: context,
                            isPublished: true,
                            shouldReload: true,
                            height: maxHeight);
                      } else {
                        // double maxHeight = height.reduce((a, b) => a > b ? a : b);
                        postController.callApiForCreatePost(
                            context: context,
                            isPublished: true,
                            height: maxHeight);
                      }
                    }
                  } else {
                    if (postController.args != null &&
                        (postController.args["isCommunity"] != null)) {
                      CommonFunction.showCustomSnackbar(
                          message: "Cover Photo is required*",
                          backgroundColor: AppTheme.red,
                          isError: true);
                    } else {
                      CommonFunction.showCustomSnackbar(
                          message: "Cover Photo and Visibility are required*",
                          backgroundColor: AppTheme.red,
                          isError: true);
                    }
                  }
                  Get.find<ProjectsController>().selectedSubProjectIndex.value =
                      -1;
                },
              ),
            ),
          ],
        ),
        postController.args != null &&
                postController.args["isCommunity"] != null
            ? SizedBox()
            : Space.height(20),
        postController.args != null &&
                postController.args["isCommunity"] != null
            ? SizedBox()
            : postController.isDraftLoading.value ? Loader() : InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () async {
                  if (postController.args != null &&
                      postController.args["isPostEdit"] != null ||postController.args != null &&
                      postController.args["isDraft"] != null) {
                    double maxHeight = await postController.getMaxImageHeight(
                        postController.imageList,
                        MySize.screenWidth - MySize.getScaledSizeWidth(40));
                    postController
                        .callApiForUpdatePost(
                            context: context,
                            shouldReload: true,
                            height: maxHeight);
                  } else {
                    double maxHeight = await postController.getMaxImageHeight(
                        postController.imageList,
                        MySize.screenWidth - MySize.getScaledSizeWidth(40));
                    postController.callApiForCreatePost(
                        context: context, height: maxHeight);
                  }
                },
                child: TypoGraphy(
                  text: "Save as Draft",
                  level: 4,
                  color: AppTheme.red,
                ),
              ),
      ],
    ),
  );
}

Column projectAndVisibility(
    PostController postController, BuildContext context) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          if (postController.project.value == null) {
            Get.put(ProjectsController()).hasMoreData.value = true;
            Get.put(ProjectsController()).selectedIndex.value = -1;
            Get.put(ProjectsController()).page.value = 1;
            Get.put(ProjectsController()).createdProject.clear();
            Get.put(ProjectsController()).selectedTabIndex.value = 0;
            Get.put(ProjectsController())
                .callApiForProjectData(context: Get.context!, isSelected: true);
            Get.toNamed(Routes.select_project, arguments: {"isSelected": true});
          }
        },
        child: Row(
          children: [
            TypoGraphy(
              text: "Select Project (optional)",
              level: 12,
              fontWeight: FontWeight.w500,
            ),
            SizedBox(
              width: MySize.getScaledSizeWidth(10),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: MySize.getScaledSizeWidth(18),
              color: AppTheme.whiteWithNull,
            )
          ],
        ),
      ),
      Obx(() => postController.project.value != null ? Stack(
            children: [
              InkWell(
                onTap: () {
                  Get.put(ProjectsController()).isChecked.value = true;
                  Get.put(ProjectsController()).callApiForOneSubProject(
                      context: context,
                      projectId: postController
                          .project.value?.parentProjectData !=
                          null
                          ? postController.project.value?.parentProjectData?.id
                          .toString()
                          : postController.project.value?.id.toString());
                  showMoveToProjectBottomSheet(
                    isShowParentProject: false,
                    isSubProject: (postController.args != null &&
                        postController.args["isSubProject"] != null ||
                        postController.args != null &&
                            postController.args["isSubProjectEdit"] == true)
                        ? true
                        : false,
                    context: context,
                    project: postController.project.value!,
                    isChecked: Get.put(ProjectsController()).isChecked,
                    isSubLoading: Get.put(ProjectsController()).isSubLoading,
                    selectedSubProjectIndex:
                    Get.put(ProjectsController()).selectedSubProjectIndex,
                    subProjectList:
                    Get.put(ProjectsController()).subProjectList,
                  );
                },
                child: Container(
                  margin: EdgeInsets.only(
                      top: MySize.getScaledSizeHeight(13),
                      right: MySize.getScaledSizeHeight(40)),
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: box.read('isDarkMode') ? AppTheme.darkBackground : AppTheme.lightGrey,
                    // color: AppTheme.lightGrey,
                    border: Border.all(color: AppTheme.borderWithTrans),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: Row(
                    children: [
                      Container(
                        height: MySize.getScaledSizeHeight(76),
                        width: MySize.getScaledSizeWidth(90),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(25),
                          child: (postController.project.value
                              ?.parentProjectData?.image ??
                              postController.project.value?.image) !=
                              ""
                              ? NetworkImageComponent(
                            imageUrl: postController.project.value
                                ?.parentProjectData?.image ??
                                postController.project.value?.image,
                            simmerHeight: MySize.getScaledSizeHeight(76),
                            width: MySize.getScaledSizeWidth(90),
                          )
                              : Image.asset(AppImage.defaultImage),
                        ),
                      ),
                      SizedBox(width: MySize.getScaledSizeWidth(14)),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TypoGraphy(
                              text:
                              "${postController.project.value?.parentProjectData?.subProjectsCount ?? postController.project.value?.subProjectsCount ?? 0} Sub-projects",
                              level: 2,
                              fontWeight: FontWeight.w400,
                              color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey : null,

                            ),
                            SizedBox(height: MySize.getScaledSizeHeight(4)),
                            TypoGraphy(
                              text: postController
                                  .project.value?.parentProjectData?.name ??
                                  postController.project.value?.name ??
                                  '',
                              maxLines: 2,
                              textStyle: TextStyle(
                                fontSize: MySize.getScaledSizeHeight(17),
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (!(postController.args != null &&
                  postController.args["isCrossVisible"] != null))
                Positioned(
                  right: MySize.getScaledSizeWidth(35),
                  top: MySize.getScaledSizeHeight(5),
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      postController.project.value = null;
                      // postController.update();
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(50),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(
                          sigmaX: 25,
                          sigmaY: 25,
                        ),
                        child: Container(
                          height: MySize.getScaledSizeHeight(25),
                          width: MySize.getScaledSizeWidth(25),
                          color: AppTheme.grey.withValues(alpha: 0.3),
                          child: Padding(
                            padding: EdgeInsets.all(6),
                            child: SvgPicture.asset(
                              AppImage.closeImage,
                              height: MySize.getScaledSizeHeight(28),
                              width: MySize.getScaledSizeWidth(28),
                              color: AppTheme.whiteWithNull,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ) : SizedBox(),),
      SizedBox(
        height: MySize.getScaledSizeHeight(40),
      ),
      /*TypoGraphy(
        text: "Visibility*",
        level: 12,
        fontWeight: FontWeight.w500,
      ),
      SizedBox(
        height: MySize.getScaledSizeHeight(5),
      ),
      Obx(
        () => Row(
          children: [
            Transform.scale(
              scale: 1.2,
              child: Radio(
                activeColor: AppTheme.primary1,
                value: 0,
                onChanged: (value) {
                  postController.visibilityValue.value = value!;
                  postController.visibilityName.value = "Public";
                  postController.update();
                },
                groupValue: postController.visibilityValue.value,
              ),
            ),
            TypoGraphy(
              text: "Public",
              textStyle: TextStyle(
                  fontSize: MySize.getScaledSizeHeight(18),
                  fontWeight: FontWeight.w400),
              // level: 12,
              // fontWeight: FontWeight.w400,
            ),
            SizedBox(
              width: MySize.getScaledSizeWidth(40),
            ),
            Transform.scale(
              scale: 1.2,
              child: Radio(
                activeColor: AppTheme.primary1,
                value: 1,
                onChanged: (value) {
                  postController.visibilityValue.value = value!;
                  postController.visibilityName.value = "Private";
                  postController.update();
                },
                groupValue: postController.visibilityValue.value,
              ),
            ),
            TypoGraphy(
              text: "Private",
              textStyle: TextStyle(
                  fontSize: MySize.getScaledSizeHeight(18),
                  fontWeight: FontWeight.w400),
            ),
          ],
        ),
      ),*/
    ],
  );
}
