import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import 'package:url_launcher/url_launcher.dart';


String addNofollowToLinks(String htmlContent) {
  final document = html_parser.parse(htmlContent);

  for (Element link in document.getElementsByTagName('a')) {
    final relAttr = link.attributes['rel'];

    if (relAttr == null) {
      link.attributes['rel'] = 'nofollow noopener noreferrer';
    } else {
      final relValues = relAttr.split(RegExp(r'\s+')).toSet();
      relValues.add('nofollow');
      link.attributes['rel'] = relValues.join(' ');
    }
  }

  return document.body?.innerHtml ?? htmlContent;
}

Future<bool> openLink(String url) async {
  // Step 1: Remove `unsafe:` prefix if present
  url = url.replaceFirst(RegExp(r'^unsafe:'), '');

  // Step 2: Ensure it has a valid scheme
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = 'https://$url';
  }

  final uri = Uri.parse(url);

  // Step 3: Try launching the URL
  if (await canLaunchUrl(uri)) {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
    return true;
  } else {
    // debugPrint('Could not launch $url');
    return false;
  }
}

