import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill_extensions/flutter_quill_extensions.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';

class CommonEditorToolbar extends StatelessWidget {
  final QuillController descriptionController;
  final FocusNode editorFocusNode;
  final VoidCallback onBotTap;
  final ThemeData theme;

  const CommonEditorToolbar({
    super.key,
    required this.descriptionController,
    required this.editorFocusNode,
    required this.onBotTap,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            GestureDetector(
              onTap: onBotTap,
              child: Padding(
                padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(20)),
                child: SvgPicture.asset(
                  AppImage.editorBot,
                  height: MySize.getScaledSizeHeight(50),
                ),
              ),
            ),
            Expanded(
              child: QuillSimpleToolbar(
                focusNode: editorFocusNode,
                controller: descriptionController,
                config: QuillSimpleToolbarConfig(
                  color: theme.brightness == Brightness.dark
                      ? AppTheme.darkBackground
                      : AppTheme.white,
                  embedButtons: FlutterQuillEmbeds.toolbarButtons(),
                  showClipboardCopy: false,
                  showClipboardCut: false,
                  showClipboardPaste: false,
                  multiRowsDisplay: false,
                  showUndo: false,
                  showRedo: false,
                  showHeaderStyle: true,
                  showFontSize: false,
                  showBackgroundColorButton: false,
                  showSearchButton: false,
                  showSuperscript: false,
                  showClearFormat: false,
                  showCodeBlock: false,
                  showDirection: false,
                  showDividers: false,
                  showListBullets: true,
                  showJustifyAlignment: false,
                  showLineHeightButton: false,
                  showSubscript: false,
                  showListNumbers: true,
                  showInlineCode: false,
                  showQuote: false,
                  showListCheck: false,
                  showStrikeThrough: false,
                  showIndent: false,
                  showColorButton: true,
                  showAlignmentButtons: true,
                  showCenterAlignment: true,
                  showRightAlignment: true,
                  showLeftAlignment: true,
                  showSmallButton: true,
                  showLink: true,
                  showFontFamily: false,
                  showBoldButton: true,
                  showItalicButton: true,
                  showUnderLineButton: true,
                  buttonOptions: QuillSimpleToolbarButtonOptions(
                    linkStyle: QuillToolbarLinkStyleButtonOptions(
                      linkRegExp: RegExp(
                        r'(?:(?:https?:\/\/)?(?:www\.)?)?[a-zA-Z0-9-]+(?:\.[a-zA-Z]{2,})(?:\/[^\s]*)?',
                        caseSensitive: false,
                      ),
                    ),
                    base: QuillToolbarBaseButtonOptions(
                      afterButtonPressed: () {
                        final isDesktop = {
                          TargetPlatform.linux,
                          TargetPlatform.windows,
                          TargetPlatform.macOS,
                        }.contains(defaultTargetPlatform);
                        if (isDesktop) {
                          editorFocusNode.requestFocus();
                        }
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
