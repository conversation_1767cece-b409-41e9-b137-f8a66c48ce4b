// Add this method to show the bottom sheet
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:incenti_ai/app/modules/post/controllers/post_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/buttons.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../utillites/app_theme.dart';
import '../../../../utillites/search_field.dart';

showGenieBottomSheet(
    {required BuildContext context, required PostController controller}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    clipBehavior: Clip.none,
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        constraints: BoxConstraints(maxHeight: MySize.getScaledSizeHeight(500)),
        decoration: BoxDecoration(
          color: AppTheme.appBottomSheet,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(40), topRight: Radius.circular(40)),
        ),
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: MySize.getScaledSizeWidth(30),
                      vertical: MySize.getScaledSizeHeight(13)),
                  child: Row(
                    children: [
                      SvgPicture.asset(AppImage.editorBot),
                      Space.height(8),
                      TypoGraphy(
                        text: "Ask Genie",
                        level: 3,
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: SvgPicture.asset(
                          AppImage.closeImage,
                          height: MySize.getScaledSizeHeight(20),
                          width: MySize.getScaledSizeWidth(20),
                          color: AppTheme.whiteWithNull,
                        ),
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    controller: controller.scrollController,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(30)),
                      child: Column(
                        children: [
                          SearchAppTextField(
                            // textInputAction: TextInputAction.newline,
                            textInputType: TextInputType.multiline,
                            textCapitalization: TextCapitalization.sentences,
                            controller: controller.messageController.value,
                            hintText: "Type message",
                            hintStyle: TextStyle(
                              fontSize: 16,
                              fontFamily: "Inter",
                              fontWeight: FontWeight.w400,
                              color: AppTheme.grey,
                            ),
                            maxLines: null,
                          ),
                          Space.height(10),
                          Obx(() {
                            final responseText = controller.isStreaming.value
                                ? controller.streamedResponse.value
                                : controller.messages.value;

                            return SelectableText.rich(
                              TextSpan(
                                text: responseText,
                                style: TextStyle(
                                  color: AppTheme.whiteWithBase,
                                  fontSize: MySize.getScaledSizeHeight(16),
                                  fontFamily: GoogleFonts.inter().fontFamily,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              scrollPhysics: const BouncingScrollPhysics(),
                              textAlign: TextAlign.left,
                              cursorColor: AppTheme.primary1,
                            );
                          }),
                          Space.height(50),
                        ],
                      ),
                    ),
                  ),
                ),
                // Space.height(60),
              ],
            ),
            Positioned(
              bottom: 10,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ValueListenableBuilder(
                    valueListenable: controller.messageController.value,
                    builder: (context, value, child) {
                      return Obx(
                        () => Buttons(
                          isDisable: controller.messageController.value.text
                                  .trim()
                                  .isEmpty ||
                              controller.isStreaming.value,
                          disableColor:
                              AppTheme.primary1.withValues(alpha: 0.4),
                          textDisableColor: AppTheme.white,
                          buttonText: controller.isStreaming.value
                              ? "Creating..."
                              : (controller.messageController.value.text
                                          .trim()
                                          .isNotEmpty &&
                                      !controller.isStreaming.value &&
                                      controller.messages.isNotEmpty)
                                  ? "Insert"
                                  : "Create",
                          onTap: () {
                            String message =
                                controller.messageController.value.text.trim();
                            if (controller.isStreaming.value == false &&
                                controller.messageController.value.text.trim().isNotEmpty &&
                                controller.messages.isNotEmpty) {
                              controller.insertGenieContentToEditor(
                                controller.streamedResponse.value.isNotEmpty
                                  ? controller.streamedResponse.value
                                  : controller.messages.value,
                              );
                              Navigator.pop(context);
                            } else if (message.isNotEmpty) {
                              controller.sendMessage(context);
                            }
                          },
                          borderRadius: BorderRadius.circular(50),
                          height: MySize.getScaledSizeHeight(50),
                          width: controller.isStreaming.value
                              ? MySize.getScaledSizeWidth(130)
                              : MySize.getScaledSizeWidth(95),
                          buttonTextLevel: 4,
                        ),
                      );
                    },
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    ),
  ).then(
    (value) {
      controller.messages.value = "";
      controller.messageController.value.clear();
    },
  );
}
