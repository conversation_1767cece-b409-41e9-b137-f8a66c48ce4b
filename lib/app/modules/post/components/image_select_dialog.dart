import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../constants/app_size_constant.dart';


void ImageTypeSelectPopup({
  required Function() onCamera,
  required Function() onGallery,
}) {
  Get.dialog(
    BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
      child: Dialog(
        insetPadding: EdgeInsets.symmetric(horizontal: MySize.size10 ?? 20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.0),
        ),
        child: Container(
          // height: MySize.getScaledSizeHeight(170),
          width: Get.width - 80,
          padding: EdgeInsets.symmetric(
            horizontal: MySize.getScaledSizeWidth(10),
            vertical: MySize.getScaledSizeHeight(10),
          ),

          decoration: BoxDecoration(
            color: AppTheme.appBottomSheet,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Space.height(15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TypoGraphy(
                    text: "SELECT AN OPTION",
                    level: 4,
                    color: AppTheme.primaryIconDark,
                      fontWeight: FontWeight.bold
                  ),
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Icon(
                      CupertinoIcons.clear_thick,
                      size: MySize.size25,
                      color: AppTheme.whiteWithNull,
                    ),
                  ),
                ],
              ),
              Space.height(20),
              GestureDetector(
                onTap: onGallery,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: MySize.getScaledSizeHeight(10),
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.primary1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        CupertinoIcons.photo,
                        size: MySize.size25,
                        color: AppTheme.primaryIconDark,
                      ),
                      Space.width(10),
                      Text(
                        "Upload From Gallery",
                        style: TextStyle(
                          fontSize: MySize.size17,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryIconDark,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Space.height(15),
              GestureDetector(
                onTap: onCamera,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: MySize.getScaledSizeHeight(10),
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.primary1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        CupertinoIcons.camera,
                        size: MySize.size25,
                        color: AppTheme.primaryIconDark,
                      ),
                      Space.width(10),
                      Text(
                        "Capture a Photo",
                        style: TextStyle(
                          fontSize: MySize.size17,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryIconDark,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Space.height(15),
            ],
          ),
        ),
      ),
    ),
  );
}

/*
class ImageTypeSelectPopup extends StatelessWidget {
  final Function() onCamera;
  final Function() onGallery;

  const ImageTypeSelectPopup({
    super.key,
    required this.onCamera,
    required this.onGallery,
  });

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            height: MySize.getScaledSizeHeight(170),
            width: Get.width - 80,
            padding: EdgeInsets.symmetric(
              horizontal: MySize.getScaledSizeWidth(10),
              vertical: MySize.getScaledSizeHeight(10),
            ),
            margin: EdgeInsets.symmetric(
              horizontal: MySize.getScaledSizeWidth(25),
            ),
            decoration: BoxDecoration(
              color: AppTheme.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Icon(
                    CupertinoIcons.clear_circled,
                    size: MySize.size30,
                    color: AppTheme.red,
                  ),
                ),
                Container(
                  height: MySize.getScaledSizeHeight(10),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: onCamera,
                        child: Container(
                          margin: EdgeInsets.only(
                            left: MySize.getScaledSizeWidth(10),
                            right: MySize.getScaledSizeWidth(10),
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: MySize.getScaledSizeHeight(10),
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppTheme.primary1,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                CupertinoIcons.camera,
                                size: MySize.size50,
                                color: AppTheme.primary1,
                              ),
                              Text(
                                "Camera",
                                style: TextStyle(
                                  fontSize: MySize.size17,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.primary1,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: MySize.getScaledSizeWidth(10),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: onGallery,
                        child: Container(
                          margin: EdgeInsets.only(
                            right: MySize.getScaledSizeWidth(10),
                            left: MySize.getScaledSizeWidth(10),
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: MySize.getScaledSizeHeight(10),
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppTheme.primary1,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                CupertinoIcons.photo,
                                size: MySize.size50,
                                color: AppTheme.primary1,
                              ),
                              Text(
                                "Gallery",
                                style: TextStyle(
                                  fontSize: MySize.size17,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.primary1,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
*/

class CommonFilePick {
  Future imagePick() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: [
        'jpg',
        'png',
        'jpeg',
      ],
    );
    return result;
  }
}
