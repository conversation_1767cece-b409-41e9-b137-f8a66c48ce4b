import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import '../components/community_widget_view.dart';
import '../controllers/communities_controller.dart';

class CommunitiesView extends StatefulWidget {
  const CommunitiesView({super.key});

  @override
  State<CommunitiesView> createState() => _CommunitiesViewState();
}

class _CommunitiesViewState extends State<CommunitiesView> {
  final CommunitiesController controller = Get.find<CommunitiesController>();

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Column(
        children: [
          Space.height(40),
          buildHeader(controller: controller,context: context),
          buildSearchField(context,controller: controller),
          Space.height(30),
          buildCommunityList(context,controller: controller),
        ],
      ),
    );
  }
}
