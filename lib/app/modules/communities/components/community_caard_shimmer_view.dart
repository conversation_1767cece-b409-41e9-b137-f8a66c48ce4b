
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';

class ShimmerGroupCard extends StatelessWidget {
  const ShimmerGroupCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(30),right: MySize.getScaledSizeWidth(30),bottom: MySize.getScaledSizeWidth(30)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: AppTheme.baseShimmer,
            highlightColor: AppTheme.highlightShimmer,
            child: Container(
              height: MySize.getScaledSizeHeight(108.31),
              width: MySize.getScaledSizeWidth(110),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(500),
              ),
            ),
          ),
          Space.width(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: AppTheme.baseShimmer,
                  highlightColor: AppTheme.highlightShimmer,
                  child: Container(
                    height: MySize.getScaledSizeWidth(16),
                    width: MySize.getScaledSizeWidth(140),
                    color: Colors.white,
                  ),
                ),
                Space.height(8),
                Shimmer.fromColors(
                  baseColor: AppTheme.baseShimmer,
                  highlightColor: AppTheme.highlightShimmer,
                  child: Container(
                    height: MySize.getScaledSizeHeight(12),
                    width: MySize.getScaledSizeWidth(100),
                    color: Colors.white,
                  ),
                ),
                Space.height(15),
                Shimmer.fromColors(
                  baseColor: AppTheme.baseShimmer,
                  highlightColor: AppTheme.highlightShimmer,
                  child: Container(
                    height: MySize.getScaledSizeHeight(40),
                    width: MySize.getScaledSizeWidth(84),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
