import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../models/app_community_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';

class CommunitiesController extends GetxController with WidgetsBindingObserver {
  DateTime? _backgroundTime;
  Rx<TextEditingController> searchController = TextEditingController().obs;
  ScrollController scrollController = ScrollController();
  RxInt selectedTabIndex = 0.obs;
  FocusNode searchFocusNode = FocusNode();
  RxBool isisable = true.obs;
  RxList tabList = ["Created", "Joined", "Explore"].obs;
  RxBool isLoading = false.obs;
  RxBool isJoinLoading = false.obs;
  ApiManager apiManager = ApiManager();
  RxList<CommunityData> communityCreatedList = <CommunityData>[].obs;
  RxList<CommunityData> communityJoinedList = <CommunityData>[].obs;
  RxList<CommunityData> communityExploreList = <CommunityData>[].obs;
  RxBool isLoadingCreated = false.obs;
  RxBool isLoadingJoined = false.obs;
  RxBool isLoadingExplore = false.obs;
  RxBool hasMoreData = true.obs;
  int limit = 10;
  RxInt page = 1.obs;
  Timer? debounceTimer;

  // bool isFetching = false;

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    callApiForGetCommunity(context: Get.context!);
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  void clearCommunityLists() {
    communityCreatedList.clear();
    communityJoinedList.clear();
    communityExploreList.clear();
  }

  List<CommunityData> get selectedCommunityList {
    switch (selectedTabIndex.value) {
      case 0:
        return communityCreatedList;
      case 1:
        return communityJoinedList;
      default:
        return communityExploreList;
    }
  }

  bool get selectedCommunityLoading {
    switch (selectedTabIndex.value) {
      case 0:
        return isLoadingCreated.value;
      case 1:
        return isLoadingJoined.value;
      default:
        return isLoadingExplore.value;
    }
  }

  void setTabLoading(int index) {
    isLoadingCreated.value = index == 0;
    isLoadingJoined.value = index == 1;
    isLoadingExplore.value = index == 2;
  }


  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      _backgroundTime = DateTime.now();
    } else if (state == AppLifecycleState.resumed) {
      if (_backgroundTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_backgroundTime!);
        if (difference.inMinutes >= 3) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            scrollController.animateTo(0, duration: Duration(seconds: 1), curve: Curves.easeInOut).then((value) =>  pullRefresh(),);
          });
        }
      }
    }
  }

  pullRefresh() {
    page.value = 1;
    hasMoreData.value = true;
    _clearCurrentTabList();
    callApiForGetCommunity(context: Get.context!);
  }

  void _clearCurrentTabList() {
    communityCreatedList.clear();
    communityJoinedList.clear();
    communityExploreList.clear();
  }
  Future<void> callApiForGetCommunity({required BuildContext context}) async {
    final isSearching = searchController.value.text.isNotEmpty;
    // Cancel any previous debounce
    if (isSearching && page.value == 1) {
      debounceTimer?.cancel();
      debounceTimer = Timer(Duration(milliseconds: 500), () {
        _clearCurrentTabList();
        fetchCommunityData(context: context);
      });
    } else {
      fetchCommunityData(context: context);
    }
  }



  void fetchCommunityData({required BuildContext context}) {
    int currentTab = selectedTabIndex.value;
    if (!hasMoreData.value) return;

    // isFetching = true;
    _setLoadingState(currentTab, true);

    final params = {
      if (currentTab == 0) "created": 1,
      if (currentTab == 1) "joined": 1,
      "page": page.value,
      "limit": limit,
      if (searchController.value.text.isNotEmpty)
        "searchQuery": searchController.value.text.trim(),
    };

    apiManager.callApi(
      APIS.communities.getCommunities,
      params: params,
      successCallback: (response, message) {
        try {
          if (response['status'] == 'success') {
            CommunityResponse communityResponse = CommunityResponse.fromJson(
                response);

            if (communityResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }
            if (selectedTabIndex.value != currentTab) return;

            _updateCommunityList(currentTab, communityResponse.data.data);
          }
        } catch (error) {
          log("Error processing response: $error");
          hasMoreData.value = false;
        } finally {
          _setLoadingState(currentTab, false);
        }
      },
      failureCallback: (message, statusCode) {
        log("Error: $message");
        _setLoadingState(currentTab, false);
        hasMoreData.value = false;
      },
    );
  }

  void _setLoadingState(int tab, bool isLoading) {
    if (tab == 0) {
      isLoadingCreated.value = isLoading;
    } else if (tab == 1) {
      isLoadingJoined.value = isLoading;
    } else {
      isLoadingExplore.value = isLoading;
    }
  }

  void _updateCommunityList(int tab, List<CommunityData> newData) {
    if (tab == 0) {
      final existingIds = communityCreatedList.map((e) => e.id).toSet();

      final newItems = newData.where((item) => !existingIds.contains(item.id));
      communityCreatedList.addAll(newItems);
    } else if (tab == 1) {
      final existingIds = communityJoinedList.map((e) => e.id).toSet();

      final newItems = newData.where((item) => !existingIds.contains(item.id));
      communityJoinedList.addAll(newItems);
    } else {
      final existingIds = communityExploreList.map((e) => e.id).toSet();

      final newItems = newData.where((item) => !existingIds.contains(item.id));
      communityExploreList.addAll(newItems);
    }
    page.value++;
  }

  Future<void> callApiForJoinCommunity(
      {required BuildContext context, required String communityId, required int index}) {
    FocusScope.of(context).unfocus();
    isJoinLoading.value = true;
    if (selectedTabIndex.value == 1) {
      communityJoinedList[index].isJoined?.value =
      !(communityJoinedList[index].isJoined?.value ?? false);
    } else if (selectedTabIndex.value == 2) {
      communityExploreList[index].isJoined?.value =
      !(communityExploreList[index].isJoined?.value ?? false);
    }

    final ApiModel getPost = ApiModel(
        "/community-members/$communityId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            isJoinLoading.value = false;
          }
        } catch (error) {
          log("error === $error");
          isJoinLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isJoinLoading.value = false;
      },
    );
  }
}
