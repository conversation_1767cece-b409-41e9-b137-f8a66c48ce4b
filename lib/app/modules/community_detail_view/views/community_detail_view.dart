import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/communities/controllers/communities_controller.dart';
import 'package:incenti_ai/app/modules/project_detail_view/components/project_detail_header.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:intl/intl.dart';
import '../../../../constants/app_image.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/common_shimmer_effect.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../bottom_bar/views/bottom_bar_view.dart';
import '../../project_detail_view/components/project_detail_about_view.dart';
// import '../../project_detail_view/components/project_detail_header.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../components/community_post_view.dart';
import '../components/community_shimmer_view.dart';
import '../components/member_view.dart';
import '../controllers/community_detail_view_controller.dart';

class CommunityDetailView extends GetWidget<CommunityDetailViewController> {
  const CommunityDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      bottomNavigationBar:
          controller.args != null && controller.args["isNeedBottom"] != null
              ? customBottomNavigation()
              : null,
      floatingActionButton: Obx(
        () => (Get.find<CommunitiesController>().selectedTabIndex.value != 2 ||
                controller.getCommunityData.value.isJoined?.value == true)
            ? Padding(
                padding: EdgeInsets.only(
                    right: MySize.getScaledSizeWidth(10),
                    bottom: MySize.getScaledSizeHeight(10)),
                child: FloatingActionButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Get.toNamed(Routes.post, arguments: {
                      'isCommunity': true,
                      "communityId": controller.getCommunityData.value.id,
                      "communityUserId": controller.getCommunityData.value.UserId
                    })?.then(
                      (value) {
                        controller.page.value = 1;
                        controller.hasMoreData.value = true;
                        controller.communityPostDataList.clear();
                        return controller.callApiForGetCommunityPost(
                          context: context,
                        );
                      },
                    );
                  },
                  child: SvgPicture.asset(AppImage.postIcon),
                ),
              )
            : SizedBox(),
      ),
      body: Column(
        children: [
          Obx(
            () => controller.isCommunityLoading.value
                ? Expanded(child: CommunityShimmerView())
                : Expanded(
                    child: Obx(
                      () => CustomScrollView(
                        physics: ClampingScrollPhysics(),
                        slivers: [
                          SliverToBoxAdapter(
                            child: Stack(
                              children: [
                                buildProjectImage(
                                    imageUrl: controller
                                        .getCommunityData.value.image, defaultImageUrl: AppImage.defaultCommunity),
                                buildTopGradient(),
                                buildTopBarCommunity(context, controller: controller),
                              ],
                            ),
                          ),
                          SliverToBoxAdapter(child: Space.height(30)),
                          SliverToBoxAdapter(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MySize.getScaledSizeWidth(30)),
                                  child: InkWell(
                                    onTap: () {
                                      ImagePickerBottomSheet.show(
                                        context: context,
                                        child: ProjectDetailsWidget(
                                          currentUseId: controller.getCommunityData.value
                                              .user?.id ?? 0,
                                          communityMember: controller
                                              .getCommunityData.value.members,
                                          projectName: controller
                                                  .getCommunityData
                                                  .value
                                                  .name ??
                                              "",
                                          projectDescription: controller
                                                  .getCommunityData
                                                  .value
                                                  .description ??
                                              "",
                                          userImage: controller.getCommunityData
                                                  .value.user?.image ??
                                              "",
                                          userName: controller.getCommunityData
                                                  .value.user?.firstName ??
                                              "test",
                                          userLastName: controller
                                                  .getCommunityData
                                                  .value
                                                  .user
                                                  ?.lastName ??
                                              "name",
                                          createdAt: DateFormat('MMM d, y')
                                              .format(DateTime.parse(controller
                                                  .getCommunityData
                                                  .value
                                                  .createdAt
                                                  .toString())),
                                          isCurrentUser: CurrentUser.user.id ==
                                              controller.getCommunityData.value
                                                  .user?.id,
                                          isCommunity: true,
                                          isCommunityJoin:
                                              Get.find<CommunitiesController>()
                                                      .selectedTabIndex
                                                      .value ==
                                                  1,
                                          communityJoinDate:
                                              controller.joiningDate.value,
                                        ),
                                      );
                                    },
                                    child: RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: controller.getCommunityData
                                                    .value.name ??
                                                "",
                                            style: TextStyle(
                                              fontSize: 30,
                                              fontWeight: FontWeight.w700,
                                              color: AppTheme.whiteWithBase,
                                            ),
                                          ),
                                          WidgetSpan(
                                            // alignment: PlaceholderAlignment.middle,
                                            child: Padding(
                                              padding: EdgeInsets.only(
                                                  left: 10, bottom: 5),
                                              child: SvgPicture.asset(
                                                AppImage.back,
                                                height: MySize.size16,
                                                color: AppTheme.whiteWithBase,
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                Space.height(8),
                                if (CurrentUser.user.id ==
                                    controller.getCommunityData.value.UserId)
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.size30 ?? 30),
                                    child: TypoGraphy(
                                      text: "Created by me",
                                      level: 2,
                                      color: AppTheme.grey,
                                      fontWeight: FontWeight.w400,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                CurrentUser.user.id ==
                                        controller.getCommunityData.value.UserId
                                    ? Space.height(8)
                                    : Space.height(0),
                                if (controller
                                        .getCommunityData.value.location !=
                                    "") ...[
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.size30 ?? 30),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.only(
                                              top: MySize.size2 ?? 10),
                                          child: Image.asset(
                                            AppImage.location,
                                            height:
                                                MySize.getScaledSizeHeight(15),
                                            width:
                                                MySize.getScaledSizeWidth(15),
                                          ),
                                        ),
                                        Space.width(4),
                                        Expanded(
                                          child: TypoGraphy(
                                            text: controller.getCommunityData
                                                .value.location,
                                            level: 3,
                                            color: AppTheme.grey,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                Space.height(20),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.getCommunityData
                                                  .value.members ==
                                              "0"
                                          ? MySize.size5 ?? 30
                                          : MySize.size30 ?? 30),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: ((controller
                                                        .getCommunityData
                                                        .value
                                                        .communityMembers
                                                        .length) -
                                                    1) *
                                                MySize.getScaledSizeWidth(26) +
                                            MySize.getScaledSizeWidth(40),
                                        height: MySize.getScaledSizeHeight(35),
                                        child: Stack(
                                          clipBehavior: Clip.none,
                                          children: List.generate(
                                              (controller
                                                  .getCommunityData
                                                  .value
                                                  .communityMembers
                                                  .length), (index) {
                                            return Positioned(
                                              left: index *
                                                  MySize.getScaledSizeWidth(26),
                                              // Dynamic spacing
                                              child: Container(
                                                height:
                                                    MySize.getScaledSizeHeight(
                                                        40),
                                                width:
                                                    MySize.getScaledSizeWidth(
                                                        40),
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          500),
                                                  border: Border.all(
                                                      color: AppTheme.white,
                                                      width: 2),
                                                ),
                                                child: Stack(
                                                  children: [
                                                    profileImage(
                                                        url: controller
                                                                .getCommunityData
                                                                .value
                                                                .communityMembers[
                                                                    index]
                                                                .user
                                                                ?.image ??
                                                            "",
                                                        userName: controller
                                                                .getCommunityData
                                                                .value
                                                                .communityMembers[
                                                                    index]
                                                                .user
                                                                ?.firstName ??
                                                            "",
                                                        height:
                                                            MySize.getScaledSizeHeight(
                                                                40),
                                                        width:
                                                            MySize.getScaledSizeWidth(
                                                                40),
                                                        iconHeight:
                                                            MySize.getScaledSizeHeight(
                                                                40),
                                                        iconWidth:
                                                            MySize.getScaledSizeHeight(
                                                                40),
                                                        borderColor:
                                                            Colors.transparent,
                                                        color: AppTheme
                                                            .darkGrey[100]),
                                                    if (index == 7)
                                                      Container(
                                                        padding: EdgeInsets.symmetric(
                                                            horizontal: MySize
                                                                .getScaledSizeWidth(
                                                                    8)),
                                                        height: MySize
                                                            .getScaledSizeHeight(
                                                                40),
                                                        width: MySize
                                                            .getScaledSizeWidth(
                                                                40),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: AppTheme.black
                                                              .withValues(
                                                                  alpha: 0.5),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      500),
                                                        ),
                                                        child: SvgPicture.asset(
                                                          AppImage.moreVertIcon,
                                                          color: AppTheme.white,
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          }),
                                        ),
                                      ),
                                      Space.width(15),
                                      InkWell(
                                        onTap: () {
                                          if (controller.getCommunityData.value
                                                  .members !=
                                              "0") {
                                            controller.memberPage.value = 1;
                                            controller.hasMoreData.value = true;
                                            controller.memberDataList.clear();
                                            controller
                                                .callApiForGetCommunityMember(
                                                    context: context,
                                                    communityId: controller
                                                        .getCommunityData
                                                        .value
                                                        .id
                                                        .toString());
                                            Get.bottomSheet(
                                              const MemberView(),
                                              isScrollControlled: true,
                                              backgroundColor: Colors.white,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.vertical(
                                                        top: Radius.circular(
                                                            20)),
                                              ),
                                            ).then((value) {
                                              controller.page.value = 1;
                                              controller.hasMoreData.value = true;
                                              controller.communityPostDataList.clear();
                                              controller.callApiForGetCommunityPost(
                                                context: context,
                                              );
                                            },);
                                          }
                                        },
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            TypoGraphy(
                                              text:
                                                  "${controller.getCommunityData.value.members} members ",
                                              level: 2,
                                              fontWeight: FontWeight.w400,
                                              color: AppTheme.grey,
                                            ),
                                            Space.height(2),
                                            if (controller.getCommunityData
                                                    .value.members !=
                                                "0")
                                              TypoGraphy(
                                                text: "see all",
                                                level: 4,
                                                color: AppTheme.primary1,
                                              ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Space.height(25),
                                if (CurrentUser.user.id !=
                                        controller
                                            .getCommunityData.value.user?.id &&
                                    (controller.getCommunityData.value.isJoined
                                            ?.value ==
                                        false))
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Buttons(
                                        buttonText: "Join",
                                        buttonTextLevel: 4,
                                        height: MySize.getScaledSizeHeight(64),
                                        width: MySize.getScaledSizeWidth(125),
                                        onTap: () {
                                          HapticFeedback.lightImpact();
                                          Get.find<CommunitiesController>()
                                              .callApiForJoinCommunity(
                                            context: context,
                                            communityId: controller
                                                .getCommunityData.value.id
                                                .toString(),
                                            index: controller.index.value,
                                          );
                                          controller.getCommunityData
                                              .update((val) {
                                            if (val != null) {
                                              val.isJoined?.value =
                                                  !(val.isJoined?.value ??
                                                      true);
                                            }
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                if (CurrentUser.user.id !=
                                        controller
                                            .getCommunityData.value.user?.id &&
                                    (controller.getCommunityData.value.isJoined
                                            ?.value ==
                                        true))
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          HapticFeedback.lightImpact();
                                          Get.find<CommunitiesController>()
                                              .callApiForJoinCommunity(
                                            context: context,
                                            communityId: controller
                                                .getCommunityData.value.id
                                                .toString(),
                                            index: controller.index.value,
                                          );
                                          controller.getCommunityData
                                              .update((val) {
                                            if (val != null) {
                                              val.isJoined?.value =
                                                  !(val.isJoined?.value ??
                                                      true);
                                            }
                                          });
                                        },
                                        child: Container(
                                          height:
                                              MySize.getScaledSizeHeight(64),
                                          width:
                                              MySize.getScaledSizeHeight(125),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(20),
                                            border: Border.all(
                                              color: AppTheme.grey,
                                              width: 1,
                                            ),
                                          ),
                                          alignment: Alignment.center,
                                          child: TypoGraphy(
                                            text: "Joined",
                                            textStyle: TextStyle(
                                              fontSize:
                                                  MySize.getScaledSizeHeight(
                                                      15),
                                              fontWeight: FontWeight.w600,
                                              color: AppTheme.grey,
                                            ),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                              ],
                            ),
                          ),
                          controller.isCommunityPostLoading.value &&
                                  controller.communityPostDataList.isEmpty
                              ? SliverToBoxAdapter(child: ShimmerPostCard())
                              : communityPostDataView(context,controller),
                        ],
                      ),
                    ),
                  ),
          )
        ],
      ),
    );
  }
}
