import 'package:flutter/material.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_shimmer_effect.dart';

class CommunityShimmerView extends StatelessWidget {

  const CommunityShimmerView({super.key,});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Shimmer.fromColors(
        baseColor: AppTheme.baseShimmer,
        highlightColor: AppTheme.highlightShimmer,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                width: double.infinity,
                height: MySize.getScaledSizeHeight(182),
                color: Colors.grey[400],
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Space.height(20),
                    // Name placeholder
                    Container(
                      width: MySize.getScaledSizeWidth(150),
                      height: MySize.getScaledSizeHeight(14),
                      color: Colors.grey[400],
                    ),
                    Space.height(10),

                    Container(
                      width: MySize.getScaledSizeWidth(180),
                      height:  MySize.getScaledSizeHeight(12),
                      color: Colors.grey[400],
                    ),
                    Space.height(10),

                    Container(
                      width: MySize.getScaledSizeWidth(317),
                      height: MySize.getScaledSizeHeight(40),
                      decoration: BoxDecoration(
                        color: Colors.grey[400],
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    Space.height(25),
                    Center(
                      child: Container(
                        width: MySize.getScaledSizeWidth(125),
                        height: MySize.getScaledSizeHeight(64),
                        decoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                    ),
                    Space.height(50),
                  ],
                ),
              ),
              ShimmerPostCard(isPadding: false),
            ],
          ),
        ),
      ),
    );
  }
}
