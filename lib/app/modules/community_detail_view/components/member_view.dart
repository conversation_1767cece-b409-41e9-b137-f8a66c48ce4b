import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/models/app_member_model.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../routes/app_pages.dart';
import '../../profile_view/components/remover_follower.dart';
import '../controllers/community_detail_view_controller.dart';

class MemberView extends StatelessWidget {
  const MemberView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CommunityDetailViewController>();
    return Scaffold(
      resizeToAvoidBottomInset: false,
      // backgroundColor: AppTheme.white,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Space.height(60),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            // mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(left: MySize.size30 ?? 20),
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    Get.back();
                  },
                  child: SvgPicture.asset(
                    AppImage.backArrow,
                    height: MySize.size28,
                    width: MySize.size28,
                    color: AppTheme.whiteWithNull,
                  ),
                ),
              ),
              // Spacer(),
              TypoGraphy(
                text: "Members",
                level: 12,
              ),
              Padding(
                padding: EdgeInsets.only(left: MySize.size30 ?? 20),
                child: SizedBox(
                  height: MySize.size28,
                  width: MySize.size28,
                ),
              ),
            ],
          ),
          Space.height(21),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
            child: ValueListenableBuilder(
              valueListenable: controller.searchController.value,
              builder: (context, value, child) {
                return AppTextField(
                  controller: controller.searchController.value,
                  focusNode: controller.searchFocusNode,
                  padding: EdgeInsets.only(
                    top: MySize.size12 ?? 12,
                    bottom: MySize.size10 ?? 12,
                  ),
                  onChangedValue: (text) {
                    if ((text?.trim() ?? "").isNotEmpty) {
                      controller.debounceTimer?.cancel();
                      controller.debounceTimer =
                          Timer(Duration(milliseconds: 500), () {
                        controller.memberPage.value = 1;
                        controller.memberDataList.clear();
                        controller.pullRefresh();
                      });
                    }
                  },
                  height: MySize.getScaledSizeHeight(50),
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(4),
                    child: SvgPicture.asset(
                      AppImage.searchIcon,
                      height: MySize.size24,
                      width: MySize.size24,
                      color: AppTheme.grey,
                    ),
                  ),
                  suffixIcon:
                      (controller.searchController.value.text.isNotEmpty)
                          ? GestureDetector(
                              onTap: () {
                                controller.searchController.value.clear();
                                controller.memberPage.value = 1;

                                Future.delayed(
                                  Duration(milliseconds: 500),
                                  () {
                                    controller.memberDataList.clear();
                                    controller.pullRefresh();
                                  },
                                );
                              },
                              child: Padding(
                                padding:
                                    EdgeInsets.only(right: MySize.size15 ?? 20),
                                child: SvgPicture.asset(
                                  AppImage.textFieldClear,
                                  color: AppTheme.grey,
                                ),
                              ),
                            )
                          : null,
                  hintText: "Search",
                  hintStyle: TextStyle(
                    color: AppTheme.grey,
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w400,
                  ),
                );
              },
            ),
          ),
          Space.height(30),
          Expanded(
            child: Obx(() => controller.isLoading.value &&
                    controller.memberDataList.isEmpty
                ? Loader()
                : controller.searchController.value.text.isNotEmpty &&
                        controller.memberDataList.isEmpty
                    ? Padding(
                        padding: EdgeInsets.only(
                          top: MySize.getScaledSizeHeight(250),
                          left: paddingHoriZontal,
                          right: paddingHoriZontal,
                        ),
                        child: Column(
                          children: [
                            SvgPicture.asset(
                              "assets/images/icon_search.svg",
                              height: MySize.size50,
                              color: AppTheme.whiteWithNull,
                            ),
                            Empty(
                              title: "Search Result Not Found !",
                            ),
                          ],
                        ),
                      )
                    : CustomScrollView(
                        slivers: [
                          CustomSliverListView(
                            emptyWidget: Padding(
                                padding: EdgeInsets.only(
                                  top: MySize.getScaledSizeHeight(250),
                                  left: paddingHoriZontal,
                                  right: paddingHoriZontal,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    TypoGraphy(
                                      text: "No Member",
                                      level: 5,
                                    ),
                                    Space.height(5),
                                    TypoGraphy(
                                      text: "No member yet!",
                                      level: 3,
                                      color: AppTheme.grey,
                                    )
                                  ],
                                )),
                            maximumReachedWidget: const SizedBox(),
                            itemBuilder: (p0, p1, index) {
                              return Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size30 ?? 30,vertical: MySize.getScaledSizeHeight(8)),
                                child: InkWell(
                                  onTap: () {
                                    if(CurrentUser.user.id !=
                                        controller.memberDataList[index].user?.id) {
                                     Get.toNamed(Routes.other_user_profile,arguments: {"UserId": controller.memberDataList[index].user?.id});
                                    } else {
                                      Get.to(Routes.profile);
                                    }
                                  },
                                  child: ListTile(
                                      contentPadding: EdgeInsets.zero,
                                      leading: profileImage(
                                        url: controller.memberDataList[index].user
                                                ?.image ??
                                            "",
                                        userName: controller.memberDataList[index]
                                                .user?.firstName ??
                                            "",
                                        iconHeight:
                                            MySize.getScaledSizeHeight(60),
                                        iconWidth: MySize.getScaledSizeWidth(60),
                                        height: MySize.getScaledSizeHeight(60),
                                        width: MySize.getScaledSizeWidth(60),
                                      ),
                                      title: TypoGraphy(
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        text: () {
                                          final isSearching = controller.searchController.value.text.isNotEmpty;
                                          if(!isSearching && index == 0) {
                                            return "${controller.memberDataList[index].user?.firstName} ${controller.memberDataList[index].user?.lastName} (Admin)";
                                          } else if(controller.memberDataList[index] is AdminMemberData){
                                            return "${controller.memberDataList[index].user?.firstName} ${controller.memberDataList[index].user?.lastName} (Admin)";
                                          }
                                          else if (CurrentUser.user.id ==
                                              controller.memberDataList[index]
                                                  .user?.id) {
                                            return "${controller.memberDataList[index].user?.firstName} ${controller.memberDataList[index].user?.lastName} (You)";
                                          } else {
                                            return "${controller.memberDataList[index].user?.firstName} ${controller.memberDataList[index].user?.lastName}";
                                          }
                                        }(),
                                            // "${controller.memberDataList[index].user?.firstName} ${controller.memberDataList[index].user?.lastName}",
                                        level: 12,
                                        // color: AppTheme.baseBlack,
                                      ),
                                      // subtitle: CurrentUser.user.id ==
                                      //         controller
                                      //             .memberDataList[index].user?.id
                                      //     ? TypoGraphy(
                                      //         maxLines: 1,
                                      //         overflow: TextOverflow.ellipsis,
                                      //         text: "You",
                                      //         level: 2,
                                      //         fontWeight: FontWeight.w400,
                                      //         color: AppTheme.grey,
                                      //       )
                                      //     : TypoGraphy(
                                      //         maxLines: 1,
                                      //         overflow: TextOverflow.ellipsis,
                                      //         text:
                                      //             "${controller.memberDataList[index].user?.email}",
                                      //         level: 2,
                                      //         color: AppTheme.grey,
                                      //       ),
                                      trailing: CurrentUser.user.id ==
                                              controller
                                                  .memberDataList[index].user?.id
                                          ? SizedBox()
                                          : Obx(
                                              () => InkWell(
                                                onTap: () {
                                                  if (controller
                                                          .memberDataList[index]
                                                          .user
                                                          ?.isFollowing
                                                          ?.value ==
                                                      false) {
                                                    showModalBottomSheet(
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      context: context,
                                                      builder: (context) {
                                                        return RemoveFollower(
                                                          userName:
                                                              "${controller.memberDataList[index].user?.firstName} ${controller.memberDataList[index].user?.lastName}",
                                                          image: controller
                                                                  .memberDataList[
                                                                      index]
                                                                  .user
                                                                  ?.image ??
                                                              "",
                                                          onRemove: () {
                                                            controller.callApiForUnFollowUser(
                                                                context:
                                                                    Get.context!,
                                                                userId: controller
                                                                    .memberDataList[
                                                                        index]
                                                                    .user
                                                                    ?.id,
                                                                isUnFollowed:
                                                                    true,
                                                                index: index);
                                                          },
                                                          isForUnfollow: true,
                                                        );
                                                      },
                                                    );
                                                  } else {
                                                    controller
                                                        .memberDataList[index]
                                                        .user
                                                        ?.isFollowing
                                                        ?.value = false;
                                                    controller
                                                        .callApiForFollowUser(
                                                            context: Get.context!,
                                                            userId: controller
                                                                .memberDataList[
                                                                    index]
                                                                .user
                                                                ?.id);
                                                  }
                                                },
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(50),
                                                    color: (Theme.of(context)
                                                        .brightness ==
                                                        Brightness
                                                            .dark &&
                                                        !(controller
                                                            .memberDataList[
                                                        index]
                                                            .user
                                                            ?.isFollowing
                                                            ?.value ==
                                                            false))
                                                        ? AppTheme.bottomBar
                                                        : Colors.transparent,
                                                    border: Border.all(
                                                        color: controller
                                                                    .memberDataList[
                                                                        index]
                                                                    .user
                                                                    ?.isFollowing
                                                                    ?.value ==
                                                                false
                                                            ? AppTheme.grey
                                                            : Theme.of(context)
                                                            .brightness ==
                                                            Brightness
                                                                .dark
                                                            ? AppTheme.grey
                                                            : AppTheme
                                                            .primary1),
                                                  ),
                                                  child: Padding(
                                                    padding: EdgeInsets.symmetric(
                                                      horizontal: MySize
                                                          .getScaledSizeWidth(17),
                                                      vertical: MySize
                                                          .getScaledSizeHeight(
                                                              11),
                                                    ),
                                                    child: TypoGraphy(
                                                      text: controller
                                                                  .memberDataList[
                                                                      index]
                                                                  .user
                                                                  ?.isFollowing
                                                                  ?.value ==
                                                              false
                                                          ? "Unfollow"
                                                          : "Follow",
                                                      textStyle: TextStyle(
                                                        fontFamily: "Inter",
                                                        color: controller
                                                                    .memberDataList[
                                                                        index]
                                                                    .user
                                                                    ?.isFollowing
                                                                    ?.value ==
                                                                false
                                                            ? AppTheme.grey
                                                            : AppTheme.primaryIconDark,
                                                        fontSize: MySize
                                                            .getScaledSizeHeight(
                                                                15),
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            )),
                                ),
                              );
                            },
                            items: controller.memberDataList,
                            isLoading: controller.apiManager.isLoading,
                            hasMoreData: controller.hasMoreData.value,
                            onLoadMore: () {
                              return controller.callApiForGetCommunityMember(
                                context: context,
                                communityId:
                                    controller.communityId.value.toString(),
                              );
                            },
                          )
                        ],
                      )),
          ),
        ],
      ),
    );
  }
}
