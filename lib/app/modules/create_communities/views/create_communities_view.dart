import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/typography.dart';
import '../../../../constants/app_image.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/locaion_input_field.dart';
import '../../../../utillites/network_image.dart';
import '../../post/components/image_select_dialog.dart';
import '../../user_detail/components/profile_image_view.dart';
import '../controllers/create_communities_controller.dart';

class CreateCommunitiesView extends GetWidget<CreateCommunitiesController> {
  const CreateCommunitiesView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      // backgroundColor: AppTheme.white,
      body: Stack(
        children: [
          Column(
            // mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Space.height(51.84),
              Padding(
                padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(30)),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: SvgPicture.asset(
                      AppImage.closeImage,
                      height: MySize.getScaledSizeHeight(20),
                      width: MySize.getScaledSizeHeight(20),
                      color: AppTheme.whiteWithBase,
                    ),
                  ),
                ),
              ),
              Space.height(11.83),
              TypoGraphy(
                text: (controller.args != null && controller.args["communityId"] != null) ? "Edit Community" : "Create Community",
                level: 8,
                fontWeight: FontWeight.w700,
              ),
              Space.height(4),
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(60)),
                child: TypoGraphy(
                  text:
                      "Create a new community to discuss with others who share the same interests as you.",
                  level: 3,
                  fontWeight: FontWeight.w400,
                  color: AppTheme.grey,
                  textAlign: TextAlign.center,
                ),
              ),
              Space.height(30),
              Expanded(
                child: SingleChildScrollView(
                  keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(45)),
                        child: AppTextField(
                          focusNode: controller.communityNameFocusNode,
                          controller: controller.communityNameController.value,
                          labelText: "Community Name*",
                          maxLength: 50,
                          onChangedValue: (p0) {
                            controller.communityNameController.refresh();
                            controller.update();
                          },
                          textCapitalization: TextCapitalization.sentences,
                        ),
                      ),
                      Obx(
                            () => (controller.communityNameController.value.text
                            .length >=
                            50)
                            ? Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal:
                              MySize.getScaledSizeWidth(45)).copyWith(top: MySize.getScaledSizeWidth(10)),
                          child: TypoGraphy(
                            text: "Maximum 50 characters allowed",
                            level: 2,
                            fontWeight: FontWeight.w400,
                            color: AppTheme.red,
                          ),
                        )
                            : SizedBox.shrink(),
                      ),
                      // Space.height(20),
                      ExpansionTile(
                        childrenPadding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(45)),
                        iconColor: AppTheme.whiteWithBase,
                        collapsedIconColor: AppTheme.whiteWithBase,
                        tilePadding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                        shape: const RoundedRectangleBorder(
                          side: BorderSide.none,
                        ),
                        title: TypoGraphy(
                          text: "Add More (Optional)",
                          level: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        children: [
                          GetBuilder<CreateCommunitiesController>(
                            builder: (controller) => AppTextField(
                              textInputAction: TextInputAction.newline,
                              textInputType: TextInputType.multiline,
                              textCapitalization: TextCapitalization.sentences,
                              height: MySize.size140,
                              focusNode:
                              controller.communityDescriptionFocusNode.value,
                              controller:
                              controller.communityDescriptionController.value,
                              labelText: controller.communityDescriptionFocusNode
                                  .value.hasFocus ||
                                  controller.communityDescriptionController.value
                                      .text.isNotEmpty
                                  ? "Description"
                                  : null,
                              hintText: controller
                                  .communityDescriptionFocusNode.value.hasFocus
                                  ? ""
                                  : "Description",
                              maxLines: 5,
                              maxLength: 250,
                            ),
                          ),
                          Space.height(6),
                          Obx(
                                () => Align(
                              alignment: Alignment.centerRight,
                              child: TypoGraphy(
                                text:
                                "${controller.charCount}/${controller.maxChars}",
                                level: 2,
                                fontWeight: FontWeight.w400,
                                color: AppTheme.grey,
                              ),
                            ),
                          ),
                          Space.height(20),
                          LocationInputField(
                            controller: controller.locationController,
                            key: controller.locationFieldKey,
                            selectedSuggestion: controller.meetLocation,
                            onFocused: () {
                              controller.scrollToLocationField(context);
                            },
                          ),
                          Space.height(5),
                          TypoGraphy(
                            text: "Add a location to find specific Community easily.",
                            level: 3,
                            fontWeight: FontWeight.w300,
                            fontStyle: FontStyle.italic,
                            color: AppTheme.grey,
                          ),
                          Space.height(30),
                          Obx(
                                () => GestureDetector(
                              onTap: () {
                                if (controller.communityNameFocusNode.hasFocus) {
                                  controller.communityNameFocusNode.unfocus();
                                }
                                if (controller
                                    .communityDescriptionFocusNode.value.hasFocus) {
                                  controller.communityDescriptionFocusNode.value
                                      .unfocus();
                                }
                                if (controller.selectedImage.value == "") {
                                  // Get.dialog(
                                  //   barrierDismissible: false,
                                  ImageTypeSelectPopup(
                                    onCamera: () async {
                                      // Get.back();
                                      Navigator.pop(context);
                                      File? pick =
                                      await CommonFunction.pickImageFromCamera();

                                      if (pick != null) {
                                        if (await controller.isValidImage(pick)) {
                                          controller.callApiForProjectImage(
                                              context: context, imageFile: pick.path);
                                          // controller.selectedImage.value = pick;
                                        } else {
                                          CommonFunction.showCustomSnackbar(
                                              message:
                                              "Please upload a JPEG or PNG file under 2MB.",
                                              backgroundColor: AppTheme.red,
                                              isError: true);
                                        }
                                      }
                                    },
                                    onGallery: () async {
                                      // Get.back();
                                      Navigator.pop(context);
                                      File? pick =
                                      await CommonFunction.pickImageFromGallery();
                                      if (pick != null) {
                                        if (await controller.isValidImage(pick)) {
                                          controller.callApiForProjectImage(
                                              context: context, imageFile: pick.path);
                                          // controller.selectedImage.value = pick;
                                        } else {
                                          CommonFunction.showCustomSnackbar(
                                              message:
                                              "Please upload a JPEG or PNG file under 2MB.",
                                              backgroundColor: AppTheme.red,
                                              isError: true);
                                        }
                                      }
                                    },
                                  );
                                  // );
                                }
                              },
                              child: controller.selectedImage.value != ""
                                  ? ProfileImageWithEditIcon(
                                top: -8,
                                right: -10,
                                clipOval: ClipRRect(
                                    borderRadius: BorderRadius.circular(14),
                                    child: NetworkImageComponent(
                                      imageUrl: controller.selectedImage.value,
                                      simmerHeight:
                                      MySize.getScaledSizeHeight(220),
                                      height: MySize.size220,
                                      width: double.infinity,
                                      // aspectRatio: 430/183,
                                    )),
                                editIconPath: AppImage.closeImage,
                                color: AppTheme.baseBlack,
                                padding: 10,
                                imageSize: MySize.size120 ?? 120,
                                iconSize: MySize.size32 ?? 34,
                                blurSigma: MySize.size35 ?? 30,
                                onEditIconTap: () {
                                  controller.selectedImage.value = "";
                                },
                              )
                                  : Container(
                                height: MySize.size220,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  color: Theme.of(context).brightness == Brightness.dark ? AppTheme.darkBackground : AppTheme.lightGrey,
                                ),
                                child: DottedBorder(
                                  borderType: BorderType.RRect,
                                  dashPattern: const [8, 6],
                                  color: AppTheme.grey,
                                  borderPadding:
                                  EdgeInsets.all(MySize.size1 ?? 1),
                                  radius: Radius.circular(14),
                                  child: Center(
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                      MainAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                          AppImage.uploadImage,
                                          height: MySize.size50,
                                          width: MySize.size50,
                                        ),
                                        Space.height(12),
                                        TypoGraphy(
                                          text: "Upload Image",
                                          level: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        Space.height(4),
                                        TypoGraphy(
                                          text:
                                          "Formats: JPEG, PNG",
                                          level: 3,
                                          fontWeight: FontWeight.w300,
                                          color: AppTheme.grey,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      Space.height(40),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Obx(
                            () => Buttons(
                              height: MySize.getScaledSizeHeight(70),
                              width: MySize.getScaledSizeWidth(198),
                              buttonText: controller.args != null && controller.args["communityId"] != null ? "Update" : "Create",
                              buttonTextLevel: 4,
                              isLoading: controller.isLoading.value,
                             /* onTap: () {
                                if (controller.communityNameController.value.text
                                    .trim()
                                    .isNotEmpty) {
                                  if(controller.args != null && controller.args["communityId"] != null) {
                                    controller.callApiForUpdateOneCommunity(
                                        context: context,communityId: controller.communityId.value.toString());
                                  } else {
                                    if(controller.meetLocation.value == controller.locationController.value.text) {
                                      controller.callApiForCreateCommunity(
                                          context: context);
                                    } else {
                                      CommonFunction.showCustomSnackbar(
                                          message: "Please Choose Correct Location",
                                          backgroundColor: AppTheme.red,
                                          isError: true);
                                    }
                                    // controller.callApiForCreateCommunity(
                                    //     context: context);
                                  }

                                } else {
                                  if (controller.communityNameController.value.text
                                          .isNotEmpty &&
                                      controller.communityNameController.value.text
                                          .trim()
                                          .isEmpty) {
                                    CommonFunction.showCustomSnackbar(
                                        message: "Space not allowed",
                                        backgroundColor: AppTheme.red,
                                        isError: true);
                                  } else {
                                    CommonFunction.showCustomSnackbar(
                                        message: "Community Name is Required*",
                                        backgroundColor: AppTheme.red,
                                        isError: true);
                                  }
                                }
                              },*/
                              onTap: () {
                                String communityName = controller.communityNameController.value.text.trim();

                                // Check if the community name is empty
                                if (communityName.isEmpty) {
                                  if (controller.communityNameController.value.text
                                      .isNotEmpty &&
                                      controller.communityNameController.value.text
                                          .trim()
                                          .isEmpty) {
                                    CommonFunction.showCustomSnackbar(
                                        message: "Space not allowed",
                                        backgroundColor: AppTheme.red,
                                        isError: true);
                                  } else {
                                    CommonFunction.showCustomSnackbar(
                                      message: "Community Name is Required*",
                                      backgroundColor: AppTheme.red,
                                      isError: true,
                                    );
                                  }

                                  return;
                                }

                                // Check if the community name contains only special characters
                                if (!RegExp(r'[A-Za-z0-9]').hasMatch(communityName)) {
                                  CommonFunction.showCustomSnackbar(
                                    message: "Only special characters are not allowed",
                                    backgroundColor: AppTheme.red,
                                    isError: true,
                                  );
                                  return;
                                }

                                // Check if the community name contains only numbers
                                if (RegExp(r'^[0-9 ]+$').hasMatch(communityName)) {
                                  CommonFunction.showCustomSnackbar(
                                    message: "Community Name cannot contain only numbers",
                                    backgroundColor: AppTheme.red,
                                    isError: true,
                                  );
                                  return;
                                }
                                bool isLocationFilled = controller.locationController.value.text.trim().isNotEmpty;
                                bool isEditing = controller.args != null && controller.args["communityId"] != null;
                                if (!isLocationFilled || controller.meetLocation.value == controller.locationController.value.text) {
                                  if (isEditing) {
                                    controller.callApiForUpdateOneCommunity(
                                        context: context, communityId: controller.communityId.value.toString());
                                  } else {
                                    controller.callApiForCreateCommunity(context: context);
                                  }
                                } else {
                                  CommonFunction.showCustomSnackbar(
                                      message: "Please Choose Correct Location",
                                      backgroundColor: AppTheme.red,
                                      isError: true
                                  );
                                }
                                // Proceed with API call
                                // if (controller.args != null && controller.args["communityId"] != null) {
                                //   controller.callApiForUpdateOneCommunity(
                                //       context: context, communityId: controller.communityId.value.toString());
                                // } else {
                                //   if (controller.meetLocation.value == controller.locationController.value.text) {
                                //     controller.callApiForCreateCommunity(context: context);
                                //   } else {
                                //     CommonFunction.showCustomSnackbar(
                                //       message: "Please Choose Correct Location",
                                //       backgroundColor: AppTheme.red,
                                //       isError: true,
                                //     );
                                //   }
                                // }
                              },

                            ),
                          ),
                        ],
                      ),
                      Space.height(20),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Obx(
                () => controller.isCommunityLoading.value && controller.args != null && controller.args["communityId"] != null
                ? Container(
              color: AppTheme.black.withValues(alpha: 0.5),
              child: Loader(),
            )
                : SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
