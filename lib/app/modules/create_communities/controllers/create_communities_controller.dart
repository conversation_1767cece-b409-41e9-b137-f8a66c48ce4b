
import 'dart:developer';
import 'dart:io';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:incenti_ai/app/modules/communities/controllers/communities_controller.dart';

import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/common_function.dart';


class CreateCommunitiesController extends GetxController {

  FocusNode communityNameFocusNode = FocusNode();
  Rx<FocusNode> communityDescriptionFocusNode = FocusNode().obs;
  Rx<TextEditingController> communityNameController = TextEditingController().obs;
  Rx<TextEditingController> communityDescriptionController = TextEditingController().obs;
  TextEditingController locationController = TextEditingController();
  RxInt charCount = 0.obs;
  RxInt maxChars = 250.obs;
  RxString selectedImage = "".obs;
  ApiManager apiManager = ApiManager();
  Rxn<String> meetLocation = Rxn(null);
  final GlobalKey locationFieldKey = GlobalKey();
  RxBool isLoading = false.obs;
  var args = Get.arguments;
  RxInt communityId = (-1).obs;
  RxBool isCommunityLoading = false.obs;

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
    Get.delete<CreateCommunitiesController>();
  }

  void scrollToLocationField(BuildContext context) {
    Future.delayed(Duration(milliseconds: 700),(){
      Scrollable.ensureVisible(
        locationFieldKey.currentContext!,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  void onInit() {
    super.onInit();
    if (args != null && args['communityId'] != null) {
      communityId.value = args['communityId'];
    }
    if (communityId.value != -1) {
      callApiForGetOneCommunity(context: Get.context!,communityId: communityId.value.toString());
    }
    communityDescriptionController.value.addListener(
          () {
        updateCharCount();
        update();
      },
    );
  }

  void updateCharCount() {
    charCount.value =
        communityDescriptionController.value.text.length.clamp(0, maxChars.value);
  }

  Future<bool> isValidImage(File file) async {
    // Get file size in bytes
    // int fileSize = await file.length();
    // if (fileSize > 2 * 1024 * 1024) {
    //   // 2MB limit
    //   return false;
    // }

    // Get file extension
    String fileExtension = file.path.split('.').last.toLowerCase();
    if (fileExtension != 'jpg' &&
        fileExtension != 'jpeg' &&
        fileExtension != 'png') {
      return false;
    }

    return true;
  }

  callApiForProjectImage(
      {required BuildContext context,
        required String imageFile}) async {
    app.resolve<CustomDialogs>().showCircularDialog(context);

    try {
      Map<String, dynamic> dict = {
        "file": await MultipartFile.fromFile(imageFile,
            filename: imageFile.split('/').last.trim())
      };

      FormData formData = FormData.fromMap(dict);

      apiManager.callApi(
        APIS.generalImageUpload.imageUpload,
        params: formData,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            String imageUrl =
            response['data'][0]['link']; // Extract URL from response
              selectedImage.value = imageUrl;
            app.resolve<CustomDialogs>().hideCircularDialog(context);
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          app.resolve<CustomDialogs>().hideCircularDialog(context);
        },
      );
    } catch (e) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
      log("Exception: $e");
    }
  }

  callApiForCreateCommunity({required BuildContext context}) async {
    isLoading.value = true;

    try {
      Map<String, dynamic> dict = {};
      if (selectedImage.value.isNotEmpty) {
        dict['image'] = selectedImage.value;
      }
      if (communityNameController.value.text.trim().isNotEmpty) {
        dict['name'] = communityNameController.value.text.trim();
      }
      if (communityDescriptionController.value.text.trim().isNotEmpty) {
        dict['description'] = communityDescriptionController.value.text;
      }
      if (meetLocation.value != null && (meetLocation.value?.trim() ?? "").isNotEmpty) {
        dict['location'] = meetLocation.value;
      } else {
        dict['location'] = null;
      }


      apiManager.callApi(
        APIS.communities.createCommunities,
        params: dict,
        successCallback: (response, message) {
          if (response['status'] == 'success') {
            isLoading.value = false;
            Get.back();
            Get.find<CommunitiesController>().selectedTabIndex.value = 0;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        },
        failureCallback: (message, statusCode) {
          log("Error: $message");
          isLoading.value = false;
        },
      );
    } catch (e) {
      log("Exception: $e");
      isLoading.value = false;
    }
  }

  Future<void> callApiForUpdateOneCommunity(
      {required BuildContext context, required String communityId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel updateCommunity = ApiModel("/communities/$communityId", APIType.PATCH);
    Map<String, dynamic> dict = {};
    if (selectedImage.value.isNotEmpty) {
      dict['image'] = selectedImage.value;
    } else {
      dict['image'] = null;
    }
    if (communityNameController.value.text.trim().isNotEmpty) {
      dict['name'] = communityNameController.value.text.trim();
    }
    if (communityDescriptionController.value.text.trim().isNotEmpty) {
      dict['description'] = communityDescriptionController.value.text;
    } else {
      dict['description'] = null;
    }
    if ((meetLocation.value?.trim() ?? "").isNotEmpty) {
      dict['location'] = meetLocation.value;
    } else {
      dict['location'] = null;
    }

    return apiManager.callApi(
      updateCommunity,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForDeleteOneCommunity(
      {required BuildContext context, required String communityId}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost = ApiModel("/communities/$communityId", APIType.DELETE);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            Get.back();
            update();
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetOneCommunity(
      {required BuildContext context, required String communityId}) {
    FocusScope.of(context).unfocus();
    isCommunityLoading.value = true;
    final ApiModel getPost = ApiModel("/communities/$communityId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            communityNameController.value.text = response['data']['name'];
            communityDescriptionController.value.text = response['data']['description'] ?? "";
            meetLocation.value = response['data']['location'];
            locationController.text = response['data']['location'] ?? "";
            selectedImage.value = response['data']['image'];
            isCommunityLoading.value = false;
          }
        } catch (error) {
          log("error === $error");
          isCommunityLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isCommunityLoading.value = false;
      },
    );
  }

}
