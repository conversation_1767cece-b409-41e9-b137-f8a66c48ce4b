import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../utillites/common_shimmer_effect.dart';
import '../../../../utillites/common_shimmer_grid_view.dart';
import '../../../../utillites/common_shimmer_profile.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../bottom_bar/views/bottom_bar_view.dart';
import '../../explore/components/common_widget_view.dart';
import '../../profile_view/components/follower_sheet.dart';
import '../../profile_view/components/following_sheet.dart';
import '../../profile_view/components/profile_widget_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../components/other_profile_widget.dart';
import '../controllers/other_user_profile_controller.dart';

class OtherUserProfileView extends StatelessWidget {
  const OtherUserProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return GetBuilder<OtherUserProfileController>(
        init: OtherUserProfileController(),
        tag: Get.arguments != null ? Get.arguments['UserId']?.toString() : null,
        builder: (controller) {
          return Scaffold(
            bottomNavigationBar: customBottomNavigation(),
            body: Obx(
              () => controller.isProfileLoading.value &&
                      controller.isLoading.value
                  ? ShimmerProfilePage()
                  : controller.isProfileNotFound.value
                      ? Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: MySize.getScaledSizeWidth(30)),
                          child: SafeArea(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                buildIcon(AppImage.backArrow, () {
                                  print("route == ${Get.previousRoute}");
                                  Get.back();
                                }, padding: 7, color: AppTheme.whiteWithBase),
                                // Space.height(200),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Center(
                                      child: Image.asset(
                                        "assets/images/not_person.png",
                                        width: MySize.getScaledSizeWidth(200),
                                        height: MySize.getScaledSizeHeight(200),
                                      ),
                                    ),
                                    Space.height(50),
                                    Center(
                                      child: TypoGraphy(
                                        text: "User not found!",
                                        level: 4,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox.shrink(),
                              ],
                            ),
                          ),
                        )
                      : Column(
                          children: [
                            Stack(
                              children: [
                                Obx(
                                  () => controller
                                              .userList.value.backgroundImage ==
                                          ""
                                      ? Image.asset(
                                          "assets/images/banner.png",
                                          width: double.infinity,
                                          height:
                                              MySize.getScaledSizeHeight(182),
                                          fit: BoxFit.fill,
                                        )
                                      : SizedBox(),
                                ),
                                Obx(
                                  () => controller
                                              .userList.value.backgroundImage !=
                                          ""
                                      ? NetworkImageComponent(
                                          imageUrl: controller
                                              .userList.value.backgroundImage,
                                          height:
                                              MySize.getScaledSizeHeight(182),
                                          width: double.infinity,
                                          simmerHeight:
                                              MySize.getScaledSizeHeight(182),
                                        )
                                      : SizedBox(),
                                ),
                                Container(
                                  width: double.infinity,
                                  height: MySize.getScaledSizeHeight(182),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        AppTheme.black.withOpacity(0.8),
                                        AppTheme.black.withOpacity(0),
                                      ],
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                    ),
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Space.height(60),
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: MySize.size30 ?? 30),
                                      child: Row(
                                        children: [
                                          buildIcon(AppImage.backArrow, () {
                                            print(
                                                "route == ${Get.previousRoute}");
                                            Get.back();
                                          }, padding: 7),
                                        ],
                                      ),
                                    ),
                                    Space.height(4),
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: MySize.getScaledSizeWidth(30)),
                                      child: Obx(
                                        () => profileImage(
                                          url:
                                              controller.userList.value.image ??
                                                  "",
                                          userName: controller
                                                  .userList.value.firstName ??
                                              "",
                                          iconHeight:
                                              MySize.getScaledSizeHeight(117),
                                          iconWidth:
                                              MySize.getScaledSizeWidth(117),
                                          width:
                                              MySize.getScaledSizeHeight(117),
                                          height:
                                              MySize.getScaledSizeWidth(117),
                                          strokeWidth: MySize.size3 ?? 1.0,
                                          borderColor: AppTheme.white,
                                          textStyle: TextStyle(
                                              fontSize: MySize.size65,
                                              color: AppTheme.white),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Expanded(
                              child: Obx(
                                () => CustomScrollView(
                                  controller:
                                      controller.currentSelectedIndex.value == 1
                                          ? controller.scrollController
                                          : null,
                                  physics: ClampingScrollPhysics(),
                                  slivers: [
                                    SliverToBoxAdapter(
                                      child: buildOtherProfileSection(
                                        about:
                                            controller.userList.value.about ??
                                                "",
                                        email:
                                            controller.userList.value.email ??
                                                "",
                                        fullName:
                                            "${controller.userList.value.firstName} ${controller.userList.value.lastName}",
                                        followersCount: int.parse(controller
                                            .userList.value.followersCount
                                            .toString()),
                                        followingCount: int.parse(controller
                                            .userList.value.followingsCount
                                            .toString()),
                                        onFollowersTap: () {
                                          controller.searchController.value
                                              .clear();
                                          controller.callApiForFollowersData(
                                              context: Get.context!);
                                          showModalBottomSheet(
                                            context: Get.context!,
                                            isScrollControlled: true,
                                            // Ensures the bottom sheet resizes with the keyboard
                                            backgroundColor: Colors.transparent,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.vertical(
                                                      top: Radius.circular(40)),
                                            ),
                                            builder: (context) {
                                              return StatefulBuilder(
                                                builder: (context, setState) {
                                                  return FollowerSheet(
                                                    index: 0,
                                                    isOtherUserProfile: true,
                                                    forFollowersCount:
                                                        "${int.parse(controller.userList.value.followersCount.toString())}",
                                                    searchController: controller
                                                        .searchController.value,
                                                    isLoading:
                                                        controller.isLoading,
                                                    followersList: controller
                                                        .followersList,
                                                    onClearSearch: () {
                                                      controller
                                                          .searchController
                                                          .value
                                                          .clear();
                                                      controller
                                                          .callApiForFollowersData(
                                                              context:
                                                                  Get.context!);
                                                    },
                                                    onSearchChanged: (text) {
                                                      if (text
                                                          .trim()
                                                          .isNotEmpty) {
                                                        controller
                                                            .callApiForFollowersData(
                                                                context: Get
                                                                    .context!);
                                                      }
                                                    },
                                                    onFollowToggle:
                                                        (user, index) {
                                                      user.isFollowing?.value =
                                                          true;
                                                      controller
                                                          .callApiForFollowUser(
                                                              context:
                                                                  Get.context!,
                                                              userID: user.id,
                                                              isFollower: true);
                                                    },
                                                    onUnfollowConfirm:
                                                        (user, index) {
                                                      controller
                                                          .callApiForUnFollowerUser(
                                                        context: Get.context!,
                                                        userId: user.id,
                                                        isUnFollowed: true,
                                                        index: index,
                                                      );
                                                    },
                                                  );
                                                },
                                              );
                                            },
                                          ).then(
                                            (value) {
                                              controller.callApiForGetUser(
                                                  context: Get.context!,
                                                  userId: controller
                                                      .userId.value
                                                      .toString());
                                            },
                                          );
                                        },
                                        onFollowingTap: () {
                                          controller.searchController.value
                                              .clear();
                                          controller.callApiForFollowingData(
                                              context: Get.context!);
                                          showModalBottomSheet(
                                            context: Get.context!,
                                            isScrollControlled: true,
                                            // Ensures the bottom sheet resizes with the keyboard
                                            backgroundColor: Colors.transparent,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.vertical(
                                                      top: Radius.circular(40)),
                                            ),
                                            builder: (context) {
                                              return StatefulBuilder(
                                                builder: (context, setState) {
                                                  return FollowingSheet(
                                                    index: 0,
                                                    forFollowersCount:
                                                        "${controller.userList.value.followingsCount}",
                                                    searchController: controller
                                                        .searchController.value,
                                                    isLoading:
                                                        controller.isLoading,
                                                    followersList: controller
                                                        .followersList,
                                                    onClearSearch: () {
                                                      controller
                                                          .searchController
                                                          .value
                                                          .clear();
                                                      controller
                                                          .callApiForFollowingData(
                                                              context:
                                                                  Get.context!);
                                                    },
                                                    onSearchChanged: (text) {
                                                      if (text
                                                          .trim()
                                                          .isNotEmpty) {
                                                        controller
                                                            .callApiForFollowingData(
                                                                context: Get
                                                                    .context!);
                                                      }
                                                    },
                                                    onFollowToggle:
                                                        (user, index) {
                                                      user?.isFollowing?.value =
                                                          true;
                                                      controller
                                                          .callApiForFollowUser(
                                                              context:
                                                                  Get.context!,
                                                              userID: user?.id,
                                                              isFollower: true);
                                                    },
                                                    onUnfollowConfirm:
                                                        (user, index) {
                                                      controller
                                                          .callApiForUnFollowerUser(
                                                        context: Get.context!,
                                                        userId: user?.id,
                                                        isUnFollowed: true,
                                                        index: index,
                                                      );
                                                    },
                                                  );
                                                },
                                              );
                                            },
                                          ).then(
                                            (value) {
                                              CurrentUser.getMe();
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                    SliverToBoxAdapter(
                                        child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        if (!controller.isFollowing.value)
                                          Buttons(
                                            buttonText: "Follow",
                                            buttonTextLevel: 4,
                                            width:
                                                MySize.getScaledSizeWidth(125),
                                            height:
                                                MySize.getScaledSizeHeight(64),
                                            onTap: () {
                                              HapticFeedback.lightImpact();
                                              controller.callApiForFollowUser(
                                                  context: context);
                                            },
                                          ),
                                        if (controller.isFollowing.value)
                                          InkWell(
                                            onTap: () {
                                              HapticFeedback.lightImpact();
                                              controller.callApiForUnFollowUser(
                                                  context: context);
                                            },
                                            child: Container(
                                              width: MySize.getScaledSizeWidth(
                                                  125),
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      64),
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                  border: Border.all(
                                                      width: 1,
                                                      color: AppTheme.grey)),
                                              alignment: Alignment.center,
                                              child: TypoGraphy(
                                                text: "Following",
                                                textStyle: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.w600,
                                                    fontFamily: "Inter",
                                                    color: AppTheme.grey),
                                              ),
                                            ),
                                          )
                                      ],
                                    )),
                                    SliverToBoxAdapter(child: Space.height(20)),
                                    SliverToBoxAdapter(
                                      child: buildStoryHighlightSection(
                                        highlightList: controller.highlights,
                                        fontSize: MySize.size13 ?? 13,
                                        context: context,
                                        isLoading: controller.isLoading.value,
                                      ),
                                    ),
                                    SliverToBoxAdapter(child: Space.height(20)),
                                    SliverToBoxAdapter(
                                      child: buildPostCategorySection(
                                        userPostList:
                                            controller.otherUserPostList,
                                        currentSelectedIndex: controller
                                            .currentSelectedIndex.value,
                                        onCategorySelected: (index) {
                                          controller.currentSelectedIndex
                                              .value = index;
                                          if (index == 1) {
                                            controller.page.value = 1;
                                            controller.hasMoreData.value = true;
                                            controller.userProject.clear();
                                            controller.fetchProjectData(
                                                context,
                                                controller.userList.value.id
                                                    .toString());
                                          }
                                          if (index == 0) {
                                            controller.page.value = 1;
                                            controller.hasMoreData.value = true;
                                            controller.postDataList.clear();
                                            controller.callApiForUserGetPost(
                                                context: Get.context!,
                                                userId: controller
                                                    .userList.value.id
                                                    .toString());
                                          }
                                        },
                                      ),
                                    ),
                                    if (controller.currentSelectedIndex.value ==
                                        0) ...[
                                      Obx(() {
                                        return controller
                                                    .apiManager.isLoading &&
                                                controller.postDataList.isEmpty
                                            ? SliverToBoxAdapter(
                                                child: ShimmerPostCard())
                                            : otherPostDataView(
                                                context, controller);
                                      })
                                    ],
                                    if (controller.currentSelectedIndex.value ==
                                        1) ...[
                                      if (controller.isProjectLoading.value &&
                                          controller.userProject.isEmpty)
                                        SliverToBoxAdapter(
                                            child: Padding(
                                          padding: EdgeInsets.only(
                                              left: MySize.size30 ?? 30,
                                              right: MySize.size30 ?? 30,
                                              top: MySize.size25 ?? 25),
                                          child: ShimmerGridView(),
                                        )),
                                      SliverPadding(
                                        padding: EdgeInsets.only(
                                            left: MySize.size30 ?? 30,
                                            right: MySize.size30 ?? 30,
                                            top: MySize.size25 ?? 25),
                                        sliver: SliverGrid(
                                          gridDelegate:
                                              SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 2,
                                            crossAxisSpacing:
                                                MySize.size20 ?? 20,
                                            mainAxisSpacing:
                                                MySize.size10 ?? 30,
                                            childAspectRatio: 0.77,
                                          ),
                                          delegate: SliverChildBuilderDelegate(
                                            (context, index) {
                                              var project =
                                                  controller.userProject[index];
                                              return InkWell(
                                                onTap: () {
                                                  if (project.isPrivate) {
                                                    CommonFunction
                                                        .showCustomSnackbar(
                                                      message:
                                                          "This Project is Private.",
                                                    );
                                                  } else {
                                                    Get.toNamed(
                                                        Routes.project_detail,
                                                        arguments: {
                                                          "projectId":
                                                              controller
                                                                  .userProject[
                                                                      index]
                                                                  .id,
                                                          "isOtherUser": true
                                                        });
                                                  }
                                                },
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Stack(
                                                      children: [
                                                        Container(
                                                          height:
                                                              MySize.size142,
                                                          width:
                                                              double.infinity,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        25),
                                                          ),
                                                          child: project.image
                                                                  .isNotEmpty
                                                              ? ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              25),
                                                                  child:
                                                                      NetworkImageComponent(
                                                                    imageUrl:
                                                                        project
                                                                            .image,
                                                                    simmerHeight:
                                                                        MySize
                                                                            .size142,
                                                                    width: double
                                                                        .infinity,
                                                                  ),
                                                                )
                                                              : Image.asset(
                                                                  AppImage
                                                                      .defaultImage,
                                                                  width: double
                                                                      .infinity,
                                                                  height: MySize
                                                                      .size142,
                                                                ),
                                                        ),
                                                        if(!project.isPrivate)
                                                        Positioned(
                                                          right: MySize
                                                              .getScaledSizeWidth(
                                                                  12.11),
                                                          top: MySize
                                                              .getScaledSizeHeight(
                                                                  12),
                                                          child: InkWell(
                                                            onTap: () {
                                                              HapticFeedback
                                                                  .lightImpact();
                                                              ImagePickerBottomSheet
                                                                  .show(
                                                                      context:
                                                                          context,
                                                                      child:
                                                                          showPostOption(
                                                                        isProject:
                                                                            true,
                                                                        projectId: controller
                                                                            .userProject[index]
                                                                            .id,
                                                                        projectSlug: controller
                                                                            .userProject[index]
                                                                            .slug,
                                                                        title: controller
                                                                            .userProject[index]
                                                                            .name,
                                                                        onTap:
                                                                            () {
                                                                          Navigator.pop(
                                                                              context);
                                                                          showCommonReportBottomSheet(
                                                                            context:
                                                                                context,
                                                                            title:
                                                                                "Report",
                                                                            subTitle:
                                                                                "Why are you reporting this project?",
                                                                            description:
                                                                                "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                                                                            options:
                                                                                controller.repostData,
                                                                            onOptionTap:
                                                                                (selectedOption) async {
                                                                              controller.selectedReason.value = selectedOption;
                                                                              await controller.callApiForReportProject(
                                                                                context: context,
                                                                                projectId: controller.userProject[index].id.toString(),
                                                                              );
                                                                            },
                                                                          );
                                                                        },
                                                                      ));

                                                              // onTap(index);
                                                            },
                                                            child: SvgPicture
                                                                .asset(
                                                              AppImage
                                                                  .moreVertIcon,
                                                              color: AppTheme
                                                                  .white,
                                                              height:
                                                                  MySize.size24,
                                                              width:
                                                                  MySize.size24,
                                                            ),
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                                    Space.height(6),
                                                    // TypoGraphy(
                                                    //   text:
                                                    //   '${project.subProjectsCount} Sub-projects',
                                                    //   level: 2,
                                                    //   fontWeight:
                                                    //   FontWeight.w400,
                                                    // ),
                                                    Space.height(4),
                                                    Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        if (project
                                                            .isPrivate) ...[
                                                          Image.asset(
                                                            AppImage
                                                                .lockProject,
                                                            height: MySize
                                                                .getScaledSizeHeight(
                                                                    32),
                                                            width: MySize
                                                                .getScaledSizeWidth(
                                                                    32),
                                                          ),
                                                          Space.width(8),
                                                        ],
                                                        Expanded(
                                                          child: TypoGraphy(
                                                            text: project.name,
                                                            textStyle: TextStyle(
                                                                fontSize: 18,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w700,
                                                                fontFamily:
                                                                    'Inter'),
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            maxLines: 2,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                            childCount:
                                                controller.userProject.length,
                                          ),
                                        ),
                                      ),
                                      if (controller.userProject.isEmpty &&
                                          !controller.isProjectLoading.value)
                                        SliverToBoxAdapter(
                                            child: Padding(
                                          padding: EdgeInsets.only(
                                              top: MySize.getScaledSizeHeight(
                                                  70)),
                                          child: Empty(
                                            title: "No Project available!",
                                          ),
                                        )),
                                      if (controller.hasMoreData.value)
                                        SliverToBoxAdapter(
                                          child: SizedBox(
                                            height: 60,
                                            // Ensures enough space for the loader
                                            child: Center(
                                              child: Loader(),
                                            ),
                                          ),
                                        ),
                                      SliverToBoxAdapter(
                                          child: Space.height(35)),
                                    ]
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
            ),
          );
        });
  }
}
