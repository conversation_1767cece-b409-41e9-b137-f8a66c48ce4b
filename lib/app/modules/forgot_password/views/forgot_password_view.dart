import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../controllers/forgot_password_controller.dart';

class ForgotPasswordView extends GetWidget<ForgotPasswordController> {
  const ForgotPasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      // backgroundColor: AppTheme.white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Space.height(58),
          Padding(
            padding: EdgeInsets.only(left: MySize.size30 ?? 30),
            child: <PERSON>gn(
              alignment: Alignment.centerLeft,
              child: InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  Get.back();
                },
                child: SvgPicture.asset(
                  AppImage.backArrow,
                  height: MySize.size28,
                  width: MySize.size28,
                  color: AppTheme.whiteWithNull,
                ),
              ),
            ),
          ),
          Space.height(30),
          Image.asset(
            AppImage.appLogo,
            color: AppTheme.whiteWithBase,
            width: MySize.getScaledSizeWidth(110),
            // height: MySize.size39,
          ),
          Space.height(22),
          TypoGraphy(
            text: "Forgot Password?",
            level: 8,
            fontWeight: FontWeight.w700,
            // color: AppTheme.baseBlack,
          ),
          Space.height(10),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size50 ?? 50),
            child: TypoGraphy(
              text: "Enter registered email id to reset your password.",
              level: 3,
              fontWeight: FontWeight.w400,
              color: AppTheme.grey,
              textAlign: TextAlign.center,
            ),
          ),
          Space.height(25),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size40 ?? 52),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTextField(
                  errorMessage: controller.isError.value,
                  focusNode: controller.emailFocusNode,
                  controller: controller.emailController,
                  labelText: "Email",
                  onChangedValue: (p0) {
                    controller.validateEmail();
                  },
                ),
              ],
            ),
          ),
          Space.height(30),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size40 ?? 52),
            child: Obx(
                  () => Buttons(
                onTap: () {
                  HapticFeedback.lightImpact();
                  controller.callApiForForgotPassword(context: context);
                },
                buttonText: 'Continue',
                buttonTextLevel: 4,
                width: MySize.size188,
                height: MySize.size70 ?? 70,
                isLoading: controller.isLoading.value,
              ),
            ),
          ),
        ],
      ),
    );
  }
}