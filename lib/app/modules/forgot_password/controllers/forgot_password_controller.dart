import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_function.dart';

import '../../../../constants/api.dart';
import '../../../../constants/app_argument_key.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../routes/app_pages.dart';


class ForgotPasswordController extends GetxController {

  TextEditingController emailController = TextEditingController();
  FocusNode emailFocusNode = FocusNode();
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;
  ApiManager apiManager = ApiManager();
  RxString emailErrorMessage = "".obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    emailFocusNode.requestFocus();

  }

  validateEmail() {
    if (emailController.text.isEmpty) {
      emailErrorMessage.value = "Email cannot be empty";
      isError.value = true;
      return false;
    } else if (!GetUtils.isEmail(emailController.text)) {
      emailErrorMessage.value = "Please enter a valid email";
      isError.value = true;
      return false;
    } else {
      emailErrorMessage.value = "";
      isError.value = false;
      return true;
    }
  }

  callApiForForgotPassword({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    bool isEmailValid = validateEmail();

    if (!isEmailValid) {
      return;
    }
    isLoading.value = true;
    Map<String, dynamic> dict = {
      "email": emailController.value.text,
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.forgotPassword,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String? token = response['token'];
            box.write("token", token);
            Get.toNamed(Routes.Verify_Otp, arguments: {
              AppArguments.email: emailController.text,
              AppArguments.isForgotPassword: true
            });
            isLoading.value = false;
          } else {
            CommonFunction.showCustomSnackbar(message: response['message'],backgroundColor: AppTheme.red,isError: true);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(message: statusCode,backgroundColor: AppTheme.red,isError: true);
        isLoading.value = false;
      },
    );
  }


}
