import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:incenti_ai/constants/api.dart';
import 'package:incenti_ai/models/app_project_model.dart';
import 'package:incenti_ai/services/api_manager.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/current_user.dart';

class ProjectFolderDetailViewController extends GetxController {
  ProjectFolderDetailViewController({
    required this.folder,
  });

  final ProjectFolder folder;

  final TextEditingController folderNameController = TextEditingController();

  RxBool isLoading = true.obs;
  RxBool isFileUploading = false.obs;
  RxList<FolderWrapper> itemsList = <FolderWrapper>[].obs;

  final searchController = TextEditingController();
  final searchFocusNode = FocusNode();

  final ApiManager apiManager = ApiManager();

  RxBool isLoadingProjects = true.obs;
  RxList<Project> projects = <Project>[].obs;

  RxBool otherProjectFoldersLoading = true.obs;
  RxList<FolderWrapper> otherProjectFolders = <FolderWrapper>[].obs;

  Rxn<ProjectFolder> selectedFolder = Rxn<ProjectFolder>(null);

  @override
  onInit() {
    super.onInit();
    getAllItems(folder.id);
  }

  getAllProjects() async {
    isLoadingProjects.value = true;
    await apiManager.callApi(
      APIS.project.getAllMyProject,
      params: {'UserId': CurrentUser.user.id},
      successCallback: (response, message) {
        projects.value = List<Project>.from(
            response["data"]["data"].map((x) => Project.fromJson(x)));
        projects.value = projects.where(
                (element) =>
            (CurrentUser.user.id == element.userId || (element.projectMembers.isNotEmpty && element.projectMembers[0].access != "read"))).toList();


        isLoadingProjects.value = false;
      },
      failureCallback: (error, message) {
        isLoadingProjects.value = false;
      },
    );
  }

  getAllItems(int folderId) async {
    isLoading.value = true;

    final allItems = await Future.wait(
        [_getChildFolders(folderId), _getChildFiles(folderId)]);

    itemsList.value = allItems.expand((e) => e).toList();
    isLoading.value = false;
  }

  Future<List<FolderWrapper>> _getChildFolders(int folderId) async {
    final response = await apiManager.callApi(
      APIS.folders.getAllFolders,
      params: {
        'ProjectId': folder.projectId,
        'ParentId': folderId,
      },
    );
    return response['status'] == 'success'
        ? List<FolderWrapper>.from(
            response["data"]["data"].map((x) => ProjectFolder.fromJson(x)))
        : [];
  }

  Future<List<FolderWrapper>> _getChildFiles(int folderId) async {
    final response = await apiManager.callApi(
      APIS.folders.getAllFiles,
      params: {
        'ProjectId': folder.projectId,
        'FolderId': folderId,
      },
    );
    return response['status'] == 'success'
        ? List<FolderWrapper>.from(
            response["data"]["data"].map((x) => ProjectFile.fromJson(x)))
        : [];
  }

  moveItemToSelectedFolder({
    required int itemId,
  }) async {
    apiManager.callApi(
      CommonFunction.prepareApi(APIS.folders.editFile, {
        'id': itemId.toString(),
      }),
      params: {
        'FolderId': selectedFolder.value?.id,
      },
      successCallback: (response, message) async {
        if (response['status'] != 'success') {
          CommonFunction.showCustomSnackbar(
            message: message,
            backgroundColor: AppTheme.red,
            isError: true,
          );
          return;
        }

        Get.back();
        Get.back();
        await getAllItems(folder.id);
        CommonFunction.showCustomSnackbar(
          message: "Item moved to Folder: ${selectedFolder.value?.name}",
        );
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
          message: message,
          backgroundColor: AppTheme.red,
          isError: true,
        );
      },
    );
  }

  Future<void> createFolder({
    required BuildContext context,
    required String folderName,
  }) async {
    FocusScope.of(context).unfocus();
    Get.back();
    folderNameController.clear();
    isLoading.value = true;
    apiManager.callApi(
      APIS.folders.createFolder,
      params: {
        'ProjectId': folder.projectId,
        'ParentId': folder.id,
        'name': folderName,
      },
      successCallback: (response, message) {
        getAllItems(folder.id);
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          isError: true,
          backgroundColor: AppTheme.red,
        );
        isLoading.value = false;
      },
      showErrorDialog: false,
    );
  }

  Future<void> editFolderName({
    required BuildContext context,
    int? folderId,
    required String folderName,
  }) async {
    FocusScope.of(context).unfocus();
    Get.back();
    folderNameController.clear();
    isLoading.value = true;
    apiManager.callApi(
      CommonFunction.prepareApi(APIS.folders.editFolder, {
        'id': folderId.toString(),
      }),
      params: {
        'name': folderName,
      },
      successCallback: (response, message) {
        getAllItems(folder.id);
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          isError: true,
          backgroundColor: AppTheme.red,
        );
        isLoading.value = false;
      },
      showErrorDialog: false,
    );
  }

  Future<void> deleteFolder({
    required int folderId,
  }) async {
    Get.back();
    isLoading.value = true;
    apiManager.callApi(
      CommonFunction.prepareApi(
        APIS.folders.deleteFolder,
        {'id': folderId.toString()},
      ),
      successCallback: (response, message) {
        getAllItems(folder.id);
      },
      failureCallback: (message, statusCode) {},
    );
  }

  deleteItem({
    required int itemId,
  }) async {
    apiManager.callApi(
      CommonFunction.prepareApi(APIS.folders.deleteFile, {
        'id': itemId.toString(),
      }),
      successCallback: (response, message) async {
        await getAllItems(folder.id);
      },
      failureCallback: (message, statusCode) {},
    );
  }

  uploadAndCreatePickedFiles(FilePickerResult result) async {
    if (result.files.isNotEmpty) {
      isFileUploading.value = true;
      final uploadTasks = List.generate(
        result.files.length,
        (index) => _FileUploadTask(result.files[index].xFile, ApiManager()),
      );

      itemsList.addAll(uploadTasks.map(
        (e) => FileUploadInProgress(
          id: 0,
          projectId: 0,
          name: e.file.name,
          progress: e.fileUploadManager.liveProgress,
        ),
      ));

      await Future.wait([
        for (final task in uploadTasks) _uploadFile(task),
      ]);

      isFileUploading.value = false;

      await getAllItems(folder.id);
    }
  }

  Future<void> _uploadFile(_FileUploadTask task) async {
    final response = await task.fileUploadManager.callApi(
      APIS.generalImageUpload.imageUpload,
      params: FormData.fromMap({
        'file': await MultipartFile.fromFile(task.file.path,
            filename: task.file.name),
      }),
    );

    if (response['status'] == 'success') {
      await apiManager.callApi(
        APIS.folders.createFile,
        params: {
          'FolderId': folder.id,
          'name': task.file.name,
          'link': response['data'].first['link'],
        },
      );
    }
  }

  getOtherProjectFolders(int projectId) async {
    otherProjectFoldersLoading.value = true;
    await apiManager.callApi(
      APIS.folders.getAllFolders,
      params: {'ProjectId': projectId},
      successCallback: (response, message) {
        otherProjectFolders.value = List<FolderWrapper>.from(
            response["data"]["data"].map((x) => ProjectFolder.fromJson(x)));

        otherProjectFoldersLoading.value = false;
      },
      failureCallback: (error, message) {
        otherProjectFoldersLoading.value = false;
      },
    );
  }
}

class _FileUploadTask {
  final XFile file;
  final ApiManager fileUploadManager;

  _FileUploadTask(this.file, this.fileUploadManager);
}
