import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';

class TeamMemberTileShimmer extends StatelessWidget {
  const TeamMemberTileShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: MySize.getScaledSizeWidth(30)),
      child: Shimmer.fromColors(
        baseColor: AppTheme.baseShimmer,
        highlightColor: AppTheme.highlightShimmer,
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  height: MySize.getScaledSizeHeight(60),
                  width: MySize.getScaledSizeWidth(60),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
                SizedBox(width: MySize.getScaledSizeWidth(16)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: MySize.getScaledSizeWidth(150),
                        height: MySize.getScaledSizeHeight(14),
                        color: Colors.white,
                      ),
                      SizedBox(height: MySize.getScaledSizeHeight(8)),
                      Container(
                        width: MySize.getScaledSizeWidth(100),
                        height: MySize.getScaledSizeHeight(12),
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: MySize.getScaledSizeWidth(16)),
                Container(
                  height: MySize.getScaledSizeWidth(10),
                  width: MySize.getScaledSizeHeight(30),
                  color: Colors.white,
                ),
              ],
            ),
            SizedBox(height: MySize.getScaledSizeWidth(20)),

          ],
        ),
      ),
    );
  }
}
