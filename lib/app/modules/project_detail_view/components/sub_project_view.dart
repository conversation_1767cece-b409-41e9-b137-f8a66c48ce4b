
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/common_grid_view.dart';
import '../../../../utillites/common_shimmer_grid_view.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';

class CommonSubProjectProjectView extends StatelessWidget {
  final ProjectDetailViewController controller;
  final Function(int index) onTap;
  final bool isNeedBottom;

  const CommonSubProjectProjectView(
      {super.key,
      required this.controller,
      required this.onTap,this.isNeedBottom = false});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => controller.isUserSubProjectLoading.value
          ? ShimmerGridView()
          : controller.subProjectList.isNotEmpty
              ? GridView.builder(
                  padding: EdgeInsets.zero,
                  keyboardDismissBehavior:
                      ScrollViewKeyboardDismissBehavior.onDrag,
                  gridDelegate: FixedHeightGridDelegate(
                    itemHeight: MySize.size220 ?? 200,
                    crossAxisCount: 2,
                    crossAxisSpacing: MySize.size20 ?? 20,
                    mainAxisSpacing: MySize.size10 ?? 30,
                  ),
                  physics: ClampingScrollPhysics(),
                  shrinkWrap: true,
                  controller: ScrollController(),
                  itemCount: controller.subProjectList.length,
                  itemBuilder: (context, index) {
                    var project = controller.subProjectList[index];
                    return InkWell(
                      onTap: () {
                        if (CurrentUser.user.id != controller.getProjectDetailData.value.userId && (CurrentUser.user.id !=
                            controller.subProjectList[index].userId &&
                            (controller.subProjectList[index].isPrivate && controller.subProjectList[index].projectMembers.isEmpty))) {
                          CommonFunction.showCustomSnackbar(
                            message: "This Project is Private.",
                          );
                        } else {
                          Get.toNamed(Routes.subProject_detail, arguments: {
                            "projectId": controller.subProjectList[index].id,
                            if(isNeedBottom) "isNeedBottom": true,
                          })?.then(
                            (value) {
                              controller.callApiForOneSubProject(
                                  context: Get.context!,
                                  projectId:
                                      controller.projectId.value.toString(),
                                  userId: controller
                                      .getProjectDetailData.value.userId);
                            },
                          );
                        }
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Stack(
                            children: [
                              Container(
                                height: MySize.size142,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                child: project.image != null
                                    ? ClipRRect(
                                        borderRadius: BorderRadius.circular(25),
                                        child: NetworkImageComponent(
                                          imageUrl: project.image,
                                          simmerHeight: MySize.size142,
                                          width: double.infinity,
                                        ),
                                      )
                                    : Image.asset(
                                        AppImage.defaultImage,
                                        width: double.infinity,
                                        height: MySize.size142,
                                      ),
                              ),
                              if ((CurrentUser.user.id ==
                                  controller.subProjectList[index].userId) || controller.getProjectDetailData.value.userId == CurrentUser.user.id ||
                                  (controller.subProjectList[index]
                                          .projectMembers.isNotEmpty) || !controller.subProjectList[index].isPrivate) ...[
                                Positioned(
                                  right: MySize.getScaledSizeWidth(12.11),
                                  top: MySize.getScaledSizeHeight(12),
                                  child: InkWell(
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                      onTap(index);
                                    },
                                    child: SvgPicture.asset(
                                      AppImage.moreVertIcon,
                                      color: AppTheme.white,
                                      height: MySize.size24,
                                      width: MySize.size24,
                                    ),
                                  ),
                                )
                              ]
                            ],
                          ),
                          // Space.height(6),
                          // if (controller.subProjectList[index].postsCount !=
                          //     "0") ...[
                          //   TypoGraphy(
                          //     text:
                          //         '${controller.subProjectList[index].postsCount} Post',
                          //     level: 2,
                          //     fontWeight: FontWeight.w400,
                          //   )
                          // ],
                          Space.height(4),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (controller.subProjectList[index].isPrivate) ...[
                                Image.asset(
                                  AppImage.lockProject,
                                  height: MySize.getScaledSizeHeight(32),
                                  width: MySize.getScaledSizeWidth(32),
                                ),
                                Space.width(8),
                              ],
                              Expanded(
                                child: TypoGraphy(
                                  text: project.name,
                                  textStyle: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                      fontFamily: 'Inter'),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                )
              : Padding(
                  padding: EdgeInsets.only(top: MySize.size50 ?? 50),
                  child: const Empty(
                    title: "No Sub Project available!",
                  ),
                ),
    );
  }
}
