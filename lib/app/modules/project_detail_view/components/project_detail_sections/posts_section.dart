import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/explore/components/common_widget_view.dart';
import 'package:incenti_ai/models/section_base.dart';
import 'package:incenti_ai/app/modules/user_detail/components/image_picker_bottom_sheet.dart';
import 'package:incenti_ai/app/routes/app_pages.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/models/app_post_model.dart';
import 'package:incenti_ai/models/app_project_model.dart';
import 'package:incenti_ai/services/app_link_service.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/comment_bottomsheet_view.dart';
import 'package:incenti_ai/utillites/common_post_like_bottomsheet.dart';
import 'package:incenti_ai/utillites/common_report_bottom_sheet.dart';
import 'package:incenti_ai/utillites/common_shimmer_effect.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/custom_sliver_list_view.dart';
import 'package:incenti_ai/utillites/empty.dart';

import '../../../explore/components/common_post_widget.dart';
import '../../controllers/project_detail_view_controller.dart';

class PostsSection extends SectionBase<ProjectDetailViewController> {
  PostsSection({required super.controller});

  @override
  String get title => 'Posts';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();

        Get.toNamed(Routes.post, arguments: {
          "isCreateProject": true,
          "isCrossVisible": false,
          "projectModel": Project(
              id: controller.getProjectDetailData.value.id,
              name: controller.getProjectDetailData.value.name,
              image: controller.getProjectDetailData.value.image,
              subProjectsCount:
                  controller.getProjectDetailData.value.subProjectsCount)
        })?.then(
          (value) {
            print("Post created then value");
            controller.page.value = 1;
            controller.hasMoreData.value = true;
            controller.postDataList.clear();
            controller.callApiForUserGetPost(
                context: Get.context!,
                isPublished: controller.currentSelectedIndex.value == 5);
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      controller.isUserPostLoading.value && controller.postDataList.isEmpty
          ? SliverToBoxAdapter(child: ShimmerPostCard())
          : CustomSliverListView(
              emptyWidget: Padding(
                padding: EdgeInsets.only(
                  top: MySize.size25 ?? 100,
                  left: paddingHoriZontal,
                  right: paddingHoriZontal,
                ),
                child: Padding(
                  padding: EdgeInsets.only(top: MySize.size50 ?? 50),
                  child: const Empty(
                    title: "No Post available!",
                  ),
                ),
              ),
              maximumReachedWidget: const SizedBox(),
              itemBuilder: (context, Post post, index) {
                Post res = controller.postDataList[index];
                return res.isPrivate == true
                    ? const SizedBox()
                    : commonFeedView(
                        onTap: () {
                          Get.toNamed(Routes.post_detail, arguments: {
                            "postID": res.id,
                            "index": index,
                            "source": "project",
                            "projectId": res.projectId,
                          })?.then(
                            (value) async {
                              controller.page.value = 1;
                              controller.hasMoreData.value = true;
                              controller.postDataList.clear();
                              await controller.callApiForUserGetPost(
                                context: Get.context!,
                              );
                            },
                          );
                        },
                        onLikeLongPress: () {
                          controller.postLikeList.clear();

                          controller.callApiForGetPostLike(
                              context: context, postId: res.id);
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(16),
                              ),
                            ),
                            builder: (context) => CommonPostLikeBottomSheet(
                              index: index,
                              isLoading: controller.isLoading,
                              postLikeList: controller.postLikeList,
                            ),
                          );
                        },
                        onShare: () {
                          HapticFeedback.lightImpact();
                          AppLinkService().shareMedia(
                              slug: res.slug ?? '',
                              mediaType: ShareMediaType.posts,
                              title: res.title);
                        },
                        onFeedHeadTap: controller
                                    .getProjectDetailData.value.userId ==
                                CurrentUser.user.id
                            ? () {
                                HapticFeedback.lightImpact();
                                ImagePickerBottomSheet.show(
                                    context: context,
                                    child: showUserPostOption(
                                        postId: res.id,
                                        isPin: res.pinnedAt,
                                        title: res.title,
                                        isSubProjectEdit:
                                            res.project?.parentProjectData !=
                                                null,
                                      isBack: true,
                                      isProjectEdit: true,
                                    ));
                              }
                            : null,
                        onRepostTap: () async {
                          Navigator.pop(context);
                          showCommonReportBottomSheet(
                            context: context,
                            title: "Report",
                            subTitle: "Why are you reporting this post?",
                            description:
                                "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                            options: controller.repostData,
                            onOptionTap: (selectedOption) async {
                              controller.selectedReason.value = selectedOption;
                              await controller.callApiForReportPost(
                                context: context,
                                postId: res.id.toString(),
                              );
                            },
                          );
                        },
                        res,
                        index,
                        context,
                        isPostEditUser: (controller.getProjectDetailData.value
                                .projectMembers.isNotEmpty &&
                            controller.getProjectDetailData.value
                                    .projectMembers[0].access ==
                                "write"),
                        onLike: () => controller.callApiForLikeProject(
                              context: context,
                              postId: res.id.toString(),
                              index: index,
                            ),
                        onComment: () {
                          controller.commentController.clear();
                          controller.callApiForGetCommentProject(
                              context: context,
                              postId: res.id.toString(),
                              index: index);
                          showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(40))),
                              builder: (context) => CommonBottomSheet(
                                    index: index,
                                    userId: res.user?.id ?? 0,
                                    isLoading: controller.isLoading,
                                    commentController:
                                        controller.commentController,
                                    commentDataList: controller.commentDataList,
                                    commentFocusNode:
                                        controller.commentFocusNode,
                                    onCommentSend: () {
                                      controller.callApiForCommentProject(
                                          context: context,
                                          postId:
                                              "${controller.postDataList[index].id}",
                                          index: index);
                                      controller.commentController.clear();
                                    },
                                  )).then((value) {
                            controller.callApiForGetCommentProject(
                                context: context,
                                postId: res.id.toString(),
                                index: index);
                                  },);
                        },
                        onBookmark: () => controller.callApiForBookMarkProject(
                              context: context,
                              postId: res.id.toString(),
                              index: index,
                            ),
                        isLiked: res.isLiked?.value ?? false,
                        likesCount: res.likesCount?.value ?? 0,
                        isBookmarked: res.isBookMarked?.value ?? false,
                        commentsCount: res.commentsCount?.value ?? 0,
                        isUser:
                            res.user?.id == CurrentUser.user.id ? true : false);
              },
              isLoading: controller.apiManager.isLoading,
              items: controller.postDataList,
              hasMoreData: controller.hasMoreData.value,
              onLoadMore: () {
                return controller.callApiForUserGetPost(
                  context: context,
                );
              },
            )
    ];
  }

  @override
  void onCategorySelected() {
    controller.page.value = 1;
    controller.hasMoreData.value = true;
    controller.postDataList.clear();
    controller.callApiForUserGetPost(context: Get.context!);
  }
}
