import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';

import 'package:incenti_ai/utillites/app_theme.dart';

import '../../../../../utillites/common_report_bottom_sheet.dart';
import '../../../../../utillites/current_user.dart';
import '../../../create_project/controllers/create_project_controller.dart';
import '../../../create_project/views/create_sub_project.dart';
import '../../../explore/components/common_widget_view.dart';
import '../../../user_detail/components/image_picker_bottom_sheet.dart';
import '../../controllers/project_detail_view_controller.dart';
import '../sub_project_edit.dart';
import '../sub_project_view.dart';
import '../../../../../models/section_base.dart';

class SubProjectsSection extends SectionBase<ProjectDetailViewController> {
  @override
  String get title => 'Sub-projects';

  SubProjectsSection({
    required super.controller,
  });

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();
        if (controller.getProjectDetailData.value.isPrivate == true) {
          if (!Get.isRegistered<CreateProjectController>()) {
            Get.put(CreateProjectController()).visibilityValue.value = 1;
          } else {
            Get.find<CreateProjectController>().visibilityValue.value = 1;
          }
        }
        showModalBottomSheet(
          context: Get.context!,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          builder: (context) => subProjectVisibility(context, false,
              isSubCreate: true,
              projectId:
              controller.getProjectDetailData.value.id.toString()),
        ).then(
              (value) {
            controller.subProjectList.clear();
            controller.callApiForOneSubProject(
                context: Get.context!,
                projectId: controller.projectId.value.toString(),
                userId: controller.getProjectDetailData.value.userId);
            Get.delete<CreateProjectController>();
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(
              left: MySize.size30 ?? 30,
              right: MySize.size30 ?? 30,
              top: MySize.size25 ?? 30),
          child: CommonSubProjectProjectView(
            controller: controller,
            isNeedBottom: controller.args != null &&
                    controller.args["isNeedBottom"] != null
                ? true
                : false,
            onTap: (index) {
              ImagePickerBottomSheet.show(
                context: context,
                child: (CurrentUser.user.id == controller
                    .subProjectList[index].userId || CurrentUser.user.id == controller.getProjectDetailData.value.userId) ? showSubProjectEditOption(
                  isBack: false,
                  projectName: controller
                      .subProjectList[index].name,
                  isHideDelete: (controller
                          .subProjectList[index].projectMembers.isNotEmpty &&
                      controller
                              .subProjectList[index].projectMembers[0].access ==
                          "write"),
                  projectId: controller.subProjectList[index].id.toString(),
                  mainProjectVisibility:
                      controller.getProjectDetailData.value.isPrivate,
                  isPrivate: controller.subProjectList[index].isPrivate,
                  isHide: controller.subProjectList[index].hide,
                  hidePrivate: () {
                    controller.callApiForHideProject(
                        context: context,
                        projectId:
                            controller.subProjectList[index].id.toString(),
                        isHide: controller.subProjectList[index].hide == false
                            ? true
                            : false,
                        isSubProject: true,
                        index: index);
                  },
                ) : showPostOption(
                  projectId: controller.subProjectList[index].id,
                  projectSlug: controller.subProjectList[index].slug,
                  isProject: true,
                  onTap: () {
                    Navigator.pop(context);
                    showCommonReportBottomSheet(
                      context: context,
                      title: "Report",
                      subTitle: "Why are you reporting this project?",
                      description:
                      "Your report is anonymous. If someone is in\nimmediate danger, call the local emergency\nservices - don't wait.",
                      options: controller.repostData,
                      onOptionTap: (selectedOption) async {
                        controller.selectedReason.value = selectedOption;
                        await controller.callApiForReportProject(
                          context: context,
                          projectId: controller.subProjectList[index].id.toString(),
                        );
                      },
                    );
                  },
                  title: controller.subProjectList[index].name,
                ),
              );
            },
          ),
        ),
      ),
      SliverToBoxAdapter(child: Space.height(35))
    ];
  }

  @override
  void onCategorySelected() {
    controller.callApiForOneSubProject(
        context: Get.context!,
        projectId: controller
            .projectId.value
            .toString(),
        userId: controller
            .getProjectDetailData
            .value
            .userId);
  }
}
