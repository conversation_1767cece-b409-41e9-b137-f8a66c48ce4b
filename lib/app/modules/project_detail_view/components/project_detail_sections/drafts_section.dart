import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/user_detail/components/image_picker_bottom_sheet.dart';
import 'package:incenti_ai/app/routes/app_pages.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/main.dart';
import 'package:incenti_ai/models/app_post_model.dart';
import 'package:incenti_ai/models/app_project_model.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/common_shimmer_effect.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/custom_sliver_list_view.dart';
import 'package:incenti_ai/utillites/delete_confirmation_dialog.dart';
import 'package:incenti_ai/utillites/empty.dart';
import 'package:incenti_ai/utillites/image_slider_show.dart';
import 'package:incenti_ai/utillites/network_image.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:intl/intl.dart';

import '../../../../../models/section_base.dart';
import '../../controllers/project_detail_view_controller.dart';

class DraftsSection extends SectionBase<ProjectDetailViewController> {
  DraftsSection({required super.controller});

  @override
  String get title => 'Drafts';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();
        Get.toNamed(Routes.post, arguments: {
          "isCreateProject": true,
          "isCrossVisible": false,
          "projectModel": Project(
              id: controller.getProjectDetailData.value.id,
              name: controller.getProjectDetailData.value.name,
              image: controller.getProjectDetailData.value.image,
              subProjectsCount:
              controller.getProjectDetailData.value.subProjectsCount)
        })?.then(
              (value) {
            controller.page.value = 1;
            controller.hasMoreData.value = true;
            controller.postDataList.clear();
            controller.callApiForUserGetPost(
                context: Get.context!,
                isPublished: controller.currentSelectedIndex.value == 5);
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      Obx(() {
        return controller.isUserPostLoading.value &&
                controller.postDataList.isEmpty
            ? SliverToBoxAdapter(child: ShimmerPostCard())
            : CustomSliverListView(
                emptyWidget: Padding(
                  padding: EdgeInsets.only(
                    top: MySize.size25 ?? 100,
                    left: paddingHoriZontal,
                    right: paddingHoriZontal,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(top: MySize.size50 ?? 50),
                    child: const Empty(
                      title: "No drafts available!",
                    ),
                  ),
                ),
                maximumReachedWidget: const SizedBox(),
                itemBuilder: (context, Post post, index) {
                  Post res = controller.postDataList[index];
                  List<Media> sortedMedia = List.from(res.media);
                  sortedMedia.sort((a, b) =>
                      (b.isCoverImage == true ? 1 : 0) -
                      (a.isCoverImage == true ? 1 : 0));
                  return res.isPrivate == true
                      ? const SizedBox()
                      : Padding(
                          padding: EdgeInsets.only(
                            left: MySize.getScaledSizeWidth(24),
                            right: MySize.getScaledSizeWidth(24),
                            top: MySize.getScaledSizeHeight(20),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TypoGraphy(
                                    text:
                                        "Edited on ${DateFormat('MMM d, y').format(DateTime.parse(res.updatedAt.toString()))}",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: box.read('isDarkMode')
                                        ? AppTheme.grey
                                        : AppTheme.baseBlack,
                                  ),
                                  Spacer(),
                                  (controller.getProjectDetailData.value
                                                  .userId ==
                                              CurrentUser.user.id ||
                                          controller.postDataList[index].user
                                                  ?.id ==
                                              CurrentUser.user.id)
                                      ? GestureDetector(
                                          onTap: () {
                                            HapticFeedback.lightImpact();
                                            ImagePickerBottomSheet.show(
                                                context: context,
                                                child: Column(
                                                  children: [
                                                    Space.height(40),
                                                    InkWell(
                                                      onTap: () {
                                                        Get.toNamed(Routes.post,
                                                            arguments: {
                                                              "PostId": res.id,
                                                              "isDraft": true,
                                                              "isCrossVisible": false,
                                                              "isProjectEdit": true
                                                            })?.then(
                                                          (value) {
                                                            Get.back();
                                                            controller
                                                                .page.value = 1;
                                                            controller
                                                                .hasMoreData
                                                                .value = true;
                                                            controller
                                                                .postDataList
                                                                .clear();
                                                            controller
                                                                .callApiForUserGetPost(
                                                                    context: Get
                                                                        .context!,
                                                                    isPublished:
                                                                        true);
                                                          },
                                                        );
                                                      },
                                                      child: Row(
                                                        children: [
                                                          Space.width(30),
                                                          SvgPicture.asset(
                                                            AppImage.editIcon,
                                                            height:
                                                                MySize.size24,
                                                            width:
                                                                MySize.size24,
                                                            color: AppTheme
                                                                .whiteWithBase,
                                                          ),
                                                          Space.width(20),
                                                          TypoGraphy(
                                                            text: "Edit",
                                                            level: 5,
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                    if (controller
                                                                .getProjectDetailData
                                                                .value
                                                                .userId ==
                                                            CurrentUser
                                                                .user.id ||
                                                        controller
                                                                .postDataList[
                                                                    index]
                                                                .user
                                                                ?.id ==
                                                            CurrentUser
                                                                .user.id) ...[
                                                      Space.height(41),
                                                      InkWell(
                                                        onTap: () async {
                                                          Navigator.pop(
                                                              Get.context!);
                                                          HapticFeedback
                                                              .heavyImpact();
                                                          showDeleteConfirmationDialog(
                                                            context:
                                                                Get.context!,
                                                            description:
                                                                "Are you sure you want to delete project draft permanently?",
                                                            onConfirm:
                                                                () async {
                                                              await controller
                                                                  .callApiForDeleteOnePost(
                                                                      context: Get
                                                                          .context!,
                                                                      postId: res
                                                                          .id,
                                                                      isDraft:
                                                                          true)
                                                                  .then(
                                                                (value) {
                                                                  controller
                                                                      .page
                                                                      .value = 1;
                                                                  controller
                                                                      .hasMoreData
                                                                      .value = true;
                                                                  controller
                                                                      .postDataList
                                                                      .clear();
                                                                  controller.callApiForUserGetPost(
                                                                      context: Get
                                                                          .context!,
                                                                      isPublished:
                                                                          true);
                                                                },
                                                              );
                                                            },
                                                            title:
                                                                "Delete Project Draft",
                                                            onCancel: () {
                                                              Get.back();
                                                            }, isLoading: controller.isLoading
                                                          );
                                                        },
                                                        child: Row(
                                                          children: [
                                                            Space.width(30),
                                                            SvgPicture.asset(
                                                              AppImage
                                                                  .trashIcon,
                                                              height:
                                                                  MySize.size24,
                                                              width:
                                                                  MySize.size24,
                                                              color:
                                                                  AppTheme.red,
                                                            ),
                                                            Space.width(20),
                                                            TypoGraphy(
                                                              text: "Delete",
                                                              level: 5,
                                                              color:
                                                                  AppTheme.red,
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                    Space.height(61),
                                                  ],
                                                ));
                                          },
                                          child: SvgPicture.asset(
                                            AppImage.moreVertIcon,
                                            height:
                                                MySize.getScaledSizeWidth(24),
                                            width:
                                                MySize.getScaledSizeHeight(24),
                                            color: box.read('isDarkMode')
                                                ? AppTheme.grey
                                                : null,
                                          ),
                                        )
                                      : SizedBox(),
                                ],
                              ),
                              InkWell(
                                onTap: () {
                                  if (controller.getProjectDetailData.value
                                              .userId ==
                                          CurrentUser.user.id ||
                                      controller.postDataList[index].user?.id ==
                                          CurrentUser.user.id) {
                                    Get.toNamed(Routes.post, arguments: {
                                      "PostId": res.id,
                                      "isDraft": true,
                                      "isCrossVisible": false,
                                      "isProjectEdit": true
                                    })?.then(
                                      (value) {
                                        controller.page.value = 1;
                                        controller.hasMoreData.value = true;
                                        controller.postDataList.clear();
                                        controller.callApiForUserGetPost(
                                            context: Get.context!,
                                            isPublished: true);
                                      },
                                    );
                                  } else {
                                    CommonFunction.showCustomSnackbar(
                                        message: "You can not edit this draft",
                                        isError: true,
                                        backgroundColor: AppTheme.red);
                                  }
                                },
                                child: SizedBox(
                                  width: double.infinity,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: res.media.isEmpty
                                            ? 0
                                            : MySize.getScaledSizeHeight(15),
                                      ),
                                      res.media.isEmpty
                                          ? const SizedBox()
                                          : ImageSlideshow(
                                              width: double.infinity,
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      254),
                                              initialPage: 0,
                                              indicatorColor:
                                                  sortedMedia.length == 1
                                                      ? Colors.transparent
                                                      : AppTheme.white,
                                              indicatorBackgroundColor:
                                                  sortedMedia.length == 1
                                                      ? Colors.transparent
                                                      : AppTheme.white
                                                          .withOpacity(0.50),
                                              indicatorRadius:
                                                  MySize.getScaledSizeWidth(
                                                      3.5),
                                              isLoop: false,
                                              autoPlayInterval: 0,
                                              children: [
                                                for (int i = 0;
                                                    i < sortedMedia.length;
                                                    i++) ...[
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              25),
                                                    ),
                                                    child: ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              25),
                                                      child:
                                                          NetworkImageComponent(
                                                        imageUrl:
                                                            sortedMedia[i].link,
                                                        // simmerHeight: MySize.getScaledSizeHeight(76),
                                                        width: MySize
                                                            .getScaledSizeWidth(
                                                                90),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                      res.title == ""
                                          ? const SizedBox()
                                          : SizedBox(
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      20),
                                            ),
                                      res.title == ""
                                          ? const SizedBox()
                                          : TypoGraphy(
                                              text: res.title,
                                              level: 4,
                                            ),
                                    ],
                                  ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                  top: MySize.getScaledSizeHeight(
                                      res.description == "" ||
                                              res.description == "<br> "
                                          ? 0
                                          : 18),
                                  bottom: MySize.getScaledSizeHeight(18),
                                ),
                              ),
                              Divider(
                                thickness: 1,
                                color: AppTheme.grey[50],
                                height: 1,
                              ),
                            ],
                          ),
                        );
                },
                isLoading: controller.apiManager.isLoading,
                items: controller.postDataList,
                hasMoreData: controller.hasMoreData.value,
                onLoadMore: () {
                  return controller.callApiForUserGetPost(
                      context: context, isPublished: true);
                },
              );
      })
    ];
  }

  @override
  void onCategorySelected() {
    controller.page.value = 1;
    controller.hasMoreData.value = true;
    controller.postDataList.clear();
    controller.callApiForUserGetPost(
        context: Get.context!,
        isPublished: true);
  }
}
