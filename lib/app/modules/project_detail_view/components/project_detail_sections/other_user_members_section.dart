import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/profile_view/components/remover_follower.dart';
import 'package:incenti_ai/app/modules/project_detail_view/components/team_shimmer_effect.dart';
import 'package:incenti_ai/app/routes/app_pages.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/models/app_project_team_member_model.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_profile_widget.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/custom_sliver_list_view.dart';
import 'package:incenti_ai/utillites/empty.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../../models/section_base.dart';
import '../../controllers/project_detail_view_controller.dart';

class OtherUserMembersSection extends SectionBase<ProjectDetailViewController> {
  OtherUserMembersSection({required super.controller});

  @override
  String get title => 'Members';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();
        Get.toNamed(Routes.create_todo, arguments: {
          "isProjectCreate": true,
          "ProjectId": controller.projectId.value
        })?.then(
              (value) {
            controller.page.value = 1;
            controller.hasMoreData.value = true;
            controller.todoList.clear();
            controller.callApiForToDo(context: context);
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(child: Space.height(15)),
      Obx(() {
        List<ProjectTeamMemberData> teamMembers = controller.teamMemberDataList
            .where((element) => ((element.status ?? false)))
            .toList();
        return controller.isLoading.value && teamMembers.isEmpty
            ? SliverToBoxAdapter(
                child: ListView.builder(
                itemCount: 5,
                shrinkWrap: true,
                padding: EdgeInsets.only(top: MySize.size10 ?? 10),
                controller: ScrollController(),
                itemBuilder: (context, index) {
                  return TeamMemberTileShimmer();
                },
              ))
            : CustomSliverListView(
                emptyWidget: Padding(
                  padding: EdgeInsets.only(
                    top: MySize.size10 ?? 100,
                    left: paddingHoriZontal,
                    right: paddingHoriZontal,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(top: MySize.size50 ?? 50),
                    child: const Empty(
                      title: "No Member available!",
                    ),
                  ),
                ),
                maximumReachedWidget: const SizedBox(),
                itemBuilder:
                    (context, ProjectTeamMemberData projectTeamMember, index) {
                  return InkWell(
                    onTap: () {
                      if(teamMembers[index].user?.id == CurrentUser.user.id) {
                        Get.toNamed(Routes.profile);
                      } else {
                        Get.toNamed(Routes.other_user_profile,
                            arguments: {"UserId": teamMembers[index].user?.id});
                      }
                    },
                    child: ListTile(
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(30),
                            vertical: MySize.getScaledSizeHeight(8)),
                        leading: profileImage(
                          url: teamMembers[index].user?.image ?? "",
                          userName: teamMembers[index].user?.firstName ?? "",
                          iconHeight: MySize.getScaledSizeHeight(60),
                          iconWidth: MySize.getScaledSizeWidth(60),
                          height: MySize.getScaledSizeHeight(60),
                          width: MySize.getScaledSizeWidth(60),
                        ),
                        title: TypoGraphy(
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          text:
                              "${teamMembers[index].user?.firstName} ${teamMembers[index].user?.lastName}",
                          level: 12,
                          // color: AppTheme.baseBlack,
                        ),
                        trailing: CurrentUser.user.id ==
                                teamMembers[index].user?.id
                            ? SizedBox()
                            : Obx(
                                () => InkWell(
                                  onTap: () {
                                    if (teamMembers[index]
                                            .user
                                            ?.isFollowing
                                            ?.value ==
                                        false) {
                                      showModalBottomSheet(
                                        backgroundColor: Colors.transparent,
                                        context: context,
                                        builder: (context) {
                                          return RemoveFollower(
                                            userName:
                                                "${teamMembers[index].user?.firstName} ${teamMembers[index].user?.lastName}",
                                            image:
                                                teamMembers[index].user?.image ??
                                                    "",
                                            onRemove: () {
                                              controller.callApiForUnFollowUser(
                                                  context: Get.context!,
                                                  userId:
                                                      teamMembers[index].user?.id,
                                                  isUnFollowed: true,
                                                  index: index);
                                            },
                                            isForUnfollow: true,
                                          );
                                        },
                                      );
                                    } else {
                                      teamMembers[index]
                                          .user
                                          ?.isFollowing
                                          ?.value = false;
                                      controller.callApiForFollowUser(
                                          context: Get.context!,
                                          userId: teamMembers[index].user?.id);
                                    }
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(50),
                                      color: (Theme.of(context)
                                          .brightness ==
                                          Brightness
                                              .dark &&
                                          !(teamMembers[index]
                                              .user
                                              ?.isFollowing
                                              ?.value ==
                                              false))
                                          ? AppTheme.bottomBar
                                          : Colors.transparent,
                                      border: Border.all(
                                          color: teamMembers[index]
                                                      .user
                                                      ?.isFollowing
                                                      ?.value ==
                                                  false
                                              ? AppTheme.grey
                                              : Theme.of(context)
                                              .brightness ==
                                              Brightness
                                                  .dark
                                              ? AppTheme.grey
                                              : AppTheme
                                              .primary1),
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: MySize.getScaledSizeWidth(17),
                                        vertical: MySize.getScaledSizeHeight(11),
                                      ),
                                      child: TypoGraphy(
                                        text: teamMembers[index]
                                                    .user
                                                    ?.isFollowing
                                                    ?.value ==
                                                false
                                            ? "Unfollow"
                                            : "Follow",
                                        textStyle: TextStyle(
                                          fontFamily: "Inter",
                                          color: teamMembers[index]
                                                      .user
                                                      ?.isFollowing
                                                      ?.value ==
                                                  false
                                              ? AppTheme.grey
                                              : AppTheme.primaryIconDark,
                                          fontSize:
                                              MySize.getScaledSizeHeight(15),
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              )),
                  );
                },
                isLoading: controller.apiManager.isLoading,
                items: teamMembers,
                hasMoreData: false,
                onLoadMore: () {
                  return controller.callApiForGetProjectTeamMember(
                    context: context,
                  );
                },
              );
      })
    ];
  }

  @override
  void onCategorySelected() {
    controller.teamMemberDataList.clear();
    controller
        .callApiForGetProjectTeamMember(
        context: Get.context!);
  }
}
