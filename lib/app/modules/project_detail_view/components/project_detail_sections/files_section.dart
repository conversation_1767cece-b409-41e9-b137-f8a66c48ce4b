import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/components/common_project_file_widget.dart';
import 'package:incenti_ai/app/modules/project_detail_view/views/project_folder_detail_view.dart';
import 'package:incenti_ai/app/modules/user_detail/components/image_picker_bottom_sheet.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/main.dart';
import 'package:incenti_ai/models/app_project_model.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/common_grid_view.dart';
import 'package:incenti_ai/utillites/common_shimmer_grid_view.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/empty.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../../models/section_base.dart';
import '../../controllers/project_detail_view_controller.dart';

class FilesSection extends SectionBase<ProjectDetailViewController> {
  FilesSection({required super.controller});

  @override
  String get title => 'Files';

  @override
  Widget floatingActionButtonBuilder(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: AppTheme.primary1,
      // shape: const CircleBorder(),
      onPressed: () async {
        HapticFeedback.lightImpact();
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
          ),
          builder: (context) => createOrEditFolderBottomSheet(context,
              controller: controller),
        ).then(
              (value) {
            controller.folderNameController.clear();
          },
        );
      },
      child: SvgPicture.asset(
        controller.iconMap[controller.currentSelectedIndex.value] ??
            AppImage.postIcon,
        height: MySize.size70,
      ),
    );
  }

  @override
  List<Widget> viewBuilder(BuildContext context) {
    return [
      SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(
              left: MySize.size30 ?? 30,
              right: MySize.size30 ?? 30,
              top: MySize.size25 ?? 30),
          child: Obx(
            () => controller.isFoldersLoading.value
                ? ShimmerGridView()
                : controller.folderList.isNotEmpty
                    ? GridView.builder(
                        padding: EdgeInsets.zero,
                        keyboardDismissBehavior:
                            ScrollViewKeyboardDismissBehavior.onDrag,
                        gridDelegate: FixedHeightGridDelegate(
                          itemHeight: MySize.size220 ?? 200,
                          crossAxisCount: 2,
                          crossAxisSpacing: MySize.size20 ?? 20,
                          mainAxisSpacing: MySize.size10 ?? 30,
                        ),
                        physics: ClampingScrollPhysics(),
                        shrinkWrap: true,
                        controller: ScrollController(),
                        itemCount: controller.folderList.length,
                        itemBuilder: (context, index) {
                          var folder =
                              controller.folderList[index] as ProjectFolder;
                          return GestureDetector(
                            onTap: () {
                              Get.to(
                                () => ProjectFolderDetailView(
                                  folder: folder,
                                  isIconVisible: CurrentUser.user.id ==
                                          controller.getProjectDetailData.value
                                              .userId ||
                                      (controller.getProjectDetailData.value
                                              .projectMembers.isNotEmpty &&
                                          controller.getProjectDetailData.value
                                                  .projectMembers[0].access ==
                                              "write"),
                                  isDeleteVisible: CurrentUser.user.id ==
                                      controller
                                          .getProjectDetailData.value.userId,
                                ),
                              )?.then(
                                (value) {
                                  controller.callApiForGetFolders(
                                      context: context);
                                },
                              );
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Stack(
                                  children: [
                                    Container(
                                      height: MySize.getScaledSizeHeight(142),
                                      width: MySize.safeWidth,
                                      decoration: BoxDecoration(
                                        color: box.read('isDarkMode')
                                            ? AppTheme.darkBackground
                                            : Color(0xfff6f6f6),
                                        border: Border.all(
                                            color: AppTheme.borderWithTrans),
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      child: Center(
                                        child: Image.asset(
                                          AppImage.folder,
                                          height: MySize.size90,
                                          width: MySize.size90,
                                          color: AppTheme.whiteWithNull,
                                        ),
                                      ),
                                    ),
                                    if ((CurrentUser.user.id ==
                                                controller.getProjectDetailData
                                                    .value.userId ||
                                            CurrentUser.user.id ==
                                                controller.folderList[index]
                                                    .userId) ||
                                        (controller.getProjectDetailData.value
                                                .projectMembers.isNotEmpty &&
                                            controller
                                                    .getProjectDetailData
                                                    .value
                                                    .projectMembers[0]
                                                    .access ==
                                                "write"))
                                      Positioned(
                                        top: MySize.size12,
                                        right: MySize.size12,
                                        child: GestureDetector(
                                          onTap: () {
                                            HapticFeedback.lightImpact();
                                            ImagePickerBottomSheet.show(
                                              context: context,
                                              child: showFolderOption(
                                                  controller: controller,
                                                  folder,
                                                  isDeleteVisible: CurrentUser
                                                              .user.id ==
                                                          controller
                                                              .getProjectDetailData
                                                              .value
                                                              .userId ||
                                                      CurrentUser.user.id ==
                                                          controller
                                                              .folderList[index]
                                                              .userId),
                                            );
                                          },
                                          child: SvgPicture.asset(
                                            AppImage.moreVertIcon,
                                            color: AppTheme.grey,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                Space.height(6),
                                TypoGraphy(
                                  text: folder.name,
                                  level: 12,
                                ),
                                Space.height(4),
                                TypoGraphy(
                                  text: '${folder.filesCount} Files',
                                  color:  box.read('isDarkMode')
                                      ? AppTheme.grey : AppTheme.baseBlack,
                                  level: 2,
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    : Padding(
                        padding: EdgeInsets.only(top: MySize.size75 ?? 50),
                        child: const Empty(
                          title: "No Files available!",
                        ),
                      ),
          ),
        ),
      ),
      SliverToBoxAdapter(child: Space.height(35)),
    ];
  }

  @override
  void onCategorySelected() {
    controller.callApiForGetFolders(
        context: Get.context!);
  }
}
