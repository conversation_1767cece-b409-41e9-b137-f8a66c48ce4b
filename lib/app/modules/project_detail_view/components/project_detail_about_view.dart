import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';

class ProjectDetailsWidget extends StatelessWidget {
  final String projectName;
  final String projectDescription;
  final String? communityMember;
  final String userImage;
  final String userName;
  final String userLastName;
  final String? communityJoinDate;
  final String createdAt;
  final bool isCurrentUser;
  final bool isCommunity;
  final bool isCommunityJoin;
  final int currentUseId;

  const ProjectDetailsWidget({
    super.key,
    required this.projectName,
    required this.projectDescription,
    this.communityMember,
    required this.userImage,
    required this.userName,
    required this.userLastName,
    required this.createdAt,
    required this.currentUseId,
    this.isCurrentUser = false,
    this.isCommunity = false,
    this.isCommunityJoin = false,
    this.communityJoinDate,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(30)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Space.height(20),
          Align(
            alignment: Alignment.center,
            child: TypoGraphy(
              text: "About",
              level: 11,
              fontWeight: FontWeight.w700,
            ),
          ),
          Space.height(24),
          TypoGraphy(
            text: projectName,
            level: 8,
            fontWeight: FontWeight.w700,
          ),
          if(projectDescription.isNotEmpty) ...[
            Space.height(21),
            TypoGraphy(
              text: projectDescription,
              level: 4,
              fontWeight: FontWeight.w400,
            ),
          ],
          Space.height(30),
          InkWell(
            onTap: () {
              Navigator.pop(context);
              if(!isCurrentUser) {
                Get.toNamed(Routes.other_user_profile,arguments: {"UserId": currentUseId});
              } else {
                Get.toNamed(Routes.profile);
              }

            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: profileImage(
                    url: userImage,
                    userName: userName,
                    iconHeight: MySize.getScaledSizeHeight(36),
                    iconWidth: MySize.getScaledSizeWidth(36),
                    width: MySize.getScaledSizeHeight(36),
                    height: MySize.getScaledSizeWidth(36),
                    backgroundColor: Colors.transparent,
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "$userName $userLastName",
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: AppTheme.whiteWithBase,
                              ),
                            ),
                            if (isCurrentUser)
                              TextSpan(
                                text: " (You)",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: AppTheme.whiteWithBase,
                                ),
                              ),
                          ],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      TypoGraphy(
                        text: "Created on $createdAt",
                        level: 3,
                        fontWeight: FontWeight.w400,
                        color: AppTheme.grey,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (isCommunity && isCurrentUser) ...[
            Space.height(30),
            Center(
              child: TypoGraphy(
                text: communityMember == "0" ? "No one has joined this community yet." : (int.tryParse(communityMember!)! >= 2) ? "$communityMember members have joined your community" : "$communityMember member has joined your community",
                level: 3,
                fontWeight: FontWeight.w400,
                color: AppTheme.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          if (isCommunityJoin) ...[
            Space.height(30),
            Center(
              child: TypoGraphy(
                text: "You Joined on ${DateFormat('MMM d, y')
                    .format(DateTime.parse(communityJoinDate.toString()))}",
                level: 3,
                fontWeight: FontWeight.w400,
                color: AppTheme.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          Space.height(50),
        ],
      ),
    );
  }
}
