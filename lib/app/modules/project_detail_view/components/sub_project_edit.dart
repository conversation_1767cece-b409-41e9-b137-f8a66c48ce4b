import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_detail_view_controller.dart';
import 'package:incenti_ai/app/modules/sub_project_detail_view/controllers/sub_project_detail_view_controller.dart';

import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/delete_confirmation_dialog.dart';
import '../../../../utillites/typography.dart';
import '../../create_project/controllers/create_project_controller.dart';
import '../../create_project/views/create_sub_project.dart';
import '../../projects/controllers/projects_controller.dart';
ProjectDetailViewController get projectController =>
    Get.put(ProjectDetailViewController(),tag: Get.arguments['projectId'].toString());
Column showSubProjectEditOption(
    {String? projectId,
    String? projectName,
    bool isSubProject = false,
    bool isPrivate = false,
    bool isBack = true,
      bool isHideDelete = false,
      bool mainProjectVisibility = false,
    required void Function() hidePrivate,
    bool isHide = false,void Function()? callBack}) {
  return Column(
    children: [
      Space.height(40),
      InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          // if(isPrivate) {
          //   if(!Get.isRegistered<CreateProjectController>()) {
          //     Get.put(CreateProjectController()).visibilityValue.value = 1;
          //   } else {
          //     Get.find<CreateProjectController>().visibilityValue.value = 1;
          //   }
          // }
          Navigator.pop(Get.context!);
          showModalBottomSheet(
            context: Get.context!,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            builder: (context) =>
                subProjectVisibility(context, true, projectId: projectId,isVisibilityValue: isPrivate ? 1 : 0,mainSubProjectVisibility: mainProjectVisibility),
          ).then(
            (value) {
              if (isSubProject) {
                Get.find<SubProjectDetailViewController>()
                    .callApiForGetOneProject(
                        context: Get.context!, projectId: projectId.toString());
              } else {
                projectController.callApiForOneSubProject(
                    context: Get.context!,
                    projectId: projectController
                        .projectId
                        .value
                        .toString(),
                    userId: projectController
                        .getProjectDetailData
                        .value
                        .userId);
              }
              Get.delete<CreateProjectController>();
            },
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(
              AppImage.editIcon,
              height: MySize.size24,
              width: MySize.size24,
              color: AppTheme.whiteWithBase,
            ),
            Space.width(20),
            TypoGraphy(
              text: "Edit",
              level: 5,
            )
          ],
        ),
      ),
      if (isPrivate) ...[
        Space.height(41),
        InkWell(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          // onTap: hidePrivate,
          onTap: () {
            Navigator.pop(Get.context!);
            Get.dialog(
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Dialog(
                  insetPadding: EdgeInsets.symmetric(horizontal: MySize.size20 ?? 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(30.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Space.height(20),
                        // Title
                        TypoGraphy(
                          text: "${!isHide ? "Hide Private" : "Undo Hide"} Project",
                          level: 12,
                          textAlign: TextAlign.center,
                        ),
                        Space.height(10),

                        // Description
                        TypoGraphy(
                          text: isHide ? "Currently others can’t see your Private Project $projectName. Do you want to undo that? " :
                          "Currently others are able to see your Private Project $projectName. Do you want to hide that?",
                          level: 3,
                          color: AppTheme.grey,
                          textAlign: TextAlign.center,
                        ),
                        Space.height(40),

                        // Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // Cancel Button
                            Expanded(
                              child: InkWell(
                                highlightColor: Colors.transparent,
                                splashColor: Colors.transparent,
                                onTap: () => Get.back(),
                                child: Container(
                                  height: MySize.getScaledSizeHeight(70),
                                  width: MySize.getScaledSizeHeight(170),
                                  alignment: Alignment.center,
                                  child: TypoGraphy(
                                    text: "No",
                                    level: 4,
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(width: 10),

                            // Confirm Delete Button
                            Expanded(
                              child: Buttons(
                                buttonText: "Yes",
                                buttonTextLevel: 4,
                                color: AppTheme.red,
                                height: MySize.getScaledSizeHeight(70),
                                onTap: () {
                                  HapticFeedback.heavyImpact();
                                  hidePrivate(); // Execute Delete Action
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
          child: Row(
            children: [
              Space.width(30),
              Icon(CupertinoIcons.lock_fill,
                  size: MySize.getScaledSizeHeight(20)),
              Space.width(20),
              TypoGraphy(
                text: isHide ? "Undo Hide Project" : "Hide Private Project",
                level: 5,
              )
            ],
          ),
        ),
      ],
      if(!isHideDelete) Space.height(41),
      if(!isHideDelete)
        InkWell(
        onTap: () {
          Navigator.pop(Get.context!);
          showDeleteConfirmationDialog(
            context: Get.context!,
            description:
                "Are you sure you want to delete Sub Project permanently?",
            onConfirm: () async {
              Get.back();
              if (!Get.isRegistered<ProjectsController>()) {
                Get.put(ProjectsController());
              }
              projectController
                  .callApiForDeleteOneProject(
                      context: Get.context!, projectId: projectId,isBack: isBack,isSubProject: true)
                  .then(
                (value) {
                  if (isSubProject) {
                    Get.find<SubProjectDetailViewController>()
                        .callApiForGetOneProject(
                            context: Get.context!,
                            projectId: projectId.toString());
                  } else {
                    projectController
                        .callApiForOneSubProject(
                            context: Get.context!,
                            projectId:
                            projectController
                                    .projectId
                                    .value
                                    .toString(),
                            userId: projectController
                                .getProjectDetailData
                                .value
                                .userId);
                  }
                },
              );
            },
            title: "Delete Sub Project",
            onCancel: () {
              Get.back();
            }, isLoading: projectController.isLoading,
          );
        },
        child: Row(
          children: [
            Space.width(30),
            SvgPicture.asset(AppImage.trashIcon,
                height: MySize.size24, width: MySize.size24),
            Space.width(20),
            TypoGraphy(
              text: "Delete",
              level: 5,
              color: AppTheme.red,
            )
          ],
        ),
      ),
      Space.height(61),
    ],
  );
}
