import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/custom_sliver_list_view.dart';
import '../../../../utillites/empty.dart';
import '../../../../utillites/loader.dart';
import '../controllers/project_team_member_controller.dart';

class ProjectTeamMemberView extends GetWidget<ProjectTeamMemberController> {
  const ProjectTeamMemberView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      // backgroundColor: AppTheme.white,
      body: Column(
        children: [
          Space.height(50),
          Padding(
            padding:
                EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(37)),
            child: Row(
              children: [
                InkWell(
                  onTap: () {
                    Get.back();
                  },
                  child: SvgPicture.asset(
                    AppImage.backArrow,
                    height: MySize.getScaledSizeHeight(28),
                    width: MySize.getScaledSizeHeight(28),
                    color: AppTheme.whiteWithNull,
                  ),
                ),
                Spacer(),
                TypoGraphy(
                  text: "Add team member",
                  level: 12,
                ),
                Spacer(),
                Obx(

                  () => controller.hasInitialData.isTrue
                      ? InkWell(
                          onTap: () {
                            final selectedIds = controller
                                .selectedUsersMap.entries
                                .where((entry) => entry.value)
                                .map((entry) => entry.key)
                                .toList();
                            if (selectedIds.isNotEmpty) {
                              showModalBottomSheet(
                                context: context,
                                backgroundColor: Colors.transparent,
                                isScrollControlled: true,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(20)),
                                ),
                                builder: (context) {
                                  return BackdropFilter(
                                    filter:
                                        ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                                    child: FractionallySizedBox(
                                      heightFactor: 0.4,
                                      child: Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: AppTheme.appBottomSheet,
                                          border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(40),
                                              topRight: Radius.circular(40)),
                                        ),
                                        child: Column(
                                          children: [
                                            Space.height(41),
                                            Row(
                                              children: [
                                                Space.width(30),
                                                InkWell(
                                                  onTap: () {
                                                    Navigator.pop(context);
                                                  },
                                                  child: SvgPicture.asset(
                                                      AppImage.backArrow,color: AppTheme.whiteWithBase),
                                                ),
                                                Spacer(),
                                                TypoGraphy(
                                                  text: "What can they do",
                                                  level: 12,
                                                ),
                                                Spacer(),
                                                SvgPicture.asset(
                                                    AppImage.backArrow,color: AppTheme.appBottomSheet,),
                                                Space.width(30),
                                              ],
                                            ),
                                            Space.height(32),
                                            Obx(
                                              () => InkWell(
                                                onTap: () {
                                                  controller
                                                      .selectedIndex.value = 0;
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: MySize
                                                          .getScaledSizeWidth(
                                                              30)),
                                                  child: Row(
                                                    children: [
                                                      Image.asset(
                                                        AppImage.viewOnly,
                                                        height: MySize
                                                            .getScaledSizeHeight(
                                                                24),
                                                        width: MySize
                                                            .getScaledSizeHeight(
                                                                24),
                                                        color: controller
                                                                    .selectedIndex
                                                                    .value ==
                                                                0
                                                            ? AppTheme.primary1
                                                            : AppTheme.whiteWithBase,
                                                      ),
                                                      Space.width(20),
                                                      TypoGraphy(
                                                        text: "View only",
                                                        level: 5,
                                                        color: controller
                                                                    .selectedIndex
                                                                    .value ==
                                                                0
                                                            ? AppTheme.primary1
                                                            : AppTheme.whiteWithBase,
                                                      ),
                                                      Spacer(),
                                                      if (controller
                                                              .selectedIndex
                                                              .value ==
                                                          0)
                                                        SvgPicture.asset(
                                                            AppImage.checkIcon,
                                                            height: MySize
                                                                .getScaledSizeHeight(
                                                                    24),
                                                            width: MySize
                                                                .getScaledSizeHeight(
                                                                    24),
                                                            color: AppTheme
                                                                .primary1),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            Space.height(40),
                                            Obx(
                                              () => InkWell(
                                                onTap: () {
                                                  controller
                                                      .selectedIndex.value = 1;
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: MySize
                                                          .getScaledSizeWidth(
                                                              30)),
                                                  child: Row(
                                                    children: [
                                                      Image.asset(
                                                        AppImage.editMember,
                                                        height: MySize
                                                            .getScaledSizeHeight(
                                                                24),
                                                        width: MySize
                                                            .getScaledSizeHeight(
                                                                24),
                                                        color: controller
                                                                    .selectedIndex
                                                                    .value ==
                                                                1
                                                            ? AppTheme.primary1
                                                            : AppTheme.whiteWithBase,
                                                      ),
                                                      Space.width(20),
                                                      TypoGraphy(
                                                        text: "Can edit",
                                                        level: 5,
                                                        color: controller
                                                                    .selectedIndex
                                                                    .value ==
                                                                1
                                                            ? AppTheme.primary1
                                                            : AppTheme.whiteWithBase,
                                                      ),
                                                      Spacer(),
                                                      if (controller
                                                              .selectedIndex
                                                              .value ==
                                                          1)
                                                        SvgPicture.asset(
                                                            AppImage.checkIcon,
                                                            height: MySize
                                                                .getScaledSizeHeight(
                                                                    24),
                                                            width: MySize
                                                                .getScaledSizeHeight(
                                                                    24),
                                                            color: AppTheme
                                                                .primary1),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            Space.height(40),
                                            Obx(
                                              () => Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Buttons(
                                                    buttonText: "Invite",
                                                    buttonTextLevel: 4,
                                                    height: MySize
                                                        .getScaledSizeHeight(
                                                            70),
                                                    width: MySize
                                                        .getScaledSizeWidth(
                                                            189),
                                                    isLoading: controller
                                                        .isLoading.value,
                                                    onTap: () {
                                                      HapticFeedback
                                                          .lightImpact();
                                                      controller
                                                          .callApiForInviteProjectUser(
                                                        context: context,
                                                        projectMemberList:
                                                            selectedIds,
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            } else {
                              CommonFunction.showCustomSnackbar(
                                message: "Please select team member",
                                isError: true,
                                backgroundColor: AppTheme.red,
                              );
                            }
                          },
                          child: TypoGraphy(
                            text: "Next",
                            level: 12,
                            color: AppTheme.primaryIconDark,
                          ),
                        )
                      : SizedBox(),
                ),
              ],
            ),
          ),
          Space.height(22),
         /* Obx(
            () => (controller.hasInitialData.isTrue)
                ? Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
                    child: ValueListenableBuilder(
                      valueListenable: controller.searchController.value,
                      builder: (context, value, child) {
                        return AppTextField(
                          controller: controller.searchController.value,
                          focusNode: controller.searchFocusNode,
                          padding: EdgeInsets.only(
                            top: MySize.size12 ?? 12,
                            bottom: MySize.size10 ?? 12,
                          ),
                          onChangedValue: (text) {
                            if ((text?.trim() ?? "").isNotEmpty) {
                              controller.debounceTimer?.cancel();
                              controller.debounceTimer =
                                  Timer(Duration(milliseconds: 500), () {
                                controller.page.value = 1;
                                controller.projectUserList.clear();
                                controller.pullRefresh();
                              });
                            }
                          },
                          height: MySize.getScaledSizeHeight(50),
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(4),
                            child: SvgPicture.asset(
                              AppImage.searchIcon,
                              height: MySize.size24,
                              width: MySize.size24,
                              color: AppTheme.grey,
                            ),
                          ),
                          suffixIcon: (controller
                                  .searchController.value.text.isNotEmpty)
                              ? GestureDetector(
                                  onTap: () {
                                    controller.searchController.value.clear();
                                    controller.page.value = 1;
                                    controller.projectUserList.clear();
                                    controller.pullRefresh();
                                  },
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        right: MySize.size15 ?? 20),
                                    child: SvgPicture.asset(
                                      AppImage.textFieldClear,
                                      color: AppTheme.grey,
                                    ),
                                  ),
                                )
                              : null,
                          hintText: "Search",
                          hintStyle: TextStyle(
                            color: AppTheme.grey,
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w400,
                          ),
                        );
                      },
                    ),
                  )
                : SizedBox(),
          ),*/
          Padding(
            padding:
            EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
            child: ValueListenableBuilder(
              valueListenable: controller.searchController.value,
              builder: (context, value, child) {
                return AppTextField(
                  controller: controller.searchController.value,
                  focusNode: controller.searchFocusNode,
                  padding: EdgeInsets.only(
                    top: MySize.size12 ?? 12,
                    bottom: MySize.size10 ?? 12,
                  ),
                  onChangedValue: (text) {
                    if ((text?.trim() ?? "").isNotEmpty) {
                      controller.debounceTimer?.cancel();
                      controller.debounceTimer =
                          Timer(Duration(milliseconds: 500), () {
                            controller.page.value = 1;
                            controller.projectUserList.clear();
                            controller.pullRefresh();
                          });
                    }
                  },
                  height: MySize.getScaledSizeHeight(50),
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(4),
                    child: SvgPicture.asset(
                      AppImage.searchIcon,
                      height: MySize.size24,
                      width: MySize.size24,
                      color: AppTheme.grey,
                    ),
                  ),
                  suffixIcon: (controller
                      .searchController.value.text.isNotEmpty)
                      ? GestureDetector(
                    onTap: () {
                      controller.searchController.value.clear();
                      controller.page.value = 1;
                      controller.projectUserList.clear();
                      controller.pullRefresh();
                    },
                    child: Padding(
                      padding: EdgeInsets.only(
                          right: MySize.size15 ?? 20),
                      child: SvgPicture.asset(
                        AppImage.textFieldClear,
                        color: AppTheme.grey,
                      ),
                    ),
                  )
                      : null,
                  hintText: "Search",
                  hintStyle: TextStyle(
                    color: AppTheme.grey,
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w400,
                  ),
                );
              },
            ),
          ),
          Space.height(22),
          Expanded(
            child: Obx(
              () => controller.isLoading.value &&
                      controller.projectUserList.isEmpty
                  ? Loader()
                  : controller.searchController.value.text.isNotEmpty &&
                          controller.projectUserList.isEmpty
                      ? Padding(
                          padding: EdgeInsets.only(
                            top: MySize.getScaledSizeHeight(250),
                            left: paddingHoriZontal,
                            right: paddingHoriZontal,
                          ),
                          child: Column(
                            // mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                "assets/images/icon_search.svg",
                                height: MySize.size50,
                                color: AppTheme.whiteWithNull,
                              ),
                              Empty(
                                title: "Search Result Not Found !",
                              ),
                            ],
                          ),
                        )
                      : CustomScrollView(
                          keyboardDismissBehavior:
                              ScrollViewKeyboardDismissBehavior.onDrag,
                          slivers: [
                            CustomSliverListView(
                              emptyWidget: Padding(
                                  padding: EdgeInsets.only(
                                    top: MySize.getScaledSizeHeight(250),
                                    left: paddingHoriZontal,
                                    right: paddingHoriZontal,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      TypoGraphy(
                                        text: "No team member",
                                        level: 5,
                                      ),
                                      Space.height(5),
                                      TypoGraphy(
                                        text: "No team member yet!",
                                        level: 3,
                                        color: AppTheme.grey,
                                      )
                                    ],
                                  )),
                              maximumReachedWidget: const SizedBox(),
                              itemBuilder: (p0, p1, index) {
                                return Obx(
                                  () {
                                    final userList = controller.projectUserList
                                        .where(
                                          (element) =>
                                              (element.id != CurrentUser.user.id && element.id != controller.createdBy.value),
                                        )
                                        .toList();
                                    final userId = userList[index].id ?? 0;
                                    final isSelected =
                                        controller.selectedUsersMap[userId] ??
                                            false;
                                    return ListTile(
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal:
                                            MySize.getScaledSizeWidth(30),
                                        vertical: MySize.getScaledSizeHeight(8),
                                      ),
                                      leading: profileImage(
                                        url: userList[index].image ?? "",
                                        userName:
                                            userList[index].firstName ?? "",
                                        iconHeight:
                                            MySize.getScaledSizeHeight(60),
                                        iconWidth:
                                            MySize.getScaledSizeWidth(60),
                                        height: MySize.getScaledSizeHeight(60),
                                        width: MySize.getScaledSizeWidth(60),
                                      ),
                                      title: TypoGraphy(
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        text:
                                            "${userList[index].firstName} ${userList[index].lastName}",
                                        level: 12,
                                        // color: AppTheme.baseBlack,
                                      ),
                                      // subtitle: TypoGraphy(
                                      //   maxLines: 1,
                                      //   overflow: TextOverflow.ellipsis,
                                      //   text: userList[index].email,
                                      //   level: 2,
                                      //   fontWeight: FontWeight.w400,
                                      //   color: AppTheme.grey,
                                      // ),
                                      trailing: GestureDetector(
                                        onTap: () {
                                          HapticFeedback.lightImpact();
                                          controller.selectedUsersMap[userId] =
                                              !isSelected;
                                        },
                                        child: Container(
                                          height:
                                              MySize.getScaledSizeHeight(28),
                                          width: MySize.getScaledSizeWidth(28),
                                          decoration: BoxDecoration(
                                            color: Colors.transparent,
                                            border: Border.all(
                                              color: AppTheme.baseBlack,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(6.14),
                                          ),
                                          child: isSelected
                                              ? Padding(
                                                  padding: EdgeInsets.all(4),
                                                  child: SvgPicture.asset(
                                                    AppImage.checkIcon,
                                                    color: AppTheme.primary1,
                                                  ),
                                                )
                                              : null,
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              items: controller.projectUserList
                                  .where(
                                    (element) =>
                                    (element.id != CurrentUser.user.id && element.id != controller.createdBy.value)
                                  )
                                  .toList(),
                              isLoading: controller.isLoading.value,
                              hasMoreData: controller.hasMoreData.value,
                              onLoadMore: () {
                                return controller.callApiForGetProjectUser(
                                  context: context,
                                  projectId:
                                      controller.projectId.value.toString(),
                                  isSubProject: controller.isSubProject.value,
                                  parentProjectId: controller
                                      .parentProjectId.value
                                      .toString(),
                                );
                              },
                            )
                          ],
                        ),
            ),
          ),
        ],
      ),
    );
  }
}
