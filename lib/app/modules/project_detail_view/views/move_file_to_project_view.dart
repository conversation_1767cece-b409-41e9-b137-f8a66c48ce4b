import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_folder_detail_view_controller.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/models/app_project_model.dart';
import 'package:incenti_ai/utillites/app_text_field.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/buttons.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:incenti_ai/utillites/network_image.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../../../../main.dart';

class MoveFileToProjectView extends StatefulWidget {
  const MoveFileToProjectView({
    super.key,
    required this.projectFile,
    required this.controller,
  });

  final ProjectFile projectFile;
  final ProjectFolderDetailViewController controller;

  @override
  State<MoveFileToProjectView> createState() => _MoveFileToProjectViewState();
}

class _MoveFileToProjectViewState extends State<MoveFileToProjectView> {
  late ProjectFolderDetailViewController controller;

  initState() {
    controller = widget.controller;
    super.initState();
    controller.getAllProjects();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Space.height(24),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Space.width(24),
                  TypoGraphy(text: 'Move to Project'),
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: SvgPicture.asset(AppImage.closeImage,
                      color: AppTheme.whiteWithNull,
                    ),
                  ),
                ],
              ),
            ),
            Space.height(24),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30),
              child: ValueListenableBuilder(
                valueListenable: controller.searchController,
                builder: (context, value, child) {
                  return AppTextField(
                    controller: controller.searchController,
                    focusNode: controller.searchFocusNode,
                    padding: EdgeInsets.only(
                      top: MySize.size12 ?? 12,
                      bottom: MySize.size10 ?? 12,
                    ),
                    onChangedValue: (p0) {},
                    height: MySize.getScaledSizeHeight(50),
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(4),
                      child: SvgPicture.asset(
                        AppImage.searchIcon,
                        height: MySize.size24,
                        width: MySize.size24,
                        color: AppTheme.grey,
                      ),
                    ),
                    suffixIcon: (controller.searchController.text.isNotEmpty)
                        ? GestureDetector(
                            onTap: () {
                              controller.searchController.clear();
                            },
                            child: Padding(
                              padding:
                                  EdgeInsets.only(right: MySize.size15 ?? 20),
                              child: SvgPicture.asset(
                                AppImage.textFieldClear,
                                // height: MySize.size20,
                                // width: MySize.size20,
                                color: AppTheme.grey,
                              ),
                            ),
                          )
                        : null,
                    hintText: "Search",
                    hintStyle: TextStyle(
                      color: AppTheme.grey,
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w400,
                    ),
                  );
                },
              ),
            ),
            Space.height(8),
            Expanded(
              child: Obx(
                () => controller.isLoadingProjects.value
                    ? Center(
                        child: Loader(),
                      )
                    : controller.projects.isEmpty
                        ? Center(
                            child: TypoGraphy(
                              text: 'No Projects',
                            ),
                          )
                        : GridView.builder(
                            padding: EdgeInsets.all(MySize.size30 ?? 30),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              crossAxisSpacing: MySize.size20 ?? 20,
                              mainAxisSpacing: MySize.size10 ?? 30,
                              childAspectRatio: 175 / 214,
                            ),
                            itemCount: controller.projects.length,
                            itemBuilder: (context, index) {
                              return _projectItem(
                                project: controller.projects[index],
                                onTap: () {
                                  controller.getOtherProjectFolders(
                                      controller.projects[index].id);
                                  showFoldersBottomSheet(context);
                                },
                              );
                            },
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _projectItem({
    required Project project,
    required Function onTap,
  }) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                height: MySize.size142,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                ),
                child: project.image.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(25),
                        child: NetworkImageComponent(
                          imageUrl: project.image,
                          simmerHeight: MySize.size142,
                          width: double.infinity,
                        ),
                      )
                    : Image.asset(
                        AppImage.defaultImage,
                        width: double.infinity,
                        height: MySize.size142,
                      ),
              ),
            ],
          ),
          Space.height(6),
          // TypoGraphy(
          //   text: '${project.subProjectsCount} Sub-projects',
          //   level: 2,
          //   fontWeight: FontWeight.w400,
          //   color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey : null,
          //
          // ),
          Space.height(4),
          TypoGraphy(
            text: project.name,
            textStyle: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              fontFamily: 'Inter',
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  showFoldersBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: FractionallySizedBox(
                heightFactor: 0.85,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.subBottom,
                    border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(40),
                        topRight: Radius.circular(40)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        Space.height(8),
                        Container(
                          height: MySize.size5,
                          width: MySize.size34,
                          decoration: BoxDecoration(
                            color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(100),
                          ),
                        ),
                        Space.height(20),
                        TypoGraphy(text: "Move to Folder", level: 12),
                        Space.height(30),
                        Expanded(
                          child: Obx(
                            () => controller.otherProjectFoldersLoading.value
                                ? Center(
                                    child: Loader(),
                                  )
                                : controller.otherProjectFolders.isEmpty
                                    ? Center(
                                        child: TypoGraphy(
                                            text: 'No Folders in Project'),
                                      )
                                    : GridView.builder(
                                        gridDelegate:
                                            SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 2,
                                          crossAxisSpacing: MySize.size20 ?? 20,
                                          mainAxisSpacing: MySize.size10 ?? 30,
                                          childAspectRatio: 0.9,
                                        ),
                                        itemCount:
                                            controller.otherProjectFolders.length,
                                        itemBuilder: (context, index) {
                                          return _folderView(
                                            context,
                                            controller.otherProjectFolders[index],
                                          );
                                        },
                                      ),
                          ),
                        ),
                        Space.height(20),
                        Obx(
                          () => controller.selectedFolder.value != null
                              ? Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Buttons(
                                          buttonText: "Move here",
                                          buttonTextLevel: 4,
                                          width: MySize.size198,
                                          onTap: () async {
                                            if (controller.selectedFolder.value !=
                                                null) {
                                              await controller
                                                  .moveItemToSelectedFolder(
                                                itemId: widget.projectFile.id,
                                              );
                                            } else {
                                              CommonFunction.showCustomSnackbar(
                                                message: "Please select a folder",
                                                backgroundColor: AppTheme.red,
                                                isError: true,
                                              );
                                            }
                                          },
                                        ),
                                      ],
                                    ),
                                    Space.height(20),
                                  ],
                                )
                              : SizedBox(),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _folderView(BuildContext context, FolderWrapper folder) {
    return GestureDetector(
      onTap: () {
        if (controller.selectedFolder.value?.id == folder.id) {
          controller.selectedFolder.value = null;
        } else {
          controller.selectedFolder.value = folder;
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                height: MySize.getScaledSizeHeight(142),
                width: MySize.safeWidth,
                decoration: BoxDecoration(
                  color: box.read('isDarkMode')
                      ? AppTheme.darkBackground
                      : Color(0xfff6f6f6),
                  border: Border.all(
                      color: AppTheme.borderWithTrans),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Image.asset(
                    AppImage.folder,
                    height: MySize.size70,
                    width: MySize.size70,
                    color: AppTheme.whiteWithNull,
                  ),
                ),
              ),
              Positioned(
                top: MySize.size12,
                right: MySize.size12,
                child: Obx(
                  () => Container(
                    height: MySize.size25,
                    width: MySize.size25,
                    decoration: BoxDecoration(
                      border: controller.selectedFolder.value?.id == folder.id
                          ? null
                          : Border.all(width: 1.43, color: AppTheme.baseBlack),
                      shape: BoxShape.circle,
                      color: controller.selectedFolder.value?.id == folder.id
                          ? AppTheme.success
                          : Colors.transparent,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(4),
                      child: SvgPicture.asset(
                        AppImage.checkIcon,
                        color: AppTheme.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          Space.height(6),
          TypoGraphy(
            text: folder.name,
            level: 12,
          ),
          Space.height(4),
          TypoGraphy(
            text: '${(folder as ProjectFolder).filesCount} Files',
            color:  box.read('isDarkMode')
                ? AppTheme.grey : AppTheme.baseBlack,
            level: 2,
          ),
        ],
      ),
    );
  }
}
