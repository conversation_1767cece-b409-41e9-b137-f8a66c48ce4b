
import 'dart:ui';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/project_detail_view/controllers/project_folder_detail_view_controller.dart';
import 'package:incenti_ai/app/modules/project_detail_view/views/move_file_to_project_view.dart';
import 'package:incenti_ai/app/modules/user_detail/components/image_picker_bottom_sheet.dart';
import 'package:incenti_ai/constants/app_image.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/models/app_project_model.dart';
import 'package:incenti_ai/utillites/app_text_field.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:incenti_ai/utillites/buttons.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/common_validation.dart';
import 'package:incenti_ai/utillites/empty.dart';
import 'package:incenti_ai/utillites/file_downloader.dart';
import 'package:incenti_ai/utillites/loader.dart';
import 'package:incenti_ai/utillites/typography.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../main.dart';
import '../../../../utillites/current_user.dart';

class ProjectFolderDetailView extends StatefulWidget {
  const ProjectFolderDetailView({
    super.key,
    required this.folder,
    required this.isIconVisible,
    required this.isDeleteVisible,
  });

  final FolderWrapper folder;
  final bool isIconVisible;
  final bool isDeleteVisible;

  @override
  State<ProjectFolderDetailView> createState() =>
      _ProjectFolderDetailViewState();
}

class _ProjectFolderDetailViewState extends State<ProjectFolderDetailView> {
  late ProjectFolderDetailViewController controller;

  @override
  initState() {
    controller = Get.put<ProjectFolderDetailViewController>(
      ProjectFolderDetailViewController(
        folder: widget.folder as ProjectFolder,
      ),
      tag: widget.folder.id.toString(),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: widget.isIconVisible
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Obx(
                  () => FloatingActionButton(
                    heroTag: 'uploadFile',
                    backgroundColor: AppTheme.primary1,
                    onPressed: controller.isFileUploading.value
                        ? null
                        : () async {
                            final result = await FilePicker.platform.pickFiles(
                              type: FileType.any,
                              allowMultiple: true,
                            );

                            if (result != null) {
                              await controller
                                  .uploadAndCreatePickedFiles(result);
                            }
                          },
                    child: Obx(
                      () => controller.isFileUploading.value
                          ? Loader(
                              color: AppTheme.white,
                            )
                          : SvgPicture.asset(AppImage.uploadImage),
                    ),
                  ),
                ),
                Space.height(15),
                FloatingActionButton(
                  heroTag: 'createFolder',
                  backgroundColor: AppTheme.primary1,
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(40)),
                      ),
                      builder: (context) =>
                          _createOrEditFolderBottomSheet(context),
                    ).then(
                      (value) {
                        controller.folderNameController.clear();
                      },
                    );
                  },
                  child: SvgPicture.asset(AppImage.projectIcon),
                ),
                Space.height(15),
              ],
            )
          : SizedBox(),
      body: SafeArea(
        child: Column(
          children: [
            Space.height(16),
            Padding(
              padding: EdgeInsets.symmetric(
                vertical: 10,
                horizontal: MySize.size30 ?? 30,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(7.0),
                      child: SvgPicture.asset(
                        AppImage.backArrow,
                        height: MySize.size28,
                        width: MySize.size28,
                        color: AppTheme.whiteWithNull,
                      ),
                    ),
                  ),
                  TypoGraphy(
                    text: widget.folder.name,
                    level: 12,
                  ),
                  Space.width(MySize.size42 ?? 42),
                ],
              ),
            ),
            Expanded(
              child: Obx(
                () => controller.isLoading.value
                    ? Center(child: Loader())
                    : controller.itemsList.isNotEmpty
                        ? ListView.builder(
                            itemCount: controller.itemsList.length,
                            itemBuilder: (context, index) {
                              final file = controller.itemsList[index];
                              return switch (file) {
                                ProjectFile file => ListTile(
                                    onTap: () async {
                                      await launchUrl(
                                        Uri.parse(file.link),
                                        mode: LaunchMode.externalApplication,
                                      );
                                    },
                                    leading: SvgPicture.asset(
                                      _getSvgPathForFileType(file.name),
                                      height: MySize.size66,
                                      width: MySize.size66,
                                    ),
                                    title: TypoGraphy(
                                      text: file.name,
                                      level: 4,
                                    ),
                                    subtitle: TypoGraphy(
                                      text:
                                          'Added ${DateFormat('MMM dd, yyyy').format(file.createdAt)}',
                                      level: 2,
                                    ),
                                    trailing: (widget.isIconVisible || CurrentUser.user.id ==
                                        controller
                                            .itemsList[index]
                                            .userId) ? GestureDetector(
                                      onTap: () {
                                        ImagePickerBottomSheet.show(
                                          context: context,
                                          child: showItemOption(file,
                                              isDeleteVisible: widget.isDeleteVisible ||
                                                  CurrentUser.user.id ==
                                                      controller
                                                          .itemsList[index]
                                                          .userId),
                                        );
                                      },
                                      child: SvgPicture.asset(
                                        AppImage.moreVertIcon,
                                        color: AppTheme.grey,
                                      ),
                                    ) : SizedBox(),
                                  ),
                                ProjectFolder folder => InkWell(
                                    onTap: () {
                                      Get.to(
                                        () => ProjectFolderDetailView(
                                          folder: folder,
                                          isIconVisible: widget.isIconVisible,
                                          isDeleteVisible:
                                              widget.isDeleteVisible,
                                        ),
                                        preventDuplicates: false,
                                      );
                                    },
                                    child: ListTile(
                                      leading: Image.asset(
                                        AppImage.folder,
                                        height: MySize.size50,
                                        width: MySize.size50,
                                        color: AppTheme.whiteWithNull,
                                      ),
                                      title: TypoGraphy(
                                        text: folder.name,
                                        level: 4,
                                      ),
                                      trailing: (widget.isIconVisible || CurrentUser.user.id ==
                                          controller
                                              .itemsList[index]
                                              .userId) ? InkWell(
                                        onTap: () {
                                          ImagePickerBottomSheet.show(
                                            context: context,
                                            child: showFolderOption(folder,
                                                isDeleteVisible: widget.isDeleteVisible||
                                                    CurrentUser.user.id ==
                                                        controller
                                                            .itemsList[index]
                                                            .userId),
                                          );
                                        },
                                        child: SvgPicture.asset(
                                          AppImage.moreVertIcon,
                                          color: AppTheme.grey,
                                        ),
                                      ) : SizedBox(),
                                    ),
                                  ),
                                FileUploadInProgress file => ListTile(
                                    leading: SvgPicture.asset(
                                      _getSvgPathForFileType(file.name),
                                      height: MySize.size66,
                                      width: MySize.size66,
                                    ),
                                    title: TypoGraphy(
                                      text: file.name,
                                      level: 4,
                                    ),
                                    subtitle: Obx(
                                      () {
                                        return LinearProgressIndicator(
                                          value: file.progress.value / 100,
                                          color: AppTheme.primary1,
                                          backgroundColor: AppTheme.lightGrey,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        );
                                      },
                                    ),
                                  ),
                                _ => SizedBox(
                                    child: TypoGraphy(
                                      text: file.toString(),
                                    ),
                                  ),
                              };
                            },
                          )
                        : const Empty(
                            title: "No Files available!",
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  showItemOption(ProjectFile file, {bool isDeleteVisible = false}) {
    return Column(
      children: [
        Space.height(40),
        InkWell(
          onTap: () {
            Get.back();
            Get.to(
              () => MoveFileToProjectView(
                projectFile: file,
                controller: controller,
              ),
            );
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(
                AppImage.movePost,
                height: MySize.size24,
                width: MySize.size24,
                color: AppTheme.whiteWithBase,
              ),
              Space.width(20),
              TypoGraphy(
                text: "Move file to",
                level: 5,
              )
            ],
          ),
        ),
        Space.height(41),
        FileDownloadButton(
          file: file,
        ),
        Space.height(41),
        if (isDeleteVisible)
          InkWell(
            onTap: () async {
              Get.back();
              await controller.deleteItem(itemId: file.id);
            },
            child: Row(
              children: [
                Space.width(30),
                SvgPicture.asset(
                  AppImage.trashIcon,
                  height: MySize.size24,
                  width: MySize.size24,
                ),
                Space.width(20),
                TypoGraphy(
                  text: "Delete",
                  level: 5,
                  color: AppTheme.red,
                )
              ],
            ),
          ),
        Space.height(41),
      ],
    );
  }

  showFolderOption(ProjectFolder folder, {bool isDeleteVisible = false}) {
    return Column(
      children: [
        Space.height(40),
        InkWell(
          onTap: () {
            Get.back();
            controller.folderNameController.text = folder.name;
            showModalBottomSheet(
              context: Get.context!,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
              ),
              builder: (context) => _createOrEditFolderBottomSheet(context,
                  isEdit: true, folder: folder),
            ).then(
              (value) {
                controller.folderNameController.clear();
              },
            );
          },
          child: Row(
            children: [
              Space.width(30),
              SvgPicture.asset(
                AppImage.editIcon,
                height: MySize.size24,
                width: MySize.size24,
                color: AppTheme.whiteWithBase,
              ),
              Space.width(20),
              TypoGraphy(
                text: "Rename",
                level: 5,
                color: AppTheme.whiteWithNull,
              )
            ],
          ),
        ),
        Space.height(41),
        if (isDeleteVisible)
          InkWell(
            onTap: () {
              controller.deleteFolder(folderId: folder.id);
            },
            child: Row(
              children: [
                Space.width(30),
                SvgPicture.asset(
                  AppImage.trashIcon,
                  height: MySize.size24,
                  width: MySize.size24,
                ),
                Space.width(20),
                TypoGraphy(
                  text: "Delete",
                  level: 5,
                  color: AppTheme.red,
                )
              ],
            ),
          ),
        Space.height(41),
      ],
    );
  }

  String _getSvgPathForFileType(String fileName) {
    final extension = fileName.split('.').last;

    return switch (extension) {
      'doc' || 'docx' => AppImage.fileTypeDocument,
      'pdf' => AppImage.fileTypePdf,
      'jpg' ||
      'jpeg' ||
      'png' ||
      'gif' ||
      'bmp' ||
      'tif' ||
      'tiff' ||
      'webp' ||
      'heic' ||
      'heif' ||
      'ico' =>
        AppImage.fileTypeImage,
      'mp4' ||
      'mov' ||
      'avi' ||
      'wmv' ||
      'flv' ||
      'mkv' =>
        AppImage.fileTypeVideo,
      'pptx' ||
      'ppt' ||
      'ppsx' ||
      'pps' ||
      'odp' =>
        AppImage.fileTypePresentation,
      'xlsx' || 'xls' || 'ods' => AppImage.fileTypeSpreadsheet,
      'txt' || 'rtf' || 'csv' || 'tsv' => AppImage.fileTypeText,
      'zip' ||
      'rar' ||
      '7z' ||
      'tar.gz' ||
      'tar.bz2' ||
      'gz' ||
      'bz2' ||
      'tgz' ||
      'tbz2' ||
      'zipx' ||
      'z' ||
      'tar.xz' ||
      'xz' =>
        AppImage.fileTypeZip,
      _ => AppImage.fileTypeUnknown,
    };
  }

  Widget _createOrEditFolderBottomSheet(BuildContext context,
      {bool isEdit = false, ProjectFolder? folder}) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.appBottomSheet,
          border: BorderDirectional(top: BorderSide(color: AppTheme.borderColor),start: BorderSide(color: AppTheme.borderColor),end: BorderSide(color: AppTheme.borderColor)),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(40),
              topRight: Radius.circular(40)),
        ),
        child: Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Space.height(8),
              Container(
                height: MySize.size5,
                width: MySize.size34,
                decoration: BoxDecoration(
                  color: box.read('isDarkMode') ? AppTheme.grey : AppTheme.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(100),
                ),
              ),
              Space.height(30),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      Get.back();
                    },
                    child: SvgPicture.asset(
                      AppImage.closeImage,
                      height: MySize.size16,
                      width: MySize.size16,
                      color: AppTheme.whiteWithNull,
                    ),
                  ),
                ),
              ),
              Space.height(10),
              Align(
                alignment: Alignment.center,
                child: TypoGraphy(
                  text: 'New Folder',
                  level: 8,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Space.height(10),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                child: StatefulBuilder(builder: (context, setTextState) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Space.height(20),
                      AppTextField(
                        controller: controller.folderNameController,
                        labelText: "Folder Name",
                        maxLength: 30,
                        textCapitalization: TextCapitalization.words,
                        onChangedValue: (p0) {
                          setTextState(() {});
                        },
                        validators: [
                          (value) => CommonValidation.isEmpty(value: value ?? ''),
                        ],
                      ),
                      Space.height(30),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Buttons(
                              isDisable: controller.folderNameController.text
                                  .trim()
                                  .isEmpty,
                              disableColor:
                                  AppTheme.primary1.withValues(alpha: 0.5),
                              textDisableColor: AppTheme.white,
                              width: MySize.size198,
                              buttonText: isEdit ? "Update" : "Create",
                              buttonTextLevel: 4,
                              onTap: () {
                                if (controller.folderNameController.value.text
                                    .trim()
                                    .isNotEmpty) {
                                  if (isEdit) {
                                    controller.editFolderName(
                                      context: context,
                                      folderId: folder!.id,
                                      folderName: controller
                                          .folderNameController.value.text
                                          .trim(),
                                    );
                                  } else {
                                    controller.createFolder(
                                      context: context,
                                      folderName: controller
                                          .folderNameController.value.text
                                          .trim(),
                                    );
                                  }
                                } else {
                                  CommonFunction.showCustomSnackbar(
                                    message: "Please enter folder name",
                                    backgroundColor: AppTheme.red,
                                    isError: true,
                                  );
                                }
                              }),
                        ],
                      ),
                      Space.height(30),
                    ],
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FileDownloadButton extends StatefulWidget {
  const FileDownloadButton({
    super.key,
    required this.file,
  });

  final ProjectFile file;

  @override
  State<FileDownloadButton> createState() => _FileDownloadButtonState();
}

class _FileDownloadButtonState extends State<FileDownloadButton> {
  RxBool isDownloading = false.obs;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        isDownloading.value = true;
        await FileDownloader.downloadFile(widget.file.link, widget.file.name);
        isDownloading.value = false;
      },
      child: Row(
        children: [
          Space.width(30),
          Obx(
            () => isDownloading.value
                ? SizedBox.square(
                    dimension: MySize.size20,
                    child: CircularProgressIndicator.adaptive(
                      valueColor: AlwaysStoppedAnimation(AppTheme.whiteWithBase),
                      strokeWidth: MySize.size2 ?? 2,
                      strokeCap: StrokeCap.round,
                    ))
                : SvgPicture.asset(
                    AppImage.download,
                    height: MySize.size24,
                    width: MySize.size24,
              color: AppTheme.whiteWithBase,

            ),
          ),
          Space.width(20),
          Obx(
            () => TypoGraphy(
              text: isDownloading.value ? 'Downloading...' : "Download",
              level: 5,
            ),
          )
        ],
      ),
    );
  }
}
