import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;

import '../../../../main.dart';

class ChatBotViewController extends GetxController {
  TextEditingController messageSendController = TextEditingController();
  FocusNode messageFocusNode = FocusNode();
  var messages = <ChatMessage>[].obs;
  final ScrollController scrollController = ScrollController();
  RxBool isLoading = false.obs;
  RxBool isStreaming = false.obs;
  RxString streamedResponse = "".obs;
  RxInt lastResponseTimestamp = 0.obs;
  IO.Socket? socket;
  // final String socketUrl = 'https://stag-backend.incenti.ai';
  final String socketUrl = 'https://backend.floment.ai';
  RxInt userMessageCount = 0.obs;

  @override
  void onInit() {
    super.onInit();
    loadMessages();
    _initSocketConnection();
    userMessageCount.value = box.read("userMessageCount");
    if (messages.isEmpty) {
      Future.delayed(Duration(milliseconds: 500), () {
        messages.add(ChatMessage(
          text:
              "Hi, I am Genie. How can I help you build your Project journey?",
          isUser: false,
          timestamp: DateTime.now(),
        ));
        saveMessages();
      });
    }

    Future.delayed(Duration(milliseconds: 300), _scrollToBottom);

    ever(lastResponseTimestamp, (_) {
      if (isStreaming.value && lastResponseTimestamp.value > 0) {
        Future.delayed(Duration(milliseconds: 1500), () {
          int currentTime = DateTime.now().millisecondsSinceEpoch;
          if (currentTime - lastResponseTimestamp.value > 1000 &&
              isStreaming.value) {
            _finalizeStreamedMessage();
          }
        });
      }
    });
  }

  void _finalizeStreamedMessage() {
    if (streamedResponse.value.isNotEmpty) {
      String finalResponse = streamedResponse.value;
      messages.add(ChatMessage(
        text: finalResponse,
        isUser: false,
        timestamp: DateTime.now(),
      ));
      saveMessages();

      streamedResponse.value = "";
      isStreaming.value = false;
      isLoading.value = false;
      _scrollToBottom();
      lastResponseTimestamp.value = 0;
    }
  }

  @override
  void onClose() {
    _disconnectSocket();
    super.onClose();
  }

  void _initSocketConnection() {
    try {
      _disconnectSocket();

      socket = IO.io(socketUrl, <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': true,
      });

      socket?.onConnect((_) {
      });

      socket?.onDisconnect((_) {
        isStreaming.value = false;
        if (streamedResponse.value.isNotEmpty) {
          _finalizeStreamedMessage();
        }
      });

      socket?.onError((error) {
        isStreaming.value = false;
        isLoading.value = false;
        if (streamedResponse.value.isNotEmpty) {
          _finalizeStreamedMessage();
        }
      });

      socket?.on('connect_error', (error) {
        isStreaming.value = false;
        isLoading.value = false;
      });

      socket?.on('chatChunk', (data) {
        _handleSocketMessage(data);
      });

      socket?.connect();
    } catch (e, stackTrace) {
      isLoading.value = false;
    }
  }

  void _disconnectSocket() {
    socket?.disconnect();
    socket?.dispose();
    socket = null;
  }

  void _handleSocketMessage(dynamic data) {
    isLoading.value = false;
    lastResponseTimestamp.value = DateTime.now().millisecondsSinceEpoch;

    try {
      if (data is Map) {
        if (data.containsKey('content')) {
          streamedResponse.value += data['content'];
          isStreaming.value = true;
          _scrollToBottom();
        }
      } else if (data is String) {
        try {
          Map<String, dynamic> jsonData = json.decode(data);
          if (jsonData.containsKey('content')) {
            streamedResponse.value += jsonData['content'];
            isStreaming.value = true;
            _scrollToBottom();
          }
        } catch (e) {
          streamedResponse.value += data;
          isStreaming.value = true;
          _scrollToBottom();
        }
      }
    } catch (e) {
    }
  }

  void sendMessage(BuildContext context) {
    isLoading.value = true;
    String userMessage = messageSendController.text.trim();
    if (userMessage.isEmpty) return;

    messages.add(ChatMessage(
        text: userMessage,
        isUser: true,
        timestamp: DateTime.now()
    ));

    messageSendController.clear();
    _scrollToBottom();
    saveMessages();
    userMessageCount++;
    box.write("userMessageCount", userMessageCount.value);
    // Check if limit is reached
    if (userMessageCount.value % 5 == 0) {
      // Show upgrade message instead of calling the bot
      messages.add(ChatMessage(
        text: "You've reached your free search limit! 🚀\n\n"
            "Upgrade to continue searching! Get unlimited searches for just \$99.00/month.",
        isUser: false,
        timestamp: DateTime.now(),
        type: ChatMessageType.subscriptionPrompt,
      ));
      userMessageCount.value = 0;
      saveMessages();
      isLoading.value = false;
      isStreaming.value = false;
      return;
    }

    streamedResponse.value = "";
    isStreaming.value = true;

    List<Map<String, String>> conversationPayload = messages.map((msg) {
      return {
        "role": msg.isUser ? "user" : "assistant",
        "content": msg.text,
      };
    }).toList();

    _sendMessageViaSocket(conversationPayload);
  }

  void _sendMessageViaSocket(List<Map<String, String>> conversation) {
    try {
      if (socket == null || socket!.disconnected) {
        _initSocketConnection();
        Future.delayed(Duration(milliseconds: 300), () {
          _emitSocketMessage(conversation);
        });
      } else {
        _emitSocketMessage(conversation);
      }
    } catch (e) {
      isStreaming.value = false;

      messages.add(ChatMessage(
        text: "Failed to connect. Please check your internet connection and try again.",
        isUser: false,
        timestamp: DateTime.now(),
      ));
      saveMessages();
    }
  }

  Future<void> _emitSocketMessage(List<Map<String, String>> conversation) async {
    Map<String, dynamic> payload = {
      "conversations": conversation
    };

    socket?.emit('sendMessage', payload);

    // await Future.delayed(Duration(seconds: 10), () {
    //   if (isStreaming.value && streamedResponse.value.isEmpty) {
    //     isStreaming.value = false;
    //     isLoading.value = false;
    //     messages.add(ChatMessage(
    //       text: "No response received. Please try again.",
    //       isUser: false,
    //       timestamp: DateTime.now(),
    //     ));
    //     saveMessages();
    //   }
    // });
  }

  void _scrollToBottom() {
    Future.delayed(Duration(milliseconds: 100), () {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void saveMessages() {
    List<Map<String, dynamic>> chatList =
    messages.map((msg) => msg.toJson()).toList();
    box.write("chat_messages", chatList);
  }

  void loadMessages() {
    List<dynamic>? storedMessages = box.read<List<dynamic>>("chat_messages");
    if (storedMessages != null) {
      messages.value =
          storedMessages.map((msg) => ChatMessage.fromJson(msg)).toList();
    }
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final ChatMessageType type;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.type = ChatMessageType.normal,
  });

  Map<String, dynamic> toJson() {
    return {
      "text": text,
      "isUser": isUser,
      "DateTime": timestamp.toIso8601String(),
      "type": type.name,
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      text: json["text"],
      isUser: json["isUser"],
      timestamp: json["DateTime"] is String
          ? DateTime.parse(json["DateTime"])
          : (json["DateTime"] ?? DateTime.now()),
      type: ChatMessageType.values.firstWhere(
        (e) => e.name == (json["type"] ?? "normal"),
        orElse: () => ChatMessageType.normal,
      ),
    );
  }
}

enum ChatMessageType {
  normal,
  subscriptionPrompt,
}
