import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/app_theme.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../constants/app_image.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/jumping_dot_animation.dart';
import '../../../../utillites/search_field.dart';
import '../../../../utillites/typography.dart';
import '../controllers/chat_bot_view_controller.dart';

class ChatBotView extends StatefulWidget {
  const ChatBotView({super.key});

  @override
  State<ChatBotView> createState() => _ChatBotViewState();
}

class _ChatBotViewState extends State<ChatBotView> {
  final ChatBotViewController controller = Get.put(ChatBotViewController());
  final RxBool isHeaderDark = false.obs;

  @override
  void initState() {
    super.initState();
    controller.scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    if (controller.scrollController.offset > 100) {
      if (!isHeaderDark.value) isHeaderDark.value = true;
    } else {
      if (isHeaderDark.value) isHeaderDark.value = false;
    }
  }

  @override
  void dispose() {
    controller.scrollController.removeListener(_handleScroll);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      // backgroundColor: AppTheme.white,
      body: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: SingleChildScrollView(
                  keyboardDismissBehavior:
                      ScrollViewKeyboardDismissBehavior.onDrag,
                  controller: controller.scrollController,
                  child: Column(
                    children: [
                      Space.height(130),
                      TypoGraphy(
                        text: DateFormat("MMM dd, yyyy").format(DateTime.now()),
                        level: 2,
                        fontWeight: FontWeight.w400,
                        color: AppTheme.grey,
                      ),
                      Space.height(20),
                      buildChatMessages(),
                      Obx(() => controller.isStreaming.value
                          ? Padding(
                              padding: EdgeInsets.only(
                                  left: MySize.size30 ?? 30,
                                  right: MySize.size30 ?? 30,
                                  bottom: MySize.size20 ?? 20),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Image.asset(
                                    AppImage.chatBotImage,
                                    height: MySize.getScaledSizeHeight(28),
                                    width: MySize.getScaledSizeWidth(28),
                                  ),
                                  Space.width(4),
                                  Obx(() => controller
                                          .streamedResponse.value.isEmpty
                                      ? Container(
                                          width: MySize.getScaledSizeWidth(78),
                                          height: MySize.getScaledSizeWidth(50),
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            color: Color(0xFFF7F7F2),
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(25),
                                              topRight: Radius.circular(25),
                                              bottomRight: Radius.circular(25),
                                            ),
                                          ),
                                          child: JumpingDots(
                                            color: AppTheme.baseBlack,
                                            radius: 7,
                                            numberOfDots: 3,
                                            animationDuration:
                                                Duration(milliseconds: 200),
                                            verticalOffset: -5,
                                          ),
                                        )
                                      : Container(
                                    width:  MySize.screenWidth - MySize.getScaledSizeWidth(100),
                                          decoration: BoxDecoration(
                                            color: Color(0xFFF7F7F2),
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(25),
                                              topRight: Radius.circular(25),
                                              bottomRight: Radius.circular(25),
                                            ),
                                          ),
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal:
                                                    MySize.getScaledSizeWidth(
                                                        18),
                                                vertical:
                                                    MySize.getScaledSizeHeight(
                                                        18)),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                TypoGraphy(
                                                  text: controller
                                                      .streamedResponse.value,
                                                  level: 4,
                                                  color: AppTheme.baseBlack,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                                Space.height(8),
                                                Align(
                                                  alignment:
                                                      Alignment.centerRight,
                                                  child: TypoGraphy(
                                                    text: DateFormat.jm()
                                                        .format(DateTime.now()),
                                                    level: 2,
                                                    fontWeight: FontWeight.w400,
                                                    color: AppTheme.grey,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        )),
                                ],
                              ),
                            )
                          : SizedBox()),
                    ],
                  ),
                ),
              ),
              buildMessageInput(),
            ],
          ),
          Obx(() => SizedBox(
            height: MySize.getScaledSizeHeight(106),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Space.height(51.83),
                    Padding(
                      padding: EdgeInsets.only(
                          right: MySize.getScaledSizeWidth(41.83)),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: SvgPicture.asset(
                            AppImage.closeImage,
                            height: MySize.getScaledSizeHeight(20),
                            width: MySize.getScaledSizeHeight(20),
                            color: isHeaderDark.value ? AppTheme.baseBlack : AppTheme.whiteWithBase,
                          ),
                        ),
                      ),
                    ),
                    buildHeader(isHeaderDark.value),
                  ],
                ),
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget buildHeader([bool isDark = false]) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          AppImage.chatBotImage,
          height: MySize.getScaledSizeHeight(24),
          width: MySize.getScaledSizeWidth(24),
        ),
        Space.width(8),
        TypoGraphy(
          text: "Genie",
          level: 12,
          color: isDark ? AppTheme.baseBlack : null,
        )
      ],
    );
  }

  Widget buildChatMessages() {
    return Obx(() => ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          reverse: false,
          physics: NeverScrollableScrollPhysics(),
          itemCount: controller.messages.length,
          itemBuilder: (context, index) {
            final message = controller.messages[index];
            return ChatBubble(
              message: message,
            );
          },
        ));
  }

  Widget buildMessageInput() {
    return Padding(
      padding: EdgeInsets.only(
          left: MySize.size30 ?? 30,
          right: MySize.size30 ?? 30,
          bottom: controller.messageFocusNode.hasFocus
              ? MySize.size10 ?? 30
              : MySize.size30 ?? 30),
      child: Row(
        children: [
          Expanded(
              child: ValueListenableBuilder<TextEditingValue>(
            valueListenable: controller.messageSendController,
            builder: (context, value, child) {
              return SearchAppTextField(
                textInputType: TextInputType.multiline,
                textCapitalization: TextCapitalization.sentences,
                controller: controller.messageSendController,
                focusNode: controller.messageFocusNode,
                hintText: "Type message",
                hintStyle: TextStyle(
                  fontSize: MySize.size16,
                  fontFamily: "Inter",
                  fontWeight: FontWeight.w400,
                  color: AppTheme.grey,
                ),
                maxLines: null,
              );
            },
          )),
          ValueListenableBuilder(
            valueListenable: controller.messageSendController,
            builder: (context, value, child) {
              return (value.text.trim().isNotEmpty)
                  ? Space.width(10)
                  : SizedBox();
            },
          ),
          ValueListenableBuilder(
            valueListenable: controller.messageSendController,
            builder: (context, value, child) {
              return (value.text.trim().isNotEmpty)
                  ? Obx(
                      () => !controller.isStreaming.value
                          ? InkWell(
                              onTap: () => controller.sendMessage(context),
                              child: SvgPicture.asset(
                                AppImage.messageSend,
                                height: MySize.getScaledSizeHeight(65),
                                width: MySize.getScaledSizeWidth(65),
                              ),
                            )
                          : SizedBox(),
                    )
                  : SizedBox();
            },
          ),
        ],
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    bool isUser = message.isUser;
    if (message.type == ChatMessageType.subscriptionPrompt) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: MySize.size30 ?? 30).copyWith(bottom: MySize.size20 ?? 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment:
              MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Image.asset(
                      AppImage.chatBotImage,
                      height: MySize.getScaledSizeHeight(28),
                      width: MySize.getScaledSizeWidth(28),
                    ),
                    Space.width(4),
                    Container(
                      width: MySize.screenWidth - MySize.getScaledSizeWidth(100),
                      decoration: BoxDecoration(
                        color: Color(0xFFF7F7F2),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(25),
                          topRight: Radius.circular(25),
                          bottomRight: Radius.circular(25),
                        ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(18),
                            vertical: MySize.getScaledSizeHeight(18)),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TypoGraphy(
                              text: "Help us fund our Startup Journey! 🚀\n\nSubscribe and be the first to unlock the premium features over our next update for just",
                              level: 4,
                              color: AppTheme.baseBlack,
                              fontWeight: FontWeight.w400,
                            ),
                            TypoGraphy(
                              text: "\$19.99/month.",
                              level: 4,
                              color: AppTheme.baseBlack,
                              fontWeight: FontWeight.w600,
                            ),
                            Space.height(8),
                            InkWell(
                              onTap: () async {
                                Uri url = Uri.parse("https://buy.stripe.com/3cscNCeCc2Ot04o288");
                                if (!await launchUrl(url)) {
                                  throw Exception('Could not launch $url');
                                }
                              },
                              child: Container(
                                width: MySize.getScaledSizeWidth(126),
                                height: MySize.getScaledSizeHeight(32),
                                decoration: BoxDecoration(
                                    border: Border.all(color: AppTheme.baseBlack,width: 1),
                                    borderRadius: BorderRadius.circular(25)
                                ),
                                alignment: Alignment.center,
                                child: TypoGraphy(
                                  text: "Subscribe Now",
                                  level: 3,
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme.baseBlack,
                                ),
                              ),
                            ),
                            Space.height(8),
                            Align(
                              alignment: Alignment.centerRight,
                              child: TypoGraphy(
                                text: DateFormat.jm().format(message.timestamp),
                                level: 2,
                                fontWeight: FontWeight.w400,
                                color: AppTheme.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                Space.width(5),
                if (isUser)
                  InkWell(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: message.text));
                      CommonFunction.showCustomSnackbar(
                        message: "Copied to clipboard!.",
                      );
                    },
                    child: Icon(Icons.copy,
                        color: Colors.grey, size: MySize.getScaledSizeHeight(18)),
                  ),
              ],
            ),
            if (!isUser) Space.height(10),
            if (!isUser)
              Padding(
                padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(30)),
                child: InkWell(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: message.text));
                    CommonFunction.showCustomSnackbar(
                      message: "Copied to clipboard!.",
                    );
                  },
                  child: Icon(Icons.copy,
                      color: Colors.grey, size: MySize.getScaledSizeHeight(18)),
                ),
              ),
          ],
        ),
      );
    }
    return Padding(
      padding: EdgeInsets.only(
          left: MySize.size30 ?? 30,
          right: MySize.size30 ?? 30,
          bottom: MySize.size20 ?? 20),
      child: Column(
        crossAxisAlignment:
            isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment:
                isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!isUser) ...[
                    Image.asset(
                      AppImage.chatBotImage,
                      height: MySize.getScaledSizeHeight(28),
                      width: MySize.getScaledSizeWidth(28),
                    ),
                    Space.width(4),
                  ],
                  Container(
                    width: isUser
                        ? MySize.getScaledSizeWidth(249)
                        : MySize.screenWidth - MySize.getScaledSizeWidth(100),
                    decoration: BoxDecoration(
                      color: isUser ? AppTheme.subPrimary : Color(0xFFF7F7F2),
                      borderRadius: isUser
                          ? BorderRadius.only(
                              topLeft: Radius.circular(25),
                              topRight: Radius.circular(25),
                              bottomLeft: Radius.circular(25),
                            )
                          : BorderRadius.only(
                              topLeft: Radius.circular(25),
                              topRight: Radius.circular(25),
                              bottomRight: Radius.circular(25),
                            ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(18),
                          vertical: MySize.getScaledSizeHeight(18)),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TypoGraphy(
                            text: message.text,
                            level: 4,
                            color: AppTheme.baseBlack,
                            fontWeight: FontWeight.w600,
                          ),
                          Space.height(8),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TypoGraphy(
                              text: DateFormat.jm().format(message.timestamp),
                              level: 2,
                              fontWeight: FontWeight.w400,
                              color: AppTheme.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Space.width(5),
              if (isUser)
                InkWell(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: message.text));
                    CommonFunction.showCustomSnackbar(
                      message: "Copied to clipboard!.",
                    );
                  },
                  child: Icon(Icons.copy,
                      color: Colors.grey, size: MySize.getScaledSizeHeight(18)),
                ),
            ],
          ),
          if (!isUser) Space.height(10),
          if (!isUser)
            Padding(
              padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(30)),
              child: InkWell(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: message.text));
                  CommonFunction.showCustomSnackbar(
                    message: "Copied to clipboard!.",
                  );
                },
                child: Icon(Icons.copy,
                    color: Colors.grey, size: MySize.getScaledSizeHeight(18)),
              ),
            ),
        ],
      ),
    );
  }
}
