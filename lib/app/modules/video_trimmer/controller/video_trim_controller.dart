import 'dart:developer';
import 'dart:io';

import 'package:get/get.dart';

import '../../../../constants/app_argument_key.dart';
import '../../../routes/app_pages.dart';
import '../view/trimmer.dart';

class VideoTrimController extends GetxController {
  final Trimmer trimmer = Trimmer();
  var argument = Get.arguments;
  File? pickedFile;

  double startValue = 0.0;
  double endValue = 0.0;

  bool isPlaying = false;
  bool progressVisibility = false;

  void _loadVideo() {
    trimmer.loadVideo(videoFile: pickedFile!);
  }

  Future<void> saveVideo() async {
    await trimmer.saveTrimmedVideo(
      startValue: startValue,
      endValue: endValue,
      onSave: (outputPath) {
        if((outputPath ?? '').isNotEmpty) {
          log('output path ===> ${outputPath}');
          Get.offAndToNamed(Routes.story_editor, arguments: {AppArguments.file: File(outputPath ?? ''), AppArguments.isVideo: true});
        }
      },
    );
  }

  @override
  void onInit() {
    if (argument != null) {
      pickedFile = argument[AppArguments.file];
      _loadVideo();
    }
    super.onInit();
  }
}
