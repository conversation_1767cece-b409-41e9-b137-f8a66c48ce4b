import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/app/modules/video_trimmer/view/trim_viewer/trim_viewer.dart';
import 'package:incenti_ai/app/modules/video_trimmer/view/video_viewer.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/typography.dart';

import '../controller/video_trim_controller.dart';

class VideoTrimView extends StatefulWidget {
  const VideoTrimView({super.key});

  @override
  State<VideoTrimView> createState() => _VideoTrimViewState();
}

class _VideoTrimViewState extends State<VideoTrimView> {

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VideoTrimController>(
      init: VideoTrimController(),
      builder:(controller){ return Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: MySize.getScaledSizeHeight(140)),
                child: Center(
                  child: VideoViewer(trimmer: controller.trimmer),
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Column(
                  children: [
                    TrimViewer(
                      trimmer: controller.trimmer,
                      viewerHeight: MySize.getScaledSizeHeight(50),
                      viewerWidth: MediaQuery.of(context).size.width,
                      maxVideoLength: const Duration(seconds: 50),
                      onChangeStart: (value) => controller.startValue = value,
                      onChangeEnd: (value) => controller.endValue = value,
                      onChangePlaybackState: (isPlaying) {
                        controller.isPlaying = isPlaying;
                        controller.update();
                      },
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                            onPressed: () {
                              Get.back();
                            },
                            child: TypoGraphy(
                              text: "Back",
                              color: Colors.white,
                            )),
                        TextButton(
                          child: controller.isPlaying
                              ? Icon(
                            Icons.pause,
                            size: MySize.size40,
                            color: Colors.white,
                          )
                              : Icon(
                            Icons.play_arrow,
                            size: MySize.size40,
                            color: Colors.white,
                          ),
                          onPressed: () async {
                            bool playbackState =
                            await controller.trimmer.videoPlaybackControl(
                              startValue: controller.startValue,
                              endValue: controller.endValue,
                            );
                            controller.isPlaying = playbackState;
                            controller.update();
                          },
                        ),
                        TextButton(
                            onPressed: () async {
                              await controller.saveVideo();
                            },
                            child: TypoGraphy(
                              text: "Next",
                              color: Colors.white,
                            )),
                      ],
                    )
                  ],
                ),
              ),

            ],
          ),
        ),
      );},
    );
  }
}
