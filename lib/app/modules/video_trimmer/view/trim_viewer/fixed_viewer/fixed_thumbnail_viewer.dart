import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../../utils/trimmer_utils.dart';
import '../transparent_image.dart';

class FixedThumbnailViewer extends StatelessWidget {
  final File videoFile;
  final int videoDuration;
  final double thumbnailHeight;
  final BoxFit fit;
  final int numberOfThumbnails;
  final VoidCallback onThumbnailLoadingComplete;
  final int quality;
  const FixedThumbnailViewer({
    super.key,
    required this.videoFile,
    required this.videoDuration,
    required this.thumbnailHeight,
    required this.numberOfThumbnails,
    required this.fit,
    required this.onThumbnailLoadingComplete,
    this.quality = 75,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<Uint8List?>>(
      stream: generateThumbnail(
          videoPath: videoFile.path,
          videoDuration: videoDuration,
          numberOfThumbnails: numberOfThumbnails,
          quality: quality,
          onThumbnailLoadingComplete: onThumbnailLoadingComplete),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          List<Uint8List?> imageBytes = snapshot.data!;
          return Row(
            mainAxisSize: MainAxisSize.max,
            children: List.generate(
              numberOfThumbnails,
              (index) => SizedBox(
                height: thumbnailHeight,
                width: thumbnailHeight,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Opacity(
                      opacity: 0.2,
                      child: Image.memory(
                        imageBytes[0] ?? kTransparentImage,
                        fit: fit,
                      ),
                    ),
                    index < imageBytes.length
                        ? FadeInImage(
                            placeholder: MemoryImage(kTransparentImage),
                            image: MemoryImage(imageBytes[index]!),
                            fit: fit,
                          )
                        : const SizedBox(),
                  ],
                ),
              ),
            ),
          );
        } else {
          return Container(
            color: Colors.grey[900],
            height: thumbnailHeight,
            width: double.maxFinite,
          );
        }
      },
    );
  }
}
