import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import '../../../../constants/api.dart';
import '../../../../main.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/api_use_dialog.dart';
import '../../../../utillites/app_common_social_auth.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';
import '../../../routes/app_pages.dart';
import 'package:signin_with_linkedin/signin_with_linkedin.dart';

class SignInController extends GetxController {

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  RxBool isPasswordVisible = true.obs;
  RxBool isLoading = false.obs;
  RxBool isError = false.obs;
  RxBool isPasswordError = false.obs;
  ApiManager apiManager = ApiManager();
  RxString accessToken = "".obs;
  LinkedInUser? linkedInUser;
  RxString emailErrorMessage = "".obs;
  RxString passwordErrorMessage = "".obs;

  validateEmail() {
    if (emailController.text.isEmpty) {
      emailErrorMessage.value = "Email cannot be empty";
      isError.value = true;
      return false;
    } else if (!GetUtils.isEmail(emailController.text)) {
      emailErrorMessage.value = "Please enter a valid email";
      isError.value = true;
      return false;
    } else {
      emailErrorMessage.value = "";
      isError.value = false;
      return true;
    }
  }

  validatePassword() {
    if (passwordController.text.isEmpty) {
      passwordErrorMessage.value = "Password cannot be empty";
      isPasswordError.value = true;
      return false;
    } else {
      passwordErrorMessage.value = "";
      isPasswordError.value = false;
      return true;
    }
  }


  callApiForLogin({required BuildContext context}) {
    FocusScope.of(context).unfocus();

    bool isEmailValid = validateEmail();
    bool isPasswordValid = validatePassword();

    if (!isEmailValid || !isPasswordValid) {
      return;
    }

    isLoading.value = true;
    Map<String, dynamic> dict = {
      "email": emailController.value.text,
      "password": passwordController.value.text,
    };
    log("BODY : $dict");

    return apiManager.callApi(
      APIS.auth.login,
      params: dict,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            String? token = response['token'];
            box.write("token", token);
            box.write("finalToken", token);
            CurrentUser.getMe(callback: () async {
              Get.offAllNamed(Routes.Bottom_Bar); // Replace with your desired route
            });
            isLoading.value = false;
          } else {
            isError.value = true;
            isPasswordError.value = true;
            isLoading.value = false;
          }
        } catch (error) {
          isError.value = true;
          isPasswordError.value = true;
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isError.value = true;
        isPasswordError.value = true;
        isLoading.value = false;
        CommonFunction.showCustomSnackbar(
          message: statusCode,
          backgroundColor: Color(0xFFEF3B41),
          isError: true
        );
      },
    );
  }

  signInWithGoogle({required BuildContext context}) {
    isLoading.value = true;
    app.resolve<CustomDialogs>().showCircularDialog(context);
    CommonAPIs.signInWithGoogle(
      context: context,
      successCallback: (response, message) {
        try {
          String token = response['token'];
          box.write('token', token);
          if (response['data']['isNewUser'] == true) {
            box.write('firstName', response['data']['firstName']);
            box.write('lastName', response['data']['lastName']);
            box.write('image', response['data']['image']);
            box.write('uid', response['data']['uid']);
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
            // app.resolve<CustomDialogs>().hideCircularDialog(context);
            Get.toNamed(Routes.User_Detail);
            // app.resolve<CustomDialogs>().hideCircularDialog(context);
            // isLoading.value = false;
          } else {
            box.write("finalToken", token);
            CurrentUser.getMe(callback: () async {
              Get.offAllNamed(
                  Routes.Bottom_Bar); // Replace with your desired route
            });
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
          }
        } catch (error) {
          app.resolve<CustomDialogs>().hideCircularDialog(context);
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        isLoading.value = false;
      },
    );
    isLoading.value = false;
  }

  signInWithApple({required BuildContext context}) {
    isLoading.value = true;
    app.resolve<CustomDialogs>().showCircularDialog(context);
    CommonAPIs.signInWithApple(
      context: context,
      successCallback: (response, message) {
        try {
          String token = response['token'];
          box.write('token', token);
          log("response ==> $response");
          print("response ==> $response");
          if (response['data']['isNewUser'] == true) {
            // box.write('firstName', response['data']['firstName']);
            // box.write('lastName', response['data']['lastName']);
            // box.write('image', response['data']['image']);
            box.write('uid', response['data']['uid']);
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
            Get.toNamed(Routes.User_Detail,arguments: {"isFromApple": true});

            // app.resolve<CustomDialogs>().hideCircularDialog(context);
            // isLoading.value = false;
          } else {
            box.write("finalToken", token);
            CurrentUser.getMe(callback: () async {
              Get.offAllNamed(
                  Routes.Bottom_Bar); // Replace with your desired route
            });
            isLoading.value = false;
            app.resolve<CustomDialogs>().hideCircularDialog(context);
          }
        } catch (error) {
          isLoading.value = false;
          app.resolve<CustomDialogs>().hideCircularDialog(context);
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
        app.resolve<CustomDialogs>().hideCircularDialog(context);
      },
    );
    isLoading.value = false;
  }

  signInWithLinkedin({required BuildContext context}) {
    isLoading.value = true;
    CommonAPIs.signInWithLinkedIn(
      context: context,
      successCallback: (response, message) {
        try {
          String token = response['token'];
          box.write('token', token);
          if (response['data']['isNewUser'] == true) {
            box.write('firstName', response['data']['firstName']);
            box.write('lastName', response['data']['lastName']);
            box.write('image', response['data']['image']);
            box.write('uid', response['data']['uid']);
            box.write('email', response['data']['email']);
            app.resolve<CustomDialogs>().hideCircularDialog(context);
            isLoading.value = false;
            Get.toNamed(Routes.User_Detail,arguments: {"isFromLinkedIn": true});
            // isLoading.value = false;
            // app.resolve<CustomDialogs>().hideCircularDialog(context);
          } else {
            box.write("finalToken", token);
            CurrentUser.getMe(callback: () async {
              Get.offAllNamed(
                  Routes.Bottom_Bar); // Replace with your desired route
            });
            // app.resolve<CustomDialogs>().hideCircularDialog(context);
            // isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
    isLoading.value = false;
  }
}
