import 'package:flutter/material.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';

import '../../../../utillites/app_theme.dart';
import '../../../../utillites/typography.dart';

class DividerWithText extends StatelessWidget {
  final String text;
  final double? dividerWidth;
  final int? level;
  final Color? dividerColor;
  final double? spacing;

  const DividerWithText({
    super.key,
    required this.text,
    this.dividerWidth,
    this.dividerColor,
    this.level,
    this.spacing,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: 1,
          width: dividerWidth,
          decoration: BoxDecoration(
            color: dividerColor,
          ),
        ),
        Space.width(8),
        TypoGraphy(
          text: text,
          level: level ?? 4,
          fontWeight: FontWeight.w400,
          color: AppTheme.getSubTextColor(context),
        ),
        Space.width(8),
        Container(
          height: 1,
          width: dividerWidth,
          decoration: BoxDecoration(
            color: dividerColor,
          ),
        ),
      ],
    );
  }
}
