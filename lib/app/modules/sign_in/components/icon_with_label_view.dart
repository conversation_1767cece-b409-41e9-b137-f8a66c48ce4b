import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:incenti_ai/constants/app_size_constant.dart';
import 'package:incenti_ai/utillites/typography.dart';


class IconWithLabel extends StatelessWidget {
  final String? iconPath;
  final String? label;
  final double? iconHeight;
  final double? space;
  final Widget? iconWeight;
  final FontWeight? fontWeight;
  final int? level;
  final void Function()? onTap;

  const IconWithLabel({
    super.key,
    this.iconPath,
    this.space,
    this.label,
    this.iconHeight,
    this.level,
    this.onTap,
    this.iconWeight,
    this.fontWeight
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          onTap: onTap,
          child: iconWeight ?? SvgPicture.asset(
            iconPath ?? "",
            height: iconHeight ?? MySize.size70,
          ),
        ),
        Space.height(space ?? (MySize.size8 ?? 8)),
        TypoGraphy(
          text: label ?? "",
          level: level ?? 3,
          fontWeight: fontWeight ?? FontWeight.w400,
          // color: AppTheme.baseBlack,
        )
      ],
    );
  }
}
