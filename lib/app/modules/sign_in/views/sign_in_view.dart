import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/app_text_field.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../components/divider_with_text_view.dart';
import '../components/icon_with_label_view.dart';
import '../controllers/sign_in_controller.dart';

class SignInView extends GetWidget<SignInController> {
  const SignInView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return WillPopScope(
      onWillPop: () {
        return Future.value(true);
      },
      child: Scaffold(
        // backgroundColor: AppTheme.white,
        body: NotificationListener<ScrollNotification>(
          onNotification: (scrollNotification) {
            if (scrollNotification is ScrollStartNotification) {
              FocusScope.of(context).unfocus();
            }
            return false;
          },
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Space.height(108.07),
                Image.asset(
                  AppImage.appLogo,
                  color: AppTheme.whiteWithBase,
                  width: MySize.getScaledSizeWidth(120),
                  // height: MySize.size39,
                ),
                Space.height(46.34),
                TypoGraphy(
                  text: "Welcome",
                  level: 8,
                  // color: AppTheme.baseBlack,
                  fontWeight: FontWeight.w700,
                ),
                Space.height(26),
                Obx(
                      () => Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(45)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppTextField(
                          errorMessage: controller.isError.value,
                          controller: controller.emailController,
                          labelText: "Email",
                          onChangedValue: (p0) {
                            controller.validateEmail();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                Space.height(20),
                Obx(
                      () => Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(45)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppTextField(
                          errorMessage: controller.isPasswordError.value,
                          controller: controller.passwordController,
                          labelText: "Password",
                          suffixIconAsset: AppImage.showPassword,
                          obscureText: controller.isPasswordVisible.value,
                          onChangedValue: (p0) {
                            controller.validatePassword();
                          },
                          suffixIcon: Padding(
                            padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(20)),
                            child: InkWell(
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {
                                controller.isPasswordVisible.value =
                                !controller.isPasswordVisible.value;
                              },
                              child: controller.isPasswordVisible.value
                                  ? SvgPicture.asset(
                                AppImage.showPassword,
                                color:  AppTheme.whiteWithNull,
                              )
                                  : Icon(
                                Icons.visibility_outlined,
                                color:  AppTheme.whiteWithBase,
                                size: MySize.getScaledSizeHeight(20),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Space.height(20),
                Padding(
                  padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(60)),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: InkWell(
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () {
                        Get.toNamed(Routes.Forgot_Password);
                      },
                      child: TypoGraphy(
                        text: "Forgot password?",
                        level: 3,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey[50] : null,
                      ),
                    ),
                  ),
                ),
                Space.height(29),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(45)),
                  child: Obx(
                    () => Buttons(
                      buttonText: 'Sign In',
                      buttonTextLevel: 4,
                      width: MySize.getScaledSizeWidth(198),
                      height: MySize.getScaledSizeHeight(70),
                      isLoading: controller.isLoading.value,
                      onTap: () {
                        HapticFeedback.lightImpact();
                        controller.callApiForLogin(context: context);
                      },
                    ),
                  ),
                ),
                Space.height(35),
                DividerWithText(
                  text: "or continue with",
                  dividerWidth: MySize.getScaledSizeWidth(54),
                  dividerColor: AppTheme.grey[50],
                ),
                Space.height(25),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconWithLabel(
                      iconPath: AppImage.googleLogo,
                      label: "Google",
                      onTap: () {
                        HapticFeedback.lightImpact();
                        controller.signInWithGoogle(context: context);
                      },
                    ),
                    Space.width(20),
                    IconWithLabel(
                      iconPath: AppImage.linkdinLogo,
                      label: "LinkedIn",
                      onTap: () {
                        HapticFeedback.lightImpact();
                        controller.signInWithLinkedin(context: context);
                      },
                    ),
                    if (Platform.isIOS) ...[
                      Space.width(22),
                      IconWithLabel(
                        iconPath: AppImage.appleLogo,
                        label: "Apple",
                        onTap: () {
                          HapticFeedback.lightImpact();
                          controller.signInWithApple(context: context);
                        },
                      ),
                    ],
                ],
              ),
              Space.height(70),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TypoGraphy(
                    text: "Don't have an account?",
                    level: 4,
                    fontWeight: FontWeight.w400,
                    color: Theme.of(context).brightness == Brightness.dark ? AppTheme.mutedTextColor : null,
                  ),
                  InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Get.offAndToNamed(Routes.Sign_Up);
                    },
                    child: TypoGraphy(
                      text: " Sign Up",
                      level: 4,
                      fontWeight: Theme.of(context).brightness == Brightness.dark ? FontWeight.w500 : FontWeight.w700,
                      color: Theme.of(context).brightness == Brightness.dark ? AppTheme.grey[50] : AppTheme.primary1,
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
      ),
    );
  }
}