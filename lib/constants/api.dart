import 'constant.dart';

class APIS {
  static final Auth auth = Auth();
  static final User user = User();
  static final General generalImageUpload = General();
  static final ProjectGet project = ProjectGet();
  static final PostApi post = PostApi();
  static final AppConfig appConfig = AppConfig();
  static final BookMark bookMark = BookMark();
  static final StoryApi story = StoryApi();
  static final Followers followers = Followers();
  static final ChatBot chatBot = ChatBot();
  static final Communities communities = Communities();
  static final Folders folders = Folders();
  static final ProjectMember projectMember = ProjectMember();
  static final Todo todo = Todo();
  static final Notifications notification = Notifications();
}

class Auth {
  final ApiModel login = const ApiModel("/users/login", APIType.POST);
  final ApiModel signUp = const ApiModel("/users/signup", APIType.POST);
  final ApiModel verifySignUpOTP =
      const ApiModel("/users/verify-signup", APIType.POST);
  final ApiModel socialAuth =
      const ApiModel("/users/social-auth", APIType.POST);
  final ApiModel linkedinAuth =
      const ApiModel("/users/linkedin-auth", APIType.POST);
  final ApiModel forgotPassword =
      const ApiModel("/users/forgot-password", APIType.POST);
  final ApiModel verifyForgotPassword =
      const ApiModel("/users/auth/forgot-password", APIType.POST);
  final ApiModel resetPassword =
      const ApiModel("/users/forgotten-password", APIType.PATCH);
  final ApiModel changePassword =
      const ApiModel("/users/password", APIType.PATCH);
}

class Communities {
  final ApiModel createCommunities =
      const ApiModel("/communities", APIType.POST);
  final ApiModel getCommunities = const ApiModel("/communities", APIType.GET);
}

class ProjectMember {
  final ApiModel getAllProjectMember =
      const ApiModel("/project-members/users", APIType.GET);
  final ApiModel inviteMember =
      const ApiModel("/project-members", APIType.POST);
  final ApiModel acceptMember =
      const ApiModel("/project-members/accept/:id", APIType.PATCH);
  final ApiModel getAllTeamMember =
      const ApiModel("/users/search", APIType.GET);
  }

class ChatBot {
  final ApiModel sendMessage =
      const ApiModel("/general/chat-boat", APIType.POST);
}

class User {
  final ApiModel getMe = const ApiModel("/users/get-me", APIType.GET);
  final ApiModel updateMe = const ApiModel("/users/update-me", APIType.PATCH);
  final ApiModel deleteMe = const ApiModel("/users/delete-me", APIType.DELETE);
  final ApiModel uploadProfilePic =
      const ApiModel("/users/profile-pic", APIType.PATCH);
  final ApiModel createUser =
      const ApiModel("/users/complete-profile", APIType.PATCH);
}

class General {
  final ApiModel imageUpload = const ApiModel("/general/files", APIType.POST);
}

class Todo {
  final ApiModel createTodo = const ApiModel("/todos", APIType.POST);
  final ApiModel getTodo = const ApiModel("/todos", APIType.GET);
}

class ProjectGet {
  final ApiModel getAllMyProject = const ApiModel("/projects", APIType.GET);
  final ApiModel getAllSubProject = const ApiModel("/projects/sub-projects", APIType.GET);
  final ApiModel createMyProject = const ApiModel("/projects", APIType.POST);
}

class PostApi {
  final ApiModel createPost = const ApiModel("/posts", APIType.POST);
  final ApiModel getAllExplorePost =
      const ApiModel("/posts/explore", APIType.GET);
  final ApiModel getAllUsePost = const ApiModel("/posts", APIType.GET);
  final ApiModel approvePost =
      const ApiModel("/posts/approve/:id", APIType.PATCH);
}

class AppConfig {
  final ApiModel get = const ApiModel("/app-configs", APIType.GET);
}

class BookMark {
  final ApiModel get = const ApiModel("/bookmarks", APIType.GET);
}

class StoryApi {
  final ApiModel postVideoStory =
      const ApiModel("/stories/video", APIType.POST);
  final ApiModel postStory = const ApiModel("/stories", APIType.POST);
  final ApiModel highlights = const ApiModel("/highlights", APIType.POST);
  final ApiModel updateHighlight =
      const ApiModel("/highlights/:id", APIType.PATCH);
  final ApiModel getHighlights = const ApiModel("/highlights/:id", APIType.GET);
  final ApiModel deleteHighlight =
      const ApiModel("/highlights/:id", APIType.DELETE);
}

class Followers {
  final ApiModel getFollowers =
      const ApiModel("/friends/followers", APIType.GET);
  final ApiModel getFollowings =
      const ApiModel("/friends/followings", APIType.GET);
}

class Folders {
  final ApiModel getAllFolders = const ApiModel("/folders", APIType.GET);
  final ApiModel createFolder = const ApiModel("/folders", APIType.POST);
  final ApiModel editFolder = const ApiModel("/folders/:id", APIType.PATCH);
  final ApiModel deleteFolder = const ApiModel("/folders/:id", APIType.DELETE);

  final ApiModel getAllFiles = const ApiModel("/files", APIType.GET);
  final ApiModel createFile = const ApiModel("/files", APIType.POST);
  final ApiModel editFile = const ApiModel("/files/:id", APIType.PATCH);
  final ApiModel deleteFile = const ApiModel("/files/:id", APIType.DELETE);
}

class Notifications {
  final ApiModel getNotifications =
      const ApiModel("/notifications", APIType.GET);
}
