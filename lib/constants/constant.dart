// lib/config/environment.dart
enum Environment { development, staging, production }

enum ErrorPosition { topRight, Bottom, center, bottomRight }
enum BackButtonType { circle, normal }


// ignore_for_file: constant_identifier_names

class ApiModel {
  final String endpoint;
  final APIType type;
  const ApiModel(this.endpoint, this.type);
  ApiModel copyWith({String? endpoint, APIType? type}) {
    return ApiModel(
      endpoint ?? this.endpoint,
      type ?? this.type,
    );
  }
}

class GoogleMapConstants {
  static const String apiKey = 'AIzaSyADXyAlgxIIVAgrD7dnj6oYY2VeQLV13B0';
}

enum APIType { GET, POST, PATCH, DELETE, PUT }

