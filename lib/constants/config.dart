import 'constant.dart';

class CONFIG {
  static final CONFIG _instance = CONFIG._internal();
  factory CONFIG() {
    return _instance;
  }

  CONFIG._internal();
  String baseUrl = "https://backend.floment.ai/api/v1";

  config(Environment environment) {
    switch (environment) {
      case Environment.development:
        {
          baseUrl = "http://54.156.242.140:3001/api/v1";
        }
      case Environment.staging:
        {
          baseUrl = "https://stag-backend.incenti.ai/api/v1";
        }
      case Environment.production:
        {
          baseUrl = "https://backend.floment.ai/api/v1";
        }
    }
  }
}
