import 'package:get/get.dart';
import 'package:incenti_ai/models/app_user_data_model.dart';

class ProjectResponse {
  String status;
  ProjectData data;

  ProjectResponse({
    this.status = "",
    required this.data,
  });

  factory ProjectResponse.fromJson(Map<String, dynamic> json) =>
      ProjectResponse(
        status: json["status"] ?? "",
        data: ProjectData.fromJson(json["data"] ?? {}),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data.toJson(),
      };
}

class ProjectData {
  int totalRecords;
  int result;
  List<Project> data;

  ProjectData({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory ProjectData.fromJson(Map<String, dynamic> json) => ProjectData(
        totalRecords: json["totalRecords"] ?? 0,
        result: json["result"] ?? 0,
        data: json["data"] == null
            ? []
            : List<Project>.from(json["data"].map((x) => Project.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "totalRecords": totalRecords,
        "result": result,
        "data": data.map((x) => x.toJson()).toList(),
      };
}

class Project {
  int id;
  int? userId;
  String name;
  String image;
  String description;
  bool isPrivate;
  bool? hide;
  String subProjectsCount;
  String? postsCount;
  String? location;
  String? slug;
  String? isFollowed;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? deletedAt;
  UserData? userData;
  List<SubProjectData> subProjects;
  List<ProjectMembersData> projectMembers;
  ParentProjectData? parentProjectData;

  Project(
      {this.id = 0,
      this.name = "",
      this.image = "",
      this.description = "",
      this.isPrivate = false,
      this.subProjectsCount = "0",
      this.location,
      this.slug = '',
      this.userId,
      this.createdAt,
      this.updatedAt,
      this.hide,
      this.deletedAt,
      this.postsCount,
      this.isFollowed = "0",
      this.subProjects = const [],
      this.parentProjectData,
      this.projectMembers = const [],
      this.userData});

  factory Project.fromJson(Map<String, dynamic> json) => Project(
        id: json["id"] ?? 0,
        name: json["name"] ?? "",
        image: json["image"] ?? "",
        isPrivate: json["isPrivate"] ?? false,
        description: json["description"] ?? "",
        userId: json["UserId"] ?? 0,
        subProjectsCount: json["subProjectsCount"] ?? "0",
        location: json["location"],
    slug: json["slug"],
        isFollowed: json["isFollowed"] ?? "0",
        hide: json["hide"] ?? false,
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        deletedAt: json["deletedAt"] == null
            ? null
            : DateTime.parse(json["deletedAt"]),
        postsCount: json["postsCount"] ?? "0",
        subProjects: json["SubProjects"] == null
            ? []
            : List<SubProjectData>.from(
                json["SubProjects"].map((x) => SubProjectData.fromJson(x))),
        projectMembers: json["ProjectMembers"] == null
            ? []
            : List<ProjectMembersData>.from(json["ProjectMembers"]
                .map((x) => ProjectMembersData.fromJson(x))),
        userData: UserData.fromJson(json["User"] ?? {}),
        parentProjectData: json["ParentProject"] != null
            ? ParentProjectData.fromJson(json["ParentProject"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "isPrivate": isPrivate,
        "slug": slug,
        "subProjectsCount": subProjectsCount,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        'deletedAt': deletedAt?.toIso8601String(),
        "SubProjects": subProjects.map((x) => x.toJson()).toList(),
      };
}

class SubProjectResponse {
  String status;
  Data data;

  SubProjectResponse({
    this.status = "",
    required this.data,
  });

  factory SubProjectResponse.fromJson(Map<String, dynamic> json) => SubProjectResponse(
    status: json["status"],
    data: Data.fromJson(json["data"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data.toJson(),
  };
}

class Data {
  int totalRecords;
  int result;
  List<SubProjectData> data;

  Data({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    totalRecords: json["totalRecords"],
    result: json["result"],
    data: json["data"] == null
        ? []
        : List<SubProjectData>.from(json["data"].map((x) => SubProjectData.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "totalRecords": totalRecords,
    "result": result,
    "data": data.map((x) => x.toJson()).toList(),
  };
}

class SubProjectData {
  int id;
  int? userId;
  String name;
  String? slug;
  String? image;
  String? location;
  String? description;
  bool isPrivate;
  bool hide;
  String? postsCount;
  List<ProjectMembersData> projectMembers;
  UserData? userData;

  SubProjectData({
    this.id = 0,
    this.name = "",
    this.image,
    this.location,
    this.slug,
    this.description = "",
    this.isPrivate = false,
    this.hide = false,
    this.postsCount,
    this.userId,
    this.projectMembers = const [],
    this.userData,
  });

  factory SubProjectData.fromJson(Map<String, dynamic> json) => SubProjectData(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      image: json["image"],
      location: json["location"],
      hide: json["hide"] ?? false,
      isPrivate: json["isPrivate"] ?? false,
      slug: json["slug"] ?? "",
      description: json["description"] ?? "",
      userId: json["UserId"] ?? 0,
      userData: UserData.fromJson(json["User"] ?? {}),
      projectMembers: json["ProjectMembers"] == null
          ? []
          : List<ProjectMembersData>.from(json["ProjectMembers"]
          .map((x) => ProjectMembersData.fromJson(x))),
      postsCount: json["postsCount"] ?? "");

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "isPrivate": isPrivate,
      };
}

class ProjectMembersData {
  int id;
  String access;

  ProjectMembersData({
    this.id = 0,
    this.access = "",
  });

  factory ProjectMembersData.fromJson(Map<String, dynamic> json) =>
      ProjectMembersData(
        id: json["id"] ?? 0,
        access: json["access"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "access": access,
      };
}

class ParentProjectData {
  int id;
  int? userId;
  String name;
  String? image;
  bool isPrivate;
  String? description;
  String? postsCount;
  String? subProjectsCount;

  ParentProjectData(
      {this.id = 0,
      this.name = "",
      this.image,
      this.isPrivate = false,
      this.description = "",
      this.postsCount,
      this.userId,
      this.subProjectsCount});

  factory ParentProjectData.fromJson(Map<String, dynamic> json) =>
      ParentProjectData(
          id: json["id"] ?? 0,
          name: json["name"] ?? "",
          image: json["image"] ?? "",
          isPrivate: json["isPrivate"] ?? false,
          description: json["description"] ?? "",
          postsCount: json["postsCount"] ?? "",
          userId: json['UserId'] ?? 0,
          subProjectsCount: json["subProjectsCount"] ?? "");

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "isPrivate": isPrivate,
      };
}

abstract class FolderWrapper {
  FolderWrapper({
    required this.id,
    required this.name,
    required this.projectId,
    required this.children,
    this.userId,
  });

  int id;
  int projectId;
  String name;
  int? userId;

  RxList<FolderWrapper> children;
}

class ProjectFolder extends FolderWrapper {
  ProjectFolder({
    required super.id,
    required super.projectId,
    required super.name,
    required super.userId,
    this.parentId,
    required this.filesCount,
    required super.children,
  });

  final String filesCount;
  final int? parentId;

  factory ProjectFolder.fromJson(Map<String, dynamic> json) => ProjectFolder(
        id: json["id"] ?? 0,
        projectId: json["ProjectId"] ?? 0,
        name: json["name"] ?? "",
        filesCount: json['filesCount'] ?? '0',
        parentId: json['ParentId'],
        children: RxList<FolderWrapper>(),
    userId: json["UserId"] ?? 0,
      );
}

class ProjectFile extends FolderWrapper {
  ProjectFile({
    required super.id,
    required super.projectId,
    required super.name,
    required super.userId,
    // required this.type,
    required this.link,
    required this.createdAt,
    required this.updatedAt,
  }) : super(children: RxList([]));

  // final String type;
  final String link;
  final DateTime createdAt;
  final DateTime updatedAt;

  factory ProjectFile.fromJson(Map<String, dynamic> json) => ProjectFile(
        id: json["id"] ?? 0,
        projectId: json["ProjectId"] ?? 0,
        name: json["name"] ?? "",
        // type: json["type"] ?? "",
        link: json["link"] ?? "",
        createdAt: DateTime.parse(json["createdAt"] ?? ""),
        updatedAt: DateTime.parse(json["updatedAt"] ?? ""),
    userId: json["UserId"] ?? 0,
      );
}

class FileUploadInProgress extends FolderWrapper {
  FileUploadInProgress({
    required super.id,
    required super.projectId,
    required super.name,
    required this.progress,
    // required this.type,
  }) : super(children: RxList([]));

  final RxDouble progress;
}
