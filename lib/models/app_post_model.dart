import 'package:get/get.dart';

import 'app_community_model.dart';
import 'app_project_model.dart';
import 'app_user_data_model.dart';

class PostResponse {
  String status;
  PostData data;

  PostResponse({
    this.status = "",
    required this.data,
  });

  factory PostResponse.fromJson(Map<String, dynamic> json) => PostResponse(
        status: json["status"] ?? "",
        data: PostData.fromJson(json["data"] ?? {}),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data.toJson(),
      };
}

class PostData {
  int totalRecords;
  int result;
  List<Post> data;

  PostData({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory PostData.fromJson(Map<String, dynamic> json) => PostData(
        totalRecords: json["totalRecords"] ?? 0,
        result: json["result"] ?? 0,
        data: json["data"] == null
            ? []
            : List<Post>.from(json["data"].map((x) => Post.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "totalRecords": totalRecords,
        "result": result,
        "data": data.map((x) => x.toJson()).toList(),
      };
}

class Post {
  int id;
  String title;
  String description;
  List<Media> media;
  bool isPrivate;
  bool isPublished;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? projectId;
  UserData? user;
  Project? project;
  RxBool? isBookMarked;
  RxBool? isLiked;
  RxInt? likesCount;
  RxInt? commentsCount;
  String? pinnedAt;
  String? slug;
  int? bookmarksCount;
  Properties? properties;
  CommunityData? community;

  Post(
      {this.id = 0,
      this.title = "",
      this.description = "",
      this.properties,
      this.media = const [],
      this.isPrivate = false,
      this.bookmarksCount,
      this.isBookMarked,
      this.updatedAt,
      this.commentsCount,
      this.isLiked,
      this.likesCount,
      this.isPublished = false,
      this.createdAt,
      this.community,
      this.projectId,
      this.slug,
      this.user,
      this.project,
      this.pinnedAt});

  factory Post.fromJson(Map<String, dynamic> json) => Post(
      id: json["id"] ?? 0,
      title: json["title"] ?? "",
      slug: json["slug"] ?? "",
      description: json["description"] ?? "",
      properties: json["properties"] == null
          ? null
          : Properties.fromJson(json["properties"]),
      media: json["media"] == null
          ? []
          : List<Media>.from(json["media"].map((x) => Media.fromJson(x))),
      pinnedAt: json["pinnedAt"] ?? "",
      isPrivate: json["isPrivate"] ?? false,
      isPublished: json["isPublished"] ?? false,
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      projectId: json["ProjectId"],
      user: UserData.fromJson(json["User"] ?? {}),
      project:
          json["Project"] == null ? null : Project.fromJson(json["Project"]),
      community: json["Community"] == null ? null : CommunityData.fromJson(json["Community"]),
      bookmarksCount: int.parse(json["bookmarksCount"] ?? '0'),
      commentsCount: (int.parse(json["commentsCount"] ?? '0').obs),
      isBookMarked:
          (int.parse(json["isBookMarked"] ?? '0') == 0 ? false : true).obs,
      isLiked: (int.parse(json["isLiked"] ?? '0') == 0 ? false : true).obs,
      likesCount: (int.parse(json["likesCount"] ?? '0').obs));

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "slug": slug,
        "description": description,
        "media": media.map((x) => x.toJson()).toList(),
        "isPrivate": isPrivate,
        "isPublished": isPublished,
        "createdAt": createdAt?.toIso8601String(),
        "ProjectId": projectId,
        "User": user?.toJson(),
        "Project": project?.toJson(),
        "properties": properties?.toJson(),
      };
}

class Properties {
  double height;

  Properties({
    required this.height,
  });

  factory Properties.fromJson(Map<String, dynamic> json) => Properties(
    height: (json["height"] ?? 0).toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "height": height,
      };
}

class Media {
  String link;
  String type;
  bool isCoverImage;
  bool isCustom;
  double height;

  Media({
    this.link = "",
    this.type = "",
    this.isCoverImage = false,
    this.isCustom = false,
    this.height = 0.0,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        link: json["link"] ?? "",
        type: json["type"] ?? "",
        isCoverImage: json["isCoverImage"] ?? false,
        isCustom: json["isCustom"] ?? false,
        height: double.parse((json["height"] ?? 0.0).toString()),
      );

  Map<String, dynamic> toJson() => {
        "link": link,
        "type": type,
        "isCoverImage": isCoverImage,
        "isCustom": isCustom,
        "height": height,
      };
}
