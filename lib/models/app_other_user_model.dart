// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);
import 'package:get/get_rx/src/rx_types/rx_types.dart';

class OtherUserModel {
  final String? status;
  final OtherUser? data;

  OtherUserModel({
    this.status,
    this.data,
  });

  factory OtherUserModel.fromJson(Map<String, dynamic> json) => OtherUserModel(
    status: json["status"],
    data: json["data"] == null ? null : OtherUser.fromJson(json["data"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data?.toJson(),
  };
}

class OtherUser {
  final String? fullName;
  final int? id;
  final String? firstName;
  final String? lastName;
  final String? about;
  final String? email;
  final bool? isPrivate;
  final String? image;
  final String? isFollowed;
  final String? backgroundImage;
  final String? followersCount;
  final String? followingsCount;
  final DateTime? passwordChangedAt;
  final DateTime? createdAt;
  final DateTime? deletedAt;
  RxBool isSelected;
  RxBool? isFollowing;

  OtherUser({
    this.fullName,
    this.id,
    this.firstName,
    this.lastName,
    this.about,
    this.email,
    this.isPrivate,
    this.isFollowed,
    this.followersCount,
    this.followingsCount,
    this.image,
    this.backgroundImage,
    this.passwordChangedAt,
    this.createdAt,
    this.deletedAt,
    RxBool? isFollowing,
    bool isSelected = false,
  }) : isSelected = isSelected.obs,
        isFollowing = isFollowing ?? false.obs;

  factory OtherUser.fromJson(Map<String, dynamic> json) {
    String isFollowed = json["isFollowed"] ?? "0";
    return OtherUser(
      fullName: json["fullName"] ?? "",
      id: json["id"],
      firstName: json["firstName"] ?? "",
      lastName: json["lastName"] ?? "",
      about: json["about"] ?? "",
      email: json["email"] ?? "",
      followingsCount: json["followingsCount"] ?? "0",
      followersCount: json["followersCount"] ?? "0",
      isFollowed: isFollowed,
      isFollowing: RxBool(isFollowed == "0"),
      isPrivate: json["isPrivate"] ?? false,
      image: json["image"] ?? "",
      backgroundImage: json["backgroundImage"] ?? "",
      passwordChangedAt: json["passwordChangedAt"] == null ? null : DateTime.parse(json["passwordChangedAt"]),
      createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      deletedAt: json["deletedAt"] == null ? null : DateTime.parse(json["deletedAt"]),
    );
  }

  Map<String, dynamic> toJson() => {
    "fullName": fullName,
    "id": id,
    "firstName": firstName,
    "lastName": lastName,
    "about": about,
    "email": email,
    "isPrivate": isPrivate,
    "image": image,
    "backgroundImage": backgroundImage,
    "passwordChangedAt": passwordChangedAt?.toIso8601String(),
    "createdAt": createdAt?.toIso8601String(),
    "deletedAt": deletedAt?.toIso8601String(),
  };
}
