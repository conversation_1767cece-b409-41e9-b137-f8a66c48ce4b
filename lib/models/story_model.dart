import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

class AllStory {
  String? status;
  List<Story>? data;

  AllStory({
    this.status,
    this.data,
  });

  factory AllStory.fromJson(Map<String, dynamic> json) => AllStory(
    status: json["status"],
    data: json["data"] == null ? [] : List<Story>.from(json["data"]!.map((x) => Story.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
  };
}

class Story {
  int? id;
  String? firstName;
  String? lastName;
  String? image;
  List<OneStory>? stories;

  Story({
    this.id,
    this.firstName,
    this.lastName,
    this.image,
    this.stories,
  });

  factory Story.fromJson(Map<String, dynamic> json) => Story(
    id: json["id"],
    firstName: json["firstName"],
    lastName: json["lastName"],
    image: json["image"],
    stories: json["Stories"] == null ? [] : List<OneStory>.from(json["Stories"]!.map((x) => OneStory.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "firstName": firstName,
    "lastName": lastName,
    "image": image,
    "Stories": stories == null ? [] : List<dynamic>.from(stories!.map((x) => x.toJson())),
  };
}

class OneStory {
  List<EditableItem>? properties;
  int? id;
  int? duration;
  String? text;
  String? mediaLink;
  String? mediaType;
  String? overlayImage;
  String? mentionedPath;
  bool? isRendered;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? deletedAt;
  int? userId;
  VideoPlayerController? videoController;
  RxBool isSeen;
  bool? showUploading = false;
  RxBool isSelected;
  String? thumbnailPath;

  OneStory({
    this.properties,
    this.id,
    this.duration,
    this.text,
    this.mediaLink,
    this.mediaType,
    this.overlayImage,
    this.mentionedPath,
    this.isRendered,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.videoController,
    bool seen = false,
    bool selected = false,
    this.showUploading,
    this.thumbnailPath,
  }) : isSelected = selected.obs, isSeen = seen.obs;

  factory OneStory.fromJson(Map<String, dynamic> json) => OneStory(
    properties: json["properties"] == null ||
        json["properties"] == '[]' ||
        (json["properties"] is List && json["properties"].isEmpty) ||
        json["properties"] is Map ?
    [] :
    List<EditableItem>.from(json["properties"]!.map((x) => EditableItem.fromJson(x))),
    id: json["id"],
    text: json["text"],
    mediaLink: json["mediaLink"],
    mediaType: json["mediaType"],
    overlayImage: json["overlayImage"],
    mentionedPath: json["mentionedPath"],
    isRendered: json["isRendered"],
    createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
    updatedAt: json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
    deletedAt: json["deletedAt"]== null ? null : DateTime.parse(json["deletedAt"]),
    userId: json["UserId"],
    videoController: json["mediaType"] == StoryType.video.name ? (json["mediaLink"].toString().contains('.m3u8')) ?VideoPlayerController.networkUrl(Uri.parse(json["mediaLink"] ?? '')) :VideoPlayerController.file(File(json["mediaLink"] ?? '')) : null,
    thumbnailPath: json["thumbNailImage"],
    duration: json["mediaType"] == StoryType.video.name ? json["duration"] ?? 50 : json["duration"] ?? 8,
  );

  Map<String, dynamic> toJson() => {
    "properties": properties == null ? [] : List<dynamic>.from(properties!.map((x) => x.toJson())),
    "id": id,
    "text": text,
    "mediaLink": mediaLink,
    "mediaType": mediaType,
    "overlayImage": overlayImage,
    "mentionedPath": mentionedPath,
    "isRendered": isRendered,
    "createdAt": createdAt?.toIso8601String(),
    "updatedAt": updatedAt?.toIso8601String(),
    "deletedAt": deletedAt?.toIso8601String(),
    "UserId": userId,
    "thumbNailImage": thumbnailPath,
    "duration": duration,
  };
}

class EditableItem {
  bool deletePosition;
  Offset position;
  double scale;
  double rotation;
  StoryType type;
  String slug;
  String text;
  Color textColor;
  TextAlign textAlign;
  double fontSize;
  int fontFamily;
  int projectId;
  int subProjectId;
  Color backGroundColor;

  EditableItem({
    this.deletePosition = false,
    this.position = const Offset(0.0, 0.0),
    this.scale = 1,
    this.rotation = 0,
    this.type = StoryType.text,
    this.text = '',
    this.slug = '',
    this.textColor = Colors.transparent,
    this.textAlign = TextAlign.center,
    this.fontSize = 20,
    this.fontFamily = 0,
    this.projectId = 0,
    this.subProjectId = 0,
    this.backGroundColor = Colors.transparent,
  });

  factory EditableItem.fromJson(Map<String, dynamic> json) {
    return EditableItem(
      deletePosition: json['deletePosition'] ?? false,
      position: Offset(json['position']['dx']?.toDouble() ?? 0.0, json['position']['dy']?.toDouble() ?? 0.0),
      scale: json['scale']?.toDouble() ?? 1.0,
      rotation: json['rotation']?.toDouble() ?? 0.0,
      type: StoryType.values[json['type'] ?? 0],
      text: json['text'] ?? '',
      slug: json['slug'] ?? '',
      textColor: Color(json['textColor'] ?? 0x00000000),
      textAlign: TextAlign.values[json['textAlign'] ?? 2],
      fontSize: json['fontSize']?.toDouble() ?? 20.0,
      fontFamily: json['fontFamily'] ?? 0,
      projectId: json['projectId'] ?? 0,
      subProjectId: json['subProjectId'] ?? 0,
      backGroundColor: Color(json['backGroundColor'] ?? 0x00000000),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deletePosition': deletePosition,
      'position': {'dx': position.dx, 'dy': position.dy},
      'scale': scale,
      'rotation': rotation,
      'type': type.index,
      'text': text,
      'slug': slug,
      'textColor': textColor.value,
      'textAlign': textAlign.index,
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'projectId': projectId,
      'subProjectId': subProjectId,
      'backGroundColor': backGroundColor.value,
    };
  }
}

class Position {
  double? dx;
  double? dy;

  Position({
    this.dx,
    this.dy,
  });

  factory Position.fromJson(Map<String, dynamic> json) => Position(
    dx: json["dx"]?.toDouble(),
    dy: json["dy"]?.toDouble(),
  );

  Map<String, dynamic> toJson() => {
    "dx": dx,
    "dy": dy,
  };
}

enum StoryType { image, text, video }
