import 'app_other_user_model.dart';

class TeamUserResponse {
  final String status;
  final int totalRecords;
  final int results;
  final List<OtherUser> data;

  TeamUserResponse({
    this.status = "",
    required this.totalRecords,
    required this.results,
    this.data = const [],
  });

  factory TeamUserResponse.fromJson(Map<String, dynamic> json) => TeamUserResponse(
    status: json["status"],
    totalRecords: json["totalRecords"],
    results: json["results"],
    data: json["data"] == null ? [] : List<OtherUser>.from(json["data"].map((x) => OtherUser.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "totalRecords": totalRecords,
    "results": results,
    "data": List<dynamic>.from(data.map((x) => x.toJson())),
  };
}