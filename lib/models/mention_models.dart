class MentionModel {
  final String name;
  final String slug;
  final String type;
  final int? userId;
  final String? image;
  final int? projectId;

  MentionModel({
    required this.name,
    required this.slug,
    required this.type,
    this.userId,
    this.image,
    this .projectId
  });

  factory MentionModel.fromJson(Map<String, dynamic> json) {
    String type = json['type'] ??
        (json.containsKey('username') || json.containsKey('firstName') ? 'user' : 'project');
    return MentionModel(
      name: json['name'] ?? json['firstName'] ?? '',
      slug: json['ProjectSlug'] ?? json['username'] ?? '',
      type: type,
      userId: json['UserId'],
      image: json['image'],
      projectId: json['ProjectId'],
    );
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'projectSlug': slug,
        'type': type,
        'UserId': userId,
        'ProjectId': projectId,
      };

  @override
  bool operator ==(Object other) {
    return other is MentionModel &&
        name == other.name &&
        slug == other.slug &&
        type == other.type &&
        userId == other.userId &&
        projectId == other.projectId;
  }

  @override
  int get hashCode => Object.hash(name, slug, type, userId, projectId);

}

