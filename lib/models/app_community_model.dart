import 'package:get/get_rx/src/rx_types/rx_types.dart';

import 'app_member_model.dart';
import 'app_user_data_model.dart';

class CommunityResponse {
  final String status;
  final Data data;

  CommunityResponse({
    this.status = "",
    required this.data,
  });

  factory CommunityResponse.fromJson(Map<String, dynamic> json) =>
      CommunityResponse(
        status: json["status"],
        data: Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data.toJson(),
      };
}

class Data {
  final int totalRecords;
  final int result;
  final List<CommunityData> data;

  Data({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        totalRecords: json["totalRecords"],
        result: json["result"],
        data: json["data"] == null
            ? []
            : List<CommunityData>.from(
                json["data"]!.map((x) => CommunityData.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "totalRecords": totalRecords,
        "result": result,
        "data": data.map((x) => x.toJson()).toList(),
      };
}

class CommunityData {
  int? id;
  String? name;
  String image;
  String? description;
  String? slug;
  String? members;
  RxBool? isJoined;
  String? location;
  int? UserId;
  UserData? user;
  DateTime? createdAt;
  DateTime? deletedAt;
  DateTime? updatedAt;
  final List<MemberData> communityMembers;

  CommunityData({
    this.id,
    this.name,
    this.image = "",
    this.description,
    this.slug,
    this.user,
    this.isJoined,
    this.members,
    this.createdAt,
    this.deletedAt,
    this.updatedAt,
    this.location,
    this.UserId,
    this.communityMembers = const [],
  });

  factory CommunityData.fromJson(Map<String, dynamic> json) => CommunityData(
        id: json["id"],
        name: json["name"] ?? "",
        image: json["image"] ?? "",
        description: json["description"] ?? "",
    slug: json["slug"] ?? "",
        isJoined: (int.parse(json["isJoined"] ?? '0') == 0 ? false : true).obs,
        members: json["members"] ?? "0",
        location: json["location"] ?? "",
        UserId: json["UserId"] ?? 0,
        communityMembers: json["recentMembers"] == null
            ? []
            : List<MemberData>.from(
                json["recentMembers"]!.map((x) => UserMemberData.fromJson(x))),
        user: json["User"] == null ? null : UserData.fromJson(json["User"]),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        deletedAt: json["deletedAt"] == null
            ? null
            : DateTime.parse(json["deletedAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "slug": slug,
        "description": description,
        "User": user?.toJson(),
      };
}
