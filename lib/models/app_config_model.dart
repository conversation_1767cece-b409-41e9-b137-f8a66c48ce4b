class AppConfigResponse {
  String status;
  AppConfig appConfig;

  AppConfigResponse({
    this.status = "",
    required this.appConfig,
  });

  factory AppConfigResponse.fromJson(Map<String, dynamic> json) => AppConfigResponse(
    status: json["status"] ?? "",
    appConfig: AppConfig.fromJson(json["appConfig"] ?? {}),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "appConfig": appConfig.toJson(),
  };
}

class AppConfig {
  int id;
  bool appInMaintenance;
  String androidVersionCode;
  String iosVersionCode;
  bool forceUpdate;
  bool softUpdate;

  AppConfig({
    this.id = 0,
    this.appInMaintenance = false,
    this.androidVersionCode = "",
    this.iosVersionCode = "",
    this.forceUpdate = false,
    this.softUpdate = false,
  });

  factory AppConfig.fromJson(Map<String, dynamic> json) => AppConfig(
    id: json["id"] ?? 0,
    appInMaintenance: json["appInMaintenance"] ?? false,
    androidVersionCode: json["androidVersionCode"] ?? "",
    iosVersionCode: json["iosVersionCode"] ?? "",
    forceUpdate: json["forceUpdate"] ?? false,
    softUpdate: json["softUpdate"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "appInMaintenance": appInMaintenance,
    "androidVersionCode": androidVersionCode,
    "iosVersionCode": iosVersionCode,
    "forceUpdate": forceUpdate,
    "softUpdate": softUpdate,
  };
}
