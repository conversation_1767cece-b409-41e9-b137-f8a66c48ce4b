// To parse this JSON data, do
//
//     final allHighLightsModel = allHighLightsModelFromJson(jsonString);
import 'dart:convert';

import 'package:incenti_ai/models/story_model.dart';

AllHighLightsModel allHighLightsModelFromJson(String str) => AllHighLightsModel.fromJson(json.decode(str));

String allHighLightsModelToJson(AllHighLightsModel data) => json.encode(data.toJson());

class AllHighLightsModel {
  String? status;
  Data? data;

  AllHighLightsModel({
    this.status,
    this.data,
  });

  factory AllHighLightsModel.fromJson(Map<String, dynamic> json) => AllHighLightsModel(
    status: json["status"],
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data?.toJson(),
  };
}

class Data {
  int? id;
  int? totalRecords;
  int? result;
  List<Highlight>? highlights;

  Data({
    this.id,
    this.totalRecords,
    this.result,
    this.highlights,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    totalRecords: json["totalRecords"],
    id: json["id"],
    result: json["result"],
    highlights: json["data"] == null ? [] : List<Highlight>.from(json["data"]!.map((x) => Highlight.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "totalRecords": totalRecords,
    "result": result,
    "id": id,
    "data": highlights == null ? [] : List<dynamic>.from(highlights!.map((x) => x.toJson())),
  };
}

class Stories{
  int? id;
  OneStory? story;

  Stories({this.id,this.story,});

  factory Stories.fromJson(Map<String, dynamic> json) => Stories(
    id: json["id"],
    story: json["Story"] == null ? null : OneStory.fromJson(json["Story"]),

  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "Story": story?.toJson() ?? {},
  };
}

class Highlight {
  int? id;
  String? title;
  String? image;
  DateTime? createdAt;
  List<Stories>? stories;

  Highlight({
    this.id,
    this.title,
    this.image,
    this.createdAt,
    this.stories,
  });

  factory Highlight.fromJson(Map<String, dynamic> json) => Highlight(
    id: json["id"],
    title: json["title"],
    image: json["image"],
    createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
    stories: json["HighlightedStories"] == null ? [] : List<Stories>.from(json["HighlightedStories"]!.map((x) => Stories.fromJson(x))),

  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "title": title,
    "image": image,
    "createdAt": createdAt?.toIso8601String(),
    "HighlightedStories": stories == null ? [] : List<dynamic>.from(stories!.map((x) => x.toJson())),
  };
}
