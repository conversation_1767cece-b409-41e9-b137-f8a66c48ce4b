
import 'app_other_user_model.dart';

class ProjectUserMemberResponse {
  String status;
  ProjectUserMemberData data;

  ProjectUserMemberResponse({
    this.status = "",
    required this.data,
  });

  factory ProjectUserMemberResponse.fromJson(Map<String, dynamic> json) => ProjectUserMemberResponse(
    status: json["status"] ?? "",
    data: ProjectUserMemberData.fromJson(json["data"] ?? {}),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data.toJson(),
  };
}

class ProjectUserMemberData {
  int totalRecords;
  int result;
  List<OtherUser> data;

  ProjectUserMemberData({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory ProjectUserMemberData.fromJson(Map<String, dynamic> json) => ProjectUserMemberData(
    totalRecords: json["totalRecords"] ?? 0,
    result: json["result"] ?? 0,
    data: json["data"] == null
        ? []
        : List<OtherUser>.from(json["data"].map((x) => OtherUser.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "totalRecords": totalRecords,
    "result": result,
    "data": data.map((x) => x.toJson()).toList(),
  };
}
