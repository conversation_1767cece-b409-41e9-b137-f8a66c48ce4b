import 'package:get/get_rx/src/rx_types/rx_types.dart';

UserData handleApiResponse(Map<String, dynamic> jsonMap) {
  UserData user = UserData.fromJson(jsonMap['data']);
  return user;
}

class UserData {
  int? id;
  String? firstName;
  String? lastName;
  String? fullName;
  String? email;
  String? location;
  String? about;
  bool? isPrivate;
  bool? isPasswordSet;
  String? image;
  String? backgroundImage;
  String? followersCount;
  String? isFollowed;
  String? followingsCount;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? deletedAt;
  RxBool? isFollowing;
  RxBool? newNotifications;
  CustomSetting? customSetting;

  UserData({
    this.id,
    this.firstName,
    this.lastName,
    this.fullName,
    this.email,
    this.about,
    this.isPrivate,
    this.isFollowed,
    this.location,
    this.image,
    this.createdAt,
    this.updatedAt,
    this.isPasswordSet,
    this.deletedAt,
    this.backgroundImage,
    this.followersCount,
    this.followingsCount,
    RxBool? isFollowing,
    RxBool? newNotifications,
    this.customSetting,
  })  : isFollowing = isFollowing ?? false.obs,
        newNotifications = newNotifications ?? false.obs;

  factory UserData.fromJson(Map<String, dynamic> json) {
    String isFollowed = json["isFollowed"] ?? "0";
    return UserData(
        id: json['id'] ?? 0,
        firstName: json['firstName'] ?? "",
        lastName: json['lastName'] ?? "",
        about: json['about'] ?? "",
        fullName: json['fullName'] ?? "",
        isPasswordSet: json['isPasswordSet'] ?? false,
        location: json['location'] ?? '',
        email: json['email'] ?? "",
        isFollowed: isFollowed,
        isFollowing: RxBool(isFollowed == "0"),
        newNotifications: RxBool(json["newNotifications"] ?? false),
        followersCount: json["followersCount"] ?? "0",
        followingsCount: json["followingsCount"] ?? "0",
        isPrivate: json['isPrivate'] ?? false,
        image: json['image'] ?? "",
        customSetting: json["customSetting"] == null
            ? null
            : CustomSetting.fromJson(json["customSetting"]),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        deletedAt: json["deletedAt"] == null
            ? null
            : DateTime.parse(json["deletedAt"]),
        backgroundImage: json["backgroundImage"] ?? "");
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'firstName': firstName,
        'email': email,
        'isPrivate': isPrivate,
        'image': image,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        'deletedAt': deletedAt?.toIso8601String(),
      };
}

class CustomSetting {
  String? homeSection;

  CustomSetting({
    this.homeSection,
  });

  factory CustomSetting.fromJson(Map<String, dynamic> json) => CustomSetting(
        homeSection: json["homeSection"],
      );

  Map<String, dynamic> toJson() => {
        "homeSection": homeSection,
      };
}
