
import 'package:get/get_rx/src/rx_types/rx_types.dart';

class FollowersResponse {
  String? status;
  Data? data;

  FollowersResponse({
    this.status,
    this.data,
  });

  factory FollowersResponse.fromJson(Map<String, dynamic> json,{required bool isFollower}) => FollowersResponse(
    status: json["status"],
    data: json["data"] == null ? null : Data.fromJson(json["data"],isFollower: isFollower),
  );

  Map<String, dynamic> toJson({required bool isFollower}) => {
    "status": status,
    "data": data?.toJson(isFollower: isFollower),
  };
}

class Data {
  int? totalRecords;
  int? results;
  List<FollowersData>? data;

  Data({
    this.totalRecords,
    this.results,
    this.data,
  });

  factory Data.fromJson(Map<String, dynamic> json,{required bool isFollower}) => Data(
    totalRecords: json["totalRecords"],
    results: json["results"],
    data: json["data"] == null ? [] :  List<FollowersData>.from(
      json["data"]!.map((x) => FollowersData.fromJson(x, isFollower: isFollower)),
    ),
  );

  Map<String, dynamic> toJson({required bool isFollower}) => {
    "totalRecords": totalRecords,
    "results": results,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson(isFollower: isFollower))),
  };
}

class FollowersData {
  int? id;
  FollowersUser? followingUser;

  FollowersData({
    this.id,
    this.followingUser,
  });

  factory FollowersData.fromJson(Map<String, dynamic> json,{required bool isFollower}) => FollowersData(
    id: json["id"],
    followingUser: json[isFollower ? "followerUser" : "followingUser"] == null
        ? null
        : FollowersUser.fromJson(json[isFollower ? "followerUser" : "followingUser"]),
  );

  Map<String, dynamic> toJson({required bool isFollower}) => {
    "id": id,
    isFollower ? "followerUser" : "followingUser": followingUser?.toJson(),
  };
}

class FollowersUser {
  int? id;
  String? firstName;
  String? lastName;
  String? slug;
  String? email;
  String? image;
  String? isFollowed;
  RxBool? isFollowing;

  FollowersUser({
    this.id,
    this.firstName,
    this.lastName,
    this.slug,
    this.email,
    this.image,
    this.isFollowed,
    RxBool? isFollowing,
  }) : isFollowing = isFollowing ?? false.obs; // Initialize inside constructor

  factory FollowersUser.fromJson(Map<String, dynamic> json) {
    String isFollowed = json["isFollowed"] ?? "0";
    return FollowersUser(
      id: json["id"],
      firstName: json["firstName"] ?? "",
      slug: json["slug"] ?? "",
      lastName: json["lastName"] ?? "",
      image: json["image"] ?? "",
      email: json["email"] ?? "",
      isFollowed: isFollowed,
      isFollowing: RxBool(isFollowed == "1"), // Initialize with backend value
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "firstName": firstName,
    "lastName": lastName,
    "slug": slug,
    "image": image,
  };
}
