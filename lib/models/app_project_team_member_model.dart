
import 'package:incenti_ai/models/app_user_data_model.dart';

import 'app_project_model.dart';

class ProjectTeamMemberResponse {
  final String status;
  final List<ProjectTeamMemberData> data;

  ProjectTeamMemberResponse({
    this.status = "",
    required this.data,
  });

  factory ProjectTeamMemberResponse.fromJson(Map<String, dynamic> json) => ProjectTeamMemberResponse(
    status: json["status"] = "",
    data: json["data"] == null
        ? []
        : List<ProjectTeamMemberData>.from(json["data"].map((x) => ProjectTeamMemberData.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data.map((x) => x.toJson()).toList(),
  };
}

class ProjectTeamMemberData {
  final int? id;
  final String? access;
  final bool? status;
  final int? projectId;
  final int? userId;
  final UserData? user;
  final AddedByUser? addedByUser;

  ProjectTeamMemberData({
    this.id,
    this.access,
    this.projectId,
    this.status,
    this.userId,
    this.user,
    this.addedByUser,
  });

  factory ProjectTeamMemberData.fromJson(Map<String, dynamic> json) => ProjectTeamMemberData(
    id: json["id"],
    access: json["access"],
    projectId: json["ProjectId"],
    userId: json["UserId"],
    status: json["status"],
    user: json["User"] == null ? null : UserData.fromJson(json["User"]),
      addedByUser: json["AddedByUser"] == null ? null : AddedByUser.fromJson(json["AddedByUser"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "access": access,
    "ProjectId": projectId,
    "UserId": userId,
    "User": user?.toJson(),
    "status": status,
    "AddedByUser": addedByUser?.toJson(),
  };
}

class AddedByUser {
  final int? id;
  final List<ProjectMembersData>? projectMembers;

  AddedByUser({
    this.id,
    this.projectMembers,
  });

  factory AddedByUser.fromJson(Map<String, dynamic> json) => AddedByUser(
    id: json["id"],
    projectMembers: json["ProjectMembers"] == null ? [] : List<ProjectMembersData>.from(json["ProjectMembers"]!.map((x) => ProjectMembersData.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "ProjectMembers": projectMembers == null ? [] : List<dynamic>.from(projectMembers!.map((x) => x.toJson())),
  };
}