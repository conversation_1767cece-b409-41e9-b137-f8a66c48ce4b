class PostLike {
  String? status;
  Data? data;

  PostLike({
    this.status,
    this.data,
  });

  factory PostLike.fromJson(Map<String, dynamic> json) => PostLike(
    status: json["status"],
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data?.toJson(),
  };
}

class Data {
  int? totalRecords;
  int? result;
  List<PostLikeData>? data;

  Data({
    this.totalRecords,
    this.result,
    this.data,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    totalRecords: json["totalRecords"],
    result: json["result"],
    data: json["data"] == null ? [] : List<PostLikeData>.from(json["data"]!.map((x) => PostLikeData.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "totalRecords": totalRecords,
    "result": result,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
  };
}

class PostLikeData {
  int? id;
  DateTime? createdAt;
  User? user;

  PostLikeData({
    this.id,
    this.createdAt,
    this.user,
  });

  factory PostLikeData.fromJson(Map<String, dynamic> json) => PostLikeData(
    id: json["id"],
    createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
    user: json["User"] == null ? null : User.fromJson(json["User"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "createdAt": createdAt?.toIso8601String(),
    "User": user?.toJson(),
  };
}

class User {
  int? id;
  String? firstName;
  String? lastName;
  String? image;

  User({
    this.id,
    this.firstName,
    this.lastName,
    this.image,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
    id: json["id"],
    firstName: json["firstName"],
    lastName: json["lastName"],
    image: json["image"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "firstName": firstName,
    "lastName": lastName,
    "image": image,
  };
}
