import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:incenti_ai/models/app_user_data_model.dart';

import 'app_project_model.dart';

class TodoResponse {
  String status;
  ToDoData data;

  TodoResponse({
    this.status = "",
    required this.data,
  });

  factory TodoResponse.fromJson(Map<String, dynamic> json) => TodoResponse(
    status: json["status"],
    data: ToDoData.fromJson(json["data"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data.toJson(),
  };
}


class ToDoData {
  int totalRecords;
  int result;
  List<ToDo> data;

  ToDoData({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory ToDoData.fromJson(Map<String, dynamic> json) => ToDoData(
    totalRecords: json["totalRecords"],
    result: json["result"],
    data: List<ToDo>.from(json["data"].map((x) => ToDo.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "totalRecords": totalRecords,
    "result": result,
    "data": List<dynamic>.from(data.map((x) => x.toJson())),
  };
}

class ToDo {
  int? id;
  String? name;
  String? description;
  int? priority;
  DateTime? dueDate;
  DateTime? createdAt;
  DateTime? deletedAt;
  DateTime? updatedAt;
  int? projectId;
  RxBool? status;
  int? userId;
  int? assignedUserId;
  UserData? user;
  UserData? assignedUser;
  List<FileElement> files;
  Project? project;

  ToDo({
    this.id,
    this.name,
    this.description,
    this.priority,
    this.dueDate,
    this.createdAt,
    this.deletedAt,
    this.updatedAt,
    this.projectId,
    this.userId,
    this.assignedUserId,
    this.user,
    this.assignedUser,
    this.files = const [],
    this.status,
    this.project
  });

  factory ToDo.fromJson(Map<String, dynamic> json) => ToDo(
    id: json["id"],
    name: json["name"] ?? "",
    description: json["description"] ?? "",
    priority: json["priority"] ?? 0,
    status: ((json["status"] ?? 0) == 0 ? false : true).obs,
    dueDate: json["dueDate"] == null ? null : DateTime.tryParse(json["dueDate"])?.toLocal(),
    createdAt: json["createdAt"] == null ? null : DateTime.tryParse(json["createdAt"]),
    deletedAt: json["deletedAt"] == null ? null : DateTime.tryParse(json["deletedAt"]),
    updatedAt: json["updatedAt"] == null ? null : DateTime.tryParse(json["updatedAt"]),
    projectId: json["ProjectId"],
    userId: json["UserId"],
    project:
    json["Project"] == null ? null : Project.fromJson(json["Project"]),
    assignedUserId: json["AssignedUserId"],
    user: json["User"] != null ? UserData.fromJson(json["User"]) : null,
    assignedUser: json["AssignedUser"] != null ? UserData.fromJson(json["AssignedUser"]) : null,
    files: json["Files"] != null
        ? List<FileElement>.from(json["Files"].map((x) => FileElement.fromJson(x)))
        : [],
  );


  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "description": description,
    "priority": priority,
    "dueDate": dueDate?.toIso8601String(),
    "createdAt": createdAt?.toIso8601String(),
    "deletedAt": deletedAt,
    "updatedAt": updatedAt?.toIso8601String(),
    "ProjectId": projectId,
    "UserId": userId,
    "AssignedUserId": assignedUserId,
    "Files": List<dynamic>.from(files!.map((x) => x.toJson())),
  };
}

class FileElement {
  int id;
  String? name;
  String? link;
  DateTime? createdAt;

  FileElement({
    required this.id,
    this.name,
    this.link,
    this.createdAt,
  });

  factory FileElement.fromJson(Map<String, dynamic> json) => FileElement(
    id: json["id"],
    name: json["name"],
    link: json["link"],
    createdAt: DateTime.parse(json["createdAt"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "link": link,
    "createdAt": createdAt?.toIso8601String(),
  };
}

