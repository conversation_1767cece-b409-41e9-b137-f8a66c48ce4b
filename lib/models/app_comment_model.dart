import 'app_user_data_model.dart';

class CommentResponse {
  String status;
  CommentData data;

  CommentResponse({
    this.status = "",
    required this.data,
  });

  factory CommentResponse.fromJson(Map<String, dynamic> json) => CommentResponse(
    status: json["status"] ?? "",
    data: CommentData.fromJson(json["data"] ?? {}),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data.toJson(),
  };
}

class CommentData {
  int totalRecords;
  int result;
  List<Comment> data;

  CommentData({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory CommentData.fromJson(Map<String, dynamic> json) => CommentData(
    totalRecords: json["totalRecords"] ?? 0,
    result: json["result"] ?? 0,
    data: json["data"] == null
        ? []
        : List<Comment>.from(json["data"].map((x) => Comment.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "totalRecords": totalRecords,
    "result": result,
    "data": data.map((x) => x.toJson()).toList(),
  };
}

class Comment {
  int id;
  String comment;
  DateTime? createdAt;
  UserData? user;

  Comment({
    this.id = 0,
    this.comment = "",
    this.createdAt,
    this.user
  });

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
    id: json["id"] ?? 0,
    comment: json["comment"] ?? "",
    createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
    user: UserData.fromJson(json["User"] ?? {}),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "comment": comment,
    'createdAt': createdAt?.toIso8601String(),
    "User": user?.toJson(),
  };
}