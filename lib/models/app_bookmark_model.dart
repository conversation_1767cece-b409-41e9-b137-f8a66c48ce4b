import 'app_post_model.dart';

class BookMarkResponse {
  String status;
  BookMarkData data;

  BookMarkResponse({
    this.status = "",
    required this.data,
  });

  factory BookMarkResponse.fromJson(Map<String, dynamic> json) => BookMarkResponse(
    status: json["status"] ?? "",
    data: BookMarkData.fromJson(json["data"] ?? {}),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data.toJson(),
  };
}

class BookMarkData {
  int totalRecords;
  int result;
  List<Post> data;

  BookMarkData({
    this.totalRecords = 0,
    this.result = 0,
    this.data = const [],
  });

  factory BookMarkData.fromJson(Map<String, dynamic> json) => BookMarkData(
    totalRecords: json["totalRecords"] ?? 0,
    result: json["result"] ?? 0,
    data: json["data"] == null
        ? []
        : List<Post>.from(json["data"].map((x) => Post.fromJson(x["Post"]))),
  );

  Map<String, dynamic> toJson() => {
    "totalRecords": totalRecords,
    "result": result,
    "data": data.map((x) => x.toJson()).toList(),
  };
}
