import 'package:flutter/material.dart';

class Font {
  FontLevel f1;
  FontLevel f2;
  FontLevel f3;
  FontLevel f4;
  FontLevel f5;
  FontLevel f6;
  FontLevel f7;
  FontLevel f8;
  FontLevel f9;
  FontLevel f10;
  FontLevel f11;
  FontLevel f12;

  Font(
      {required this.f1,
      required this.f2,
      required this.f3,
      required this.f4,
      required this.f5,
      required this.f6,
      required this.f7,
      required this.f8,
      required this.f9,
      required this.f10,
      required this.f11,
      required this.f12,
      });
}

class FontLevel {
  double fontSize;
  FontWeight fontWeight;

  FontLevel({required this.fontSize, this.fontWeight = FontWeight.normal});
}
