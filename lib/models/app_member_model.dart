import 'package:incenti_ai/models/app_user_data_model.dart';

class MemberResponse {
  final String status;
  final AdminMemberData? admin; // Made nullable
  final Data members;

  MemberResponse({
    this.status = "",
    required this.members,
    this.admin, // Optional
  });

  factory MemberResponse.fromJson(Map<String, dynamic> json) => MemberResponse(
        status: json["status"] ?? "",
        members: Data.fromJson(json["data"]["members"]),
        admin: json["data"]["admin"] != null
            ? AdminMemberData.fromJson(json["data"]["admin"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "members": members.toJson(),
        if (admin != null) "admin": admin!.toJson(), // Only include if not null
      };
}

class Data {
  final int? totalRecords;
  final int? result;
  final List<UserMemberData> data;

  Data({
    this.totalRecords,
    this.result,
    this.data = const [],
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        totalRecords: json["totalRecords"],
        result: json["result"],
        data: json["data"] == null
            ? []
            : List<UserMemberData>.from(
                json["data"]!.map((x) => UserMemberData.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "totalRecords": totalRecords,
        "result": result,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

sealed class MemberData {
  final int? id;
  final UserData? user;

  MemberData({
    this.id,
    this.user,
  });

// factory MemberData.fromJson(Map<String, dynamic> json) => MemberData(
//   id: json["id"],
//   user: json["User"] == null ? null : UserData.fromJson(json["User"]),
// );
//
// Map<String, dynamic> toJson() => {
//   "id": id,
//   "User": user?.toJson(),
// };
}

class AdminMemberData extends MemberData {
  final int? id;
  final UserData? user;

  AdminMemberData({
    this.id,
    this.user,
  });

  factory AdminMemberData.fromJson(Map<String, dynamic> json) =>
      AdminMemberData(
        id: json["id"],
        user: json["User"] == null ? null : UserData.fromJson(json["User"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "User": user?.toJson(),
      };
}

class UserMemberData extends MemberData {
  final int? id;
  final UserData? user;

  UserMemberData({
    this.id,
    this.user,
  });

  factory UserMemberData.fromJson(Map<String, dynamic> json) => UserMemberData(
        id: json["id"],
        user: json["User"] == null ? null : UserData.fromJson(json["User"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "User": user?.toJson(),
      };
}
